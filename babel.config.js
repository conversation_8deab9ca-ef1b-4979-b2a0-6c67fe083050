const path = require("path");
const { depConfig } = require("./localDebugConfig");

// 本地link依赖common时，需要对alias配置进行特殊处理
function getAliasConfig() {
  const dep = depConfig["@akulaku-rn/akulaku-ec-common"];
  if(dep && dep.startsWith("link:")){
    const depPath = dep.replace(/^(file:|link:)/, "");
    return {
      nativeModules: path.resolve(depPath, "src/nativeModules"),
      reporter: path.resolve(depPath, "src/reporter"),
      common: path.resolve(depPath, "src"),
    }
  }else {
    return {
      nativeModules: "./node_modules/@akulaku-rn/akulaku-ec-common/src/nativeModules",
      reporter: "./node_modules/@akulaku-rn/akulaku-ec-common/src/reporter",
      common: "./node_modules/@akulaku-rn/akulaku-ec-common/src",
    }
  }
}

module.exports = {
  presets: ["module:metro-react-native-babel-preset"],
  plugins: [
    [
      "@babel/plugin-proposal-decorators",
      {
        legacy: true
      }
    ],
    [
      "module-resolver",
      {
        root: ["./"],
        alias: {
          "react-dom": "react-dom/profiling",
          "scheduler/tracing": "scheduler/tracing-profiling",
          "@al/reporter-v4-sdk": "@al/reporter-v4-sdk/dist/server.js",
          ...getAliasConfig()
        }
      }
    ]
  ]
  // "env": {
  //   "production": {
  //     "plugins": [
  //       "transform-remove-console",
  //     ],
  //   },
  // },
};
