/*
 * @Description: 本地调试配置
 */

// 本地调试依赖配置
const depConfig = {
  // "akulaku-ec-trade": "link:../akulaku-ec-trade",
  // "akulaku-ec-pay": "link:../akulaku-ec-pay",
  // "akulaku-ec-bill": "link:../akulaku-ec-bill",
  // "akulaku-ec-user": "link:../akulaku-ec-user",
  // "akulaku-ec-activity": "link:../akulaku-ec-activity",
  // "@akulaku-rn/akulaku-ec-common": "link:../akulaku-ec-common"
};
// 加载其他业务包
function loadOtherBuz(current, AppRegistry) {
  if (__DEV__) {
    try {
      const buzList = ["trade", "pay", "user", "bill", "activity"];
      for (const buz of buzList) {
        if (buz !== current) {
          let App;
          switch (buz) {
            case "activity":
              App = require("akulaku-ec-activity/src/App").default;
              break;
            case "bill":
              App = require("akulaku-ec-bill/src/App").default;
              break;
            case "pay":
              App = require("akulaku-ec-pay/src/App").default;
              break;
            case "trade":
              App = require("akulaku-ec-trade/src/App").default;
              break;
            case "user":
              App = require("akulaku-ec-user/src/App").default;
              break;
          }
          AppRegistry.registerComponent(buz, () => App);
        }
      }
    } catch (e) {
      console.log("加载其他业务包失败:", e);
    }
  }
}

module.exports = {
  depConfig,
  loadOtherBuz
};
