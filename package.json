{"name": "akulaku-ec-user", "version": "1.0.0-in", "private": true, "rnModuleName": "main", "main": "index.js", "scripts": {"start": "react-native start", "al-translate": "al-<PERSON>", "dev": "cross-env NODE_ENV=development webpack-dev-server --config ./web/config/webpack.dev.conf.js --process", "build": "cross-env NODE_ENV=production webpack --config ./web/config/webpack.prod.conf.js", "ts-compile-check": "tsc -p tsconfig.json", "eslint": "eslint ./ --ext .ts,.tsx --fix", "i18n": "node ./script/createI18n.js", "checkImage": "node ./script/checkImage.js", "test": "jest", "bundle-android": "node ./node_modules/@akulaku-rn/akulaku-ec-loader/bundleScript/bundleUtil.js --isBuz true --entryFile index.user.js --platform android --forceRelease true", "bundle-android-sourceMap": "node ./node_modules/@akulaku-rn/akulaku-ec-loader/bundleScript/bundleUtil.js --isBuz true --entryFile index.user.js --platform android --forceRelease true --sourceMap true", "bundle-ios": "node ./node_modules/@akulaku-rn/akulaku-ec-loader/bundleScript/bundleUtil.js --isBuz true --entryFile index.user.js --platform ios --forceRelease true", "bundle-android-dev": "yarn run devInstall --needOtherBuz false && node ./node_modules/@akulaku-rn/akulaku-ec-loader/bundleScript/bundleUtil.js --isBuz true --entryFile index.user.js --platform android --forceRelease false", "bundle-ios-dev": "yarn run devInstall --needOtherBuz false && node ./node_modules/@akulaku-rn/akulaku-ec-loader/bundleScript/bundleUtil.js --isBuz true --entryFile index.user.js --platform ios --forceRelease false", "bundle-transform-bytecode-osx": "./node_modules/hermes-engine/osx-bin/hermesc -emit-binary -out ./CodePush/android/user/user.android.bundle.hbc ./CodePush/android/user/user.android.bundle", "bundle-transform-bytecode-linux": "./node_modules/hermes-engine/linux64-bin/hermesc -emit-binary -out ./CodePush/android/user/user.android.bundle.hbc ./CodePush/android/user/user.android.bundle", "bundle-transform-bytecode-win": "cmd /c ./node_modules/hermes-engine/win64-bin/hermesc.exe -emit-binary -out ./CodePush/android/user/user.android.bundle.hbc ./CodePush/android/user/user.android.bundle", "devInstall": "node ./node_modules/@akulaku-rn/rntools-easy/bin/devInstall.js --needOtherBuz true --country in", "formatI18nExport": "node ./node_modules/@akulaku-rn/rntools-easy/bin/formatI18nExport.js", "checkBundleContent": "node ./node_modules/@akulaku-rn/rntools-easy/bin/checkBundleContent/index.js --buzName user --platform ios", "CheckAddImage": "node ./node_modules/@akulaku-rn/rntools-easy/bin/CheckAddImage.js"}, "husky": {"hooks": {"pre-commit": "npm run ts-compile-check && lint-staged"}}, "lint-staged": {"*.{ts,tsx,json}": ["prettier --write --loglevel warn", "git add"]}, "dependencies": {"@akulaku-rn/akulaku-ec-common": "1.0.117-in", "jsencrypt": "3.0.0-rc.1"}, "peerDependencies": {"@akulaku-rn/akui-rn": "*", "@akulaku-rn/react-native-code-push": "*", "@akulaku-rn/rn-v4-sdk": "*", "@react-native-clipboard/clipboard": "*", "@react-native-community/art": "*", "@react-native-community/async-storage": "*", "@react-native-community/cameraroll": "*", "@react-native-community/image-editor": "*", "@react-native-community/netinfo": "*", "@types/color-name": "*", "@types/lodash": "*", "@types/qs": "*", "akulaku-ec-loader": "*", "i18next": "*", "lodash": "*", "lottie-react-native": "*", "mobx": "*", "mobx-react": "*", "native-base": "*", "number-precision": "*", "prop-types": "*", "react": "*", "react-i18next": "*", "react-native": "*", "react-native-fast-image": "*", "react-native-fs": "*", "react-native-linear-gradient": "*", "react-native-modal": "*", "react-native-page-control": "*", "react-native-root-siblings": "*", "react-native-sentry": "*", "react-native-svg": "*", "react-native-swiper": "*", "react-native-view-pdf": "*", "react-native-webview": "*"}, "devDependencies": {"@al/tool-cli-translate": "0.1.1-beta.29", "@babel/core": "^7.5.0", "@babel/plugin-proposal-decorators": "^7.4.4", "@types/react": "16.14.5", "@types/react-native": "0.63.52", "@typescript-eslint/eslint-plugin": "4.26.1", "@typescript-eslint/parser": "5.9.1", "babel-plugin-module-resolver": "^3.2.0", "babel-plugin-transform-remove-console": "^6.9.4", "cross-env": "^5.2.0", "eslint": "7.28.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-react": "7.24.0", "lint-staged": "^9.2.4", "metro-react-native-babel-preset": "0.55.0", "prettier": "^1.18.2", "typescript": "4.3.4", "webp-converter": "^2.3.3", "yarn": "^1.17.3"}}