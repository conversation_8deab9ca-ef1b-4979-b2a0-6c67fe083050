/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

/**
 * 符号链接调试组件使用
 */
const path = require("path");
const { depConfig } = require("./localDebugConfig");

// link依赖时，需要
function getWatchFolders() {
  const watchFolders = [];
  for (const key of Object.keys(depConfig)) {
    const dep = depConfig[key];
    if (dep.startsWith("link:")) {
      // 剔除file:link:
      const depPath = dep.replace(/^(file:|link:)/, "");
      watchFolders.push(path.resolve(depPath));
    }
  }
  return watchFolders;
}

// module.exports = {
//   transformer: {
//     getTransformOptions: async () => ({
//       transform: {
//         experimentalImportSupport: false,
//         inlineRequires: false,
//       },
//     }),
//   },
// };
/**
 * 符号链接调试组件使用
 */
module.exports = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false
      }
    })
  },
  resolver: {
    extraNodeModules: new Proxy({}, { get: (_, name) => path.resolve(".", "node_modules", name) })
  },

  // quick workaround for another issue with symlinks
  watchFolders: [...getWatchFolders()],
  cacheVersion: "aku_in"
};
