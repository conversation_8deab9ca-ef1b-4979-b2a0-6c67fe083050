{
  "compilerOptions": {
    "experimentalDecorators": true,
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "jsx": "react-native",
    "lib": ["esnext", "dom"],
    "moduleResolution": "node",
    "noEmit": true,
    "strict": true,
    "target": "esnext",
    "skipLibCheck": true,
    "incremental": true,
    "sourceMap": true,
    "resolveJsonModule": true,
    "baseUrl": "./" /* Base directory to resolve non-absolute module names. */,
    "paths": {
      "nativeModules/*": [
        "./node_modules/@akulaku-rn/akulaku-ec-common/src/nativeModules/*",
        "../@akulaku-rn/akulaku-ec-common/src/nativeModules/*"
      ],
      "reporter/*": [
        "./node_modules/@akulaku-rn/akulaku-ec-common/src/reporter/*",
        "../@akulaku-rn/akulaku-ec-common/src/reporter/*"
      ],
      "common/*": ["./node_modules/@akulaku-rn/akulaku-ec-common/src/*", "../@akulaku-rn/akulaku-ec-common/src/*"]
    }
  },
  "exclude": ["node_modules", "babel.config.js", "metro.config.js", "./web", "dist"]
}
