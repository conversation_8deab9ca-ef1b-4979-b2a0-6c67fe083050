module.exports = {
  parser: "@typescript-eslint/parser", // 定义ESLint的解析器
  extends: [
    "prettier",
    "plugin:react/recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended",
  ],
  plugins: ["@typescript-eslint", "react", "prettier"],
  settings: {
    react: {
      pragma: "React",
      version: "detect",
    }
  },
  parserOptions: {
    ecmaVersion: 2019,
    sourceType: "module",
    ecmaFeatures: {
      legacyDecorators: true,
      jsx: true,
    },
  },
  rules: {
    // 自定义规则
    // 强烈建议 **不使用**，以保证不同工程间的代码风格一致
    "react/no-unescaped-entities": ["error", { forbid: [">", "}"] }],
    "no-unused-vars": "off",
    // 'no-console': 'error',
    "no-debugger": "error",
    eqeqeq: ["error", "always"],
    "lines-between-class-members": ["error", "always"],
    // 'react/prop-types': ['error', { ignore: ['ctx', 'dispatch', 't'], skipUndeclared: true }],
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/ban-ts-ignore": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/camelcase": "off",
    "@typescript-eslint/no-namespace": "off",
    "@typescript-eslint/no-use-before-define": "off",
    "@typescript-eslint/consistent-type-assertions": "off",
    "@typescript-eslint/no-empty-function": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/ban-types": 1,
    "@typescript-eslint/no-var-requires": 1,
    "react/sort-comp": 1,
    "react/require-render-return": 1
  },
};
