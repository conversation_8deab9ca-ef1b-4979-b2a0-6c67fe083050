// 使用
const webp = require("webp-converter");
const fs = require("fs");

// this will grant 755 permission to webp executables
webp.grant_permission();

//pass input image(.jpeg,.pnp .....) path ,output image(give path where to save and image file name with .webp
// extension)
//pass option(read  documentation for options)
//cwebp(input,output,option,result_callback)

// const result = webp.cwebp("/Users/<USER>/WebstormProjects/react-native/script/ad_close.png", "/Users/<USER>/WebstormProjects/react-native/script/ad_close.webp", "-lossless", logging = "-v");
// result.then(response => {
//   console.log("zhangxiao", "response-->" + response);
// });

console.log("zhangxiao", "process.argv-->" + process.argv[2]);
let compressSize = 0;
fs.readdir(process.argv[2], async (err, files) => {
  if (err) {
    console.log("zhangxiao", "err-->" + JSON.stringify(err));
  } else {
    for (let fileName of files) {
      if (fileName.includes(".png")) {
        await webp.cwebp(
          process.argv[2] + "/" + fileName,
          process.argv[2] + "/" + fileName.replace(".png", ".webp"),
          "-lossless",
          (logging = "-v")
        );
        const originFile = fs.statSync(process.argv[2] + "/" + fileName);
        const convertFile = fs.statSync(process.argv[2] + "/" + fileName.replace(".png", ".webp"));
        console.log("zhangxiao", "originFile.size-->" + originFile.size);
        console.log("zhangxiao", "convertFile.size-->" + convertFile.size);
        if (originFile.size > convertFile.size) {
          fs.rmSync(process.argv[2] + "/" + fileName);
        } else {
          fs.rmSync(process.argv[2] + "/" + fileName.replace(".png", ".webp"));
        }
        compressSize += originFile.size - convertFile.size;
      }
    }
    console.log("zhangxiao", "本次共压缩" + compressSize + "B");
  }
});
