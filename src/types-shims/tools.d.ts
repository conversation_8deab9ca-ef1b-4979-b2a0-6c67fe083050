declare module "react-native-web-swiper" {
  const reactNativeWebSwiper: any;
  export default reactNativeWebSwiper;
}

declare module "react-native-page-control" {
  const reactNativePageControl: any;
  export default reactNativePageControl;
}

declare module "react-native-swiper" {
  import { ViewStyle } from "react-native";
  interface SwiperProps {
    disableNextButton?: boolean;
    scrollViewStyle?: ViewStyle;
    containerStyle?: ViewStyle;
  }
}

declare module "*.png";
declare module "*.jpg";
declare module "*.gif";
declare module "*.webp";
declare module "react-native-easing";
