import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ceEventEmitter,
  EmitterSubscription,
  Image,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View
} from "react-native";
import RootView from "common/components/Layout/RootView";
import { inject, observer } from "mobx-react";
import { withTranslation } from "common/services/i18n";
import { SupportReactNativeVideo } from "@akulaku-rn/akulaku-ec-loader/src/constant";
import { AkuTextComponentType, Android } from "@akulaku-rn/akui-rn/src/components/index";
import BaseContainer, { Type } from "common/components/BaseContainer";
import VideoPageStore from "./store";
import { AkuTextComponent, Loading, NAV_BAR_HEIGHT, NavigationBar } from "@akulaku-rn/akui-rn";
import { OnLoadData, OnProgressData } from "react-native-video";
import VideoSlider from "./VideoSlider";
import NativePipModule from "common/nativeModules/basics/NativePipModule";
import { getWindowSize } from "@akulaku-rn/akui-rn/src/components/DeviceInfo";

const Video = SupportReactNativeVideo ? require("react-native-video").default : View;

type Props = {
  uri: string;
  poster?: string;
};

type State = {
  pipModule: boolean;
  fullScreen: boolean;
  showControls: boolean;
  isPaused: boolean;
  muted: boolean;
  isFullScreen: boolean;
  duration: number;
  naturalSize: {
    height: number;
    width: number;
    orientation: "portrait" | "landscape";
  } | null;
  currentTime: number;
};

const { width, height } = getWindowSize();
let WINDOW_WIDTH = width;
let WINDOW_HEIGHT = height;

const IconSize = { height: 20, width: 20 };
const IconMarginSize = 16;
const VideoTextMarginRight = 8;
const PortraitHorizontal = 16;
const LandscapeHorizontal = 66;
const TimeTextWidth = 70;

@RootView({
  store: VideoPageStore
})
@inject("store")
@withTranslation("VideoPage")
@observer
export default class VideoPage extends BaseContainer<VideoPageStore, Props, {}, State> {
  private videoScale: Animated.Value;

  private rotation: Animated.Value;

  private rotationInterpolate: Animated.AnimatedInterpolation;

  constructor(props: Type.Props<VideoPageStore, Props>) {
    super(props);
    this.state = {
      pipModule: false,
      fullScreen: false,
      showControls: true,
      isPaused: false,
      isFullScreen: false,
      muted: false,
      duration: 0,
      currentTime: 0,
      naturalSize: null
    };
    this.videoScale = new Animated.Value(1); // 缩放动画
    this.rotation = new Animated.Value(0); // 初始化旋转动画值
    this.rotationInterpolate = this.rotation.interpolate({
      inputRange: [0, 1], // 0 表示竖屏，1 表示横屏
      outputRange: ["0deg", "90deg"] // 对应旋转角度
    });
    const { width, height } = getWindowSize();
    WINDOW_WIDTH = width;
    WINDOW_HEIGHT = height;
  }

  pictureInPictureListener?: EmitterSubscription;

  componentDidMount(): void {
    this.props.store.runtime.setBackgroundColor("#000");
    Loading.show({ hasBackcolor: false, useBack: true });
    const screen = this.props.store.navParams.screen;
    BackHandler.addEventListener("hardwareBackPress", this.onBack);
    this.pictureInPictureListener = DeviceEventEmitter.addListener(screen, event => {
      if (event.eventName === "InPictureInPictureMode") {
        this.setState({
          pipModule: true,
          showControls: false
        });
      } else if (event.eventName === "OutPictureInPictureMode") {
        const { width, height } = getWindowSize();
        WINDOW_WIDTH = width;
        WINDOW_HEIGHT = height;
        this.setState({
          pipModule: false,
          showControls: true
        });
      } else if (event.eventName === "OutPictureInPictureModeClosePage") {
        this.playerRef = null;
        this.setState({
          pipModule: false,
          showControls: true,
          isPaused: true
        });
      }
    });
  }

  onBack = () => {
    if (this.state.isFullScreen) {
      this.toggleFullScreen();
      return true;
    }
    return false;
  };

  componentWillUnmount(): void {
    !!this.pictureInPictureListener && this.pictureInPictureListener.remove();
    BackHandler.removeEventListener("hardwareBackPress", this.onBack);
  }

  playerRef: any;

  setPlayerRef = (ref: any) => {
    this.playerRef = ref;
  };

  handlePlayPause = () => {
    if (this.state.currentTime === this.state.duration) {
      this.playerRef.seek(0);
    }
    this.setState({
      isPaused: !this.state.isPaused
    });
  };

  handleMute = () => {
    this.setState({
      muted: !this.state.muted
    });
  };

  // 获取转换成横屏视频的缩放比例
  getScaleRate = () => {
    if (!this.state.naturalSize) return 1;
    const { width, height } = this.state.naturalSize;
    if (!width || !height) return 1;
    const scaleWidth = WINDOW_WIDTH / width; // 宽度的缩放比例
    const scaleHeight = WINDOW_HEIGHT / height; // 高度的缩放比例
    const fullScaleWidth = WINDOW_HEIGHT / width;
    const fullScaleHeight = WINDOW_WIDTH / height;
    const screenRate = Math.min(scaleWidth, scaleHeight);
    const fullScreenRate = Math.min(fullScaleWidth, fullScaleHeight);
    return fullScreenRate / screenRate;
  };

  toggleFullScreen = () => {
    const isFullScreen = this.state.isFullScreen;
    if (isFullScreen) {
      (this.props.store.runtime as any).setPaddingBottom(true);
      // 退出全屏动画
      Animated.parallel([
        Animated.timing(this.videoScale, {
          toValue: 1, // 恢复缩放
          duration: 300,
          useNativeDriver: false
        }),
        Animated.timing(this.rotation, {
          toValue: 0, // 恢复到初始角度（竖屏）
          duration: 300,
          useNativeDriver: false
        })
      ]).start(() => {
        this.setState({ isFullScreen: false });
      });
    } else {
      (this.props.store.runtime as any).setPaddingBottom(false);
      // 进入全屏动画
      Animated.parallel([
        Animated.timing(this.videoScale, {
          toValue: this.getScaleRate(), // 放大比例
          duration: 300,
          useNativeDriver: false
        }),
        Animated.timing(this.rotation, {
          toValue: 1, // 恢复到初始角度（竖屏）
          duration: 300,
          useNativeDriver: false
        })
      ]).start(() => {
        this.setState({ isFullScreen: true });
      });
    }
  };

  togglePip = () => {
    if (this.state.isFullScreen) {
      this.videoScale.setValue(1);
      this.rotation.setValue(0);
    }
    this.setState({ pipModule: !this.state.pipModule, isFullScreen: false });
    try {
      NativePipModule.enterPipModule();
    } catch (e) {
      console.log("e", e);
    }
  };

  onLoad = (data: OnLoadData) => {
    Loading.dismiss();
    this.setState({
      duration: data.duration,
      naturalSize: data.naturalSize
    });
  };

  renderNavigationBar = () => {
    return null;
  };

  formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };

  onProgress = (data: OnProgressData) => {
    this.setState({
      currentTime: data.currentTime
    });
  };

  onEnd = () => {
    // this.playerRef.seek(0);
    this.setState({
      currentTime: this.state.duration,
      isPaused: true
    });
  };

  onValuesChangeStart = () => {
    this.setState({
      isPaused: true
    });
  };

  onValuesChangeFinish = (values: number[]) => {
    this.playerRef.seek(values[0]);
    this.setState({
      isPaused: false,
      currentTime: values[0]
    });
  };

  onValuesChange = (values: number[]) => {
    this.setState({
      currentTime: values[0]
    });
  };

  getSliderWidth = () => {
    const iconWidth = IconSize.width * 3;
    const iconMarginWidth = IconMarginSize * 3 + VideoTextMarginRight;
    const NormalWidth = TimeTextWidth + iconWidth + iconMarginWidth;
    return this.state.isFullScreen
      ? WINDOW_HEIGHT - NormalWidth - LandscapeHorizontal * 2
      : WINDOW_WIDTH - NormalWidth - PortraitHorizontal * 2;
  };

  renderControls = () => {
    return (
      <View
        style={[
          {
            width: WINDOW_WIDTH,
            height: NAV_BAR_HEIGHT,
            backgroundColor: "transparent",
            justifyContent: "flex-end",
            position: "absolute",
            bottom: 0,
            left: 0,
            paddingHorizontal: PortraitHorizontal
          }
        ]}
      >
        <View style={styles.controls}>
          {/* 播放/暂停按钮 */}
          <TouchableOpacity
            style={{ marginRight: IconMarginSize }}
            hitSlop={{ left: 4, right: 4, top: 4, bottom: 4 }}
            onPress={this.handlePlayPause}
          >
            <Image
              source={this.state.isPaused ? require("./images/play.webp") : require("./images/pause.webp")}
              style={IconSize}
            />
          </TouchableOpacity>
          <View
            style={{
              flexDirection: "row",
              marginRight: VideoTextMarginRight,
              justifyContent: "space-between",
              alignItems: "center",
              width: TimeTextWidth
            }}
          >
            <AkuTextComponent
              style={{ color: "white", width: 30 }}
              type={AkuTextComponentType.Aku_font_11_bold}
              text={`${this.formatTime(this.state.currentTime)}`}
            />
            <AkuTextComponent
              style={{ color: "white", width: 10 }}
              type={AkuTextComponentType.Aku_font_11_bold}
              text={` / `}
            />
            <AkuTextComponent
              style={{ color: "white", width: 30 }}
              type={AkuTextComponentType.Aku_font_11_bold}
              text={`${this.formatTime(this.state.duration)}`}
            />
          </View>

          {/* 进度条 */}
          <VideoSlider
            onValuesChange={this.onValuesChange}
            onValuesChangeStart={this.onValuesChangeStart}
            onValuesChangeFinish={this.onValuesChangeFinish}
            sliderLength={this.getSliderWidth()}
            durationSecond={Math.ceil(this.state.duration)}
            currentTime={this.state.currentTime | 0}
          />
          <TouchableOpacity
            style={{ marginLeft: IconMarginSize }}
            hitSlop={{ left: 4, right: 4, top: 4, bottom: 4 }}
            onPress={this.handleMute}
          >
            <Image
              source={this.state.muted ? require("./images/mute.webp") : require("./images/un_mute.webp")}
              style={IconSize}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={{ marginLeft: IconMarginSize }}
            hitSlop={{ left: 4, right: 4, top: 4, bottom: 4 }}
            onPress={this.toggleFullScreen}
          >
            <Image
              source={
                this.state.isFullScreen ? require("./images/out_fullscreen.webp") : require("./images/fullscreen.webp")
              }
              style={IconSize}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  renderControlsFullScreen = () => {
    return (
      <View
        style={[
          {
            width: NAV_BAR_HEIGHT,
            height: "100%",
            justifyContent: "center",
            alignItems: "center",
            position: "absolute",
            left: 0,
            paddingHorizontal: PortraitHorizontal
          }
        ]}
      >
        <View
          style={[
            {
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "transparent",
              height: "100%"
            }
          ]}
        >
          {/* 播放/暂停按钮 */}
          <TouchableOpacity
            style={{
              marginBottom: IconMarginSize,
              transform: [
                {
                  rotate: "90deg"
                }
              ]
            }}
            hitSlop={{ left: 4, right: 4, top: 4, bottom: 4 }}
            onPress={this.handlePlayPause}
          >
            <Image
              source={this.state.isPaused ? require("./images/play.webp") : require("./images/pause.webp")}
              style={IconSize}
            />
          </TouchableOpacity>
          <View
            style={{
              flexDirection: "row",
              marginBottom: IconMarginSize,
              justifyContent: "space-between",
              alignItems: "center",
              width: TimeTextWidth,
              height: TimeTextWidth,
              transform: [
                {
                  rotate: "90deg"
                }
              ]
            }}
          >
            <AkuTextComponent
              style={{ color: "white", width: 30 }}
              type={AkuTextComponentType.Aku_font_11_bold}
              text={`${this.formatTime(this.state.currentTime)}`}
            />
            <AkuTextComponent
              style={{ color: "white", width: 10 }}
              type={AkuTextComponentType.Aku_font_11_bold}
              text={` / `}
            />
            <AkuTextComponent
              style={{ color: "white", width: 30 }}
              type={AkuTextComponentType.Aku_font_11_bold}
              text={`${this.formatTime(this.state.duration)}`}
            />
          </View>
          {/* 进度条 */}
          <View style={{ width: NAV_BAR_HEIGHT, height: this.getSliderWidth() }}>
            <VideoSlider
              onValuesChange={this.onValuesChange}
              onValuesChangeStart={this.onValuesChangeStart}
              onValuesChangeFinish={this.onValuesChangeFinish}
              sliderLength={this.getSliderWidth()}
              durationSecond={Math.ceil(this.state.duration)}
              currentTime={this.state.currentTime | 0}
              vertical={true}
            />
          </View>
          <TouchableOpacity
            style={{
              marginTop: IconMarginSize,
              transform: [
                {
                  rotate: "90deg"
                }
              ]
            }}
            hitSlop={{ left: 4, right: 4, top: 4, bottom: 4 }}
            onPress={this.handleMute}
          >
            <Image
              source={this.state.muted ? require("./images/mute.webp") : require("./images/un_mute.webp")}
              style={IconSize}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              marginTop: IconMarginSize,
              transform: [
                {
                  rotate: "90deg"
                }
              ]
            }}
            hitSlop={{ left: 4, right: 4, top: 4, bottom: 4 }}
            onPress={this.toggleFullScreen}
          >
            <Image
              source={
                this.state.isFullScreen ? require("./images/out_fullscreen.webp") : require("./images/fullscreen.webp")
              }
              style={IconSize}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  _render() {
    return (
      <>
        {!this.state.isFullScreen && !this.state.pipModule && (
          <NavigationBar
            renderRight={
              NativePipModule.supportPip ? (
                <TouchableOpacity hitSlop={{ left: 4, right: 4, top: 4, bottom: 4 }} onPress={this.togglePip}>
                  <Image source={require("./images/pip.webp")} style={IconSize} />
                </TouchableOpacity>
              ) : null
            }
            title={""}
            containerStyle={{ backgroundColor: "#000" }}
            darkMode={true}
          />
        )}
        <View style={{ flex: 1, backgroundColor: "black", justifyContent: "center" }}>
          <Animated.View
            style={[
              {
                flex: 1,
                backgroundColor: "transparent",
                transform: [
                  { scale: this.videoScale }, // 动画缩放
                  { rotate: this.rotationInterpolate }
                ]
              }
            ]}
          >
            <Video
              onProgress={this.onProgress}
              onEnd={this.onEnd}
              onLoad={this.onLoad}
              muted={this.state.muted}
              paused={this.state.isPaused}
              ref={this.setPlayerRef}
              poster={this.props.store.navParams.poster}
              posterResizeMode={"contain"}
              source={{ uri: this.props.store.navParams.uri }} // Example video URL
              style={[
                this.state.isFullScreen
                  ? styles.videoLandscape
                  : this.state.pipModule
                  ? styles.videoPipModule
                  : styles.videoPortrait
              ]}
              controls={false} // Show video controls
              pictureInPicture={this.state.pipModule} // Enable PiP
              resizeMode="contain" // Video scaling
            />
          </Animated.View>
          {!this.state.isFullScreen && !this.state.pipModule && <View style={{ height: NAV_BAR_HEIGHT }} />}
          {!this.state.pipModule && (this.state.isFullScreen ? this.renderControlsFullScreen() : this.renderControls())}
          {this.state.isFullScreen && (
            <TouchableOpacity
              onPress={this.toggleFullScreen}
              style={[
                {
                  backgroundColor: "rgba(0,0,0,0.5)",
                  borderRadius: 16,
                  position: "absolute",
                  right: 24,
                  top: (StatusBar.currentHeight || 0) + 24,
                  transform: [{ rotate: "90deg" }]
                }
              ]}
            >
              <Image
                source={require("@akulaku-rn/akui-rn/src/components/NavigationBar/img/icon_back_light.png")}
                style={{
                  height: 32,
                  width: 32
                }}
              />
            </TouchableOpacity>
          )}
          {this.state.isFullScreen && NativePipModule.supportPip && (
            <TouchableOpacity
              style={[
                {
                  backgroundColor: "rgba(0,0,0,0.5)",
                  borderRadius: 16,
                  position: "absolute",
                  right: 30,
                  bottom: 24,
                  transform: [{ rotate: "90deg" }]
                }
              ]}
              hitSlop={{ left: 4, right: 4, top: 4, bottom: 4 }}
              onPress={this.togglePip}
            >
              <Image source={require("./images/pip.webp")} style={IconSize} />
            </TouchableOpacity>
          )}
        </View>
      </>
    );
  }
}

const styles = StyleSheet.create({
  controls: {
    // paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    height: 56
  },
  videoLandscape: { width: "100%", height: "100%", justifyContent: "center", backgroundColor: "black" },
  videoPortrait: {
    flex: 1,
    justifyContent: "center",
    backgroundColor: "black"
  },
  videoPipModule: {
    flex: 1,
    justifyContent: "center",
    backgroundColor: "black"
  },
  slider: {
    flex: 1,
    marginHorizontal: 10
  }
});
