import React, { useEffect, useState } from "react";
import { View, StyleSheet } from "react-native";
import MultiSlider, { MarkerProps } from "common/components/MulitiSlider";
import { FigmaStyle } from "@akulaku-rn/akui-rn";

interface VideoSliderProps {
  durationSecond: number;
  currentTime: number;
  onValuesChangeStart?: () => void;
  onValuesChangeFinish?: (values: number[]) => void;
  onValuesChange?: (values: number[]) => void;
  sliderLength: number;
  vertical?: boolean;
}

const VideoSlider = (props: VideoSliderProps) => {
  const CustomMarker = ({ markerStyle }: MarkerProps) => {
    return <View style={{ height: 10, width: 10, borderRadius: 5, backgroundColor: "white" }} />;
  };

  const [time, setTime] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(time + 1);
    }, 1000);
    return () => {
      clearInterval(timer);
    };
  });

  const getMax = (max: number) => {
    if (!max) return 1;
    if (max <= 1) return 1;
    return max - 1;
  };

  return (
    <View style={styles.container}>
      <MultiSlider
        onValuesChangeStart={props.onValuesChangeStart}
        onValuesChangeFinish={props.onValuesChangeFinish}
        onValuesChange={props.onValuesChange}
        values={[props.currentTime]}
        markerContainerStyle={{ height: 50, backgroundColor: "transparent" }}
        containerStyle={{
          height: 32,
          ...(props.vertical ? { transform: [{ translateY: props.sliderLength / 2 - 10 }] } : {})
        }}
        enableLabel={false}
        customMarker={CustomMarker}
        selectedStyle={{ backgroundColor: FigmaStyle.Color.Primary_6 }}
        markerStyle={{
          height: 10,
          width: 8
        }}
        trackStyle={{ backgroundColor: "gray", height: 4, borderRadius: 2 }}
        sliderLength={props.sliderLength - 8}
        step={1}
        min={0}
        max={getMax(props.durationSecond || 1)}
        vertical={props.vertical}
        verticalDown={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 20,
    // width: 200,
    // backgroundColor: "pink",
    justifyContent: "center",
    alignItems: "center"
  }
});

export default VideoSlider;
