import React, { Component } from "react";
import {
  DeviceEventEmitter,
  FlatList,
  Image,
  NativeModules,
  Platform,
  requireNativeComponent,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from "react-native";
import { Loading, NavigationBar, WINDOW_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import RootView from "common/components/Layout/RootView";
import CameraRoll from "@react-native-community/cameraroll";
import { inject, observer } from "mobx-react";
import store from "./store";
import styles from "./styles";
import { withTranslation } from "common/services/i18n";
import { Props } from "common/components/BaseContainer/Type";
import { NativeAkuAddressModule, NativeNavigationModule } from "@akulaku-rn/akulaku-ec-common/src/nativeModules";
import { RetrieveData, StoreData } from "common/util/cache";
import NativeToast from "nativeModules/basics/nativeUIModule/Toast";

type State = {
  currentRank: number;
  activityID: number | null;
  categoryId: number | undefined;
  text: string;
  networkError: boolean;
};

@RootView({ store })
@inject("store")
@withTranslation("home")
@observer
export default class HomeScreen extends Component<Props<store>, State> {
  constructor(props: Props<store>) {
    super(props);
    props.configPageInfo({ sn: 10000 }, true);
  }

  navigationTo(name: string, params = {}) {
    if (!name) alert("未定义路由名称");
    this.props.navigation.navigate({
      screen: name,
      params
    });
  }

  showToast = async () => {
    // Toast.show("测试Toast", {
    //   duration: 300,
    //   position: 0
    // });
  };

  render() {
    const {
      navigation,
      store: { pageStore },
      io
    } = this.props;
    return (
      <View style={{ flex: 1 }}>
        <NavigationBar title="用户模块首页" />
        <ScrollView
          keyboardShouldPersistTaps={"always"}
          contentContainerStyle={styles.container}
          scrollEventThrottle={16}
        >
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={() => {
                this.props.navigation.navigate({
                  screen: "CreditResultPage",
                  params: { data: {} }
                });
              }}
            >
              授信结果页
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={() => {
                this.navigationTo("AuthorizeCreditUpdate");
              }}
            >
              授信资料修改
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={() => {
                this.navigationTo("helpCenterHomeScreen");
              }}
            >
              帮助中心
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={() => {
                this.navigationTo("ResultPage");
              }}
            >
              提额结果页
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={() => {
                this.navigationTo("IncreaseLimitV2");
              }}
            >
              新的额度中心
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={() => {
                this.navigationTo("ReportFraudTicketHome");
              }}
            >
              举报欺诈工单
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={() => {
                console.log("s");
                navigation.navigate({
                  url:
                    "ak://m.akulaku.com/1602?screen=/user/order-authorize-credit/apply-info&redirectUrl=https%3A%2F%2Ftest-ec-mall.akulaku.com%2Fec-basic%2Flarge-loan%2Fcredit%3FbusinessType%3D5%26orderToken%3D0x019i9GSmzCHeNjp1xtEvUQoSBcJ0z6S3BYbijUY2JPbga1fGvvWdY4a71xlh879qhkw7edXSl_Ok6N56x6bzC8vNhH8sFRKVXvIW-SnIr9Eumc70uClI688T5H_lLcBbnmkToLafFdtvfx1AvAm55k6DylpuM5U6P2dIKnXU4PLrz-cIWoO_5q2wki99YPMxhYIPAzBg7E-S1x7cJbZbXW2g%26virtualOrderId%3D13446%26deliveryType%3D1%26refNo%3DSALE13446-QR17253%26appId%3D202111122657%26isRetrieveOrder%3Dfalse%26isReCredit%3Dfalse%26orderId%3D1833806968211169282%26productId%3D57&qrCode=AKULAKU00020sq1WUQIvltnq6NILw2Ypx-l80Fu5HD9pKMTCg7PKsE&orderId=1833806968211169282"
                });
              }}
            >
              线下大额授信
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={() => {
                this.navigationTo("TestHtml");
              }}
            >
              HTML TEST
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={() => {
                this.navigationTo("VideoPage", { uri: "https://www.w3schools.com/html/mov_bbb.mp4" });
              }}
            >
              VideoPage
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={async () => {
                const {
                  store: {
                    pageStore,
                    runtime: { uid }
                  }
                } = this.props;
                try {
                  const data = await RetrieveData(`${uid}useCreditDatas`);
                  data[1103010] = "110301005";
                  console.log("JSON.stringify(data)", JSON.stringify(data));
                  StoreData(`${uid}useCreditDatas`, JSON.stringify(data)).then(res => {
                    console.log("修改成功");
                    NativeToast.showMessage("修改成功");
                  });
                } catch (e) {
                  console.log("e", e);
                }
              }}
            >
              授信添加 11030100
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonContainerNew}>
            <Text
              style={styles.btn}
              onPress={async () => {
                NativeNavigationModule.navigate({
                  url: "ak://m.akulaku.com/1602?screen=/user/authorize-credit/authorize-credit-update"
                });
              }}
            >
              跳转授信更新资料
            </Text>
          </TouchableOpacity>
          <Text>{`current hot update package version: ${pageStore.version}`}</Text>
        </ScrollView>
      </View>
    );
  }
}
