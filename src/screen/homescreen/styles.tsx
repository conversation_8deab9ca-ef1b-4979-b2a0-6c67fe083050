import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    paddingTop: 20,
    alignItems: "center",
    paddingHorizontal: 10
  },
  hello: {
    fontSize: 20,
    textAlign: "center",
    margin: 10
  },
  button: {
    marginVertical: 5
  },
  buttonContainerNew: {
    marginBottom: 10,
    marginHorizontal: 10,
    padding: 10,
    backgroundColor: "red"
  },
  btn: {
    color: "#fff",
    fontSize: 14,
    lineHeight: 16
  },
  section: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center"
  },
  title: {
    textAlign: "center",
    fontSize: 20,
    fontWeight: "bold",
    marginVertical: 32
  },
  divider: {
    height: 70
  },
  shadow1: {
    borderRadius: 20,
    shadowOpacity: 0.25,
    shadowColor: "black",
    shadowRadius: 20,
    backgroundColor: "#ECF0F3",
    width: 300,
    height: 120
  },
  shadow2: {
    borderRadius: 100,
    shadowOpacity: 0.25,
    shadowColor: "purple",
    shadowRadius: 20,
    shadowOffset: { width: 20, height: 20 },
    backgroundColor: "#ECF0F3",
    width: 200,
    height: 200
  },
  shadow3: {
    borderRadius: 20,
    shadowOpacity: 0.25,
    shadowColor: "black",
    shadowRadius: 20,
    backgroundColor: "#ECF0F3",
    width: 300,
    height: 120
  },
  shadow4: {
    borderRadius: 100,
    shadowOpacity: 0.25,
    shadowColor: "purple",
    shadowRadius: 20,
    shadowOffset: { width: 20, height: 20 },
    backgroundColor: "#ECF0F3",
    width: 200,
    height: 200
  }
});
