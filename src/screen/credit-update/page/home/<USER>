import React, { Component } from "react";
import { inject, observer } from "mobx-react";
import {
  FlatList,
  StatusBar,
  View,
  ViewabilityConfigCallbackPairs,
  SectionList,
  DeviceEventEmitter,
  EmitterSubscription
} from "react-native";
import _, { isNil } from "lodash";
import store from "../../store";
import styles from "./styles";
import {
  AKDialog,
  Android,
  iOS,
  Loading,
  NavigationBar,
  NetworkErrorComponent,
  UrlImage,
  WINDOW_WIDTH
} from "@akulaku-rn/akui-rn";
import ResultPage from "../../components/ResultPage";
import { EnterLeavePage } from "../../tool/EventTracking";
import DataPage from "../../components/DataPage";
import RootView from "common/components/Layout/RootView";
import { NativeActionLogModule, NativeNavigationModule } from "common/nativeModules";
import { withTranslation } from "common/services/i18n";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { TFunction } from "i18next";
import { configSensorEvent } from "common/components/BaseContainer/Type";
import NativeEventModule from "common/nativeModules/bussinse/nativeEventModule";
import api from "../../store/api";
import { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import { CREDIT_UPDATE_EVENT } from "../../constant";
import { ReportScene } from "common/nativeModules/basics/nativeEventReportModule";
import { DeleteData, RetrieveData } from "common/util/cache";

export type ApplyInfoNavParams = {
  callBackUrl: string; //回调地址
};

const PAGES = [1, 2];

type Props = {
  navigation: navigationModel;
  store: pageStoreModel<store>;
  t: TFunction;
  configSensorEvent: configSensorEvent;
};

type States = {
  loadFailed: boolean;
  review: boolean;
  data: any[];
  indexNum: number;
  tips: any;
  statusData: any;
};
@RootView({
  withI18n: [
    "AuthorizeCreditUpdate",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store,
  keyApi: ["/capi/credit/account/process"]
})
@withTranslation("AuthorizeCreditUpdate")
@inject("store")
@observer
export default class AuthorizeCreditUpdate extends Component<Props, States, ApplyInfoNavParams> {
  flatListRef: any;

  indexNum: number;

  viewabilityConfigCallbackPairs: ViewabilityConfigCallbackPairs;

  pageType: any;

  listener: EmitterSubscription;

  constructor(props: Props) {
    super(props);
    this.state = {
      loadFailed: false,
      review: false,
      data: [],
      indexNum: 0,
      tips: null,
      statusData: null
    };
    if (this.props.store.navParams && this.props.store.navParams.source) {
      props.store.pageStore.updateSource(this.props.store.navParams.source);
    }

    this.indexNum = 0;
    this.pageType = null;
    this.viewabilityConfigCallbackPairs = [
      {
        viewabilityConfig: {
          minimumViewTime: 100,
          viewAreaCoveragePercentThreshold: 80,
          waitForInteraction: false
        },
        onViewableItemsChanged: this._onViewableItemsChanged
      }
    ];

    this.listener = DeviceEventEmitter.addListener(CREDIT_UPDATE_EVENT, e => {
      this.getProcessPage();
    });
  }

  async componentDidMount() {
    const {
      store: { pageStore, runtime },
      navigation
    } = this.props;
    await pageStore.getConfigCompliance();
    pageStore.useCreditData = await RetrieveData(`${runtime.uid}useCreditDatas`);
    if (pageStore.source === 1) {
      // 是否授信通过，授信没通过，去授信页
      NativeActionLogModule.reportDeviceFingerPrint(ReportScene.ENTER_CREDIT_UPDATE);
      const creditPass = await this.props.store.pageStore.getCreditStatus(runtime.countryId);
      if (!creditPass) {
        if (Android) {
          navigation.popToTop();
          NativeNavigationModule.navigate({
            screen: "AuthorizeCreditApplyInfo"
          });
        } else {
          NativeNavigationModule.navigate({
            screen: "AuthorizeCreditApplyInfo",
            params: { gestureEnabled: false },
            popNumber: 0
          });
        }

        return;
      }
    }

    this.getUpdateStatus();
  }

  componentWillUnmount() {
    const enterLeavepageData = { pageType: this.pageType };
    EnterLeavePage(enterLeavepageData);
    this.listener.remove();
  }

  backAction = () => {
    const {
      store: { navParams }
    } = this.props;
    this.goBack();
    const enterLeavepageData = { pageType: this.pageType };
    EnterLeavePage(enterLeavepageData); //上报离开事件，安卓物理返回键
    return true;
  };

  setpageType = (pageType: number) => {
    this.pageType = pageType;
  };

  getUpdateStatus = async () => {
    const {
      store: { pageStore, runtime },
      navigation
    } = this.props;
    Loading.show();
    this.setState({ loadFailed: false }, () => {
      pageStore.post(
        api.STATUS,
        { countryId: runtime.countryId, source: pageStore.source },
        (response: any) => {
          //0: 未申请 1:padding 审核中 、7:reject 审核失败、8:pass 审核成功
          const { success, data } = response;
          // const success = true;
          // const data = { status: 7 };
          Loading.dismiss();
          if (!success) {
            this.alertErrorTips(response.errMsg);
            this.setState({ loadFailed: true });
            return;
          }
          const { status } = data;
          switch (status) {
            case 0:
              this.getProcessPage();
              break;
            case 1: //审核中
            case 7: // 被拒
            case 8: // 成功
              Loading.show();
              setTimeout(() => {
                Loading.dismiss();
                navigation.navigate({
                  screen: "CreditUpdateResultPage",
                  params: { status, gestureEnabled: false }
                });
              }, 800);
              break;
          }
        },
        () => {
          Loading.dismiss();
          this.setState({ loadFailed: true });
        }
      );
    });
  };

  getProcessPage = async () => {
    this.setState({ data: PAGES });
  };

  getProcessPageError = () => {
    this.setState({ loadFailed: true }, () => {
      Loading.dismiss();
    });
  };

  // 如果服务器返回 页数不为2，更新Flatlist数据源
  _onChangeDatas = (dataCount: number) => {
    if (dataCount === this.state.data.length) return;
    this.setState({
      data: Array(dataCount)
        .fill(1)
        .map((item, index) => index + 1)
    });
  };

  renderItem = ({ item }: { item: number }) => {
    const { store, t } = this.props;
    return (
      <DataPage
        nowStep={item}
        pageLength={PAGES.length}
        store={store}
        t={t}
        layerNext={this.layerNext}
        layerGoback={this.layerGoback}
        navigation={this.props.navigation}
        setpageType={this.setpageType}
        onChangeDatas={this._onChangeDatas}
      />
    );
  };

  layerNext = _.debounce(
    () => {
      if (this.indexNum + 1 === PAGES.length) {
        this.creditApply();
      } else {
        this.indexNum++;
        if (this.indexNum) {
          this.flatListRef.scrollToIndex({ viewPosition: 0, index: this.indexNum, animated: false });
        }
      }
    },
    300,
    { leading: true, trailing: false }
  );

  layerGoback = _.debounce(
    () => {
      const { gobackNum } = this.props.store.navParams;
      this.indexNum--;
      if (this.indexNum >= 0) {
        this.flatListRef.scrollToIndex({ viewPosition: 0, index: this.indexNum, animated: false });
      } else {
        NativeEventModule.postWeb({
          eventName: "applyCreditEnd",
          extraData: { applySuccess: false }
        });

        this.goBack();
      }
    },
    300,
    { leading: true, trailing: false }
  );

  goBack = async () => {
    NativeNavigationModule.goBack();
  };

  creditApply = async () => {
    const {
      t,
      navigation,
      store: { pageStore, runtime }
    } = this.props;
    Loading.show({ hasBackcolor: true });
    const { uid, countryId } = runtime;

    pageStore.post(
      api.APPLY,
      { countryId, source: pageStore.source },
      (response: any) => {
        if (response && response.success) {
          Loading.dismiss();
          DeleteData(`${uid}useCreditDatas`);
          navigation.navigate({
            screen: "CreditUpdateResultPage",
            params: { status: 1, gestureEnabled: false }
          });
        } else {
          Loading.dismiss();
          NativeToast.showMessage(response.errMsg);
        }
      },
      (error: any) => {
        Loading.dismiss();
        const { errMsg } = error;
        NativeToast.showMessage(errMsg);
      }
    );
  };

  alertErrorTips = (msg: string) => {
    AKDialog.show({
      type: DialogType.C3_3_1,
      title: msg,
      image: require("../../img/credit_update_error.webp"),
      positiveText: this.props.t("确认"),
      onPositivePress: this.goBack
    });
  };

  keyExtractor = (item: any, index: number) => index.toString();

  _onViewableItemsChanged = ({ viewableItems }: any) => {
    const { pageStore } = this.props.store;
    const item: any = _.maxBy(viewableItems, item => {
      return item.index;
    });
    pageStore.nowStep = item.item;
  };

  getItemLayout = (data: any, index: number) => {
    return {
      length: WINDOW_WIDTH,
      offset: WINDOW_WIDTH * index,
      index
    };
  };

  render() {
    const {
      t,
      store: { navParams }
    } = this.props;
    const { loadFailed, review, indexNum } = this.state;
    if (loadFailed) {
      return (
        <View style={{ flex: 1 }}>
          <NavigationBar title={t("加载中")} />
          <NetworkErrorComponent
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            onRetryPress={this.getUpdateStatus}
            buttonText={t("global:刷新")}
          />
        </View>
      );
    }
    if (review) {
      return (
        <ResultPage
          t={t}
          store={this.props.store}
          gobackNum={navParams.gobackNum}
          callBackUrl={navParams.callBackUrl}
          fromOpenPayToUseAkuPay={navParams.fromOpenPayToUseAkuPay}
        />
      );
    }

    return (
      <View style={styles.container}>
        {iOS && <StatusBar barStyle="dark-content" />}
        <FlatList
          ref={flatList => {
            this.flatListRef = flatList;
          }}
          keyExtractor={this.keyExtractor}
          initialScrollIndex={indexNum}
          overScrollMode={"never"}
          data={this.state.data}
          horizontal={true}
          pagingEnabled={true}
          getItemLayout={this.getItemLayout}
          scrollEnabled={false}
          keyboardShouldPersistTaps={"handled"}
          renderItem={this.renderItem}
          showsHorizontalScrollIndicator={false}
          viewabilityConfigCallbackPairs={this.viewabilityConfigCallbackPairs}
        />
      </View>
    );
  }
}
