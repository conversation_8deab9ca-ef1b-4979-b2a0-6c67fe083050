import { StyleSheet } from "react-native";
import { FontStyles, STATUS_BAR_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#EFF2F6",
    alignItems: "center"
  },
  topView: {
    paddingTop: STATUS_BAR_HEIGHT,
    paddingBottom: 32,
    width: "100%",
    alignItems: "center",
    backgroundColor: "#fff"
  },
  img: {
    width: 136,
    height: 90
  },
  button: {
    marginTop: 20,
    width: WINDOW_WIDTH - 32
  },
  btnStyle: {
    height: 56,
    alignSelf: "flex-end",
    marginRight: 16,
    justifyContent: "center",
    alignItems: "center"
  },
  buttonTitle: {
    fontSize: 16,
    color: "#333",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  title: {
    fontSize: 16,
    color: "#282B2E",
    marginTop: 8,
    marginHorizontal: 24,
    textAlign: "center",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  reason: {
    fontSize: 14,
    color: "#999",
    marginBottom: 16,
    paddingHorizontal: 16,
    textAlign: "center"
  },
  redReason: {
    color: "#E62117",
    fontSize: 14,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  adView: {
    paddingHorizontal: 16,
    backgroundColor: "#F5F5F5",
    width: "100%",
    marginTop: 16
  },
  wrapperStyle: {
    marginBottom: 12,
    borderRadius: 4,
    overflow: "hidden"
  }
});
