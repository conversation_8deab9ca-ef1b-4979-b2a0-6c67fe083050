import React, { PureComponent } from "react";
import {
  View,
  Image,
  Text,
  BackHandler,
  TouchableOpacity,
  Linking,
  DeviceEventEmitter,
  NativeEventSubscription,
  EmitterSubscription
} from "react-native";
import RootView from "common/components/Layout/RootView";
import { NativeNavigationModule, AkuNativeEventEmitter } from "common/nativeModules";
import { withTranslation } from "common/services/i18n";
import { inject } from "mobx-react";
import styles from "./styles";
import ADBanner from "common/components/ADGroup/ADBanner";
import { Creatives, ResAD } from "common/components/ADGroup/helpers/types";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import ActivityAdPopup from "common/components/ADGroup/ActivityAdPopup";
import {
  SensorTypeResultPageBack,
  SensorTypeResultPageDone,
  SensorTypeResultPageContinue
} from "../../tool/EventTracking";
import { TFunction } from "i18next";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import store from "../../store";
import { isNil } from "lodash";
import NativeEventModule from "common/nativeModules/bussinse/nativeEventModule";
import { AKButton, AKButtonType } from "@akulaku-rn/akulaku-ec-common/src/components/AKButton";
import { CREDIT_UPDATE_EVENT } from "../../constant";

type Props = {
  t: TFunction;
  store: pageStoreModel<store>;
  navigation: navigationModel;
};

@RootView({
  withI18n: [
    "CreditUpdateResultPage",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@inject("store")
@withTranslation("CreditUpdateResultPage")
export default class CreditUpdateResultPage extends PureComponent<Props> {
  BackHandlerListener?: NativeEventSubscription;

  screenEventListener?: EmitterSubscription;

  constructor(props: any) {
    super(props);
    const {
      store: {
        navParams: { status }
      }
    } = this.props;
    this.state = {
      isLoading: true
    };
    let PageStatus;
    if (status === 7) {
      PageStatus = 2;
    } else if (status === 8) {
      PageStatus = 3;
    }

    props.configSensorEvent({
      page_id: "1324",
      page_name: "update credit result",
      extra: { PageStatus }
    });

    this.screenEventListener = AkuNativeEventEmitter.addListener(
      props.store.navParams.screen,
      async (event: { eventName: string }) => {
        if (event.eventName === "onEnter") {
          this.BackHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.backHandler);
        } else if (event.eventName === "onLeave") {
          this.BackHandlerListener && this.BackHandlerListener.remove();
        }
      }
    );
  }

  backHandler = () => {
    SensorTypeResultPageBack();
    this.onBackPress();
    return true;
  };

  done = () => {
    SensorTypeResultPageDone();
    this.onBackPress();
  };

  onBackPress = () => {
    const {
      store: {
        navParams: { status }
      }
    } = this.props;
    NativeEventModule.postWeb({
      eventName: "applyCreditEnd",
      extraData: { applySuccess: true }
    });
    NativeNavigationModule.popTo(2);
  };

  returnImgAndtitle = () => {
    const {
      t,
      store: {
        navParams: {
          status //1：白天（30分钟内） 2：白天（超过30分钟） 3：夜间 4：申请超过每天次数上限
        }
      }
    } = this.props;
    let statusImg = null;
    let title = "";
    const msg = "";

    switch (status) {
      case 1:
        statusImg = require("../../img/credit_update_common.webp");
        title = t("资料更新正在审核中，请您耐心等待");
        break;
      case 7:
        statusImg = require("../../img/credit_update_error.webp");
        title = t("资料更新失败啦，请您检查后重新提交");
        break;
      case 8:
        statusImg = require("../../img/credit_update_success.webp");
        title = t("资料更新成功啦");
        break;
    }
    return { statusImg, title, msg };
  };

  render() {
    const {
      t,
      store: {
        navParams: { status }
      }
    } = this.props;
    const { statusImg, title, msg } = this.returnImgAndtitle();
    return (
      <View style={styles.container}>
        <View style={styles.topView}>
          <TouchableOpacity onPress={this.done} style={styles.btnStyle}>
            <Text style={styles.buttonTitle}>{t("完成1")}</Text>
          </TouchableOpacity>
          <Image source={statusImg} style={styles.img} />
          <Text style={styles.title}>{title}</Text>
          {status === 7 ? (
            <AKButton
              type={AKButtonType.B1_1_2}
              text={t("重新提交")}
              style={styles.button}
              onPress={() => {
                DeviceEventEmitter.emit(CREDIT_UPDATE_EVENT);
                SensorTypeResultPageContinue();
                this.props.navigation.goBack();
              }}
            />
          ) : null}
        </View>
      </View>
    );
  }
}
