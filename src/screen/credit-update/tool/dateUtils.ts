/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/tool/dateUtils.ts
 */
export function formatDatePure(timestamp: number) {
  // 创建一个新的Date对象
  const date = new Date(timestamp);

  // 获取年份
  const year = date.getFullYear();

  // 获取月份并补齐两位
  const month = String(date.getMonth() + 1).padStart(2, "0");

  // 获取日期并补齐两位
  const day = String(date.getDate()).padStart(2, "0");

  // 组合成YYYY-MM-DD格式
  return `${day}-${month}-${year}`;
}
