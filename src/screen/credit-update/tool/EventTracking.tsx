import { ClickNextDic, EnterLeavePageDic, SensorClickItemDic } from "../dict/EventTrackingDictionary";
import _ from "lodash";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";

const SensorTypeClickItem = (pageType: number, entryid: number) => {
  const dic = SensorClickItemDic[pageType];
  if (!!dic) {
    dic.extra = { entryid };
    NativeSensorModule.sensorLogger(SensorType.CLICK, dic);
  }
};

const ClickNext = (pageType: number) => {
  const dic = ClickNextDic[pageType];
  if (!!dic) {
    !!dic.sensorLogger && NativeSensorModule.sensorLogger(SensorType.CLICK, dic.sensorLogger);
  }
};

const EnterLeavePage = (data: { pageType: number; enter?: boolean }) => {
  const { pageType, enter } = data;
  const dic = EnterLeavePageDic[pageType];
  if (!!dic) {
    if (enter) {
      !!dic.sensorLogger && NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, { ...dic.sensorLogger });
    } else {
      !!dic.sensorLogger && NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, { ...dic.sensorLogger });
    }
  }
};

const SensorTypeClickContacts = (isFirst: boolean) => {};

const SensorTypePopView = () => {};

const SensorTypePopClick = (value: string) => {};

const SensorTypeIdSS = (type: string, extra?: any) => {};

const SensorTypeVoice = (status: string, entryid: number) => {};

const SensorTypeResultPageBack = () => {
  NativeSensorModule.sensorLogger(SensorType.CLICK, {
    page_name: "update credit result",
    element_id: "13240102",
    element_name: "return",
    module_id: "01",
    module_name: "update",
    position_id: "02",
    page_id: "1324"
  });
};

const SensorTypeResultPageDone = () => {
  NativeSensorModule.sensorLogger(SensorType.CLICK, {
    page_name: "update credit result",
    element_id: "13240103",
    element_name: "done",
    module_id: "01",
    module_name: "update",
    position_id: "03",
    page_id: "1324"
  });
};

const SensorTypeResultPageContinue = () => {
  NativeSensorModule.sensorLogger(SensorType.CLICK, {
    page_name: "update credit result",
    element_id: "13240101",
    element_name: "continue",
    module_id: "01",
    module_name: "update",
    position_id: "01",
    page_id: "1324"
  });
};

const CreditApplyReport = (pageType: number | null, isFirst: boolean) => {};

export {
  SensorTypeClickItem,
  ClickNext,
  EnterLeavePage,
  SensorTypeClickContacts,
  SensorTypePopView,
  SensorTypePopClick,
  SensorTypeIdSS,
  SensorTypeVoice,
  SensorTypeResultPageBack,
  SensorTypeResultPageDone,
  SensorTypeResultPageContinue,
  CreditApplyReport
};
