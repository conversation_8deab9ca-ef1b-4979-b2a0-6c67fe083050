import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import { getI18n } from "react-i18next";
import { ItemType } from "../dict/ComponentType";
import { PageType } from "../dict/PageType";
import store from "../store";
import { EntryInfoList, GroupInfoList, SubPageInfoList } from "../dict/ProcessData";
import DataProcess from "./DataProcess";
import { CusKindReminderData } from "../dict/CusComponentData";

export class DataHandling {
  static dataHanding = (
    data: {
      groupInfos: any;
    },
    onpress: {
      dataLevelOneOnpress?: (data: any) => void;
      dataLevelThreeOnpress?: (data: any) => void;
      dataLevelThree_TwoOnpress?: (data: any) => void;
    }
  ) => {
    const { groupInfos } = data;
    onpress.dataLevelOneOnpress && onpress.dataLevelOneOnpress(data);
    groupInfos.map(
      (i: { type: string; entries: { type: ItemType; entryId: number }[]; relatedEntries: EntryInfoList[] }) => {
        i.entries.map((item: { type: ItemType; entryId: number }) => {
          onpress.dataLevelThreeOnpress && onpress.dataLevelThreeOnpress(item);
        });
        i.relatedEntries &&
          i.relatedEntries.map((child: EntryInfoList) => {
            onpress.dataLevelThree_TwoOnpress && onpress.dataLevelThree_TwoOnpress(child);
          });
      }
    );
  };
}

export class AllItemDataHandling extends DataHandling {
  static allItems: any[] = [];

  constructor() {
    super();
  }

  static setData = (data: any) => {
    AllItemDataHandling.allItems.push(data);
  };

  static createData(data: any): any {
    AllItemDataHandling.allItems = [];
    this.dataHanding(data, {
      dataLevelThreeOnpress: AllItemDataHandling.setData,
      dataLevelThree_TwoOnpress: AllItemDataHandling.setData
    });
  }
}

export class WaitArrayDataHandling extends DataHandling {
  static waitArray: any[] = [];

  constructor() {
    super();
  }

  static setData = (data: { type: ItemType; entryId: number }) => {
    if (data.type as ItemType) {
      WaitArrayDataHandling.waitArray.push(data);
    }
  };

  static createData(data: any): any {
    WaitArrayDataHandling.waitArray = [];
    this.dataHanding(data, {
      dataLevelThreeOnpress: WaitArrayDataHandling.setData
    });
  }
}

export class SelectItemsDataHandling extends DataHandling {
  static selectItems: any[] = [];

  constructor() {
    super();
  }

  static setData = (data: any) => {
    if (data.type === ItemType.select || data.type === ItemType.date) {
      SelectItemsDataHandling.selectItems.push(data);
    }
  };

  static createData(data: any): any {
    SelectItemsDataHandling.selectItems = [];
    this.dataHanding(data, {
      dataLevelThreeOnpress: SelectItemsDataHandling.setData,
      dataLevelThree_TwoOnpress: SelectItemsDataHandling.setData
    });
  }
}

export class BasisDataHandling extends DataHandling {
  static pageType = -1;

  static asiApplicationId = "0";

  constructor() {
    super();
  }

  static dataLevelOneOnpress = (data: any) => {
    const { type, asiApplicationId } = data;

    BasisDataHandling.pageType = type;
    BasisDataHandling.asiApplicationId = asiApplicationId;
  };

  static createData(data: any): any {
    BasisDataHandling.pageType = -1;
    BasisDataHandling.asiApplicationId = "0";
    this.dataHanding(data, {
      dataLevelOneOnpress: BasisDataHandling.dataLevelOneOnpress
    });
  }
}

export class SubmitDataHandling {
  static setLastValue(data: NewEntityInfos[], pageStore: store) {
    data.map((i: NewEntityInfos) => {
      if (i.lastValue) {
        if (i.type === ItemType.iphone || i.type === ItemType.id_number) {
          const lastValue = i.lastValue.replace(/(\d{4})(?=\d)/g, "$1 ");
          pageStore.useCreditData[i.id] = lastValue;
        } else {
          pageStore.useCreditData[i.id] = i.lastValue;
        }
      }
    });
  }

  static createData(waitSubmits: any, pageStore: any) {
    const t = getI18n().getFixedT(null, "IDApplyInfo");
    const entries = [];
    for (const item of waitSubmits) {
      // 该资料项未填写
      if (!pageStore.useCreditData[item.id]) {
        // 如果是必填项直接中断
        if (item.required && item.type !== ItemType.hidden) {
          if (item.type === "auth") {
            Toast.show(t("未完成账号关联"), {
              position: 0
            });
          }
          return { isComplete: false, missingId: item.id, entries: [] };
        }
      } else {
        //逻辑层跟UI层的用户输入错误分离开了的，这边是做为一个逻辑层的输入错误，拦截提交
        let value = pageStore.useCreditData[item.id];

        if (item.type === ItemType.iphone || item.type === ItemType.id_number) {
          // 电话号码 去掉空格
          value = value.replace(/\s+/g, "");
        }
        // 判断资料项正则,是否输入错误
        if (item.regex && item.type !== ItemType.hidden) {
          const reg = new RegExp(item.regex);
          const error = !reg.test(value);
          if (error) {
            return { isComplete: false, missingId: null, entries: [] };
          }
        }
        const isUrl = item.type === ItemType.pic && typeof value === "string";
        // 收集信息
        if (item && item.type === ItemType.pic && !isUrl) {
          entries.push({ entryId: item.id, value: value.key });
        } else {
          entries.push({ entryId: item.id, value });
        }
        console.log("item.lastValue", item.id, item.lastValue);
        /// 滑块组件，如果是“0” 需要拦截
        if (item.type === ItemType.slider && (!value || value === "0")) {
          return { isComplete: false, missingId: item.id, entries: [] };
        }
      }
    }
    return { isComplete: true, missingId: null, entries };
  }
}

export type NewEntityInfos = {
  type: ItemType;
  id: number;
  pageType: number;
  lastValue: string;
  needModified: boolean;
  editable: boolean;
  kycStatus: boolean;
  sensitive: boolean;
  required: boolean;
  title: string;
  fieldName: string;
  regex: string;
  regexMap: { [key: number]: string } | null;
  typeMap: { [key: number]: ItemType } | null;
  placeholderMap: { [key: number]: string } | null;
  errMsg: string;
  desc: any;
  hint: string;
  options: { value: string; key: number; isSpecial?: boolean; isShow?: boolean }[];
  keyOptions: {
    [key: number]: number[];
  } | null;
  linkedOptions: {
    [key: number]: {
      [key: number]: number[];
    };
  } | null;
};

export const FormatData = (data: { groupInfos: GroupInfoList }, pageStore: any, pageType: PageType) => {
  const newData = JSON.parse(JSON.stringify(data));
  newData.groupInfos.map((item: any) => {
    const newEntityInfos: NewEntityInfos[] = [];
    item.entries.map((i: EntryInfoList) => {
      const formattedItemData = FormatItemData(i, pageStore, pageType, newData.kycStatus);
      DataProcess.initDataProcess(formattedItemData, { ocrTextImageUi: !!pageStore.ocrTextImageUi });
      // @ts-ignore
      newEntityInfos.push(formattedItemData);
    });

    const newRelatedEntryPageInfos: NewEntityInfos[] = [];
    item.entries = newEntityInfos;
    if (!!item.relatedEntries) {
      item.relatedEntries.push(CusKindReminderData);
      item.relatedEntries.map((child: EntryInfoList) => {
        newRelatedEntryPageInfos.push(FormatItemData(child, pageStore, pageType, newData.kycStatus));
      });
    }
    item.relatedEntries = newRelatedEntryPageInfos;
  });
  return newData;
};

const FormatItemData = (data: EntryInfoList, pageStore: any, pageType: PageType, kycStatus: boolean) => {
  const formatData: NewEntityInfos = {
    type: ItemType.unknown,
    desc: "",
    id: -1,
    pageType: -1,
    lastValue: "",
    editable: true,
    needModified: false,
    sensitive: false,
    required: true,
    title: "",
    fieldName: "",
    errMsg: "",
    options: [],
    kycStatus: true,
    regex: "",
    regexMap: null,
    hint: "",
    typeMap: null,
    placeholderMap: null,
    keyOptions: null,
    linkedOptions: null
  };
  formatData.type = data.type;
  formatData.id = data.entryId;
  formatData.pageType = pageType;
  formatData.kycStatus = kycStatus;

  formatData.lastValue = data.value || pageStore.useCreditData[data.entryId];
  formatData.editable = data.modifiable;
  formatData.needModified = data.needModified;
  formatData.sensitive = data.sensitive;

  formatData.required = data.required;
  formatData.title = data.placeholder;
  formatData.fieldName = data.fieldName;
  formatData.desc = "desc";
  formatData.errMsg = "未填写或填写错误";
  formatData.options = data.options || [];
  formatData.regex = data.regex;
  formatData.regexMap = data.regexMap;
  formatData.typeMap = data.typeMap;
  formatData.placeholderMap = data.placeholderMap;
  formatData.keyOptions = data.keyOptions;
  formatData.linkedOptions = data.linkedOptions;

  //暂时先前端处理
  if (data.fieldName === "Upload ID photo") {
    formatData.hint = "上传证件照";
  } else if (data.fieldName === "Upload the Back Side of ID") {
    formatData.hint = "上传背面照";
  }
  // @ts-ignore
  // formatData.options = JSON.parse(data.jsonDownValues);
  return formatData;
};
