//1个人信息页、2紧急人联系页、3职业信息页、4职业信息页-照片、5证件照页面、6声纹页、7账号授权页
const SensorClickItemDic: { [key: number]: any } = {
  5: {
    page_name: "update credit info",
    element_id: "13230101",
    element_name: "click data item",
    module_id: "01",
    module_name: "update",
    position_id: "01",
    page_id: "1323"
  }
};

const ClickNextDic: { [key: number]: any } = {
  5: {
    sensorLogger: {
      page_name: "personal information page",
      element_id: "15700101",
      element_name: "submit",
      module_id: "01",
      module_name: "info",
      position_id: "01",
      page_id: "1570"
    }
  }

  // 9: {
  //   sensorLogger: {
  //     page_name: "face front page",
  //     element_id: "13890101",
  //     element_name: "face",
  //     module_id: "01",
  //     module_name: "face",
  //     position_id: "01",
  //     page_id: "1389"
  //   }
  // }
};
const EnterLeavePageDic: { [key: number]: any } = {
  5: {
    sensorLogger: { page_name: "personal information page", page_id: "1570" }
  },
  9: {
    sensorLogger: { page_name: "face recognition guidance", page_id: "1573" }
  },
  100001: {
    sensorLogger: { page_name: "submit address", page_id: "1571" }
  },
  100002: {
    sensorLogger: { page_name: "face recognition page", page_id: "1574" }
  }
};

export { SensorClickItemDic, ClickNextDic, EnterLeavePageDic };
