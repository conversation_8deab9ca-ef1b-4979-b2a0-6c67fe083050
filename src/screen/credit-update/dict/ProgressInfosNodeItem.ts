export default class ProgressInfosNodeItem {
  // constructor(pageNo: number, pageType: number) {
  //   this.pageNo = pageNo;
  //   this.pageType = pageType;
  // }

  submitted: boolean = false;

  pageNo?: number;

  pageType?: number;

  next?: ProgressInfosNodeItem;

  isLast = false;

  checkPageNoPass(pageNo: number): { canSubmit: boolean; item: ProgressInfosNodeItem } {
    if (pageNo === this.pageNo) {
      return { canSubmit: true, item: this };
    } else {
      if (this.submitted) {
        if (!!this.next) {
          return this.next.checkPageNoPass(pageNo);
        } else {
          /// 这里是没有next， 那可以理解这里是最后一个节点。
          return { canSubmit: true, item: this };
        }
      } else {
        return { canSubmit: false, item: this };
      }
    }
  }

  checkAllSubmitted(): boolean {
    if (!this.next) return this.submitted;
    return this.submitted && this.next.checkAllSubmitted();
  }
}
