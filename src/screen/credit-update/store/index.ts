import { action, observable } from "mobx";
import api from "./api";
import { Loading } from "@akulaku-rn/akui-rn";
import _ from "lodash";
import { NativeNetworkModuleV2, NativeUserInfoModule } from "common/nativeModules";
import Basic from "common/store/Basic";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import ProgressInfosNodeItem from "../dict/ProgressInfosNodeItem";
import { getConfig } from "common/commonConfig";

export default class Store extends Basic {
  // 请求状态

  @observable useCreditData: Record<number, string> = {};

  @observable useCreditOtherData = {};

  @observable sumbitLoading = false;

  @observable emailPageY = 0;

  @observable email = null;

  @observable showEmailTips = false;

  @observable nowStep = null;

  @observable incomplete: Array<any> = [];

  @observable asiApplicationId = "";

  @observable providentTipShow = false;

  @observable showHideComponent = false;

  //是否勾选社保协议
  @observable isSelectSocialProtocol = false;

  //是否显示返回按钮
  @observable showBackBtn = true;

  //居住地址是否 复用 ID地址信息
  @observable isReuseAddress = true;

  //1：正常流程 2：合规流程 3: 合规2.0
  version = 1;

  //是否是新UI
  newUi = true;

  progressInfosNodeObj?: ProgressInfosNodeItem;

  //是否走新地址组件
  newAddressSubassembly = false;

  // OCR接口是否返回图片信息
  ocrTextImageUi = false;

  // 1-标签用户、2-解冻场景
  source = 1;

  complianceConfig: { [key: string]: string | boolean } = {
    link: "",
    authCreditPrivacyPolicyLink: "",
    IN: "",
    EN: "",
    is_new: false
  };

  //特殊资料项，可选项被其他资料项控制
  displayOptions: { [index: number]: number[] } = {};

  phoneNumber = "";

  getPhoneNumber = async () => {
    const { data } = await NativeUserInfoModule.getUserInfo();
    if (!data) return;
    this.phoneNumber = data.phoneNumber;
  };

  // 授信想要区分配置, 使用新配置4031
  @action("合规演示需要修改部分UI和协议，通过配置中心来配置是否开启")
  getConfigCompliance = async () => {
    const res = await getConfig(4031);
    if (!!res) {
      this.complianceConfig = res;
    }
  };

  @action
  post = async (url: string, params: object, requestSuccess: any, requestFailure?: any) => {
    this.io.POST(
      url,
      params,
      (response: any) => {
        requestSuccess(response);
      },
      (error: any) => {
        requestFailure(error);
        Loading.dismiss();
      }
    );
  };

  updateSource = (_source: string) => {
    if (_source === "unfreeze") {
      this.source = 2;
    } else {
      this.source = 1;
    }
  };

  getCreditStatus = async (countryId: number) => {
    const result = await this.io.post(api.CREDIT_STATUS, { countryId });
    const { data, success } = result;
    if (success && data.status === 8) return true;
    return false;
  };

  @action
  uploadImages = async (waitImgs: Array<any>) => {
    const entryIds: any[] = [];
    waitImgs.map(i => {
      entryIds.push(i.entryId);
    });
    let data: any = null;
    Loading.show({ hasBackcolor: true });
    try {
      const response = await this.io.post(api.UPLOAD_URL, { busiType: 10, cnt: entryIds.length });
      response.data.map((i: { url: any; key: any }, k: any) => {
        waitImgs[k].url = i.url;
        waitImgs[k].key = i.key;
      });
      const uploadSuccess = await this.uploadFilesToUrls(waitImgs);
      if (uploadSuccess) {
        data = {
          success: true,
          data: waitImgs
        };
      } else {
        data = {
          success: false
        };
      }
    } catch (e) {
      data = {
        success: false
      };
    } finally {
      Loading.dismiss();
      return data;
    }
  };

  uploadFilesToUrls = async (waitImgs: any) => {
    const newWaitImgs = JSON.parse(JSON.stringify(waitImgs));
    newWaitImgs.map((i: any) => {
      delete i.entryId;
      delete i.key;
    });
    const response = await NativeNetworkModuleV2.uploadFilesToUrls(newWaitImgs);
    console.log("上传图片结果 uploadFilesToUrls response==> ", response);
    return response.success;
  };

  @action
  dataSubmit = (data: object, callback: () => void) => {
    Loading.show({ hasBackcolor: true });
    this.sumbitLoading = true;
    this.post(
      api.SUBMIT,
      data,
      (response: any) => {
        const { success, errMsg } = response;
        if (success) {
          Loading.dismiss();
          callback();
        } else {
          Loading.dismiss();
          NativeToast.showMessage(errMsg);
        }
        this.sumbitLoading = false;
      },
      (error: any) => {
        Loading.dismiss();
        const { errMsg } = error;
        NativeToast.showMessage(errMsg);
        this.sumbitLoading = false;
      }
    );
  };

  @action
  getErrorInfo = async (data: object) => {
    const result = [];
    try {
      const response = await this.io.post(api.CREDIT_SUBMIT_PRE_VERIFY, data);
      if (response.success) {
        for (const key in response.data) {
          if (Object.prototype.hasOwnProperty.call(response.data, key)) {
            const element = response.data[key];
            if (element === false) {
              result.push(key);
            }
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
    return result;
  };
}
