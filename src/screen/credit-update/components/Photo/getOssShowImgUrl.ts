import { NativeNetworkModuleV2 } from "common/nativeModules";
import api from "../../store/api";
import { NetworkResponse } from "common/nativeModules/network/nativeNetworkModule";
import * as queryString from "querystring";

export default async function getDownloadUrl(
  key: string,
  expires?: string,
  url?: string
): Promise<{ success: true; url: string; expires: string } | { success: false; error: any }> {
  if (new Date().getTime() < Number(expires || 0) && !!expires && !!url) {
    return { success: true, url, expires };
  }
  return await new Promise(async resolve => {
    try {
      const res: NetworkResponse<{ url: string }> = await NativeNetworkModuleV2.post(api.DOWNLOAD_URL, { keyUrl: key });
      if (res.success) {
        const url = res.data.url;
        // 解析 URL 获取查询参数
        const index = url.indexOf("?");
        let expires = `${new Date().getTime()}`;
        if (index > -1) {
          const qsStr = url.substring(index + 1);
          const parsed = queryString.parse(qsStr);
          expires = !!parsed.Expires ? `${parsed.Expires}000` : `${new Date().getTime}`;
        }
        resolve({
          success: true,
          url: res.data.url,
          expires
        });
      } else {
        resolve({
          success: false,
          error: res.errMsg
        });
      }
    } catch (e) {
      resolve({ success: false, error: e });
    }
  });
}

function getExpires() {}
