import React, { useEffect, useState } from "react";
import {
  Image,
  StyleSheet,
  ImageSourcePropType,
  ImageResizeMode,
  ImageErrorEventData,
  NativeSyntheticEvent,
  StyleProp,
  ImageStyle
} from "react-native";
import isNumber from "lodash/isNumber";
import isArray from "lodash/isArray";
import getDownloadUrl from "./getOssShowImgUrl";

interface IdImgProps {
  imgkey?: string;
  onUpdateUrl?: (url: string, expires: string) => void;
  expires?: string;
  source: ImageSourcePropType;
  resizeMode?: ImageResizeMode;
  onError?: (error: NativeSyntheticEvent<ImageErrorEventData>) => void;
  style?: StyleProp<ImageStyle>;
}

// 这个组件是为了身份证图片回显，如果回显的图片链接已经过期会自动请求新的url
const IdImg = React.memo(
  function IdImgComp(props: IdImgProps) {
    const [source, setSource] = useState(props.source);
    useEffect(() => {
      if (!(isNumber(props.source) || isArray(props.source))) {
        setSource(require(""));
        if (!!props.imgkey) {
          getDownloadUrl(props.imgkey, props.expires, props.source.uri)
            .then(res => {
              if (res.success) {
                setSource({ uri: res.url });
                !!props.onUpdateUrl && props.onUpdateUrl(res.url, res.expires);
              } else {
                setSource(require("common/images/img_failed.webp"));
              }
            })
            .catch(e => {
              setSource(require("common/images/img_failed.webp"));
            });
        } else {
          setSource(props.source);
        }
      } else {
        setSource(props.source);
      }
    }, [props.source]);
    return <Image style={props.style} source={source} resizeMode={props.resizeMode} onError={props.onError} />;
  },
  (prevProps, nextProps) => {
    if (JSON.stringify(prevProps.source) !== JSON.stringify(nextProps.source)) {
      return false;
    }
    if (prevProps.imgkey !== nextProps.imgkey) {
      return false;
    }
    if (prevProps.expires !== nextProps.expires) {
      return false;
    }
    if (prevProps.resizeMode !== nextProps.resizeMode) {
      return false;
    }
    return true;
  }
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  }
});

export default IdImg;
