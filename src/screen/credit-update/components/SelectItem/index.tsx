import React, { PureComponent } from "react";
import {
  Text,
  StyleSheet,
  Animated,
  Image,
  TouchableOpacity,
  View,
  DeviceEventEmitter,
  LayoutChangeEvent
} from "react-native";
import _ from "lodash";
import { AKDialog, FontStyles, PopUpContainer, PopUpContainerType, UrlImage } from "@akulaku-rn/akui-rn";
import {
  SensorTypeClickItem
  // V3ClickItem,
  // V4ClickItem,
  // SensorFundingGuidePopView,
  // SensorFundingContinuePopClick,
  // SensorFundingResetPopClick,
  // SensorClickOccupationItem,
  // SensorFundingPopClick,
  // SensorFundingPopView
} from "../../tool/EventTracking";
import { NewEntityInfos } from "../../tool/DataHandling";
import { TFunction } from "i18next";
import store from "../../store";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import BirthdayList from "../Birthday";
import { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import VerticalList from "../VerticalList";
import { ItemType } from "../../dict/ComponentType";
import { observer } from "mobx-react";
import OptionsList from "./OptionsList";
import ConfirmInfo from "./ConfirmInfo";
import { TitleInfo, getTitleInfo } from "../../dict/GrayRelease";

type Props = {
  setValue: (value: string, id: number, type: string, key: string) => void;
  data: NewEntityInfos;
  selectItems: NewEntityInfos[];
  containerStyle?: object;
  missingId?: number | null;
  t: TFunction;
  store: pageStoreModel<store>;
};

type States = {
  color: string;
  selectedItem: string | null;
  showError: boolean;
  notInOptionsError: boolean;
  imgUrl: { url: string; width: number; height: number } | null;
  withAnimated: boolean;
};

const SIGN = "######";

const DURATION = 200;

@observer
export default class SelectItem extends PureComponent<Props, States> {
  top: Animated.Value;

  fontSize: Animated.Value;

  listener: any;

  listenerUpdateOptions: any;

  listenerSetValue: any;

  titleInfo: TitleInfo;

  constructor(props: Props) {
    super(props);
    const {
      data: { id, needModified },
      store: { pageStore }
    } = props;
    const lastValue = pageStore.useCreditData[id];

    // 如果有默认值
    if (lastValue) {
      this.markRelevanceOption(lastValue);
    }
    this.state = {
      selectedItem: this.returnSelectedItem(),
      color: lastValue ? "#999" : "#B3B3B3",
      showError: needModified,
      // 当前选项不在可选项中的错误
      notInOptionsError: false,
      imgUrl: null,
      withAnimated: true
    };
    this.titleInfo = getTitleInfo(pageStore.newUi);
    this.top = new Animated.Value(lastValue ? this.titleInfo.focus.top : this.titleInfo.blur.top);
    this.fontSize = new Animated.Value(lastValue ? this.titleInfo.focus.fontSize : this.titleInfo.blur.fontSize);
  }

  componentDidMount() {
    this.checkOptions();
    this.listener = DeviceEventEmitter.addListener("openNextSelectItem", id => {
      const {
        data,
        store: { pageStore }
      } = this.props;
      if (data.id === id && !pageStore.useCreditData[id]) {
        // 等动画结束再弹窗，不然ui线程会被卡主（特别是弹起生日弹窗，内部有大量for循环）
        setTimeout(() => {
          this.onFocus(false);
        }, DURATION + 100);
      }
    });
    // 可选项变化之后，如果已选项不在可选项中，则报错
    this.listenerUpdateOptions = DeviceEventEmitter.addListener("updateOptions", event => {
      const {
        data: { id, options },
        store: { pageStore }
      } = this.props;
      if (event.id === id) {
        this.checkOptions();
      }
    });
    this.listenerSetValue = DeviceEventEmitter.addListener("setValue", data => {
      const {
        setValue,
        data: { id, type, options }
      } = this.props;
      if (data.id === id) {
        // imgUrl 目前主要是OCR 那里的输入框可能传过来的
        if (!!data.imgUrl && this.props.store.pageStore.ocrTextImageUi) {
          this.closePlaceholderAnimated();
          this.setState({ imgUrl: data.imgUrl, withAnimated: false });
        }
        if (type === ItemType.date) {
          this.setBirthday(data.value, false);
        } else {
          const findItem = options.find(el => el.key === data.value);
          if (!findItem) return;
          this.setState({ selectedItem: findItem.value, showError: false }, () => {
            this.onBlur();
            setValue(data.value, id, ItemType.select, "");
          });
        }
      }
    });
  }

  componentWillUnmount() {
    this.listener && this.listener.remove();
    this.listenerSetValue && this.listenerSetValue.remove();
    this.listenerUpdateOptions && this.listenerUpdateOptions.remove();
  }

  returnSelectedItem = () => {
    const {
      data: { options, type, id },
      store: { pageStore }
    } = this.props;
    const lastValue = pageStore.useCreditData[id];
    if (type === "date") {
      if (lastValue) {
        return this.formatDate(lastValue);
      } else {
        return null;
      }
    } else {
      const item = options.find((i: { key: any }) => {
        return parseInt(i.key) === parseInt(lastValue);
      });
      if (!!item) {
        return item.value;
      } else {
        return null;
      }
    }
  };

  formatDate = (datetime: any) => {
    const date = new Date(parseInt(datetime));
    const year = date.getFullYear(),
      month = ("0" + (date.getMonth() + 1)).slice(-2),
      sdate = ("0" + date.getDate()).slice(-2);
    // 拼接
    const result = `${sdate}-${month}-${year}`;
    // 返回
    return result;
  };

  // 修改可选项，查看是否有特殊标记符号，对特殊标记以下的选项进行特殊处理
  modifyOptions = () => {
    const {
      data: { options }
    } = this.props;
    let isSpecial = false;
    let isShow = true;
    options.forEach(element => {
      if (element.value === SIGN) {
        isSpecial = true;
        isShow = false;
      } else {
        element.isSpecial = isSpecial;
        element.isShow = isShow;
      }
    });
  };

  // 根据选择的选项，找到被影响的资料项，并更新其被影响的资料项的可选项
  // 比如：选择了职业， 然后会影响 资金来源资料项的可选项
  // linkedOptions 数据结构是 {xxx:{yyy:[1,3]}}  其中xxx是被影响的资料项，yyy是当前资料项选中的id，最终影响xxx的可选项是1,3
  markRelevanceOption = (selectId: number | string) => {
    const {
      data: { linkedOptions },
      store: { pageStore }
    } = this.props;
    if (!linkedOptions) return;
    for (const key in linkedOptions) {
      if (Object.prototype.hasOwnProperty.call(linkedOptions, key)) {
        const element = linkedOptions[key];
        if (element[+selectId]) {
          pageStore.displayOptions[key] = element[+selectId];
          DeviceEventEmitter.emit("updateOptions", {
            id: +key
          });
        }
      }
    }
  };

  // 检测当前选中的资料项是否 满足可选的资料项集合
  checkOptions = () => {
    const {
      data: { id, options, type },
      store: { pageStore },
      setValue
    } = this.props;
    if (!this.state.selectedItem || !this.state.selectedItem.length) {
      this.setState({ selectedItem: "" }, () => {
        setValue("", id, type, "");
      });
      return;
    }
    const mOptions = pageStore.displayOptions[id];
    if (mOptions) {
      const displayData = options.filter(el => mOptions.indexOf(el.key) > -1);
      const isFind = displayData.some(item => item.value === this.state.selectedItem);
      if (!isFind) {
        this.setState({ selectedItem: "" }, () => {
          setValue("", id, type, "");
        });
      }
    }
  };

  onFocus = _.debounce(
    (notReport: boolean) => {
      const {
        data: { title, id, editable, pageType }
      } = this.props;
      if (!editable) return;

      PopUpContainer.show({
        type: PopUpContainerType.D4_1_1,
        headerProps: { title },
        renderContent: this.childrenComponent()
      });

      PopUpContainer.show({
        type: PopUpContainerType.D4_1_1,
        headerProps: { title: title, hasCloseButton: true },
        renderContent: this.childrenComponent()
      });
      SensorTypeClickItem(pageType, id);
      // V3ClickItem(id);
      // const v4Data = { entryid: id, type: "select", isFirst: isFirstApply };
      // !!notReport && V4ClickItem(v4Data);
    },
    1000,
    { leading: true, trailing: false }
  );

  childrenComponent = () => {
    const {
      data: { type, options, fieldName, id },
      store: { pageStore },
      t
    } = this.props;
    if (type === "date") {
      return (
        <BirthdayList
          overflowYear={fieldName === "expiryDate" ? 10 : 0}
          didSelectedItem={this.birthdaydidSelectedItem}
          selectedItem={this.state.selectedItem}
        />
      );
    } else {
      let displayData = options;
      const mOptions = pageStore.displayOptions[id];
      if (mOptions) {
        displayData = options.filter(el => mOptions.indexOf(el.key) > -1);
      }

      this.modifyOptions();

      return (
        <OptionsList
          t={t}
          data={displayData}
          selectedItem={this.state.selectedItem}
          didSelectedItem={this.didSelectedItem}
          version={pageStore.version}
          store={this.props.store}
        />
      );
    }
  };

  //更新收入证明
  updateIncomeSource = (selectedItem: { value: string; key: number; isSpecial?: boolean }) => {
    // 1101076：收入证明，110107604：家人，110107601：工资
    DeviceEventEmitter.emit("setValue", {
      id: 1101076,
      value: selectedItem.isSpecial ? 110107604 : 110107601
    });
  };

  didSelectedItem = (selectedItem: { value: string; key: number; isSpecial?: boolean }) => {
    const {
      setValue,
      data: { id, type, fieldName },
      t,
      store: { pageStore }
    } = this.props;
    this.markRelevanceOption(selectedItem.key);
    this.setState({ selectedItem: selectedItem.value, showError: false }, () => {
      this.onBlur();
      setValue(selectedItem.key + "", id, type, selectedItem.value);
      // const v4Data = { entryid: id, type: "select", isFirst: isFirstApply, selectData: selectedItem.value };

      // V4ClickItem(v4Data);
      PopUpContainer.dismiss();
      const reSelectFunc = () => {
        PopUpContainer.dismiss();
        setTimeout(() => {
          this.onFocus(true);
        }, 500);
      };
      if (fieldName === "occupation") {
        // SensorClickOccupationItem({ entryid: selectedItem.key, buttonName: selectedItem.value });
        // 已经全量灰度，isSpecial代表是BO职业
        if (selectedItem.isSpecial) {
          PopUpContainer.show({
            type: PopUpContainerType.D4_1_1,
            headerProps: { title: t("信息确认"), onClickClose: reSelectFunc },
            renderContent: (
              <ConfirmInfo
                t={t}
                onPressContinue={() => {
                  // SensorFundingContinuePopClick();
                  PopUpContainer.dismiss();
                }}
                onPressReset={() => {
                  // SensorFundingResetPopClick();
                  reSelectFunc();
                }}
              />
            )
          });

          // SensorFundingGuidePopView();
        }
        // 修改收入来源资料项
        this.updateIncomeSource(selectedItem);
      }
      setTimeout(() => {
        this.openNextSelectItem(id);
      });
    });
  };

  birthdaydidSelectedItem = (selectedItem: any) => {
    this.setBirthday(selectedItem);
  };

  judgeBirthday = (year: string, month: string, day: string) => {
    if (parseInt(year) > new Date().getFullYear()) return false;
    if (parseInt(month) > 12 || parseInt(month) < 1) return false;

    return new Date(parseInt(year), parseInt(month) - 1, parseInt(day)).getDate() === parseInt(day);
  };

  setBirthday(selectedItem: any, openNext = true) {
    const {
      setValue,
      data: { id, type }
    } = this.props;
    let { month, date, year } = selectedItem;
    // 增加生日判断，生日识别失败，填入1900-01-01
    if (!this.judgeBirthday(year, month, date)) {
      year = "1900";
      month = "01";
      date = "01";
    }
    const newSelectedItem = `${date}-${month}-${year}`;
    const newDate = new Date();
    newDate.setFullYear(year);
    newDate.setDate(1);
    newDate.setMonth(parseInt(month) - 1);
    newDate.setDate(date);
    newDate.setHours(0, 0, 0, 0);
    const time = newDate.getTime();
    this.setState({ selectedItem: newSelectedItem, showError: false }, () => {
      this.onBlur();
      PopUpContainer.dismiss();
      setValue(time + "", id, type, "");
      if (openNext) {
        this.openNextSelectItem(id);
      }
    });
  }

  closePlaceholderAnimated = () => {
    this.top.setValue(this.titleInfo.focus.top);
    this.fontSize.setValue(this.titleInfo.focus.fontSize);
    this.setState({
      color: "#999"
    });
  };

  openNextSelectItem = (id: number) => {
    const { selectItems } = this.props;
    const findIndex = selectItems.findIndex(i => i.id === id);
    if (!selectItems[findIndex + 1]) return;
    const nextId = selectItems[findIndex + 1].id;
    const editable = selectItems[findIndex + 1].editable;
    if (!editable) return;
    DeviceEventEmitter.emit("openNextSelectItem", nextId);
  };

  onBlur = () => {
    if (!this.state.withAnimated) return;
    if (this.state.selectedItem && !!this.state.selectedItem.length) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: this.titleInfo.focus.top,
          duration: DURATION,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: this.titleInfo.focus.fontSize,
          duration: DURATION,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#999"
      });
    } else {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: this.titleInfo.blur.top,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: this.titleInfo.blur.fontSize,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#B3B3B3"
      });
    }
  };

  getUrlImageShowWidth = (showHeight: number, oriHeight: number, oriWidth: number) => {
    const showWidth = oriWidth * (showHeight / oriHeight);
    // console.log("showWidth", showWidth, this.textItemWidth);
    if (!!this.textItemWidth && showWidth >= this.textItemWidth - 20) return this.textItemWidth - 20;
    return showWidth;
  };

  textItemWidth = 0;

  _onLayout = (event: LayoutChangeEvent) => {
    if (event.nativeEvent.layout.width !== this.textItemWidth) {
      this.textItemWidth = event.nativeEvent.layout.width;
    }
  };

  render() {
    const {
      data: { title, editable, pageType, needModified },
      containerStyle,
      data: { id },
      missingId,
      t,
      store: { pageStore, navParams }
    } = this.props;
    const { selectedItem, color, showError } = this.state;
    const errorText = !!needModified ? t("请修改") : t("此项未填写");
    if (pageType === 5 && !pageStore.showHideComponent) return null;
    return (
      <View style={containerStyle}>
        <TouchableOpacity
          style={[
            pageStore.newUi ? styles.newContainer : styles.container,
            missingId === id && !selectedItem && { borderBottomColor: "#e62117" }
          ]}
          onPress={() => {
            console.log("onPress");
            this.onFocus(true);
          }}
        >
          <View onLayout={this._onLayout} style={{ flex: 1, alignItems: "flex-start" }}>
            {!!this.state.imgUrl && (
              <UrlImage
                resizeMode={"contain"}
                source={this.state.imgUrl.url}
                style={{
                  height: 16,
                  width: this.getUrlImageShowWidth(16, this.state.imgUrl.height, this.state.imgUrl.width),
                  alignSelf: "flex-start",
                  marginTop: 3,
                  marginBottom: 6
                }}
              />
            )}
            <Text style={[styles.selectedItem, !editable && { color: "#6C6C69" }]}>{selectedItem}</Text>
          </View>
          {pageStore.newUi ? (
            <Image
              source={require("../../img/credit_icon_down.webp")}
              style={{
                position: "absolute",
                right: 16,
                width: 16,
                height: 16,
                alignSelf: "center"
              }}
            />
          ) : (
            <Image style={{ width: 12, height: 13 }} source={require("./img/choose_icon.webp")} />
          )}

          <Animated.View
            style={[
              styles.placeholderView,
              {
                top: this.top,
                left: pageStore.newUi ? 15 : 0
              }
            ]}
          >
            <Animated.Text
              numberOfLines={1}
              style={[
                styles.placeholderText,
                {
                  fontSize: this.fontSize,
                  color: pageStore.newUi ? "#989FA9" : color
                }
              ]}
            >
              {title}
            </Animated.Text>
          </Animated.View>
        </TouchableOpacity>
        {((missingId === id && !selectedItem) || showError) && (
          <View style={styles.errorView}>
            <Text style={styles.errorText}>{errorText}</Text>
          </View>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingBottom: 10,
    paddingRight: 12,
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    paddingTop: 24,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  newContainer: {
    paddingBottom: 10,
    paddingRight: 12,
    borderWidth: 1,
    borderColor: "#E2E5E9",
    paddingTop: 30,
    paddingHorizontal: 15,
    flexDirection: "row",
    justifyContent: "space-between",
    borderRadius: 6,
    alignItems: "center",
    minHeight: 60
  },
  placeholderView: {
    position: "absolute",
    zIndex: 2
  },
  placeholderText: {
    lineHeight: 18,
    maxWidth: 270,
    height: 18,
    textAlignVertical: "center"
  },
  selectedItem: {
    fontSize: 16,
    color: "#282b2e",
    maxWidth: "90%"
  },
  errorView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8
  },
  errorText: {
    fontSize: 14,
    color: "#e62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginRight: 4
  },
  button: {
    flexDirection: "row",
    justifyContent: "space-between",
    height: 50,
    paddingLeft: 16,
    alignItems: "center",
    width: "100%",
    backgroundColor: "#fff"
  },
  sep: {
    height: 0.5,
    position: "absolute",
    bottom: 0,
    left: 16,
    right: 0,
    backgroundColor: "#EBEBEB"
  },
  title: {
    fontSize: 14,
    color: "#333"
  },
  itemStyle: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: "#EFF2F6",
    flexDirection: "row",
    alignItems: "center"
  },
  tips: { width: 14, height: 14, marginRight: 8 },
  tipsText: { fontSize: 11, color: "#6E737D" },
  selectIcon: { width: 24, height: 24, marginRight: 12 },
  tipsContainer: {
    backgroundColor: "#FFF4E6",
    borderRadius: 6,
    padding: 12,
    margin: 16,
    marginBottom: 0
  },
  tipsHeader: {
    flexDirection: "row",
    alignItems: "center"
  }
});
