import React, { Component } from "react";
import {
  FlatList,
  View,
  KeyboardAvoidingView,
  Text,
  StatusBar,
  Image,
  Animated,
  NativeModules,
  UIManager,
  findNodeHandle,
  LayoutChangeEvent,
  StyleSheet,
  TouchableOpacity,
  ListRenderItem,
  ListRenderItemInfo,
  NativeSyntheticEvent,
  NativeScrollEvent
} from "react-native";
import _ from "lodash";
import styles from "./styles";
import BottomButton from "../BottomButton";
import {
  Loading,
  WINDOW_HEIGHT,
  NavigationBar,
  iOS,
  WINDOW_WIDTH,
  NetworkErrorComponent,
  AKDialog,
  FontStyles,
  AKButton,
  NAV_BAR_HEIGHT
} from "@akulaku-rn/akui-rn";
import PopUpPanel from "@akulaku-rn/akui-rn/src/components/PopUpPanel";
import Dialog from "@akulaku-rn/akui-rn/src/components/Dialog";
import { Shadow } from "@akulaku-rn/akui-rn/src/components/NeomorphShadows";
import Email from "../Email";
import api from "../../store/api";
import { autorun } from "mobx";
import { AgreementDialog, AgreementPopView } from "../Dialog/Agreement";
import { observer } from "mobx-react";
import {
  ClickNext,
  EnterLeavePage,
  // SensorClickUploadExample,
  // SensorPersonalBackClick,
  // SensorSocialProtocolClick,
  // SensorSocialProtocolExpose,
  // SensorTypeClickAgreement,
  SensorTypePopClick,
  SensorTypePopView
} from "../../tool/EventTracking";
const { GetEnvInfoModule } = NativeModules;
import {
  AllItemDataHandling,
  BasisDataHandling,
  FormatData,
  NewEntityInfos,
  SelectItemsDataHandling,
  SubmitDataHandling,
  WaitArrayDataHandling
} from "../../tool/DataHandling";
import DynamicComponent from "../DynamicComponent";
import { ItemType } from "../../dict/ComponentType";
import { NativeImageCaptureModule, NativeConfigModule, NativeNavigationModule } from "common/nativeModules";
import NativeEventReportModule, {
  ReportEventName,
  ReportScene
} from "common/nativeModules/basics/nativeEventReportModule";
import NativeAdjustModule, { getEventName, AdjustEventType } from "common/nativeModules/sdk/nativeAdjustModule";
import NativeFirebaseModule from "common/nativeModules/sdk/nativeFirebaseModule";
import { StoreData } from "common/util/cache";
import ConfigHelper from "common/util/ConfigHelper";
import DeviceInfoReportHelper from "common/util/DeviceInfoReportHelper";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { PageType } from "../../dict/PageType";
import NativePricyDialogModule, {
  NativePricyDialogModuleScene
} from "common/nativeModules/bussinse/nativePricyDialogModule";
import { PopupExposerHelper, reportClick, reporter } from "@akulaku-rn/rn-v4-sdk";
import nativeConfigModule from "common/nativeModules/basics/nativeConfigModule";
import { isLaunchSm } from "../../tool/SmUtils";
import { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import Associated from "../Associated";
import { PageItemI } from "../../dict/type";
import { BottomProtocol, IdPhotoProtocol, SocialProtocol } from "../BottomProtocol";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import store from "../../store";
import { scale } from "@akulaku-rn/akulaku-ec-common/src/util/P2D";
import UploadExample from "../UploadExample";
import { HandleGroupInfoList } from "../../dict/ProcessData";
import KindReminder from "../KindReminder";
import { formatDatePure } from "../../tool/dateUtils";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { isJsonStr } from "../../tool/utils";
import { GlobalRuntime } from "common/constant";
type Props = {
  navigation: any;
  store: pageStoreModel<store>;
  t: any;
  layerNext: () => void;
  layerGoback: () => void;
  pageLength: number;
  nowStep: number;
  setpageType: (pageType: number) => void;
  onChangeDatas?: (dataCount: number) => void;
};

type States = {
  loadFailed: boolean;
  refreshing: boolean;
  dataSource: HandleGroupInfoList[];
  shadow: boolean;
  description: string;
  footView: null | boolean;
  navTitle: string;
  missingId: number | null;
  bannerImg: string;
  topTip: boolean;
  protocol: boolean;
  agreement: boolean;
  isAgree: boolean;
};
const AnimatedFlatList = Animated.createAnimatedComponent(FlatList);
@observer
export default class DataPage extends Component<Props, States> {
  selectItems: any[];

  waitArray: NewEntityInfos[];

  firstWaitArray: NewEntityInfos[];

  allItems: any[];

  loadDone: boolean;

  isConfirmIcNumber: boolean;

  beforeStep: number | null;

  scrollY: any;

  aliveDetect: boolean;

  pageType!: PageType; //1 个人信息页 2 紧急联系人页 3 工作信息页 4 工作照片页 5 证件照片页 6 声纹页 7 账号授权页 8 税卡页 9 刷脸识别页

  flatListRef!: FlatList;

  nowShowItem: NewEntityInfos[];

  // 所有的 item的 ref
  mRefs: Record<number, any> = {};

  occupationRef?: any;

  //flatlist 内容高度
  contentHeight = 0;

  flatListY = 0;

  flatListHeight = 0;

  constructor(props: Props) {
    super(props);
    this.state = {
      loadFailed: false,
      refreshing: true,
      dataSource: [],
      missingId: null,
      shadow: false,
      description: "",
      footView: null,
      navTitle: "",
      bannerImg: "",
      isAgree: true,
      topTip: false,
      protocol: false,
      agreement: false
    };
    //所有selectView的子项数据
    this.selectItems = [];
    //当前页面所有需要提交的数据
    this.firstWaitArray = [];
    this.waitArray = [];
    //当前页面所有的选项
    this.allItems = [];
    this.loadDone = false;
    this.isConfirmIcNumber = false;
    this.beforeStep = null;
    this.scrollY = new Animated.Value(0);
    this.aliveDetect = false;
    this.nowShowItem = [];
  }

  componentDidMount() {
    const {
      nowStep,
      store: { pageStore },
      setpageType
    } = this.props;
    autorun(() => {
      if (pageStore.nowStep !== this.beforeStep) {
        if (pageStore.nowStep === nowStep) {
          this.getPageData();
        }
        this.beforeStep = pageStore.nowStep;
      }
    });
  }

  _onLayoutContainer = (event: LayoutChangeEvent) => {
    this.flatListY = event.nativeEvent.layout.y;
  };

  _onLayoutFlatList = (event: LayoutChangeEvent) => {
    this.flatListHeight = event.nativeEvent.layout.height;
  };

  scrollToItemIndex = (missingId: number) => {
    const viewNode = findNodeHandle(this.mRefs[missingId]);
    viewNode &&
      UIManager.measure(
        viewNode,
        (x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
          const headerHeight = NAV_BAR_HEIGHT;
          if (this.contentHeight - (pageY + this.offsetY - headerHeight) < this.flatListHeight - headerHeight) {
            this.flatListRef && this.flatListRef.scrollToEnd();
          } else {
            this.flatListRef && this.flatListRef.scrollToOffset({ offset: pageY + this.offsetY, animated: true });
          }
        }
      );
  };

  getPageData = () => {
    Loading.show();
    const {
      store: {
        pageStore,
        runtime: { countryId }
      },
      setpageType,
      nowStep,
      onChangeDatas
    } = this.props;
    this.setState({ refreshing: true });
    pageStore.post(
      api.PAGE_INFO,
      {
        countryId,
        pageNo: nowStep,
        source: pageStore.source
      },
      async (response: any) => {
        const { data, success } = response;
        if (success) {
          if (data.totalPage) {
            onChangeDatas && onChangeDatas(data.totalPage);
          }
          const formatData = FormatData(data, pageStore, data.pageType);
          const { groupInfos, title, bannerImg, topTip, protocol, agreement } = formatData;

          AllItemDataHandling.createData(formatData);
          WaitArrayDataHandling.createData(formatData);
          SelectItemsDataHandling.createData(formatData);
          BasisDataHandling.createData(formatData);
          // 后台需要展示社保协议，默认选中
          if (agreement) {
            pageStore.isSelectSocialProtocol = true;
            // SensorSocialProtocolExpose();
          }

          this.allItems = AllItemDataHandling.allItems;
          this.waitArray = WaitArrayDataHandling.waitArray;
          this.firstWaitArray = WaitArrayDataHandling.waitArray;
          this.selectItems = SelectItemsDataHandling.selectItems;
          const { asiApplicationId, pageType } = BasisDataHandling;
          this.pageType = pageType;
          setpageType(this.pageType);
          pageStore.asiApplicationId = asiApplicationId;
          const enterLeavepageData = {
            pageType: this.pageType,
            enter: true
          };
          EnterLeavePage(enterLeavepageData);
          SubmitDataHandling.setLastValue(this.allItems, pageStore as any);
          this.setState(
            {
              refreshing: false,
              loadFailed: false,
              dataSource: groupInfos,
              bannerImg,
              navTitle: title,
              topTip,
              protocol,
              agreement
            },
            () => {
              Loading.dismiss();
              this.loadDone = true;
            }
          );
          Loading.dismiss();
        } else {
          this.setState({ loadFailed: true, refreshing: false }, () => {
            Loading.dismiss();
          });
        }
      },
      () => {
        this.setState({ loadFailed: true, refreshing: false }, () => {
          Loading.dismiss();
        });
      }
    );
  };

  _setRef = (uid: number, ref: any) => {
    this.mRefs[uid] = ref;
  };

  _onLayoutItems = (event: LayoutChangeEvent) => {};

  // 根据关联项id，将可选项插入到对应的位置上，（主要是为了保证资料项的顺序）
  setWaitArray = (waitArray: any[], id: number) => {
    const newWaitArray: NewEntityInfos[] = [];
    this.firstWaitArray.forEach(element => {
      newWaitArray.push(element);
      if (element.id === id) {
        newWaitArray.push(...waitArray);
      }
    });
    this.waitArray = Array.from(new Set(newWaitArray));
  };

  renderItem: ListRenderItem<HandleGroupInfoList> = (info: ListRenderItemInfo<HandleGroupInfoList>) => {
    const { item, index } = info;
    const { t, store } = this.props;
    const { missingId } = this.state;
    const containerStyle = { marginBottom: store.pageStore.newUi ? 18 : 24 };

    const itemContent = info.item.entries.map((i: NewEntityInfos, k: number) => {
      return (
        <View
          key={k}
          style={[styles.listItem]}
          ref={_ref => {
            if (_ref) {
              this.mRefs[i.id] = _ref;
            }
          }}
          onLayout={this._onLayoutItems}
        >
          {!k && !!item.title ? (
            <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
              <Text style={store.pageStore.newUi ? styles.headerTextNew : styles.headerText}>{item.title}</Text>
              {i.fieldName === "Upload ID photo" && !store.pageStore.showHideComponent ? (
                <TouchableOpacity onPress={this.showUploadExample}>
                  <Text style={styles.example}>{t("查看示例")}</Text>
                </TouchableOpacity>
              ) : null}
            </View>
          ) : null}

          {i.keyOptions ? (
            <Associated
              t={t}
              setRef={this._setRef}
              missingId={missingId}
              setWaitArray={this.setWaitArray}
              data={i}
              setValue={this.setValue}
              store={store}
              allItems={this.allItems}
            />
          ) : (
            <DynamicComponent
              data={i}
              t={t}
              setValue={this.setValue}
              containerStyle={containerStyle}
              missingId={missingId}
              selectItems={this.selectItems}
              store={store}
              allItems={this.allItems}
              pageType={this.pageType}
            />
          )}
        </View>
      );
    });
    // 插入不需要吸顶的头部提示组件
    if (index === 0) {
      itemContent.unshift(this.renderHeaderNoSticky());
    }
    return <View style={{ marginTop: store.pageStore.newUi && info.index !== 0 ? 10 : 12 }}>{itemContent}</View>;
  };

  setValue = (text: string, id: number, type: string, key: string) => {
    const {
      store: {
        pageStore,
        runtime: { uid }
      }
    } = this.props;

    pageStore.useCreditData[id] = text;
    StoreData(`${uid}useCreditDatas`, JSON.stringify(pageStore.useCreditData));
  };

  keyExtractor = (item: any, index: number) => `${item.id}-${index}`;

  go2AliveDetect = async () => {
    const { t } = this.props;
    const data = this.allItems.find(i => {
      return i.type === ItemType.living;
    });
    const response = await NativeImageCaptureModule.go2AliveDetect(15);

    if (response.success && !_.isEmpty(response.data)) {
      this.aliveDetect = true;
      this.setValue(response.data.businessId, data.id, data.type, "");
      this.nextAction();
    } else {
      NativeToast.showMessage(t("人脸识别失败，请重试"));
    }
  };

  next = _.debounce(
    () => {
      this.nextAction();
    },
    2000,
    { leading: true, trailing: false }
  );

  nextAction = async () => {
    const {
      pageLength,
      store: { pageStore },
      t
    } = this.props;
    ClickNext(this.pageType);
    console.log("this.pageType==> ", this.pageType);
    console.log("this.aliveDetect==> ", this.aliveDetect);
    if (this.pageType === PageType.face_recognition && !this.aliveDetect) {
      this.listPermission();
      return;
    }
    const submitData = SubmitDataHandling.createData(this.waitArray, pageStore);
    console.log("submitData==> ", submitData);
    if (!submitData.isComplete) {
      this.setState({ missingId: submitData.missingId });
      const findIndex = this.allItems.findIndex(item => {
        return item.id === submitData.missingId;
      });
      this.scrollToItemIndex(submitData.missingId);
      if (findIndex === 3 && this.pageType === PageType.id_Photo) {
        this.flatListRef.scrollToOffset({ offset: WINDOW_HEIGHT, animated: true });
      }
      return;
    }
    const confirmItems: { title: string; value: string }[] = [];
    const errorItems: { title: string; value: string }[] = [];

    if (this.pageType === PageType.id_Photo) {
      const icNameItem = this.allItems.find(i => {
        return `${i.id}` === "1101000";
      });
      if (icNameItem) {
        confirmItems.push({ title: t("姓名"), value: pageStore.useCreditData[icNameItem.id] });
      }

      const icNumberItem = this.allItems.find(i => {
        return `${i.id}` === "1101001";
      });
      if (icNumberItem) {
        confirmItems.push({ title: t("身份证件号"), value: pageStore.useCreditData[icNumberItem.id] });
      }

      const icBirthdayItem = this.allItems.find(i => {
        return `${i.id}` === "1101009";
      });
      const icBirthAddressItem = this.allItems.find(i => {
        return `${i.id}` === "1101004";
      });
      if (icBirthdayItem || icBirthAddressItem) {
        const birthAddressItemJson = isJsonStr(pageStore.useCreditData[icBirthAddressItem.id]);
        const birthAddressValue = birthAddressItemJson.success
          ? `${birthAddressItemJson.data.city}`
          : birthAddressItemJson.data;
        confirmItems.push({
          title: t("出生地/出生日期"),
          value: `${birthAddressValue || ""}, ${formatDatePure(Number(pageStore.useCreditData[icBirthdayItem.id])) ||
            ""}`
        });
      }
      // const res = await pageStore.getErrorInfo({ entries: submitData.entries });
      // const birthdayId = res.find(el => el === "1101009");
      // const birthdayItem = this.allItems.find(i => {
      //   return i.id === Number(birthdayId);
      // });
      // if (birthdayItem) {
      //   errorItems.push({
      //     title: t("出生日期"),
      //     value: formatDatePure(Number(pageStore.useCreditData[birthdayItem.id]))
      //   });
      // }
      // const genderId = res.find(el => el === "1101010");
      // const genderItem = this.allItems.find(i => {
      //   return i.id === Number(genderId);
      // });
      // if (genderItem) {
      //   const genderValue = genderItem.options.find((el: any) => {
      //     return el["key"] === Number(pageStore.useCreditData[genderItem.id]);
      //   });
      //   errorItems.push({ title: t("性别"), value: genderValue["value"] });
      // }
      // const motherNameId = res.find(el => el === "1103401");
      // const motherNameItem = this.allItems.find(i => {
      //   return i.id === Number(motherNameId);
      // });
      // if (motherNameItem) {
      //   errorItems.push({ title: t("母亲姓名"), value: pageStore.useCreditData[motherNameItem.id] });
      // }
    }

    // if (this.pageType === PageType.id_Photo && !this.isConfirmIcNumber) {
    //   this.alertIcNumber(confirmItems, errorItems);
    //   return;
    // }
    const waitSumbit = {
      pageNo: this.props.nowStep,
      entries: submitData.entries,
      source: pageStore.source,
      asiApplicationId: pageStore.asiApplicationId
    };
    if (!!pageStore.progressInfosNodeObj) {
      const checkRes = pageStore.progressInfosNodeObj.checkPageNoPass(this.props.nowStep);
      if (!checkRes.canSubmit) {
        NativeToast.showMessage(this.props.t("抱歉！网络异常，请退出重新进入"));
        return;
      }
      pageStore.dataSubmit(waitSumbit, () => {
        checkRes.item.submitted = true;
        this.layerNextAndReport();
      });
    } else {
      pageStore.dataSubmit(waitSumbit, this.layerNextAndReport);
    }
  };

  listPermission = () => {
    //申请列表权限
    if (iOS) {
      this.privacySubmit();
    } else {
      //处理安卓隐私披露弹窗
      this.handleAndroidPermission();
    }
  };

  handleSubmitData = (isAgree: boolean) => {
    if (isAgree) {
      SensorTypePopClick("agree");
      this.privacySubmit();
    } else {
      SensorTypePopClick("disagree");
    }
  };

  handleAndroidPermission = async () => {
    if (NativeModules.GetEnvInfoModule.versionCode >= 1186) {
      const pass = await NativePricyDialogModule.showPricyDialog({ scene: NativePricyDialogModuleScene.Credit });
      this.handleSubmitData(pass);
    } else {
      const { t } = this.props;
      const dialog = new PopupExposerHelper(
        Dialog,
        {
          cn: 2,
          ei: {},
          eit: 0,
          ext: {}
        },
        { screenNumber: "300136" },
        {
          beforeReportV4: data => {
            SensorTypePopView();
            return data;
          }
        }
      );
      AKDialog.show({
        type: DialogType.C3_1_1,
        title: t("授权信息"),
        desc: t("为了给您提供完善的授信服务，我们需要获取您的安装列表和本机号码。如果您不同意，则我们视为你取消申请。"),
        positiveText: t("同意"),
        onPositivePress: () => {
          this.handleSubmitData(true);
        },
        negativeText: t("不同意"),
        onNegativePress: () => {
          this.handleSubmitData(false);
        }
      });
    }
  };

  // 是否调用数美sdk
  toInvokeSm = async () => {
    const isLaunch = await isLaunchSm();
    if (isLaunch) {
      NativeEventReportModule.reportSmDeviceData();
    }
  };

  privacySubmit = async () => {
    const {
      store: { pageStore }
    } = this.props;
    const powers = iOS
      ? [
          ReportEventName.GPS,
          ReportEventName.CONTACT,
          ReportEventName.BATTERY,
          ReportEventName.DEVICE_FINGER_PRINT,
          ReportEventName.APP_LIST
        ]
      : [
          ReportEventName.GPS,
          ReportEventName.DEVICE_FINGER_PRINT,
          ReportEventName.SENSORS,
          ReportEventName.BATTERY,
          ReportEventName.CONTACT,
          ReportEventName.APP_LIST
        ];
    const reportData: any = {
      scene: ReportScene.SUBMIT_CREDIT_AUTH,
      events: powers,
      configs: { skipReportRes: true, ext: { apply_id: pageStore.asiApplicationId } }
    };
    const result = await NativeConfigModule.getEnvInfoSilent();
    if (result.success && result.data.osVersionCode >= 31) {
      reportData.callbacks = {
        onCallbackNeedPreciseLocation: async (): Promise<void> => {
          const { t } = this.props;
          return new Promise((resolve, reject) => {
            AKDialog.show({
              type: DialogType.C3_1_1,
              title: t("确切位置提示"),
              desc: t("为了向您提供更优质的金融服务以及风险管理，建议您授权您的确切位置"),
              positiveText: t("我知道了-权限"),
              onPositivePress: () => {
                resolve();
              }
            });
          });
        }
      };
    }
    const response = await DeviceInfoReportHelper.reportEventsWithAskPermissions(reportData);
    const { success } = response;

    if (success) {
      await nativeConfigModule.requestLocationBeforeCreditApplyCommit({
        asiApplicationId: pageStore.asiApplicationId,
        status: "not_first"
      });
      this.toInvokeSm();
      const enterPageData = { pageType: this.pageType, enter: false };
      EnterLeavePage(enterPageData);
      console.log("go2AliveDetect 1");
      const addressPageData = { pageType: 100002, enter: true };
      EnterLeavePage(addressPageData);
      console.log("go2AliveDetect 2");
      await this.go2AliveDetect();
      addressPageData.enter = false;
      EnterLeavePage(addressPageData);
      console.log("go2AliveDetect 3");
      enterPageData.enter = true;
      EnterLeavePage(enterPageData);
      console.log("go2AliveDetect 4");
    } else {
      this.refusePower();
    }
  };

  refusePower = () => {
    const { t } = this.props;
    AKDialog.show({
      type: DialogType.C3_1_1,
      desc: t("请允许AKULAKU获取您的联系人、位置和电话管理权限以便给您提供完善的金融服务"),
      positiveText: t("确认")
    });
  };

  onContentSizeChange = (w: number, h: number) => {
    this.contentHeight = h;
    if (WINDOW_HEIGHT - 194 < h) {
      this.setState({
        shadow: true
      });
    }
    if (h > WINDOW_HEIGHT - 194 && h - WINDOW_HEIGHT < 130 && this.state.footView === null) {
      this.setState({
        footView: true
      });
    }
  };

  layerNextAndReport = () => {
    const { layerNext, nowStep } = this.props;
    const enterLeavepageData = { pageType: this.pageType };
    EnterLeavePage(enterLeavepageData);
    layerNext();
  };

  onScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    this.props.store.pageStore.showEmailTips = false;
    this.offsetY = e.nativeEvent.contentOffset.y;
  };

  offsetY = 0;

  layerGoback = () => {
    const { nowStep, layerGoback } = this.props;
    const enterLeavepageData = { pageType: this.pageType };
    if (nowStep !== 1) EnterLeavePage(enterLeavepageData);
    layerGoback && layerGoback();
    if (nowStep === 0) {
      // SensorPersonalBackClick();
    }
  };

  showUploadExample = () => {
    const { t } = this.props;
    // SensorClickUploadExample();
    Dialog.show({
      renderContent: <UploadExample t={t} title={t("正确示例")} />,
      containerStyle: { borderRadius: 12, width: 288 },
      positiveText: t("好的"),
      positiveStyle: { color: "#F32823", fontSize: 16, ...StyleSheet.flatten(FontStyles["rob-medium"]) },
      seperatorColor: "#E2E5E9"
    });
  };

  showProtocols = (item: any) => {
    const { t } = this.props;
    if (item.type === 1) {
      Dialog.show({
        renderContent: <AgreementDialog t={t} data={item} />,
        positiveText: t("我知道了")
      });
    }
    if (item.type === 2) {
      PopUpPanel.show({
        title: "",
        childrenComponent: <AgreementPopView t={t} data={item} />
      });
    }
  };

  _renderConfirmCell = (title: string, value: string) => {
    return (
      <View key={title} style={{ flexDirection: "row", marginTop: 6 }}>
        <Text style={styles.icNumberNew}>{`${title}${value}`}</Text>
      </View>
    );
  };

  _renderErrorCell = (title: string, value: string) => {
    return (
      <View key={title} style={{ flexDirection: "row", marginTop: 6 }}>
        <Text style={[styles.icNumberNew, { color: "#F32823" }]}>{`${title}${value}`}</Text>
      </View>
    );
  };

  alertIcNumber = (confirms: { title: string; value: string }[], errorItems: { title: string; value: string }[]) => {
    const {
      store: { pageStore },
      t
    } = this.props;
    const dialog = new PopupExposerHelper(
      Dialog,
      {
        cn: 102,
        ei: {},
        eit: 0,
        ext: {},
        sp: { status: "not_first" }
      },
      { screenNumber: "300005" }
    );
    dialog.show({
      containerStyle: { borderRadius: 12 },
      seperatorColor: "#E2E5E9",
      hiddenBottom: true,
      renderContent: (
        <View style={{ width: 280 }}>
          <Text
            style={{
              textAlign: "center",
              fontSize: 16,
              color: "#282B2E",
              marginBottom: 10,
              ...StyleSheet.flatten(FontStyles["rob-medium"])
            }}
          >
            {t("再次确认")}
          </Text>
          <View style={{ paddingHorizontal: 24 }}>
            <Text style={{ fontSize: 14, color: "#282B2E" }}>{t("请确认你的信息")}</Text>
          </View>
          <View style={styles.icNumberViewNew}>
            {confirms.map((item, index) => {
              return this._renderConfirmCell(`${item.title}: `, item.value);
            })}
          </View>
          <View style={{ height: 0.5, marginVertical: 12, backgroundColor: "#E2E5E9", marginHorizontal: 24 }} />
          <View style={{ paddingHorizontal: 24 }}>
            <Text style={{ fontSize: 14, color: "#282B2E" }}>
              {t("以下信息可能有误，请仔细核对，如有错误请及时更正")}
            </Text>
          </View>
          <View style={styles.errorView}>
            {errorItems.map((item, index) => {
              return this._renderErrorCell(`${item.title}: `, item.value);
            })}
          </View>
          <View
            style={{
              paddingHorizontal: 24,
              paddingBottom: 24,
              paddingTop: 20,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center"
            }}
          >
            <View style={{ flex: 1, height: 40 }}>
              <AKButton
                type={AKButtonType.B1_3_2}
                text={t("取消-dialog")}
                onPress={() => {
                  Dialog.dismiss();
                  reportClick({
                    sn: 300005,
                    cn: 104,
                    sp: { status: "not_first" }
                  });
                }}
                style={{ flex: 1 }}
              />
            </View>
            <View style={{ width: 12 }} />
            <View style={{ flex: 1, height: 40 }}>
              <AKButton
                type={AKButtonType.B1_1_2}
                text={t("确认-dialog")}
                onPress={() => {
                  Dialog.dismiss();
                  this.isConfirmIcNumber = true;
                  reportClick({
                    sn: 300005,
                    cn: 103,
                    sp: { status: "not_first" }
                  });
                  this.nextAction();
                }}
                style={{ flex: 1, paddingHorizontal: 0 }}
              />
            </View>
          </View>
        </View>
      ),
      buttonSequence: 0,
      positiveText: t("确认"),
      negativeText: t("修改"),
      negativeStyle: { color: "#6E737D", fontSize: 16, ...StyleSheet.flatten(FontStyles["rob-medium"]) },
      positiveStyle: { color: "#F32823", fontSize: 16, ...StyleSheet.flatten(FontStyles["rob-medium"]) }
    });
  };

  listHeaderComponent = () => {
    if (!this.state.bannerImg) return null;
    const {
      store: { pageStore },
      t
    } = this.props;
    return (
      <>
        <Image
          source={{ uri: this.state.bannerImg }}
          style={[pageStore.newUi ? styles.bannerImgNew : styles.bannerImg]}
        />
        {pageStore.newUi ? <View style={{ height: 8, backgroundColor: "#EFF2F6" }} /> : null}
      </>
    );
  };

  renderHeaderNoSticky() {
    const {
      t,
      store: { pageStore }
    } = this.props;
    const { topTip } = this.state;
    if (topTip) {
      return <KindReminder isNew={pageStore.newUi} t={t} />;
    }
    // if (this.pageType === PageType.id_Photo) {
    //   return (
    //     <View style={{ backgroundColor: "#fff" }}>
    //       <View style={styles.idPhotoTipsContainer}>
    //         <View style={styles.tipsHeader}>
    //           <Image
    //             source={require("../../img/id_photo_tips.webp")}
    //             style={{ width: 12, height: 15, marginRight: 12 }}
    //           />
    //           <View style={{ flex: 1 }}>
    //             <Text style={styles.tipsTitle2}>{t("Akulaku为您提供银行级别的安全防护以保护您的个人隐私信息")}</Text>
    //           </View>
    //         </View>
    //       </View>
    //     </View>
    //   );
    // }
    return <View style={{ height: 8 }} />;
  }

  showFooter = () => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    const { protocol } = this.state;
    // 服务器控制
    // if (!protocol) return false;
    if (this.pageType === PageType.id_Photo) {
      // return pageStore.showHideComponent;
      return true;
    }

    return this.pageType === PageType.emergency_contact;
  };

  listFooterComponent = () => {
    const showFooter = this.showFooter();

    const {
      t,
      store: { pageStore }
    } = this.props;
    const { isAgree, agreement } = this.state;
    if (showFooter) {
      if (this.pageType === PageType.emergency_contact) {
        return (
          <BottomProtocol
            isSelected={isAgree}
            isNew={pageStore.newUi}
            content={t("特此声明所填写的资金提供者信息及资金来源均真实准确。")}
            onPressIcon={() => {
              this.setState({ isAgree: !isAgree });
              // SensorTypeClickAgreement(mData.pageType);
            }}
          />
        );
      } else if (this.pageType === PageType.id_Photo) {
        return (
          <IdPhotoProtocol
            config4030={pageStore.complianceConfig}
            t={t}
            style={{ marginBottom: pageStore.newUi ? 0 : 24 }}
            isNew={pageStore.newUi}
            isSelected={isAgree}
            onPressIcon={() => {
              this.setState({ isAgree: !isAgree });
              // SensorTypeClickAgreement(mData.pageType);
            }}
          />
        );
      }
    }
    if (agreement) {
      return (
        <SocialProtocol
          style={{ marginBottom: pageStore.newUi ? 0 : 24 }}
          isNew={pageStore.newUi}
          isSelected={pageStore.isSelectSocialProtocol}
          content={`Anda setuju dan mengizinkan Akulaku untuk melakukan verifikasi data pribadi Anda yang terdapat di BPJSTK. Verifikasi ini bertujuan untuk meningkatkan penilaian kredit Anda.`}
          protocol={"Kerjasama Akulaku dan BPJSTK."}
          onPressIcon={() => {
            pageStore.isSelectSocialProtocol = !pageStore.isSelectSocialProtocol;
            // SensorSocialProtocolClick();
          }}
          onPressContent={() => {
            NativeNavigationModule.navigate({
              url: "https://www.akulaku.com/artikel/bpjsauth/"
            });
          }}
        />
      );
    }
    return <View />;
  };

  render() {
    const {
      nowStep,
      store,
      store: { pageStore },
      t
    } = this.props;
    const { dataSource, loadFailed, refreshing, missingId, isAgree } = this.state;
    if (pageStore.nowStep !== nowStep) {
      return <View style={styles.container} />;
    }
    if (refreshing) {
      return <View style={styles.container} />;
    }
    if (loadFailed) {
      return (
        <View style={{ flex: 1 }}>
          <NavigationBar title={t("加载中")} onBackPress={this.layerGoback} />
          <NetworkErrorComponent
            containerStyle={styles.container}
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            onRetryPress={this.getPageData}
            buttonText={t("global:刷新")}
          />
        </View>
      );
    }
    const disabled = this.showFooter() && !isAgree;
    return (
      <View style={styles.container}>
        {iOS && <StatusBar barStyle="dark-content" />}
        <NavigationBar
          renderLeft={nowStep === 0 && !pageStore.showBackBtn ? <View style={{ width: 32, height: 32 }} /> : null}
          title={this.state.navTitle}
          onBackPress={this.layerGoback}
        />
        <KeyboardAvoidingView style={{ flex: 1 }} onLayout={this._onLayoutContainer}>
          <Animated.FlatList
            onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: this.scrollY } } }], {
              useNativeDriver: false,
              listener: (event: NativeSyntheticEvent<NativeScrollEvent>) => {
                // 在这里做一些额外的工作
                this.onScroll(event);
              }
            })}
            stickyHeaderIndices={pageStore.newUi ? [] : [0]}
            onLayout={this._onLayoutFlatList}
            ref={(flatList: FlatList) => {
              this.flatListRef = flatList;
            }}
            removeClippedSubviews={false}
            scrollEventThrottle={1}
            bounces={false}
            keyExtractor={this.keyExtractor}
            data={dataSource}
            extraData={[missingId, pageStore.isSelectSocialProtocol]}
            renderItem={this.renderItem}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps={"handled"}
            ListHeaderComponent={this.listHeaderComponent}
            ListFooterComponent={this.listFooterComponent}
            onContentSizeChange={this.onContentSizeChange}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
          <Email store={store} allItems={this.allItems} />
          <BottomButton
            style={{ paddingTop: pageStore.newUi ? 12 : 8 }}
            text={this.pageType === PageType.face_recognition ? t("立即刷脸") : t("下一步")}
            next={this.next}
            disabled={disabled}
            t={t}
          />
        </KeyboardAvoidingView>
      </View>
    );
  }
}
