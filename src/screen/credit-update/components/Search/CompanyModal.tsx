/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/components/Search/CompanyModal.tsx
 */
import { StyleSheet, Text, TextInput, View, Image, FlatList, TouchableOpacity, DeviceEventEmitter } from "react-native";
import React, { Component, PureComponent, useRef } from "react";
import { ItemType } from "../../dict/ComponentType";
import { InputBizInfo } from "@akulaku-rn/rn-v4-sdk/src/ReporterSdk/types";
import { HocActionLogInput, HocActionLogInputCompProps } from "@akulaku-rn/rn-v4-sdk";
import { TFunction } from "i18next";
import _ from "lodash";
import store from "../../store";
import api from "../../store/api";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import { pageStoreModel } from "@akulaku-rn/akulaku-ec-common/src/components/BaseContainer/Type";
import { FontStyles, PopUpContainer, WINDOW_HEIGHT } from "@akulaku-rn/akui-rn";
import { dynamicT } from "common/services/i18n";
// import { SensorCompanyInputClick, SensorCompanyInputClose } from "../../tool/EventTracking";

interface ItemI {
  name: string;
  id: string;
  recommendIndex: string | null;
}

type Props = {
  id: number;
  type: string;
  screen: string;
  t: TFunction;
  store: pageStoreModel<store>;
};

type State = {
  data: ItemI[];
  key: string;
  isfocus: boolean;
};

class CompanyModal extends Component<Props, State> {
  flatListRef: any;

  textInputRef: any;

  searchKey: any;

  actionLogKey: string;

  ActionTextInput: React.ComponentType<HocActionLogInputCompProps>;

  constructor(props: Props) {
    super(props);

    this.state = {
      data: [],
      key: "",
      isfocus: false
    };
    this.searchKey = "";
    this.textInputRef = null;
    this.actionLogKey = "";
    this.ActionTextInput = this.getActionTextInput();
  }

  componentDidMount() {
    this.featchCompanyData();
  }

  componentWillUnmount() {
    // SensorCompanyInputClose();
  }

  getActionTextInput = () => {
    const { type } = this.props;
    const v4Dic =
      type === ItemType.search_company
        ? {
            sn: 300021,
            cn: 1,
            sp: { status: "not_first" }
          }
        : ({} as InputBizInfo);
    if (!this.ActionTextInput) {
      this.ActionTextInput = HocActionLogInput(v4Dic)(TextInput);
    }
    return this.ActionTextInput;
  };

  featchCompanyData = () => {
    // if (!this.searchKey || !this.searchKey.length) return;
    const {
      store: {
        pageStore,
        runtime: { countryId }
      }
    } = this.props;
    const url = api.SEARCH_COMPANY;

    const tempKey = this.searchKey;
    pageStore.post(
      url,
      { key: tempKey, countryId },
      (response: any) => {
        const { data } = response;
        if (!!data) {
          this.setState({ data, key: tempKey });
        }
      },
      (error: any) => {
        const { message } = error;
        Toast.show(message, {
          position: 0
        });
      }
    );
  };

  _handleSearchKey = _.debounce(() => {
    this.featchCompanyData();
  }, 500);

  onChangeText = (searchKey: string) => {
    this.searchKey = searchKey;
    this._handleSearchKey();
  };

  didSelectedItem = (data: { name: string }) => {
    const { id } = this.props;
    const params = {
      data,
      id
    };
    DeviceEventEmitter.emit("setValue", params);
    PopUpContainer.dismiss();
  };

  renderItem = ({ item, index }: { item: any; index: number }) => {
    return <Button t={this.props.t} data={item} didSelectedItem={this.didSelectedItem} />;
  };

  _ListFooterComponent = () => {
    if (this.state.data.length > 1) return null;
    const { t } = this.props;
    return (
      <View style={styles.nodataView}>
        <Text style={styles.nodataTitle}>
          {t("噢！我们无法搜索到可以匹配的公司名称，您可以直接选择您输入的公司名称。")}
        </Text>
      </View>
    );
  };

  _onPressClean = () => {
    this.onChangeText("");
    this.textInputRef && this.textInputRef.clear();
  };

  onFocus = () => {
    // SensorCompanyInputClick();
    this.setState({ isfocus: true });
  };

  onBlur = () => {
    this.setState({ isfocus: false });
  };

  render() {
    const { t, screen } = this.props;
    const { data, key } = this.state;
    const ActionTextInput = this.getActionTextInput();
    return (
      <View style={styles.container}>
        <View style={styles.searchBorderView}>
          <Image style={styles.searchIcon} source={require("../../img/credit_compangy_search_new.webp")} />
          <ActionTextInput
            sensitive={false}
            screen={screen}
            getRef={(ref: TextInput) => (this.textInputRef = ref)}
            selectionColor={"#e62117"}
            style={styles.textInput}
            placeholder={t("输入或选择您的公司名称")}
            placeholderTextColor={"#6E737D"}
            secureTextEntry={false}
            onChangeText={this.onChangeText}
            onFocus={this.onFocus}
            onBlur={this.onBlur}
            maxLength={250}
          />
          {this.state.isfocus ? (
            <TouchableOpacity onPress={this._onPressClean} style={styles.deleteContainer}>
              <Image style={styles.deleteIcon} source={require("../../img/credit_company_delete.webp")} />
            </TouchableOpacity>
          ) : null}
        </View>
        {!!key ? null : (
          <View style={styles.header}>
            <Text style={{ fontSize: 18, color: "#282B2E", ...StyleSheet.flatten(FontStyles["roboto-bold"]) }}>
              {t("热门公司")}
            </Text>
            <View
              style={{ position: "absolute", bottom: 0, right: 0, left: 16, height: 0.5, backgroundColor: "#E2E5E9" }}
            />
          </View>
        )}
        {!!data.length && (
          <FlatList
            style={{ backgroundColor: "#fff" }}
            showsVerticalScrollIndicator={false}
            data={data}
            renderItem={this.renderItem}
            keyExtractor={(item, index) => "" + index}
            ListFooterComponent={this._ListFooterComponent}
            keyboardShouldPersistTaps={"handled"}
          />
        )}
      </View>
    );
  }
}

export default CompanyModal;

type SelectProps = {
  t: TFunction;
  data: ItemI;
  didSelectedItem: (item: ItemI) => void;
};
class Button extends PureComponent<SelectProps> {
  selectItem = () => {
    const { data, didSelectedItem } = this.props;
    didSelectedItem(data);
  };

  render() {
    const { t, data } = this.props;
    return (
      <TouchableOpacity style={styles.button} onPress={this.selectItem}>
        <Text style={styles.buttonTitle}>{data.name}</Text>
        {!!data.recommendIndex ? (
          <Text style={styles.detail}>
            {dynamicT(t, "x的用户选择", {
              ["x"]: <Text style={styles.activeText}>{`${data.recommendIndex}`}</Text>
            })}
          </Text>
        ) : null}
        <View
          style={{ position: "absolute", bottom: 0, right: 0, left: 16, height: 0.5, backgroundColor: "#E2E5E9" }}
        />
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    height: WINDOW_HEIGHT * 0.85 - 66
  },
  header: { paddingHorizontal: 16, paddingTop: 12, paddingBottom: 16 },
  searchBorderView: {
    // marginTop: 12,
    marginBottom: 12,
    marginLeft: 16,
    marginRight: 16,
    backgroundColor: "#fff",
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#E2E5E9",
    height: 40,
    flexDirection: "row"
  },
  textInput: {
    padding: 0,
    marginLeft: 4,
    marginTop: 8,
    marginBottom: 8,
    marginRight: 66,
    color: "#282B2E",
    flex: 1
  },
  searchIcon: {
    width: 24,
    height: 24,
    alignSelf: "center",
    marginLeft: 12
  },
  deleteContainer: {
    position: "absolute",
    alignSelf: "center",
    right: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "flex-end"
  },
  deleteIcon: {
    width: 20,
    height: 20
  },
  section: {
    fontSize: 14,
    color: "#333",
    height: 50,
    lineHeight: 50,
    marginLeft: 16,
    backgroundColor: "#fff"
  },
  button: {
    backgroundColor: "#fff",
    justifyContent: "center",
    flex: 1,

    padding: 16
  },
  buttonTitle: {
    color: "#282B2E",
    fontSize: 14
  },
  detail: {
    marginTop: 6,
    color: "#6E737D",
    fontSize: 12
  },
  activeText: {
    color: "#009957",
    fontSize: 14,
    ...StyleSheet.flatten(FontStyles["roboto-bold"])
  },
  nodataView: { paddingHorizontal: 16, paddingVertical: 12 },
  nodataTitle: {
    fontSize: 12,
    color: "#6E737D"
  }
});
