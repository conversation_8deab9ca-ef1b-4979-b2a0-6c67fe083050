import React, { PureComponent } from "react";
import {
  Text,
  StyleSheet,
  Animated,
  Image,
  TouchableOpacity,
  View,
  DeviceEventEmitter,
  EmitterSubscription
} from "react-native";
import { FontStyles, PopUpContainer, PopUpContainerType } from "@akulaku-rn/akui-rn";
import {
  EnterLeavePage,
  // SensorCreditCompanyPopView,
  SensorTypeClickItem
  // V3ClickItem,
  // V4ClickItem
} from "../../tool/EventTracking";
import { NewEntityInfos } from "../../tool/DataHandling";
import { TFunction } from "i18next";
import store from "../../store";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { NativeNavigationModule } from "common/nativeModules";
import { observer } from "mobx-react";
import PopUpPanel from "@akulaku-rn/akui-rn/src/components/PopUpPanel";
import CompanyModal from "./CompanyModal";
import { ItemType } from "../../dict/ComponentType";
import { TitleInfo, getTitleInfo } from "../../dict/GrayRelease";

type Props = {
  setValue: (text: string, id: number, type: any, key: any) => void;
  data: NewEntityInfos;
  containerStyle?: object;
  missingId: number | null;
  t: TFunction;
  store: pageStoreModel<store>;
};

type States = {
  color: string;
  text: string | null;
  showError: boolean;
  isfocus: boolean;
};

@observer
export default class Search extends PureComponent<Props, States> {
  top: Animated.Value;

  fontSize: Animated.Value;

  listener!: EmitterSubscription;

  titleInfo: TitleInfo;

  constructor(props: Props) {
    super(props);
    const {
      data: { id, needModified },
      store: { pageStore }
    } = props;
    const lastValue = pageStore.useCreditData[id];
    this.state = {
      text: lastValue ? lastValue : null,
      color: lastValue ? "#999" : "#B3B3B3",
      showError: needModified,
      isfocus: false
    };
    this.titleInfo = getTitleInfo(pageStore.newUi);
    this.top = new Animated.Value(lastValue ? this.titleInfo.focus.top : this.titleInfo.blur.top);
    this.fontSize = new Animated.Value(lastValue ? this.titleInfo.focus.fontSize : this.titleInfo.blur.fontSize);
  }

  componentDidMount() {
    this.listener = DeviceEventEmitter.addListener("setValue", params => {
      const { data, id } = params;
      if (this.props.data.id === id) {
        this.setValue(data);
      }
    });
  }

  componentWillUnmount() {
    this.listener.remove();
  }

  SensorTypeClickItem = (id: any) => {
    SensorTypeClickItem(this.props.data.pageType, id);
    // V3ClickItem(id);
    // const v4Data = { entryid: id, type: "search", isFirst: this.props.isFirstApply };
    // V4ClickItem(v4Data);
  };

  onFocus = () => {
    const {
      data: { id, title, pageType, type },
      t,
      store
    } = this.props;
    const { text } = this.state;
    this.setState({ isfocus: true });
    this.SensorTypeClickItem(id);
    const enterPageData = { pageType, enter: false };
    EnterLeavePage(enterPageData);

    if (store.pageStore.newUi) {
      // SensorCreditCompanyPopView();
      PopUpContainer.show({
        type: PopUpContainerType.D4_1_1,
        headerProps: { title },
        renderContent: (
          <CompanyModal store={store} t={t} id={id} screen={store.navParams.screen} type={ItemType.search_company} />
        )
      });
    } else {
      NativeNavigationModule.navigate({
        screen: "CreditSearchPage",
        params: { lastValue: text, type, id, title, pageType }
      });
    }
  };

  setValue = (data: any) => {
    const {
      setValue,
      data: { id, regex }
    } = this.props;
    let showError = false;
    if (!!regex && !!data.name) {
      const reg = new RegExp(regex);
      showError = !reg.test(data.name);
    }
    this.setState({ text: data.name, showError }, () => {
      this.onBlur();
    });
    setValue(data.name, id, "search", data.id);
  };

  returnErrorText = () => {
    const {
      t,
      data: { needModified, errMsg }
    } = this.props;
    const { showError } = this.state;

    let errorText = "";
    if (showError) {
      if (!!errMsg) {
        errorText = t(errMsg);
      } else {
        errorText = t("格式不正确");
      }
      if (needModified) {
        errorText = t("请修改");
      }
    } else {
      errorText = t("此项未填写");
    }
    return errorText;
  };

  onBlur = () => {
    this.setState({ isfocus: false });
    if (this.state.text && !!this.state.text.length) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: this.titleInfo.focus.top,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: this.titleInfo.focus.fontSize,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#999"
      });
    } else {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: this.titleInfo.blur.top,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: this.titleInfo.blur.fontSize,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#B3B3B3"
      });
    }
  };

  render() {
    const {
      containerStyle,
      data: { id, title, needModified },
      missingId,
      t,
      store: { pageStore }
    } = this.props;
    const { text, color, showError } = this.state;
    return (
      <View style={containerStyle}>
        <TouchableOpacity
          style={[
            pageStore.newUi ? styles.newContainer : styles.container,
            missingId === id && !text && { borderBottomColor: "#e62117" }
          ]}
          onPress={this.onFocus}
        >
          <Text style={styles.text}>{text}</Text>
          {pageStore.newUi ? (
            <Image
              source={require("../../img/credit_icon_down.webp")}
              style={{
                position: "absolute",
                right: 16,
                width: 16,
                height: 16,
                alignSelf: "center"
              }}
            />
          ) : (
            <Image style={styles.arrowIcon} source={require("../../img/credit_input_ico_arrow.webp")} />
          )}
          <Animated.View
            style={{
              position: "absolute",
              zIndex: 9,
              top: this.top,
              left: pageStore.newUi ? 15 : 0
            }}
          >
            <Animated.Text
              style={{ fontSize: this.fontSize, color: pageStore.newUi ? "#989FA9" : color, lineHeight: 18 }}
            >
              {title}
            </Animated.Text>
          </Animated.View>
        </TouchableOpacity>
        {((missingId === id && !text) || showError) && (
          <View style={styles.errorView}>
            <Text style={styles.errorText}>{this.returnErrorText()}</Text>
          </View>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingBottom: 10,
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    paddingTop: 24,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingRight: 12,
    alignItems: "center"
  },
  newContainer: {
    paddingBottom: 10,
    paddingTop: 30,
    paddingRight: 12,
    borderWidth: 1,
    borderColor: "#E2E5E9",
    paddingHorizontal: 15,
    borderRadius: 6,
    flexDirection: "row"
  },
  text: {
    fontSize: 16,
    color: "#282b2e",
    maxWidth: "90%"
  },
  errorView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8
  },
  errorText: {
    fontSize: 14,
    color: "#e62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginRight: 4
  },
  arrowIcon: {
    width: 12,
    height: 13
  }
});
