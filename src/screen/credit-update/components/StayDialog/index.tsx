import { AKButton, AKButtonType } from "common/components/AKButton";
import { Android, FontStyles } from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";
import { isNil } from "lodash";
import React, { PureComponent } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  TextStyle,
  BackHandler,
  ViewStyle,
  Image
} from "react-native";
import RootSiblings from "react-native-root-siblings";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { NativeNavigationModule } from "common/nativeModules";
import store from "../../store";

type Options = {
  store: pageStoreModel<store>;
  t: TFunction;
};

type Props = {
  store: pageStoreModel<store>;
  t: TFunction;
};

export default class StayDialog extends PureComponent<Props> {
  opacity: Animated.Value;

  showAni: Animated.CompositeAnimation;

  dismissAni: Animated.CompositeAnimation;

  backHandlerListener: any;

  static show(options: Options) {
    StayDialog.dialogArr.push(
      new RootSiblings(
        (
          <StayDialog
            ref={(comp: any) => {
              if (comp) {
                StayDialog.refArr.push(comp);
              }
            }}
            key={"AKDialog"}
            {...options}
          />
        )
      )
    );
  }

  static dismiss() {
    if (StayDialog.dialogArr.length >= 1) {
      const ref = this.refArr.pop();
      ref && ref.dismiss();
      // @ts-ignore
      const window = StayDialog.dialogArr.pop();
      window && window.destroy();
    }
  }

  constructor(props: any) {
    super(props);
    this.opacity = new Animated.Value(0);
    this.showAni = Animated.spring(this.opacity, {
      toValue: 1,
      useNativeDriver: true
    });
    this.dismissAni = Animated.spring(this.opacity, {
      toValue: 0,
      useNativeDriver: true
    });
  }

  componentDidMount(): void {
    this.showAni.start();
    if (Android) {
      this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
    }
  }

  componentWillUnmount() {
    if (Android) {
      this.backHandlerListener && this.backHandlerListener.remove();
    }
  }

  static refArr: Array<any> = [];

  static dialogArr: Array<RootSiblings> = [];

  listenerArr: Array<any> = [];

  onBackPress = () => {
    StayDialog.dismiss();
    return true;
  };

  dismiss() {
    this.dismissAni.start(() => {
      this.setState({
        visible: false
      });
    });
  }

  onNegativePress = () => {
    const {
      navParams: { gobackNum }
    } = this.props.store;
    if (!isNil(gobackNum)) {
      NativeNavigationModule.popTo(parseInt(gobackNum + "") + 1);
    } else {
      NativeNavigationModule.goBack();
    }
  };

  render() {
    const { t } = this.props;
    return (
      <Animated.View
        style={[
          styles.container,
          {
            opacity: this.opacity
          }
        ]}
      >
        <View style={styles.content}>
          <View style={{ paddingHorizontal: 24, alignItems: "center" }}>
            <Image source={require("../../img/withdraw.webp")} style={styles.image} />
            <Text style={styles.retainText}>{t("最高可得25000k消费额度，建议继续申请哦！")}</Text>
            <TouchableOpacity onPress={this.onBackPress} style={styles.close}>
              <Image style={styles.closeIcon} source={require("../../img/icon_close2.webp")} />
            </TouchableOpacity>
          </View>
          <View style={styles.buttonView}>
            <AKButton
              onPress={this.onBackPress}
              text={t("继续申请")}
              type={AKButtonType.B1_1_2}
              style={{
                marginHorizontal: 24
              }}
            />
            <Text onPress={this.onNegativePress} style={styles.bottomButtonText}>
              {t("放弃")}
            </Text>
          </View>
        </View>
      </Animated.View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    alignItems: "center",
    justifyContent: "center"
  },
  content: {
    elevation: 8,
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    width: 280,
    borderRadius: 4,
    backgroundColor: "#ffffff"
  },
  close: {
    position: "absolute",
    top: 8,
    right: 8
  },
  closeIcon: {
    width: 24,
    height: 24
  },
  retainText: {
    fontSize: 14,
    color: "#2C2C2A",
    marginVertical: 16
  },
  image: {
    width: 280,
    height: 125,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4
  },
  buttonView: {
    flexDirection: "column"
  },
  topButton: {
    paddingVertical: 10,
    height: 38,
    alignItems: "center",
    borderRadius: 19,
    marginHorizontal: 24,
    backgroundColor: "#F32823"
  },
  topButtonText: { fontSize: 14, color: "#fff", ...FontStyles["rob-medium"] },
  bottomButtonText: {
    textAlign: "center",
    color: "#999",
    fontSize: 14,
    marginTop: 16,
    marginBottom: 24,
    ...FontStyles["rob-medium"]
  }
});
