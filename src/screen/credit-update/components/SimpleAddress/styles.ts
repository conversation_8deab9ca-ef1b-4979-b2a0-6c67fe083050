import { FontStyles } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";
import { p2d } from "common/util";

export default StyleSheet.create({
  container: {
    paddingBottom: 10,
    flexDirection: "row",
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    justifyContent: "space-between",
    paddingRight: 12,
    paddingTop: 24,
    alignItems: "center"
  },
  containerNew: {
    paddingBottom: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingRight: 16,
    paddingTop: 30,
    borderWidth: 1,
    borderColor: "#E2E5E9",
    paddingHorizontal: 16,
    borderRadius: 6,
    minHeight: 60
  },
  placeholderTextFouces: {
    position: "absolute",
    fontSize: 12,
    color: "#989FA9",
    left: 16,
    top: 10
  },
  placeholderText: {
    position: "absolute",
    fontSize: 12,
    color: "#999",
    top: 0,
    left: 0
  },
  placeholderText2: {
    position: "absolute",
    fontSize: 16,
    color: "#989FA9",
    left: 16,
    alignSelf: "center"
  },
  fdView: {
    flexDirection: "row",
    alignItems: "center"
  },

  addressIcon: {
    width: 24,
    height: 24,
    marginRight: 12
  },
  arrowIcon: {
    marginLeft: 16,
    width: 12,
    height: 13
  },
  arrowIconNew: {
    position: "absolute",
    alignSelf: "center",
    right: 16,
    width: 12,
    height: 12
  },
  title: {
    fontSize: 14,
    color: "#B3B3B3",
    maxWidth: p2d(240)
  },
  newTitle: {
    fontSize: 16,
    color: "#282B2E",
    maxWidth: p2d(240)
  },
  hasTitle: {
    color: "#333"
  },
  errorView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8
  },
  errorText: {
    fontSize: 14,
    color: "#e62117"
  }
});
