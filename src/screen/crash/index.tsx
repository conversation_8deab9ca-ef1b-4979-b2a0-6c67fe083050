import { inject, observer } from "mobx-react";
import React, { Component } from "react";
import RootView from "common/components/Layout/RootView";
import store from "./store";
import { View, Text, Button } from "react-native";
import * as Sentry from "@sentry/react-native";
import { NativeBaseStationModule } from "common/nativeModules";

type Props = {};

type State = {};

@RootView({ store })
@inject("store")
@observer
export default class CrashScreen extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
  }

  render() {
    return (
      <View style={{ flexDirection: "column" }}>
        <Text>rn异常模拟</Text>
        <Button
          title={"模拟运行时nativeCrash，每次点击错误次数+1"}
          onPress={() => {
            Sentry.nativeCrash();
          }}
        ></Button>
        <Button
          title={"模拟运行时js错误，每次点击，错误次数+1"}
          onPress={() => {
            const a: any = 12345;
            const b: string = a;
          }}
        ></Button>
        <Button
          title={"模拟rn启动错误，再次点击恢复"}
          onPress={() => {
            NativeBaseStationModule.addFlag("init");
          }}
        ></Button>
        <Button
          title={"模拟rn运行时错误"}
          onPress={() => {
            NativeBaseStationModule.addFlag("runtime");
          }}
        ></Button>
        <Button
          title={"模拟bundle下载异常，再次点击恢复"}
          onPress={() => {
            NativeBaseStationModule.addFlag("bundleDownload");
          }}
        ></Button>
      </View>
    );
  }
}
