import { observable, action } from "mobx";
import Basic from "common/store/Basic";

export default class CrashScreenStore extends Basic {
  @observable name = "<PERSON>";

  @observable age = 19;

  @observable version = "xx";

  @observable response: any = null;

  page = 1;

  @action
  setPerson({ name, age }: { name: string; age: number }) {
    this.name = name;
    this.age = age;
  }

  @action
  setResponse(response: any) {
    this.response = response;
  }

  @action
  setUpdateBundleVersion(version: string) {
    this.version = version;
  }
}
