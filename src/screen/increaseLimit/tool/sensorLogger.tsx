import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { reportClick } from "@akulaku-rn/rn-v4-sdk";
const SensorTypeCLICKItem = (
  Aku_buttonId: any,
  Aku_buttonName: any,
  Aku_buttonStatus: any,
  data: { fieldID: any; fieldName: any; fieldType: any; required: any }
) => {
  const { fieldID, fieldName, fieldType, required } = data;
  NativeSensorModule.sensorLogger(SensorType.CLICK, {
    page_name: "quota submission page",
    element_id: "9150102",
    element_name: "field",
    module_id: "01",
    module_name: "quota",
    position_id: "02",
    page_id: "915",
    extra: {
      Aku_buttonId,
      Aku_buttonName,
      Aku_buttonStatus,
      fieldID,
      fieldName,
      fieldType,
      required,
      Aku_jumpSource: 0
    }
  });
  reportClick({
    cn: 1,
    sn: 100633,
    sp: { Aku_buttonId },
    ext: {
      Aku_buttonId
    }
  });
};

const SensorTypeCLICKNext = (Aku_buttonId: any, Aku_buttonName: any, Aku_buttonStatus: any, Aku_SecondChannel: any) => {
  NativeSensorModule.sensorLogger(SensorType.CLICK, {
    page_name: "quota submission page",
    element_id: "9150103",
    element_name: "submit",
    module_id: "01",
    module_name: "quota",
    position_id: "03",
    page_id: "915",
    extra: {
      Aku_buttonId,
      Aku_buttonName,
      Aku_buttonStatus,
      Aku_SecondChannel,
      Aku_jumpSource: 0
    }
  });
  reportClick({
    cn: 2,
    sn: 100633,
    sp: { Aku_buttonId },
    ext: {
      Aku_buttonId
    }
  });
};

export { SensorTypeCLICKItem, SensorTypeCLICKNext };
