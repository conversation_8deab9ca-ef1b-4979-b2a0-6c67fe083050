/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-09 16:21
 * @description
 */
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { NativeNavigationModule } from "common/nativeModules";
import _, { get } from "lodash";
import { reportClick, reporter } from "@akulaku-rn/rn-v4-sdk";

let PAGE_BASE_PARAMS = { page_id: "", page_name: "" };

let EXTRA_BASE_PARAMS = {};

export const setBaseParmas = ({ page_id, page_name, ...otherExtra }: { [key: string]: any } = {}) => {
  PAGE_BASE_PARAMS = { page_id: "915", page_name: "quota submission page" };
  EXTRA_BASE_PARAMS = otherExtra;
};

export const getPageBaseParams = (): { page_id: string; page_name: string } => ({ ...PAGE_BASE_PARAMS });

export const getExtraBaseParams = (): any => ({ ...EXTRA_BASE_PARAMS });

export default {
  sc: {
    //didi
    clickBackOnResultPage(pageStatus: string) {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "10000101",
        element_name: "return",
        module_id: "01",
        module_name: "quota",
        position_id: "01",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS,
          Aku_PageStatus: pageStatus
        }
      });
    },
    //didi
    clickReturnCenter(pageStatus: string) {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "10000102",
        element_name: "return center",
        module_id: "01",
        module_name: "quota",
        position_id: "02",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS,
          Aku_PageStatus: pageStatus
        }
      });
    }
  },
  v4: {
    //didi
    getResultScreenParams() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100635,
        sp: { Aku_buttonId, jumpSource }
      };
    },
    //didi
    clickBack() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100635,
        cn: 1,
        sp: { Aku_buttonId, jumpSource }
        // ext: getExtraBaseParams()
      });
    }
  }
};
