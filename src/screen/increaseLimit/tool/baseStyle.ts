import { Android, FontStyles, FontUtil } from "@akulaku-rn/akui-rn";

export const Regular = "400";
export const Medium = "600";
export const DemiBold = "600";
export const SemiBold = "600";
export const Bold = "700";

export const Roboto = FontUtil.getFontFamily("roboto");
export const DinBold = FontUtil.getFontFamily("din-bold");

export const Purple = "#4C65E9";
export const Blue = "#4E9BFB";
export const Red = "#E62117";
export const Green = "#84C10F";
export const Yellow = "#FF9D00";

export const segmentLineBG = "#ebebeb";
export const segmentItemBG = "#f5f5f5";
export const pageBG = "#f5f5f5";

const tabContainerStyle = Android
  ? {
      elevation: 0
    }
  : {
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.18,
      shadowRadius: 0
      // backgroundColor: "red"
    };

export const tabsStyle = {
  tabContainerStyle: tabContainerStyle,
  tabBarUnderlineStyle: {
    backgroundColor: "#E62117",
    height: 2
  }
};

export const tabStyle: any = {
  activeTextStyle: {
    color: "#E21212",
    fontSize: 14,
    ...FontStyles["rob-medium"]
  },
  textStyle: {
    color: "#999",
    fontSize: 14,
    ...FontStyles["rob-medium"]
  }
};
