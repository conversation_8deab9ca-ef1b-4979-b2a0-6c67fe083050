import React from "react";
import { action, observable } from "mobx";
import Basic from "common/store/Basic";
import { Loading } from "@akulaku-rn/akui-rn";
import { NativeNetworkModuleV2 } from "common/nativeModules";
import { SERVICE_TYPES } from "../tool/client";

export default class Store extends Basic {
  @observable useQuotaData = {};

  @observable useQuotaOtherData = {};

  @observable searchPageY = 0;

  @observable showSearchPopView = false;

  @observable nowOnFocusid = null;

  @action
  post = async (url: string, params: object, requestSuccess: any, requestFailure?: any) => {
    this.io.POST(
      url,
      params,
      (response: any) => {
        requestSuccess && requestSuccess(response);
        Loading.dismiss();
      },
      (error: any) => {
        requestFailure && requestFailure(error);
        Loading.dismiss();
      }
    );
  };

  @action
  uploadImages = async (waitImgs: Array<any>, countryId: any) => {
    const entryIds: any[] = [];
    waitImgs.map(i => {
      entryIds.push(i.entryId);
    });
    const waitImgsTwo = JSON.parse(JSON.stringify(waitImgs));
    let data: any = null;
    Loading.show();
    try {
      // const response = await this.io.post(SERVICE_TYPES.UPLOAD_URL, { countryId, entryIds, cnt: entryIds.length });
      const response = await this.io.post(SERVICE_TYPES.UPLOAD_URL2, { busiType: 10, cnt: entryIds.length });
      // const { infos } = response.data;
      response.data.map((i: { url: any; key: any }, k: any) => {
        waitImgs[k].url = i.url;
        waitImgs[k].key = i.key;
      });
      // response2.data.map((i: { url: any; key: any }, k: any) => {
      //   waitImgsTwo[k].url = i.url;
      //   waitImgsTwo[k].key = i.key;
      // });
      const uploadSuccess = await this.uploadFilesToUrls(waitImgs);
      // const uploadSuccess2 = await this.uploadFilesToUrls(waitImgsTwo);
      if (uploadSuccess) {
        waitImgs.map(i => {
          delete i.url;
        });
        data = {
          success: true,
          data: waitImgs
        };
      } else {
        data = {
          success: false
        };
      }
      // if (uploadSuccess2) {
      //   data.data.forEach((i: { entryId: any; key2: any }) => {
      //     waitImgsTwo.map((item: { entryId: any; key: any }) => {
      //       if (item.entryId === i.entryId) {
      //         i.key2 = item.key;
      //       }
      //     });
      //   });
      // } else {
      //   data = {
      //     success: false
      //   };
      // }
    } catch (e) {
      data = {
        success: false
      };
    } finally {
      Loading.dismiss();
      return data;
    }
  };

  uploadFilesToUrls = async (waitImgs: any) => {
    const newWaitImgs = JSON.parse(JSON.stringify(waitImgs));
    newWaitImgs.map((i: any) => {
      delete i.entryId;
      delete i.key;
    });
    const response = await NativeNetworkModuleV2.uploadFilesToUrls(newWaitImgs);
    return response.success;
  };
}
