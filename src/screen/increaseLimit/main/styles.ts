/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 14:28
 * @description
 */
import { StyleSheet } from "react-native";
import { WINDOW_WIDTH, FontStyles } from "@akulaku-rn/akui-rn";

const columnWidth = (WINDOW_WIDTH - 16 - 18 - 6) / 2;
const columnHeight = (columnWidth / 160) * 206;

export default StyleSheet.create({
  horizonViewBg: {
    flexDirection: "row"
  },
  bottomView1: {
    width: WINDOW_WIDTH,
    height: 8,
    backgroundColor: "#f5f5f5",
    marginTop: 7
  },
  bottomView2: {
    width: "100%",
    height: 1,
    backgroundColor: "#ebebeb"
  },
  bottomView3: {
    width: "100%",
    height: 32,
    backgroundColor: "#f5f5f5"
  },
  bottomImg: {
    width: 13,
    height: 13,
    alignSelf: "center"
  },
  bottomText: {
    lineHeight: 14,
    fontSize: 12,
    alignSelf: "center",
    color: "#3F76F4"
  },
  headImg: {
    width: WINDOW_WIDTH,
    height: WINDOW_WIDTH,
    position: "absolute"
  },
  horizontalItemDivider: {
    width: 1,
    height: 16,
    alignSelf: "center",
    backgroundColor: "#d9d9d9"
  },
  IncreaseLimitGroupDivider: {
    width: WINDOW_WIDTH,
    height: 8,
    backgroundColor: "#f5f5f5"
  },
  container: {
    backgroundColor: "#f5f5f5",
    flex: 1
  },
  grayGap: {
    height: 8,
    width: WINDOW_WIDTH,
    backgroundColor: "#F5F6F7"
  },
  bottomADContainer: {
    paddingLeft: 16,
    paddingRight: 18,
    paddingBottom: 20,
    backgroundColor: "white",
    alignItems: "center"
  },
  bottomADTitleText: {
    fontSize: 18,
    lineHeight: 21,
    ...FontStyles["rob-medium"],
    marginBottom: 8
  },
  bottomADLeft: {
    width: columnWidth,
    height: columnHeight,
    marginRight: 6,
    backgroundColor: "red",
    borderRadius: 6,
    overflow: "hidden"
  },
  bottomADLeftWrapper: {
    flexDirection: "row",
    width: "100%",
    height: columnHeight,
    marginTop: 20
  },
  bottomADRightWrapper: {
    width: columnWidth,
    justifyContent: "space-between",
    height: "100%"
  },
  bottomADRight: {
    width: columnWidth,
    height: (columnWidth / 160) * 100,
    backgroundColor: "lightblue",
    borderRadius: 6,
    overflow: "hidden"
  },
  question: {
    position: "absolute",
    top: 92,
    right: 16,
    width: 16,
    height: 16
  },
  adCarousel: {
    width: WINDOW_WIDTH - 32,
    borderRadius: 5,
    overflow: "hidden",
    alignSelf: "center",
    paddingBottom: 16
  },
  dot: {
    width: 6,
    height: 3,
    backgroundColor: "#EFF2F6",
    borderRadius: 3,
    marginHorizontal: 1.5
  },
  activeDot: {
    width: 10,
    height: 3,
    backgroundColor: "#FF5353",
    borderRadius: 3,
    marginHorizontal: 1.5,
    opacity: 1
  },
  scrollContainer: {
    alignItems: "center",
    backgroundColor: "#00000000"
  },
  functionContainer: {
    width: WINDOW_WIDTH,
    height: 40,
    flexDirection: "row",
    backgroundColor: "#F5F5F5",
    alignItems: "center",
    justifyContent: "center"
  },
  functionLine: {
    width: 32,
    height: 1,
    backgroundColor: "#c8ced4"
  },
  functionImg: {
    width: 20,
    height: 20,
    marginLeft: 10
  },
  functionText: {
    color: "#282B2E",
    fontSize: 12,
    marginRight: 9,
    marginLeft: 9,
    ...FontStyles["rob-medium"]
  },
  bnplView: {
    width: WINDOW_WIDTH,
    marginBottom: 12,
    paddingBottom: 16,
    borderRadius: 10,
    backgroundColor: "#fff",
    alignItems: "center"
  },
  bnplImgBg: {
    width: WINDOW_WIDTH - 33,
    marginBottom: 36
  },
  bnplGroup: {
    width: 208,
    height: 60,
    alignSelf: "center",
    marginTop: 24
  },
  availableCredit: {
    fontSize: 14,
    color: "#282B2E",
    alignSelf: "center",
    marginTop: 4
  },
  availableCreditValue: {
    ...FontStyles["DIN-Bold"],
    fontSize: 32,
    color: "#282B2E",
    alignSelf: "center",
    marginTop: 4
  },
  guide: {
    position: "absolute",
    padding: 4,
    right: -4,
    top: 12
  },
  bnplQuestion: {
    width: 16,
    height: 16
  },
  totalView: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: WINDOW_WIDTH - 33,
    height: 16,
    paddingHorizontal: 16,
    backgroundColor: "#00000000"
  },
  totalText: {
    fontSize: 14,
    ...FontStyles["rob-medium"],
    color: "#989fa9",
    alignSelf: "center"
  },
  totalValue: {
    fontSize: 14,
    ...FontStyles["rob-medium"],
    color: "#333333",
    alignSelf: "center"
  },
  paylaterArrow: {
    width: 0,
    height: 0,
    borderStyle: "solid",
    borderWidth: 6,
    alignSelf: "center",
    borderTopColor: "#00000000", //下箭头颜色
    borderLeftColor: "#00000000", //右箭头颜色
    borderBottomColor: "#FFA732", //上箭头颜色
    borderRightColor: "#00000000", //左箭头颜色,
    marginTop: 12
  },
  available: {
    fontSize: 12,
    color: "#FFFFFF",
    borderRadius: 15,
    borderStyle: "solid",
    backgroundColor: "#FFA732",
    paddingVertical: 6,
    paddingHorizontal: 11,
    alignSelf: "center",
    overflow: "hidden"
  },
  availableValue: {
    ...FontStyles["DIN-Bold"],
    fontSize: 12,
    color: "#ffffff"
  },
  guideView: {
    marginLeft: 5,
    width: 0,
    height: 0,
    borderStyle: "solid",
    borderWidth: 6,
    borderTopColor: "#00000000", //下箭头颜色
    borderLeftColor: "#00000000", //右箭头颜色
    borderBottomColor: "rgba(0, 0, 0, 0.7)", //上箭头颜色
    borderRightColor: "#00000000", //左箭头颜色
    position: "absolute",
    right: 18.75,
    top: 30
  },
  guideSubView: {
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    width: 206,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 12,
    position: "absolute",
    right: 0,
    top: 42
  },
  guideText: {
    fontSize: 12,
    color: "#fff"
  },
  gotItTouch: {
    borderRadius: 12,
    backgroundColor: "#E62117",
    alignSelf: "center",
    paddingHorizontal: 20,
    paddingVertical: 6,
    marginTop: 12
  },
  gotIt: {
    ...FontStyles["rob-medium"],
    fontSize: 10,
    color: "#fff"
  }
});
