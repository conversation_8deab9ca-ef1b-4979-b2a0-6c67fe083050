import { action, observable } from "mobx";
import Basic from "common/store/Basic";
import UserLimitInfo from "../../model/UserLimitInfo";
import { IncreaseLimit } from "../../model/IncreaseLimitInfo";
import NativeToast, { TOAST_POSITION } from "common/nativeModules/basics/nativeUIModule/Toast";
import { ResAD } from "common/components/ADGroup/helpers/types";
import { errCode } from "../constents";
import * as API from "../constents/api";
export default class IncreaseLimitStore extends Basic {
  @observable loading = true;

  @observable isError = false;

  @observable isShowUncredited = false;

  @observable adDataMap: Map<number, ResAD> = new Map();

  @observable userLimitInfo: UserLimitInfo = {
    //总额度
    totalCredit: "0",

    //已用额度
    usedCredit: "0",

    //可用额度
    availableCredit: "0",

    //固定额度
    fixedCredit: "0",

    //临时额度
    contingentCredit: "0",

    //已提升额度
    promotedCredit: "0",

    //额度历史开关
    detailSwitch: false,

    //冻结额度
    frozenCredit: "0",

    //通用额度
    commonCredit: "0",

    //临时额度失效时间
    contingentCreditExpireTime: "0",

    //补充资料提额额度
    furtherInfoCredit: "0",

    //PayLater可用额度
    payLaterAvailableCredit: "0",

    payLaterTotalCredit: "0",

    installmentTotalCredit: "0",

    installmentAvailableCredit: "0",

    hasContingentCredit: false,

    usedContingentCredit: "0",

    availableContingentCredit: "0",

    contingentEquityDetail: null
  };

  //是否paylater用户
  @observable isPayLater = false;

  @observable isShowBnplGuide = false;

  @observable isShowForceUpdate = false;

  @observable increaseLimitInfo: IncreaseLimit = {
    overdue: false,
    groups: []
  };

  @action saveAdData = (cn: number, data: ResAD) => {
    this.adDataMap.set(cn, data);
  };

  // 旧额度中心获取额度信息
  @action
  getUserQuota = async () => {
    const responseUserLimitInfo = await this.io.post(API.GET_USER_QUOTA, {});
    if (responseUserLimitInfo.success) {
      this.loading = false;
      this.userLimitInfo = responseUserLimitInfo.data ?? this.userLimitInfo;
      this.isPayLater = responseUserLimitInfo.data?.payLater;
    } else if (responseUserLimitInfo.errCode === errCode.UNCREDITED) {
      // 未授信的处理
      this.isShowUncredited = true;
    } else {
      this.isError = true;
      responseUserLimitInfo &&
        NativeToast.showMessage(responseUserLimitInfo.errMsg, { position: TOAST_POSITION.CENTER });
    }
  };

  @action
  getByGroupGetList = async () => {
    const responseIncreaseLimitInfo = await this.io.post(API.GET_BY_GROUP_LIST, {});
    if (responseIncreaseLimitInfo.success) {
      this.increaseLimitInfo = responseIncreaseLimitInfo.data ?? this.increaseLimitInfo;
    } else {
      responseIncreaseLimitInfo &&
        NativeToast.showMessage(responseIncreaseLimitInfo.errMsg, { position: TOAST_POSITION.CENTER });
    }
  };

  @action
  fetchData = async () => {
    this.loading = true;
    this.isError = false;
    try {
      this.getUserQuota();
      this.getByGroupGetList();
    } catch (e) {
      console.log(e);
      this.isError = true;
    } finally {
      this.loading = false;
    }
  };
}
