import { ResAD } from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/helpers/types";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { get } from "lodash";

const PAGENAME = "credit limit detail page";
const PAGEID = "912";
export const ADSID = "333";

const CreditLineCenter = {
  onClickTipsAdsCard: (adData: ResAD): void => {
    // 顶部广告的点击上报
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: PAGEID,
      page_name: PAGENAME,
      element_id: "9120116",
      element_name: "ad",
      module_name: "credit limit",
      module_id: "01",
      position_id: "16",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", "") //标签id
      }
    });
  },
  onExposeTipsAdsCard: (adData: ResAD): void => {
    NativeSensorModule.reportSence(SensorType.EXPOSE, {
      page_id: PAGEID,
      page_name: PAGENAME,
      element_id: "9120116",
      element_name: "ad",
      module_name: "credit limit",
      module_id: "01",
      position_id: "16",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", "") //标签id
      }
    });
  }
};

export default CreditLineCenter;
