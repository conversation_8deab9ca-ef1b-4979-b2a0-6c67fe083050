/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 11:12
 * @description
 */

import React from "react";
import { View, TouchableOpacity, Image, Text } from "react-native";
import styles from "./styles";

export default function HorizontalItem(props: any) {
  return (
    <TouchableOpacity style={styles.container} onPress={props.onpress}>
      <View
        style={{
          justifyContent: "center",
          flexDirection: "column"
        }}
      >
        <Text style={styles.title}>{props.text1}</Text>
        <Text style={styles.amount}>{props.text2}</Text>
      </View>
    </TouchableOpacity>
  );
}
