import React, { PureComponent } from "react";
import { Text, View, Image, Animated, TouchableOpacity, GestureResponderEvent, StatusBar } from "react-native";
import debounce from "lodash/debounce";
import image from "./img";
import Navigation from "common/nativeModules/router/nativeNavigationModule";
import { iOS, FontUtil } from "@akulaku-rn/akui-rn";
import styles from "./styles";

type Props = {
  animatedOffsetY: Animated.Value; // 动画
  linearValue: number; // 切换样式的高度
  title?: string; // 标题
  onPressShare?: (event: GestureResponderEvent) => void; // 分享点击，也会根绝判断是否有此方法来显示分享按钮
  showBackIconBg?: boolean; // 是否显示
  onBackPress?: Function;
  right: string; //右边的文案
  onRightPress: () => void;
};

type State = {
  showWhiteStyle: boolean;
};

export default class NavTopBar extends PureComponent<Props, State> {
  animatedOpacity: any;

  static defaultProps = {
    linearValue: 135,
    showBackIconBg: false
  };

  constructor(props: Props) {
    super(props);
    this.state = {
      showWhiteStyle: true // 用于判断是否显示白色样式，默认为白色
    };
    const { animatedOffsetY, linearValue } = props;
    // 获取动画曲线
    this.animatedOpacity = animatedOffsetY.interpolate({
      inputRange: [0, linearValue],
      outputRange: [0, 1]
    });

    // 监听滚动高度来切换showWhiteStyle状态
    animatedOffsetY.addListener((value: any) => {
      if (value.value >= props.linearValue) {
        this.setState({ showWhiteStyle: false });
      } else {
        this.setState({ showWhiteStyle: true });
      }
    });
  }

  handleBack = debounce(
    () => {
      const { onBackPress } = this.props;
      if (onBackPress) {
        onBackPress();
      }
    },
    500,
    { leading: true, trailing: false }
  );

  render() {
    const { title, onPressShare, showBackIconBg, onBackPress } = this.props;
    const { showWhiteStyle } = this.state;

    return (
      <Animated.View style={styles.animateContainer}>
        <Animated.View
          style={[
            styles.animateOpacity,
            {
              opacity: this.animatedOpacity
            }
          ]}
        />
        <StatusBar backgroundColor="transparent" barStyle={"dark-content"} />
        <View style={styles.container}>
          <View style={[styles.main, { flex: iOS ? 0 : 1 }]}>
            <TouchableOpacity
              style={[styles.backBox, showWhiteStyle && showBackIconBg && styles.backBoxBg]}
              onPress={this.handleBack}
            >
              <Image style={styles.backIcon} source={image.nav_back_black} />
            </TouchableOpacity>
            {!iOS && <Text style={[styles.title, { color: "rgb(51, 51, 51)" }]}>{title}</Text>}
          </View>
          <View>{iOS && <Text style={[styles.title, { color: "rgb(51, 51, 51)" }]}>{title}</Text>}</View>
          <TouchableOpacity onPress={this.props.onRightPress}>
            <Text
              style={{
                fontFamily: FontUtil.getFontFamily("roboto-regular"),
                fontSize: 14,
                color: "#666666",
                textAlign: "right"
              }}
            >
              {this.props.right}
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  }
}
