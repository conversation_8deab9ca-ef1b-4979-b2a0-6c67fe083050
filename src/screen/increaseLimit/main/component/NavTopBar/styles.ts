import { StyleSheet } from "react-native";
import { NAV_BAR_HEIGHT, STATUS_BAR_HEIGHT, WINDOW_WIDTH, FontStyles } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  animateContainer: {
    position: "absolute",
    paddingHorizontal: 15,
    paddingTop: STATUS_BAR_HEIGHT,
    height: NAV_BAR_HEIGHT,
    width: "100%",
    flex: 1,
    justifyContent: "space-between",
    backgroundColor: "#ffffff"
  },
  animateOpacity: {
    position: "absolute",
    backgroundColor: "#ffffff",
    width: WINDOW_WIDTH,
    height: NAV_BAR_HEIGHT,
    top: 0,
    left: 0
  },
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    height: NAV_BAR_HEIGHT - STATUS_BAR_HEIGHT,
    alignItems: "center"
  },
  main: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center"
  },
  backBox: {
    width: 32,
    height: 32,
    justifyContent: "center",
    marginRight: 13
  },
  backBoxBg: {
    borderRadius: 16,
    backgroundColor: "rgba(0,0,0,0.5)"
  },
  backIcon: {
    resizeMode: "stretch",
    width: 24,
    height: 24
  },
  title: {
    fontSize: 16,
    ...FontStyles["rob-medium"]
  },
  shareBox: {
    width: 30,
    height: 30,
    alignItems: "center",
    justifyContent: "center"
  },
  shareImg: {
    resizeMode: "stretch",
    width: 24,
    height: 24
  }
});
