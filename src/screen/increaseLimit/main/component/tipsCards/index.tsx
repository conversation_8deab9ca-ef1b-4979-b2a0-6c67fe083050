import { reportExpose } from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/helpers";
import { ResAD } from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/helpers/types";
import React, { useEffect } from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import CreditLineCenter from "../../eventTracking";
import styles from "./styles";

interface Props {
  tipsText: string;
  onClose: () => void;
  onTipsPress: () => void;
  adsId: string;
  tipsAdsData: ResAD;
}

const TipsCards = (props: Props) => {
  const { onClose, onTipsPress, tipsText, adsId, tipsAdsData } = props;
  useEffect(() => {
    reportExpose(adsId);
    CreditLineCenter.onExposeTipsAdsCard(tipsAdsData);
  });
  return (
    <View style={styles.tipsView}>
      <Image style={{ width: 24, height: 24 }} source={require("./images/icon-notice.webp")} />
      <TouchableOpacity onPress={onTipsPress}>
        <Text style={styles.tipsText} numberOfLines={3}>
          {tipsText}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={onClose}>
        <Image style={{ width: 16, height: 16 }} source={require("./images/close.webp")} />
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(TipsCards);
