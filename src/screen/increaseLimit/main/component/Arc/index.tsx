/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-07 19:43
 * @description
 */

import React, { Component } from "react";
import { View, Animated, Easing, Image, Text } from "react-native";
import Svg, { Circle, Defs, LinearGradient, Path, Stop } from "react-native-svg";
import styles from "./styles";

/**
 * strokeWidth 线宽
 * radius 半径
 * pointRadius 指示点半径
 */
type Props = {
  strokeWidth: number;
  radius: number;
  pointRadius: number;
  progress: number;
  text1: string;
  text2: string;
};
type State = {
  progress: number;
};

export default class Index extends Component<Props, State> {
  private radian: Animated.Value;

  constructor(props: any) {
    super(props);
    this.state = {
      progress: 0
    };
    this.radian = new Animated.Value(0);
  }

  componentDidMount() {
    Animated.timing(this.radian, {
      toValue: this.props.progress > 1 ? 1 : this.props.progress,
      easing: Easing.ease, //惯性动画，加上这一句，可以解决弧形起点闪动的问题
      duration: 1000,
      useNativeDriver: true
    }).start();
    this.radian.addListener(this.radianCallback);
  }

  radianCallback = (callback: { value: number }) => {
    if (callback.value <= this.state.progress || callback.value > 1) {
      return;
    }
    this.setState({
      progress: callback.value
    });
  };

  render() {
    const { strokeWidth, radius, pointRadius, text1, text2 } = this.props;
    /**
     * d：属性设置路径
     * M：起始位置
     * A：x半径，y半径，弧形绕中心点旋转角度（如果是圆不管怎么旋转都一样），是否是大圆弧，是否顺时针方向，终点x坐标，终点y坐标
     * */
    const d = `M${strokeWidth / 2},${radius + strokeWidth / 2} A${radius},${radius},0,0,1,${radius -
      radius * Math.cos(Math.PI * this.state.progress) +
      strokeWidth / 2},
    ${radius - radius * Math.sin(Math.PI * this.state.progress) + strokeWidth / 2}`;
    const svgWidth = this.props.radius * 2 + this.props.strokeWidth;
    const svgHeight = this.props.radius + this.props.strokeWidth;
    return (
      <View style={{ flexDirection: "column", alignItems: "center" }}>
        <Image source={require("./img/arc.webp")} style={{ width: svgWidth + 40, height: svgHeight + 12 }} />
        <Svg width={svgWidth} height={svgHeight} style={{ position: "absolute", top: 20 }}>
          <Defs>
            <LinearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
              <Stop offset={"0%"} stopColor={"#FD5756"} stopOpacity={"1"} />
              <Stop offset={"100%"} stopColor={"#EE3534"} stopOpacity={"1"} />
            </LinearGradient>
          </Defs>
          <Path
            strokeLinecap="round"
            strokeWidth={strokeWidth}
            stroke={"#FF8A84"}
            fill="none"
            d={`M${strokeWidth / 2},${radius + strokeWidth / 2} A${radius},${radius},0,0,1,${2 * radius +
              strokeWidth / 2},${radius + strokeWidth / 2}`}
          />
          <Path strokeLinecap="round" strokeWidth={strokeWidth} stroke="url(#grad1)" fill="none" d={d} />
          <Circle
            // @ts-ignore
            cx={`${radius - radius * Math.cos(Math.PI * this.state.progress) + strokeWidth / 2}`}
            // @ts-ignore
            cy={`${radius - radius * Math.sin(Math.PI * this.state.progress) + strokeWidth / 2}`}
            r={pointRadius}
            fill={"#ffffff"}
          />
        </Svg>
        <Text style={styles.title}>{text1}</Text>
        <Text style={styles.amount}>{text2}</Text>
      </View>
    );
  }
}
