/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-07 20:41
 * @description 提额首页
 */

import React, { Component } from "react";
import {
  <PERSON>,
  BackHand<PERSON>,
  DeviceEventEmitter,
  EmitterSubscription,
  Image,
  ImageBackground,
  NativeEventSubscription,
  Linking,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Text,
  TouchableOpacity,
  View
} from "react-native";
import { TFunction } from "i18next";
import RootView from "common/components/Layout/RootView";
import store from "./store";
import IncreaseLimitStore from "./store";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import { ToRepayItem } from "../component/IncreaseLimitItem";
import { Category } from "../model/IncreaseLimitInfo";
import { DialogContainer } from "common/components/DialogContainer";
import NavTopBar from "./component/NavTopBar";
import { NativeConfigModule, NativeNavigationModule } from "common/nativeModules";
import IncreaseLimitGroup from "../component/IncreaseLimitGroup";
import HorizontalItem from "./component/HorizontalItem";

import styles from "./styles";
import DialogMyLimit from "../component/Dialog/DialogMyLimit";
import { ADTitle, ADImage } from "common/components/ADGroup/BottomAD";
import { AkuNativeEventEmitter } from "common/nativeModules";
import { getCurrentScreen } from "common/constant";
import { ScreenEventName } from "common/nativeModules/emitter/nativeEventEmitter";
import _, { debounce, get, isNil } from "lodash";
import NativeSensorModule, {
  ScreenSensorEvent,
  SensorEvent,
  SensorType
} from "common/nativeModules/sdk/nativeSensroModule";
import {
  PriceComponent,
  WINDOW_WIDTH,
  FontStyles,
  NetworkErrorComponent,
  NAV_BAR_HEIGHT,
  Android
} from "@akulaku-rn/akui-rn";
import ActivityAdPopup from "common/components/ADGroup/ActivityAdPopup";
import HorizontalListAd from "common/components/ADGroup/HorizontalListAd";
import { ResAD } from "common/components/ADGroup/helpers/types";
import { NativeNavigationModuleModel } from "common/nativeModules/router/nativeNavigationModule";
import { runtimeType } from "common/components/BaseContainer/Type";
import RuntimeStore from "common/store/Runtime";
import {
  ExposeEvent,
  HOCExposerScrollView,
  PageConfig,
  reportClick as reportClickV4,
  reporter,
  ScrollViewExposer
} from "@akulaku-rn/rn-v4-sdk";
import { reportClick } from "common/components/ADGroup/helpers";
import DialogBnplForceUpdate from "../component/Dialog/DialogBnplForceUpdate";
import { when } from "mobx";
import AsyncStorage from "@react-native-community/async-storage";
import CountDown from "../component/CountDown/CountDown";
import { TimeFormat } from "common/util";
import TipsCards from "./component/tipsCards";
import { getAdList } from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/helpers";
import CreditLineCenter from "./eventTracking";
import DefaultComponent from "./component/DefaultComponent/index";
import PayLaterPremiumCard from "../mainV2/component/PayLaterPremium";

interface NavParams {
  gobackNum: number;
  screen: string;
}

const baseParams: SensorEvent = {
  page_name: "credit limit detail page",
  page_id: "912"
};

type Props = {
  navigation: NativeNavigationModuleModel;
  store: { pageStore: IncreaseLimitStore; runtime: RuntimeStore & runtimeType; navParams: NavParams };
  t: TFunction;
  configSensorEvent: (event: ScreenSensorEvent) => void;
  // configPageInfo: (config: PageConfig, isSupportV3: boolean) => void;
  configPageInfo: (config: PageConfig, isSupportV3: boolean, isInit?: boolean) => void;
};

type State = {
  tipsAdsData: ResAD[] | [];
  isShowTips: boolean;
};

@RootView({
  withI18n: [
    "increase-limit",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("increase-limit")
@inject("store")
@observer
export default class IncreaseLimitHome extends Component<Props, State> {
  listOffsetY: Animated.Value;

  listener: EmitterSubscription;

  exposer: ScrollViewExposer;

  screenEventListener?: EmitterSubscription;

  BackHandlerListener?: NativeEventSubscription;

  constructor(props: Props) {
    super(props);
    this.listener = DeviceEventEmitter.addListener("increaseLimitUpdate", () => {
      this.fetchData();
    });
    this.screenEventListener = AkuNativeEventEmitter.addListener(getCurrentScreen(), this.handleScreenEvent);
    this.props.store.runtime.setPaddingBottom(false);
    this.props.configSensorEvent &&
      this.props.configSensorEvent({
        page_id: "912",
        page_name: "credit limit detail page"
      });
    this.state = {
      tipsAdsData: [],
      isShowTips: true
    };
    this.listOffsetY = new Animated.Value(0);
    this.listOffsetY.addListener(v => {
      this.offY = v.value;
    });
    //v4埋点
    props.configPageInfo({ sn: 100630 }, true);

    this.exposer = new ScrollViewExposer({
      reporter: {
        expose: (event: ExposeEvent) => {
          reporter.expose(event);
          //V4曝光同时上报神策
          event.li.map(e => {
            const cn = get(e, "cn", 0);
            if (cn >= 14 && cn <= 17) {
              const adData = this.props.store.pageStore.adDataMap.get(cn);
              adData && NativeSensorModule.sensorLogger(SensorType.EXPOSE, this.getADSensorData(cn - 14, adData));
            }
          });
        }
      }
    });
    when(
      // 一旦...
      () => this.props?.store?.pageStore?.isShowForceUpdate,
      // ... 然后
      () => {
        NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
          page_name: "credit limit detail page",
          page_id: "912",
          extra: {
            pop_id: "pop30150",
            pop_name: "limit upgrade"
          }
        });
        const v4data = reporter.generateExposeEvent([
          {
            cn: 19,
            eit: 2,
            sp: { is_BNPL: this.props.store.pageStore.isPayLater ? 1 : 2 }
          }
        ]);
        reporter.expose(v4data);
        DialogContainer.show({
          renderContent: <DialogBnplForceUpdate t={this.props.t} isPayLater={this.props.store.pageStore.isPayLater} />
        });
      }
    );
  }

  componentDidMount() {
    this.fetchData();
    ActivityAdPopup.show({
      adData: { adGroupId: 199 },
      imgWidth: 300,
      imgHeight: 345,
      adExpose: this.activityAdExpose,
      adClick: this.activityAdClick
    });
  }

  componentWillUnmount() {
    this.listener && this.listener.remove();
    this.screenEventListener && this.screenEventListener.remove();
  }

  handleScreenEvent = debounce(
    (event: { eventName: ScreenEventName }) => {
      if (event.eventName === ScreenEventName.ON_ENTER) {
        if (this.exposer) {
          this.exposer.caculateExposeItems();
          this.exposer.startExpose();
        }
        this.BackHandlerListener = BackHandler.addEventListener("hardwareBackPress", () => {
          this.onBackPress();
          return true;
        });
      } else if (event.eventName === ScreenEventName.ON_LEAVE) {
        this.exposer.endExpose();
        this.BackHandlerListener && this.BackHandlerListener.remove();
      }
    },
    100,
    {
      leading: true,
      trailing: false
    }
  );

  activeDot = (<View style={[styles.dot, styles.activeDot]} />);

  dot = (<View style={styles.dot} />);

  offY = 0;

  arcProcess = 0;

  activityAdExpose = (adData?: ResAD) => {
    NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
      page_name: "credit limit detail page",
      page_id: "912",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: get(adData, "creatives[0].id", ""), //图片id
        pop_id: "pop30097",
        pop_name: "credit limit-popup"
      }
    });
    const v4data = reporter.generateExposeEvent([
      {
        cn: 10,
        eit: 2,
        ei: {
          spot_id: get(adData, "spotId", ""),
          ad_id: get(adData, "id", ""),
          image_id: get(adData, "creatives[0].id", "")
        }
      }
    ]);
    reporter.expose(v4data);
  };

  activityAdClick = (adData?: ResAD) => {
    const url: string = _.get(adData, "creatives[0].destUrl");
    if (url.includes("play.google.com")) {
      Linking.canOpenURL("http://play.google.com/store/apps/details?id=io.silvrr.installment")
        .then(supported => {
          if (supported) {
            Linking.openURL("http://play.google.com/store/apps/details?id=io.silvrr.installment");
          } else {
            NativeNavigationModule.navigate({
              url: "https://play.google.com/store/apps/details?id=io.silvrr.installment"
            });
          }
        })
        .catch(err => console.error("", err));
    } else {
      NativeNavigationModule.navigate({ url: url });
    }
    NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
      page_name: "credit limit detail page",
      page_id: "912",
      element_id: "pop3009701",
      element_name: "ad",
      position_id: "01",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: get(adData, "creatives[0].id", ""), //图片id
        pop_id: "pop30097",
        pop_name: "credit limit-popup"
      }
    });
    reportClickV4({
      sn: 100630,
      cn: 10,
      bct: 8,
      bi: {
        spot_id: get(adData, "spotId", ""),
        ad_id: get(adData, "id", ""),
        image_id: get(adData, "creatives[0].id", "")
      }
    });
  };

  fetchData = async () => {
    const {
      store: { pageStore }
    } = this.props;
    const data = await getAdList(333); // 获取顶部广告
    this.setState({ tipsAdsData: data });
    pageStore.fetchData().then(() => {
      this.props.configPageInfo(
        {
          sn: 100630,
          sp: { is_BNPL: this.props.store.pageStore.isPayLater ? 1 : 2 }
        },
        true
      );
    });
  };

  onUsedPress = () => {
    reportClickV4({
      cn: 2,
      sn: 100630,
      sp: { is_BNPL: this.props.store.pageStore.isPayLater ? 1 : 2 }
    });
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "9120104",
      element_name: "used",
      module_id: "01",
      module_name: "credit limit",
      position_id: "04",
      ...baseParams
    });
    this.jump2IncreaseLimitDetail();
  };

  onRightPress = () => {
    reportClickV4({
      cn: 9,
      sn: 100630
    });
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "9120101",
      element_name: "amount details",
      module_id: "01",
      module_name: "credit limit",
      position_id: "01",
      ...baseParams
    });
    this.jump2IncreaseLimitDetail();
  };

  // 关闭额度中心顶部广告提示
  onCloseTips = () => {
    this.setState({ isShowTips: false });
  };

  // 跳转广告详情
  onToTipsInfoPage = () => {
    reportClick(this.state.tipsAdsData[0].id);
    CreditLineCenter.onClickTipsAdsCard(this.state.tipsAdsData[0]);
    NativeNavigationModule.navigate({ url: this.state.tipsAdsData[0].creatives[0].destUrl });
  };

  onBackPress = () => {
    const {
      store: {
        navParams: { gobackNum }
      }
    } = this.props;
    if (!isNil(gobackNum)) {
      NativeNavigationModule.popTo(parseInt(gobackNum + "") + 1);
    } else {
      NativeNavigationModule.goBack();
    }

    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "9120102",
      element_name: "return",
      module_id: "01",
      module_name: "credit limit",
      position_id: "02",
      ...baseParams
    });
  };

  jump2IncreaseLimitDetail = () => {
    this.props.navigation.navigate({
      screen: "IncreaseLimitDetail",
      params: {}
    });
  };

  onTotalPress = debounce(
    () => {
      const {
        t,
        store: {
          runtime: { countryId },
          pageStore: { userLimitInfo }
        }
      } = this.props;
      if (userLimitInfo && !userLimitInfo.detailSwitch) {
        return;
      }
      reportClickV4({
        cn: 1,
        sn: 100630
      });
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9120103",
        element_name: "total credit limit",
        module_id: "01",
        module_name: "credit limit",
        position_id: "03",
        ...baseParams
      });
      const onTotalAmountClick = async () => {
        await DialogContainer.dismiss();
        this.props.navigation.navigate({
          screen: "IncreaseLimitDetail",
          params: {}
        });
      };
      DialogContainer.show({
        renderContent: (
          <DialogMyLimit
            totalCredit={PriceComponent.priceFormat(userLimitInfo.totalCredit, countryId, { needUnit: true })}
            fixedCredit={PriceComponent.priceFormat(userLimitInfo.fixedCredit, countryId, { needUnit: true })}
            tempCredit={PriceComponent.priceFormat(userLimitInfo.contingentCredit, countryId, { needUnit: true })}
            promotedCredit={PriceComponent.priceFormat(userLimitInfo.promotedCredit, countryId, { needUnit: true })}
            t={t}
            onTotalAmountClick={onTotalAmountClick}
          />
        )
      });
    },
    500,
    {
      leading: true,
      trailing: false
    }
  );

  callbackfn = (categoryItem: Category, index: number) => {
    return (
      <>
        {index ? <View style={styles.IncreaseLimitGroupDivider} /> : null}
        <IncreaseLimitGroup
          key={`${index}`}
          category={categoryItem}
          countryCode={this.props.store.runtime.countryCode}
          {...this.props}
        />
      </>
    );
  };

  onPress = () => {
    reportClickV4({
      cn: 8,
      sn: 100630
    });
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "9120105",
      element_name: "FAQ",
      module_id: "01",
      module_name: "credit limit",
      position_id: "05",
      ...baseParams
    });
    //跳转提额说明页
    if (NativeConfigModule.ENV === "production") {
      NativeNavigationModule.navigate({ url: `https://ec-mall.akulaku.com/ec-sale/self-publish/normal?actId=2417` });
    } else {
      NativeNavigationModule.navigate({
        url: `https://test-ec-mall.akulaku.com/ec-sale/self-publish/normal?actId=1277`
      });
    }
  };

  //底部广告位神策埋点
  getADSensorData = (index: number, adData: ResAD, isImage = true) => {
    return {
      element_id: `${9120112 + index}`,
      element_name: `banner${1 + index}`,
      page_id: "912",
      page_name: "credit limit detail page",
      module_id: "01",
      module_name: "credit limit",
      position_id: `${12 + index}`,
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: isImage ? get(adData, "creatives[0].id", "") : "" //图片id
      }
    };
  };

  //底部广告位V4点击埋点 (曝光字段在广告组件内部处理,因为数据在组件内获取)
  getADV4ClickData = (index: number, adData: ResAD) => {
    return {
      sn: 100630,
      cn: +`${14 + index}`,
      bct: 8,
      bi: {
        spot_id: get(adData, "spotId", ""),
        ad_id: get(adData, "id", ""),
        image_id: get(adData, "creatives[0].id", "")
      }
    };
  };

  renderBottomAD = () => {
    const { pageStore } = this.props.store;
    const columnWidth = (WINDOW_WIDTH - 16 - 18 - 6) / 2; /* 一拖二广告位 左间距16 右间距18 列间隔6 */
    return (
      <>
        <View style={styles.bottomADContainer}>
          <ADTitle
            adGroupId={221}
            style={styles.bottomADTitleText}
            cn={14}
            onClick={adData => {
              NativeSensorModule.sensorLogger(SensorType.CLICK, this.getADSensorData(0, adData, false));
              this.exposer.click(this.getADV4ClickData(0, adData));
            }}
            saveAdData={pageStore.saveAdData}
            uniqueItemKey={"increase limit scroll view"}
            identifier={"bottom ad banner 1"}
          />
          <View style={styles.bottomADLeftWrapper}>
            <ADImage
              adGroupId={222}
              imgWidth={columnWidth}
              imgHeigh={(columnWidth / 160) * 206}
              style={styles.bottomADLeft}
              cn={15}
              onClick={adData => {
                NativeSensorModule.sensorLogger(SensorType.CLICK, this.getADSensorData(1, adData));
                this.exposer.click(this.getADV4ClickData(1, adData));
              }}
              saveAdData={pageStore.saveAdData}
              uniqueItemKey={"increase limit scroll view"}
              identifier={"bottom ad banner 2"}
            />
            <View style={styles.bottomADRightWrapper}>
              <ADImage
                adGroupId={223}
                imgWidth={columnWidth}
                imgHeigh={(columnWidth / 160) * 100}
                style={styles.bottomADRight}
                cn={16}
                onClick={adData => {
                  NativeSensorModule.sensorLogger(SensorType.CLICK, this.getADSensorData(2, adData));
                  this.exposer.click(this.getADV4ClickData(2, adData));
                }}
                saveAdData={pageStore.saveAdData}
                uniqueItemKey={"increase limit scroll view"}
                identifier={"bottom ad banner 3"}
              />
              <ADImage
                adGroupId={224}
                imgWidth={columnWidth}
                imgHeigh={(columnWidth / 160) * 100}
                style={styles.bottomADRight}
                cn={17}
                onClick={adData => {
                  NativeSensorModule.sensorLogger(SensorType.CLICK, this.getADSensorData(3, adData));
                  this.exposer.click(this.getADV4ClickData(3, adData));
                }}
                saveAdData={pageStore.saveAdData}
                uniqueItemKey={"increase limit scroll view"}
                identifier={"bottom ad banner 4"}
              />
            </View>
          </View>
        </View>
      </>
    );
  };

  onPressQuestion = () => {
    NativeNavigationModule.navigate({
      screen: "helpCenterQuestions",
      params: {
        faq: "Limit Kredit",
        categoryId: "26"
      }
    });
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "9120109",
      element_name: "help",
      module_id: "01",
      module_name: "credit limit",
      position_id: "09",
      page_id: "912",
      page_name: "credit limit detail page"
    });
    reportClickV4({
      cn: 11,
      sn: 100630
    });
  };

  onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    this.listOffsetY.setValue(event.nativeEvent.contentOffset.y);
  };

  popAdUpOnExpose = (adData: ResAD) => {
    NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
      page_name: "credit limit detail page",
      page_id: "912",
      element_id: "9120110",
      element_name: "word",
      module_id: "01",
      module_name: "credit limit",
      position_id: "10",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", "") //标签id
      }
    });
    const v4data = reporter.generateExposeEvent([
      {
        cn: 12,
        eit: 2,
        ei: {
          spot_id: get(adData, "spotId", ""),
          ad_id: get(adData, "id", ""),
          image_id: get(adData, "creatives[0].id", "")
        }
      }
    ]);
    reporter.expose(v4data);
  };

  popAdOnClick = (adData: ResAD) => {
    NativeNavigationModule.navigate({ url: get(adData, "creatives[0].destUrl", "") });
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      page_name: "credit limit detail page",
      page_id: "912",
      element_id: "9120110",
      element_name: "word",
      module_id: "01",
      module_name: "credit limit",
      position_id: "10",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", "") //标签id
      }
    });
    reportClickV4({
      sn: 100630,
      cn: 12,
      bct: 8,
      bi: {
        spot_id: get(adData, "spotId", ""),
        ad_id: get(adData, "id", ""),
        image_id: get(adData, "creatives[0].id", "")
      }
    });
  };

  horizontalListAdOnExpose = (adData: ResAD, index: number) => {
    NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
      page_name: "credit limit detail page",
      page_id: "912",
      element_id: "9120111",
      element_name: "banner",
      module_id: "01",
      module_name: "credit limit",
      position_id: "11",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: get(adData, "creatives[0].id", ""), //图片id
        positionid: index + 1
      }
    });
  };

  horizontalListOnClick = (adData: ResAD, index: number) => {
    NativeNavigationModule.navigate({ url: adData.creatives[0].destUrl });
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      page_name: "credit limit detail page",
      page_id: "912",
      element_id: "9120111",
      element_name: "banner",
      module_id: "01",
      module_name: "credit limit",
      position_id: "11",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: get(adData, "creatives[0].id", ""), //图片id
        positionid: index + 1
      }
    });
    reportClickV4({
      sn: 100630,
      cn: 13,
      bct: 8,
      bi: {
        spot_id: get(adData, "spotId", ""),
        ad_id: get(adData, "id", ""),
        image_id: get(adData, "creatives[0].id", "")
      },
      ext: {
        positionid: index + 1
      }
    });
  };

  render() {
    const {
      t,
      store: {
        pageStore: { loading, isError, isShowUncredited, userLimitInfo, increaseLimitInfo }
      },
      navigation
    } = this.props;
    if (+userLimitInfo.totalCredit !== 0) {
      this.arcProcess = parseFloat((+userLimitInfo.availableCredit / +userLimitInfo.totalCredit).toFixed(3));
    }
    let content = null;
    let bottomContent = null;
    console.log("zhangxiao", "increaseLimitInfo?.groups-->" + JSON.stringify(increaseLimitInfo?.groups));
    if (loading) {
      content = <Loading />;
    } else if (isError) {
      content = (
        <View style={{ marginTop: NAV_BAR_HEIGHT, flex: 1 }}>
          <NetworkErrorComponent
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            buttonText={t("global:刷新")}
            onRetryPress={this.fetchData}
          />
        </View>
      );
    } else if (isShowUncredited) {
      content = (
        <View style={{ marginTop: NAV_BAR_HEIGHT, flex: 1 }}>
          <DefaultComponent
            containerStyle={{ marginHorizontal: 24 }}
            image={
              "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ak_ec_user/default_image/icon_uncredited.webp"
            }
            message={t("您还未授信或授信未通过哦，请您先申请授信~")}
            buttonText={t("申请授信")}
            onRetryPress={() => {
              if (Android) {
                navigation.popToTop();
                NativeNavigationModule.navigate({
                  screen: "AuthorizeCreditApplyInfo"
                });
              } else {
                NativeNavigationModule.navigate({
                  screen: "AuthorizeCreditApplyInfo",
                  params: { gestureEnabled: false },
                  popNumber: 0
                });
              }
            }}
          />
        </View>
      );
    } else {
      if (increaseLimitInfo && increaseLimitInfo.overdue) {
        //账单逾期
        bottomContent = (
          <>
            <View style={styles.bottomView1} />
            <ToRepayItem t={t} />
            <View style={styles.bottomView2} />
            {this.renderBottomAD()}
          </>
        );
      } else {
        bottomContent = (
          <>
            {increaseLimitInfo && increaseLimitInfo.groups && increaseLimitInfo.groups.length > 0 ? (
              <>
                <View style={{ height: 7, width: "100%", backgroundColor: "#FFFFFF" }} />
                <View style={styles.functionContainer}>
                  <View style={styles.functionLine} />
                  <Text style={styles.functionText}>{t("提额专区")}</Text>
                  <View style={styles.functionLine} />
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    marginTop: 7
                  }}
                />
                {/**电商授权各授权项入口 */}
                {increaseLimitInfo.groups.map(this.callbackfn)}
              </>
            ) : null}
            {this.renderBottomAD()}
            <View style={styles.functionContainer}>
              <View style={styles.functionLine} />
              <Text style={[styles.functionText, { fontSize: 10, color: "#989FA9" }]}>{t("没有更多了")}</Text>
              <View style={styles.functionLine} />
            </View>
          </>
        );
      }
      content = (
        <View style={{ marginTop: NAV_BAR_HEIGHT }}>
          <HOCExposerScrollView
            uniqKey={"increase limit scroll view"}
            exposer={this.exposer}
            contentContainerStyle={styles.scrollContainer}
            scrollEventThrottle={this.offY > 135 ? 0 : 16}
            onScroll={this.onScroll}
            bounces={false}
            overScrollMode={"never"}
          >
            {this.state.tipsAdsData.length > 0 &&
            this.state.tipsAdsData[0].creatives.length > 0 &&
            this.state.isShowTips ? (
              <TipsCards
                tipsText={this.state.tipsAdsData[0]?.creatives[0]?.slogan ?? ""}
                tipsAdsData={this.state.tipsAdsData[0]}
                onClose={this.onCloseTips}
                onTipsPress={this.onToTipsInfoPage}
                adsId={this.state.tipsAdsData[0].id}
              />
            ) : null}
            {this.LimitHeader()}
            <HorizontalListAd
              adGroup={201}
              imageWidth={WINDOW_WIDTH - 110}
              imageHeight={(WINDOW_WIDTH - 110) * 0.56}
              dot={<View style={styles.dot} />}
              activeDot={<View style={[styles.dot, styles.activeDot]} />}
              onExpose={this.horizontalListAdOnExpose}
              onClick={this.horizontalListOnClick}
            />
            {bottomContent}
          </HOCExposerScrollView>
        </View>
      );
    }
    return (
      <View style={styles.container}>
        <NavTopBar
          right={t("额度明细")}
          onRightPress={this.onRightPress}
          title={t("提额专区")}
          animatedOffsetY={this.listOffsetY}
          onBackPress={this.onBackPress}
        />
        {content}
      </View>
    );
  }

  deadLine = () => {
    const {
      t,
      store: {
        runtime: { countryCode },
        pageStore: { userLimitInfo }
      }
    } = this.props;
    const timeStamp = Date.parse(new Date().toString());
    if (+userLimitInfo.availableContingentCredit <= 0) {
      return null;
    }
    // eslint-disable-next-line eqeqeq
    if (timeStamp < +userLimitInfo.contingentCreditExpireTime && +userLimitInfo.availableContingentCredit == 0) {
      //未过期且已用完
      return null;
    } else {
      if (
        +userLimitInfo.contingentCreditExpireTime > timeStamp &&
        +userLimitInfo.contingentCreditExpireTime - timeStamp < 24 * 60 * 60 * 1000
      ) {
        return <CountDown deadLine={+userLimitInfo.contingentCreditExpireTime - timeStamp} />;
      } else {
        if (+userLimitInfo.contingentCreditExpireTime - timeStamp > 0) {
          return (
            <Text
              style={{
                fontSize: 12,
                color: "#2092E5",
                alignSelf: "center"
              }}
            >
              {t("失效日期：") +
                TimeFormat(new Date(+userLimitInfo.contingentCreditExpireTime), "YYYY-MM-DD", countryCode)}
            </Text>
          );
        } else {
          return (
            <Text
              style={{
                fontSize: 12,
                color: "#6E737D",
                alignSelf: "center"
              }}
            >
              {t("失效日期：") +
                TimeFormat(new Date(+userLimitInfo.contingentCreditExpireTime), "YYYY-MM-DD", countryCode)}
            </Text>
          );
        }
      }
    }
  };

  available = () => {
    const {
      t,
      store: {
        runtime: { countryId },
        pageStore: { userLimitInfo }
      }
    } = this.props;
    const timeStamp = Date.parse(new Date().toString());
    if (timeStamp < +userLimitInfo.contingentCreditExpireTime && +userLimitInfo.availableContingentCredit > 0) {
      //未过期且可用额度>0
      return (
        <View
          style={{
            flexDirection: "column"
          }}
        >
          <Text
            style={{
              color: "#6E737D",
              fontSize: 14
            }}
          >
            {t("可用")}
          </Text>
          <Text
            style={{
              color: "#333333",
              fontSize: 14,
              marginTop: 4,
              ...FontStyles["DIN-Bold"]
            }}
          >
            {PriceComponent.priceFormat(userLimitInfo.availableContingentCredit, countryId, { needUnit: true })}
          </Text>
        </View>
      );
    } else if (timeStamp < +userLimitInfo.contingentCreditExpireTime && +userLimitInfo.availableContingentCredit <= 0) {
      //未过期且可用额度=0
      return (
        <Text
          style={{
            fontSize: 12,
            color: "#282B2E"
          }}
        >
          {t("共RpXX，已用完", {
            X: PriceComponent.priceFormat(userLimitInfo.contingentCredit, countryId, { needUnit: true })
          })}
        </Text>
      );
    } else {
      return (
        <Text
          style={{
            fontSize: 12,
            color: "#282B2E"
          }}
        >
          {t("已透支Rp{{X}}，还清后恢复额度", {
            X: PriceComponent.priceFormat(userLimitInfo.usedContingentCredit, countryId, { needUnit: true })
          })}
        </Text>
      );
    }
  };

  //额度头部
  LimitHeader = () => {
    const {
      t,
      store: {
        runtime: { countryId },
        pageStore: { userLimitInfo, isPayLater }
      }
    } = this.props;
    const timeStamp = Date.parse(new Date().toString());
    const isShowLimitInfo =
      timeStamp >= +userLimitInfo.contingentCreditExpireTime && +userLimitInfo.usedContingentCredit === 0;
    // 之前这里逻辑有问题，由于金额都是字符串，所以下面等式默认为true：  userLimitInfo?.installmentTotalCredit !== 0 && userLimitInfo?.payLaterTotalCredit !== 0
    // 因此去掉了下面的 样式的判断
    return (
      <View style={styles.bnplView}>
        <View style={styles.bnplImgBg}>
          <View style={styles.bnplGroup}>
            <Text style={styles.availableCredit}>{t("可用额度") + "(Rp)"}</Text>
            <Text style={styles.availableCreditValue}>
              {PriceComponent.priceFormat(
                +userLimitInfo.installmentTotalCredit > 0
                  ? userLimitInfo.installmentAvailableCredit
                  : userLimitInfo.payLaterAvailableCredit,
                countryId,
                { needUnit: false }
              )}
            </Text>
          </View>
          {/* // 之所以写反考虑undefined的场景 */}
          {/* {!userLimitInfo.showPayLater ? null : this.bnplAvailable()}
          {!userLimitInfo.showPayLater ? null : (
            <TouchableOpacity
              style={styles.guide}
              onPress={() => {
                if (this.props?.store?.pageStore?.isShowBnplGuide) {
                  this.props.store.pageStore.isShowBnplGuide = false;
                }
                NativeNavigationModule.navigate({
                  url: "https://ec-mall.akulaku.com/ec-sale/self-publish/normal?actId=7295"
                });
                reportClickV4({
                  sn: 100630,
                  cn: 18,
                  bct: 0,
                  sp: { is_BNPL: this.props.store.pageStore.isPayLater ? 1 : 2 }
                });
                NativeSensorModule.sensorLogger(SensorType.CLICK, {
                  page_name: "credit limit detail page",
                  page_id: "912",
                  element_id: "9120401",
                  element_name: "help",
                  position_id: "01",
                  module_id: "04",
                  module_name: "bnpl"
                });
              }}
            >
              <Image source={require("../img/icon_top_question.webp")} style={styles.bnplQuestion} />
            </TouchableOpacity>
          )} */}
        </View>
        {isPayLater ? (
          <View style={styles.totalView}>
            <Text style={styles.totalText}>{t("总额度")}</Text>
            <Text style={styles.totalValue}>
              {PriceComponent.priceFormat(userLimitInfo.totalCredit, countryId, { needUnit: true })}
            </Text>
          </View>
        ) : (
          <View style={styles.horizonViewBg}>
            <HorizontalItem
              text1={t("总额度")}
              text2={PriceComponent.priceFormat(userLimitInfo.totalCredit, countryId, { needUnit: true })}
              onpress={this.onTotalPress}
            />
            <View style={styles.horizontalItemDivider} />
            <HorizontalItem
              text1={t("已用额度")}
              text2={PriceComponent.priceFormat(userLimitInfo.usedCredit, countryId, { needUnit: true })}
              onpress={this.onUsedPress}
            />
          </View>
        )}
        {this.bnplGuide()}
        {//已过期且已用额度为0,不展示临时额度模块,否则展示
        isShowLimitInfo ? null : (
          <ImageBackground
            source={require("../img/limit_bg.webp")}
            style={{
              width: WINDOW_WIDTH - 32,
              height: ((WINDOW_WIDTH - 32) * 206) / 656,
              paddingHorizontal: 16,
              paddingTop: 25,
              paddingBottom: 16,
              marginTop: 12,
              justifyContent: "space-between"
            }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between"
              }}
            >
              <Text
                style={{
                  color: "#282B2E",
                  fontSize: 14,
                  ...FontStyles["DIN-Bold"]
                }}
              >
                {t("临时额度")}
              </Text>
              {this.deadLine()}
            </View>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between"
              }}
            >
              {this.available()}
              {timeStamp < +userLimitInfo.contingentCreditExpireTime && +userLimitInfo.availableContingentCredit > 0 ? (
                <Text
                  style={{
                    color: "#282B2E",
                    fontSize: 12,
                    alignSelf: "flex-end"
                  }}
                >
                  {t("共Rp{{X}}", {
                    X: PriceComponent.priceFormat(userLimitInfo.contingentCredit, countryId, { needUnit: true })
                  })}
                </Text>
              ) : null}
            </View>
          </ImageBackground>
        )}
        {!!userLimitInfo.contingentEquityDetail ? (
          <PayLaterPremiumCard
            t={t}
            isShowLimitInfo={isShowLimitInfo}
            status={userLimitInfo.contingentEquityDetail?.status}
            usedContingentCredit={
              userLimitInfo.contingentEquityDetail?.usedContingentCredit === null
                ? "0"
                : userLimitInfo.contingentEquityDetail?.usedContingentCredit
            }
            countryId={countryId}
            expireTime={
              userLimitInfo.contingentEquityDetail?.expireTime === null
                ? 0
                : parseInt(userLimitInfo.contingentEquityDetail?.expireTime)
            }
          />
        ) : null}
      </View>
    );
  };

  bnplAvailable = () => {
    const {
      t,
      store: {
        pageStore: { userLimitInfo },
        runtime: { countryId }
      }
    } = this.props;
    return +userLimitInfo?.installmentTotalCredit !== 0 && +userLimitInfo?.payLaterTotalCredit !== 0 ? (
      <View
        style={{
          marginTop: -4
        }}
      >
        <View style={styles.paylaterArrow} />
        <Text style={styles.available}>
          {t("可用免息额度")}
          <Text style={styles.availableValue}>
            {" " + PriceComponent.priceFormat(userLimitInfo.payLaterAvailableCredit, countryId, { needUnit: true })}
          </Text>
        </Text>
      </View>
    ) : null;
  };

  bnplGuide = () => {
    const {
      t,
      store: {
        pageStore: { isShowBnplGuide, userLimitInfo }
      }
    } = this.props;
    const showBnplGuide =
      +userLimitInfo?.installmentTotalCredit !== 0 && +userLimitInfo?.payLaterTotalCredit !== 0 && isShowBnplGuide;
    if (showBnplGuide) {
      AsyncStorage.setItem("hasIntoIncreaseLimit", "1");
    }
    return showBnplGuide ? (
      <>
        <View style={styles.guideView} />
        <View style={styles.guideSubView}>
          <Text style={styles.guideText}>{t("升级后领取专享提额包，解锁0利息和提额权益")}</Text>
          <TouchableOpacity
            style={styles.gotItTouch}
            onPress={() => {
              //隐藏引导
              NativeSensorModule.sensorLogger(SensorType.CLICK, {
                page_name: "credit limit detail page",
                page_id: "912",
                element_id: "9120402",
                element_name: "help floating",
                position_id: "02",
                module_id: "04",
                module_name: "bnpl"
              });
              this.props.store.pageStore.isShowBnplGuide = false;
            }}
          >
            <Text style={styles.gotIt}>{t("我知道了")}</Text>
          </TouchableOpacity>
        </View>
      </>
    ) : null;
  };
}
