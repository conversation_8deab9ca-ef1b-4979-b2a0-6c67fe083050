import React, { PureComponent } from "react";
import { View, Text, StyleSheet, Image, TouchableHighlight, EmitterSubscription, TouchableOpacity } from "react-native";
import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { Shadow } from "@akulaku-rn/akui-rn/src/components/NeomorphShadows";
import { AKButton, AKButtonType } from "common/components/AKButton";

type Props = {
  shadow: boolean;
  next: () => void;
  text?: string;
  isVoice?: boolean;
  isAuthorized?: boolean;
  authorizedList?: JSX.Element;
  isInputDone: boolean;
};

type States = {
  isRecordFinish: boolean;
  isAgree: boolean;
};

export const SHADOW_HEIGHT = 14;

export default class BottomButton extends PureComponent<Props, States> {
  endRecord!: EmitterSubscription;

  resetRecord!: EmitterSubscription;

  constructor(props: Props) {
    super(props);
    this.state = {
      isRecordFinish: false,
      isAgree: true
    };
  }

  setIsAgree = () => {
    this.setState({ isAgree: !this.state.isAgree });
  };

  next = () => {
    const { next, isAuthorized } = this.props;
    const { isAgree } = this.state;
    if (isAuthorized && !isAgree) {
      // Toast.show("没有同意不准往后走", {
      //   position: 0
      // });
    } else {
      next();
    }
  };

  render() {
    const { shadow, text = "Next", isInputDone } = this.props;
    return (
      <Shadow width={WINDOW_WIDTH} height={64} style={shadow ? styles.shadow : styles.containerView}>
        <View style={styles.container}>
          <AKButton
            onPress={this.next}
            disabled={!isInputDone}
            text={text}
            type={AKButtonType.B1_1_2}
            style={{
              width: "100%"
            }}
          />
        </View>
      </Shadow>
    );
  }
}

const styles = StyleSheet.create({
  containerView: {
    width: WINDOW_WIDTH,
    height: 64
  },
  shadow: {
    width: WINDOW_WIDTH,
    height: 64,
    backgroundColor: "#fff",
    shadowOpacity: 0.1,
    shadowColor: "black",
    shadowRadius: 8
  },
  container: {
    minHeight: 64,
    paddingHorizontal: 12,
    paddingTop: 8,
    paddingBottom: 16,
    alignItems: "center",
    backgroundColor: "#fff"
  },
  button: {
    backgroundColor: "#E62117",
    width: "100%",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 4
  },
  disabled: {
    backgroundColor: "rgba(230, 33, 23, 0.3)"
  },
  buttonText: {
    fontSize: 16,
    color: "#fff"
  },
  // 声纹
  voiceContainer: {
    minHeight: 64,
    paddingHorizontal: 12,
    paddingTop: 8,
    paddingBottom: 16,
    alignItems: "center",
    backgroundColor: "#fff",
    flexDirection: "row"
  },
  leftBtn: {
    flex: 1,
    marginRight: 8,
    backgroundColor: "#fff",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 4,
    borderColor: "#E62117",
    borderWidth: 1
  },
  againText: {
    fontSize: 16,
    color: "#E62117"
  },
  rightBtn: {
    flex: 1,
    backgroundColor: "#E62117",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 4
  },
  agreementView: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginLeft: 16,
    marginRight: 33
  },
  selectIcon: {
    width: 12,
    height: 12,
    marginRight: 8,
    marginTop: 1
  },
  unselectIcon: {
    width: 12,
    height: 12,
    marginRight: 8,
    marginTop: 1,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#B3B3B3"
  },
  agreementTitle: {
    fontSize: 12,
    lineHeight: 14,
    color: "#999"
  }
});
