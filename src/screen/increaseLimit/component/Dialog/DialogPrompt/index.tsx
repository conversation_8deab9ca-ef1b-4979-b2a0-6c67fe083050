/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 19:13
 * @description 账号授权弹框提示
 */

import React, { Component } from "react";
import { View, StyleSheet, Text, ImageBackground, Image, TouchableOpacity } from "react-native";
import { CenterViewContainer, DialogContainer } from "common/components/DialogContainer";
import styles from "./styles";

type State = {};

type Props = {
  onConfirm: Function;
  [key: string]: any;
};

export default class DialogePrompt extends Component<Props, State> {
  render() {
    const { t } = this.props;
    return (
      <CenterViewContainer>
        <ImageBackground source={require("../../img/accountauthorizationdialog_img_bg.webp")} style={styles.background}>
          <Text style={styles.title}>{t("提示")}</Text>
        </ImageBackground>
        <View style={{ width: 280 }}>
          <Text style={styles.content}>
            {this.props.t(
              "我声明Akulaku有权收集，访问，使用，披露，处理和保护我授权给Akulaku的帐户中的个人信息或个人数据。"
            )}
          </Text>
        </View>
        <View style={styles.btnContainer} />
        <Text
          style={styles.confirm}
          onPress={() => {
            this.props.onConfirm();
          }}
        >
          {this.props.t("我同意")}
        </Text>
      </CenterViewContainer>
    );
  }
}
