/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022-03 17:12
 * @description bnpl强制升级弹窗
 */

import React, { Component } from "react";
import { View, StyleSheet, Image, Text, TouchableOpacity, NativeModules } from "react-native";
import { CenterViewContainer, DialogContainer } from "common/components/DialogContainer";
import { WINDOW_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";
import { NativeNavigationModule, NativeSystemJumpModule } from "common/nativeModules";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { reportClick, reporter } from "@akulaku-rn/rn-v4-sdk";
import { get } from "lodash";

const styles = StyleSheet.create({
  button: {
    fontSize: 16,
    color: "#E62117",
    alignSelf: "center"
  },
  verticalDivider: {
    width: 1,
    height: 47,
    alignSelf: "center",
    backgroundColor: "#D9D9D9"
  },
  horizontalDivider: {
    height: 1,
    backgroundColor: "#D9D9D9",
    marginTop: 16
  },
  img: {
    alignSelf: "center",
    width: 140,
    height: 140
  },
  text1: {
    fontSize: 16,
    color: "#666666",
    marginTop: 4,
    marginHorizontal: 24
  },
  text2: {
    fontSize: 12,
    color: "#999999",
    marginTop: 12,
    marginHorizontal: 24
  },
  bottom: {
    flexDirection: "row",
    justifyContent: "space-evenly",
    alignItems: "center"
  }
});

type State = {};

type Props = {
  t: TFunction;
  isPayLater: boolean;
};

export default class DialogBnplForceUpdate extends Component<Props, State> {
  componentDidMount() {
    DialogContainer.addBackListener(() => {
      return true;
    });
  }

  render() {
    const { t } = this.props;
    return (
      <CenterViewContainer>
        <>
          <Image source={require("../img/force_update.webp")} style={styles.img} />
          <Text style={styles.text1}>{t("请前往应用商店更新版本，以正常使用此功能")}</Text>
          <Text style={styles.text2}>{t("为保证更好的使用体验，请更新版本后使用")}</Text>
          <View style={styles.horizontalDivider} />
          <View style={styles.bottom}>
            <TouchableOpacity style={{ flex: 1 }} onPress={this.dismiss}>
              <Text style={[styles.button, { color: "#666666" }]}>{t("退出")}</Text>
            </TouchableOpacity>
            <View style={styles.verticalDivider} />
            <TouchableOpacity style={{ flex: 1 }} onPress={this.update}>
              <Text style={styles.button}>{t("立即更新")}</Text>
            </TouchableOpacity>
          </View>
        </>
      </CenterViewContainer>
    );
  }

  dismiss = () => {
    NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
      page_name: "credit limit detail page",
      page_id: "912",
      element_id: "pop3015002",
      element_name: "return",
      extra: {
        pop_id: "pop30150",
        pop_name: "limit upgrade"
      }
    });
    reportClick({
      sn: 100630,
      cn: 21,
      sp: { is_BNPL: this.props.isPayLater ? 1 : 2 }
    });
    DialogContainer.dismiss();
    NativeNavigationModule.goBack();
  };

  update = () => {
    NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
      page_name: "credit limit detail page",
      page_id: "912",
      element_id: "pop3015001",
      element_name: "upgrade",
      extra: {
        pop_id: "pop30150",
        pop_name: "limit upgrade"
      }
    });
    reportClick({
      sn: 100630,
      cn: 20
    });
    NativeSystemJumpModule.goAppMarket(NativeModules.appPackageName);
  };
}
