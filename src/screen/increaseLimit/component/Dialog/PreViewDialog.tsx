/**
 * @description  预览证件照片页面
 */

import React from "react";
import { View, StyleSheet, Image, TouchableOpacity, Text } from "react-native";
import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import i18next, { TFunction } from "i18next";

const styles = StyleSheet.create({
  bottomBtn: {
    paddingVertical: 26,
    paddingHorizontal: 24
  },
  bottomBtnText: {
    color: "#FFFFFF",
    fontSize: 16,
    lineHeight: 19
  },
  container: { flex: 1, backgroundColor: "black" },
  image: { width: WINDOW_WIDTH, flex: 1 }
});

type Props = {
  source: any;
  onReTake: () => void;
  onConfirm: () => void;
  t: TFunction;
};

export default function PreViewDialog({ source, onReTake, onConfirm, t }: Props) {
  return (
    <View style={styles.container}>
      <Image style={styles.image} source={{ uri: source }} resizeMode={"contain"} />
      <View style={{ flexDirection: "row" }}>
        <TouchableOpacity activeOpacity={0.85} onPress={onReTake} style={styles.bottomBtn}>
          <Text style={styles.bottomBtnText}>{t("重新拍摄")}</Text>
        </TouchableOpacity>
        <View style={{ flex: 1 }} />
        <TouchableOpacity activeOpacity={0.85} onPress={onConfirm} style={styles.bottomBtn}>
          <Text style={styles.bottomBtnText}>{t("使用照片")}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
