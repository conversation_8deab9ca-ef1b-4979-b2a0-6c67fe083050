/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-01 16:02
 * @description 通用弹窗容器
 */

import React, { PureComponent } from "react";
import { View, StyleSheet, Animated, Text } from "react-native";
import RootSiblings from "react-native-root-siblings";
import Modal from "react-native-modal";
import { Android } from "@akulaku-rn/akui-rn";

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "100%",
    ...StyleSheet.absoluteFillObject
  }
});

type State = {
  visible: boolean;
};

type Props = {
  renderContent: React.ComponentType<any> | React.ReactElement;
  onClose?: () => void;
};

export default class DialogContainer extends PureComponent<Props, State> {
  opacity: Animated.Value;

  showAni: any;

  dismissAni: any;

  static show(options: Props) {
    DialogContainer.windowArr.push(
      new RootSiblings(
        (
          <DialogContainer
            ref={(comp: any) => {
              if (comp) {
                DialogContainer.refArr.push(comp);
              }
            }}
            {...options}
          />
        )
      )
    );
  }

  static dismiss() {
    if (DialogContainer.windowArr.length >= 1) {
      const ref = DialogContainer.refArr.pop();
      ref && ref.close();
      // @ts-ignore
      const window = DialogContainer.windowArr.pop();
      window && window.destroy();
    }
  }

  constructor(props: any) {
    super(props);

    this.state = {
      visible: true
    };
    this.opacity = new Animated.Value(0);

    this.showAni = Animated.timing(this.opacity, {
      toValue: 0.6,
      duration: 400,
      useNativeDriver: true
    });

    this.dismissAni = Animated.timing(this.opacity, {
      toValue: 0,
      duration: 400,
      useNativeDriver: true
    });
  }

  componentDidMount(): void {
    this.showAni.start();
  }

  static windowArr: Array<RootSiblings> = [];

  static refArr: Array<any> = [];

  close = () => {
    this.dismissAni.start();
    this.setState({
      visible: false
    });
  };

  render() {
    const { renderContent } = this.props;
    return (
      <Animated.View
        style={{
          position: "absolute",
          width: "100%",
          height: "100%",
          backgroundColor: "#000000",
          opacity: this.opacity
        }}
      >
        <Modal
          isVisible={this.state.visible}
          onBackButtonPress={() => DialogContainer.dismiss()}
          backdropColor={"rgba(0,0,0,0)"}
          style={{ margin: 0 }}
          onDismiss={() => this.props.onClose && this.props.onClose()}
          useNativeDriver={Android}
        >
          {renderContent ? renderContent : null}
        </Modal>
      </Animated.View>
    );
  }
}

const EmptyView = () => <Text>没有传入renderContent</Text>;
