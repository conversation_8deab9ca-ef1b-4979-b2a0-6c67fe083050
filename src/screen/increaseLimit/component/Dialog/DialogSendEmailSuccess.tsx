/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2021-04 12:04
 * @description 发送邮件成功弹框
 */

import React, { Component } from "react";
import { View, StyleSheet, Image, Text, TouchableOpacity } from "react-native";
import { CenterViewContainer, DialogContainer } from "common/components/DialogContainer";
import { FontStyles } from "@akulaku-rn/akui-rn";
import { NativeNavigationModule } from "common/nativeModules";
import { TFunction } from "i18next";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";

const styles = StyleSheet.create({
  successIcon: {
    width: 44.8,
    height: 44.8,
    alignSelf: "center",
    marginTop: 24
  },
  successText: {
    ...FontStyles["rob-medium"],
    fontSize: 16,
    color: "#43474C",
    alignSelf: "center",
    marginTop: 16
  },
  contentText: {
    color: "#6E737D",
    fontSize: 14,
    alignSelf: "center",
    marginTop: 8,
    marginLeft: 24,
    marginRight: 24
  },
  confirmText: {
    fontSize: 16,
    color: "#FF2E2E",
    alignSelf: "center"
  },
  sendAgainText: {
    fontSize: 16,
    color: "#6E737D",
    alignSelf: "center"
  },
  horizontalDivider: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: "#E2E5E9",
    marginTop: 16
  },
  bottomContainer: {
    flexDirection: "row",
    height: 50
  },
  sendAgainTouch: {
    flex: 1,
    alignSelf: "center"
  },
  verticalDivider: {
    width: StyleSheet.hairlineWidth,
    backgroundColor: "#E2E5E9"
  },
  iKnownTouch: {
    flex: 1,
    alignSelf: "center"
  }
});

type State = {};

type Props = {
  t: TFunction;
  email: string;
};

export default class DialogSendEmailSuccess extends Component<Props, State> {
  onPress = () => NativeNavigationModule.goBack();

  render() {
    const { t, email } = this.props;
    return (
      <CenterViewContainer>
        <Image source={require("../img/icon_send_email_success.webp")} style={styles.successIcon} />
        <Text style={styles.successText}>{t("发送成功！")}</Text>
        <Text style={styles.contentText}>
          {t("我们已经向邮箱（XXX）发送了验证链接，前往邮箱点击链接，完成验证！（链接24小时内有效）", { x: email })}
        </Text>
        <View style={styles.horizontalDivider} />
        <View style={styles.bottomContainer}>
          <TouchableOpacity style={styles.sendAgainTouch} onPress={DialogContainer.dismiss}>
            <Text style={styles.sendAgainText}>{t("再次发送")}</Text>
          </TouchableOpacity>
          <View style={styles.verticalDivider} />
          <TouchableOpacity style={styles.iKnownTouch} onPress={this.onPress}>
            <Text style={styles.confirmText}>{t("我知道了")}</Text>
          </TouchableOpacity>
        </View>
      </CenterViewContainer>
    );
  }
}
