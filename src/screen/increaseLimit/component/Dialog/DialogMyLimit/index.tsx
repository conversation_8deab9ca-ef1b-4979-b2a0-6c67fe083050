/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-08 19:28
 * @description 我的额度弹框
 */

import React, { Component } from "react";
import { View, StyleSheet, Text, Image, TouchableOpacity } from "react-native";
import Svg, { Polyline } from "react-native-svg";
import BottomViewContainer from "common/components/DialogContainer/BottomViewContainer";
import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { DialogContainer } from "common/components/DialogContainer";
import i18next, { TFunction } from "i18next";
import styles from "./styles";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { AKButton, AKButtonType } from "common/components/AKButton";

type State = {};

type Props = {
  //总额度
  totalCredit: any;

  //固定额度
  fixedCredit: any;

  //临时额度
  tempCredit: any;

  //已提升额度
  promotedCredit: any;

  t: TFunction;

  onTotalAmountClick: () => void;
};

export default class DialogMyLimit extends Component<Props, State> {
  onPress = () => {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "9120202",
      element_name: "got it",
      module_id: "02",
      module_name: "my quota",
      position_id: "02",
      page_name: "credit limit detail page",
      page_id: "912"
    });
    DialogContainer.dismiss();
  };

  render() {
    NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
      element_id: "9120201",
      element_name: "my quota-popup",
      module_id: "02",
      module_name: "my quota",
      position_id: "01",
      page_name: "credit limit detail page",
      page_id: "912"
    });
    const { totalCredit, fixedCredit, tempCredit, promotedCredit, t } = this.props;

    const right_x = WINDOW_WIDTH - 24;
    const center_x = WINDOW_WIDTH / 2;

    return (
      <BottomViewContainer>
        <Text style={styles.myLimit}>{t("我的额度")}</Text>
        <TouchableOpacity style={styles.closeContainer} onPress={DialogContainer.dismiss}>
          <Image source={require("common/images/icon_close_1.webp")} style={styles.close} />
        </TouchableOpacity>
        <Text style={styles.totalLimit}>{t("当前总额度")}</Text>
        <Text style={styles.totolLimitValue} onPress={this.props.onTotalAmountClick}>
          {totalCredit}
        </Text>
        <Svg
          style={{
            alignSelf: "center",
            marginTop: 24
          }}
          width={"100%"}
          height={"6"}
        >
          <Polyline
            points={`24,5 ${center_x - 5},5 ${center_x},0 ${center_x + 5},5 ${right_x},5`}
            stroke="#d9d9d9"
            stroke-width="1"
            fill="#00000000"
          />
        </Svg>
        <View style={styles.fixLimitContainer}>
          <Text style={styles.fixLimit}>{t("固定额度")}</Text>
          <Text style={styles.fixLimitValue}>{fixedCredit}</Text>
        </View>
        <View style={styles.tempLimitContainer}>
          <Text style={styles.templimit}>{t("临时额度")}</Text>
          <Text style={styles.tempLimitValue}>{tempCredit}</Text>
        </View>
        <View style={styles.bottomContainer}>
          <Image source={require("../../img/my_quota.webp")} style={styles.bottomImg} />
          <Text style={styles.bottomText}>
            {t("哇！额度共提升了xxxx,保持良好信用，再接再厉！", { x: promotedCredit })}
          </Text>
        </View>
        <AKButton
          onPress={this.onPress}
          text={t("我知道了")}
          type={AKButtonType.B1_1_2}
          style={{
            width: WINDOW_WIDTH - 32,
            marginTop: 12,
            marginBottom: 12
          }}
        />
      </BottomViewContainer>
    );
  }
}
