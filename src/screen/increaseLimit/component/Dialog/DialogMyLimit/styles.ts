/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 20:19
 * @description
 */
import { StyleSheet } from "react-native";
import { FontUtil } from "@akulaku-rn/akui-rn";
import { WINDOW_WIDTH, FontStyles } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  myLimit: {
    ...FontStyles["rob-medium"],
    color: "#333333",
    fontSize: 16,
    lineHeight: 19,
    alignSelf: "center",
    marginTop: 16
  },
  totalLimit: {
    fontFamily: FontUtil.getFontFamily("roboto-regular"),
    fontSize: 14,
    color: "#999999",
    lineHeight: 16,
    alignSelf: "center",
    marginTop: 51
  },
  totolLimitValue: {
    ...FontStyles["DIN-Bold"],
    fontSize: 28,
    color: "#333333",
    lineHeight: 33,
    alignSelf: "center",
    marginTop: 8
  },
  fixLimitContainer: {
    flexDirection: "row",
    marginTop: 32,
    marginLeft: 24,
    marginRight: 24,
    justifyContent: "space-between"
  },
  fixLimitValue: {
    fontSize: 16,
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    color: "#333333",
    lineHeight: 19
  },
  tempLimitContainer: {
    flexDirection: "row",
    marginTop: 16,
    marginLeft: 24,
    marginRight: 24,
    justifyContent: "space-between"
  },
  tempLimitValue: {
    fontSize: 16,
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    color: "#333333",
    lineHeight: 19
  },
  bottomContainer: {
    flexDirection: "row",
    paddingLeft: 16,
    paddingRight: 16,
    backgroundColor: "rgba(230,33,23,0.08)",
    marginTop: 76
  },
  bottomImg: {
    width: 60,
    height: 60,
    position: "relative",
    top: -6
  },
  bottomText: {
    fontSize: 12,
    color: "#e62117",
    lineHeight: 13,
    marginLeft: 12,
    alignSelf: "center",
    flex: 1
  },
  confirmContainer: {
    alignItems: "center",
    width: WINDOW_WIDTH - 32,
    height: 40,
    alignSelf: "center",
    backgroundColor: "#e62117",
    marginTop: 12,
    justifyContent: "center",
    borderRadius: 4,
    marginBottom: 12
  },
  confirmText: {
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    fontSize: 16,
    color: "#ffffff",
    lineHeight: 19,
    alignSelf: "center"
  },
  closeContainer: {
    position: "absolute",
    top: 12,
    right: 12
  },
  close: {
    width: 24,
    height: 24
  },
  fixLimit: {
    fontSize: 16,
    color: "#999999",
    lineHeight: 19
  },
  templimit: {
    fontSize: 16,
    color: "#999999",
    lineHeight: 19
  }
});
