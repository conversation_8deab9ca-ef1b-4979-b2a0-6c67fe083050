/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022-03 19:35
 * @description 额度变化弹窗
 */

import React, { Component } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { CenterViewContainer } from "common/components/DialogContainer";
import { AKDialog, FigmaStyle, FontStyles, PriceComponent } from "@akulaku-rn/akui-rn";
import { DialogContainer } from "@akulaku-rn/akui-rn/src/components/DialogContainer";
import { ItemDetails } from "../../model/LimitDetailInfo";
import { TFunction } from "i18next";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { reportClick } from "@akulaku-rn/rn-v4-sdk";
import { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";

const styles = StyleSheet.create({
  title: {
    ...FontStyles["rob-medium"],
    fontSize: 16,
    color: FigmaStyle.Color.Grey_Text1,
    alignSelf: "center",
    marginTop: 24,
    marginBottom: 4,
    textAlign: "center"
  },
  content: {
    fontSize: 14,
    color: FigmaStyle.Color.Grey_Text2,
    marginTop: 4,
    alignSelf: "center",
    textAlign: "center",
    marginHorizontal: 24
  },
  content2: {
    fontSize: 14,
    color: FigmaStyle.Color.Grey_Text2,
    marginTop: 4,
    alignSelf: "center",
    textAlign: "center",
    marginHorizontal: 24
  },
  confirm: {
    fontSize: 16,
    color: FigmaStyle.Color.Primary_6,
    alignSelf: "center",
    paddingVertical: 15
  },
  divider: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: "#E2E5E9",
    marginTop: 16.5
  }
});

type State = {};

type Props = {
  t: TFunction;
  itemDetails: ItemDetails[];
  countryId: number;
  amount: number;
  detailType: number;
};

export default class DialogLimitChange extends Component<Props, State> {
  dismiss = () => {
    reportClick({
      sn: 100634,
      cn: 4,
      bct: 0
    });
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      page_name: "amount details page",
      page_id: "913",
      element_id: "9130105",
      element_name: "get",
      position_id: "05",
      module_id: "01",
      module_name: "detail"
    });
    DialogContainer.dismiss();
  };

  render() {
    const { t, itemDetails, countryId, amount, detailType } = this.props;
    let available = 0;
    let free = 0;
    itemDetails?.forEach(itemDetails => {
      if (itemDetails.quotaType === 1) {
        available = itemDetails.quota;
      } else if (itemDetails.quotaType === 2) {
        free = itemDetails.quota;
      }
    });
    return (
      <CenterViewContainer>
        <Text style={styles.title}>{t("额度说明")}</Text>
        {available ? (
          <Text style={styles.content}>
            {t(amount < 0 ? "可用额度扣减" : detailType === 2 ? "可用额度提升" : "可用额度恢复", {
              x: PriceComponent.priceFormat(available, countryId, { needUnit: true })
            })}
          </Text>
        ) : null}
        {free ? (
          <Text style={styles.content2}>
            {t(amount < 0 ? "可用免息额度扣减" : detailType === 2 ? "可用免息额度提升" : "可用免息额度恢复{XXX}", {
              x: PriceComponent.priceFormat(free, countryId, { needUnit: true })
            })}
          </Text>
        ) : null}
        <View style={styles.divider} />
        <TouchableOpacity onPress={this.dismiss}>
          <Text style={styles.confirm}>{t("我知道了")}</Text>
        </TouchableOpacity>
      </CenterViewContainer>
    );
  }
}
