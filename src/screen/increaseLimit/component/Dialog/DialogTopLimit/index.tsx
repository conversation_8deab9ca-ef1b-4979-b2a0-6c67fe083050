/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-10 17:54
 * @description
 */

import React, { PureComponent } from "react";
import { View, StyleSheet, Image, Text, TouchableHighlight, TouchableOpacity } from "react-native";
import { CenterViewContainer } from "common/components/DialogContainer";
import styles from "./styles";
import { DialogContainer } from "@akulaku-rn/akui-rn/src/components/DialogContainer";

type State = {};

type Props = {
  t: any;
  msg: string;
};

export default class DialogTopLimit extends PureComponent<Props, State> {
  onPress = () => {
    DialogContainer.dismiss();
  };

  render() {
    return (
      <CenterViewContainer>
        <Image source={require("../../img/img_upperlimit.webp")} style={styles.img} />
        <Text style={styles.text}>{this.props.msg}</Text>
        <View
          style={{
            height: StyleSheet.hairlineWidth,
            backgroundColor: "#EBEBEB"
          }}
        />
        <TouchableOpacity
          style={{
            height: 49,
            justifyContent: "center"
          }}
          onPress={this.onPress}
        >
          <Text style={styles.btn}>{this.props.t("我知道了")}</Text>
        </TouchableOpacity>
      </CenterViewContainer>
    );
  }
}
