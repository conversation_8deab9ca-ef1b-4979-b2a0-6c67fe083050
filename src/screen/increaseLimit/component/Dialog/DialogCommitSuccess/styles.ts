/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 18:00
 * @description
 */
import { StyleSheet } from "react-native";
import { FontStyles } from "@akulaku-rn/akui-rn";
export default StyleSheet.create({
  container: {
    backgroundColor: "white",
    width: 280,
    borderRadius: 4,
    paddingTop: 55,
    paddingBottom: 16,
    alignItems: "center"
  },
  title: {
    fontSize: 14,
    color: "#999999",
    lineHeight: 15,
    paddingHorizontal: 24,
    textAlign: "center"
  },
  amount: {
    ...FontStyles["DIN-Bold"],
    fontSize: 28,
    color: "#F89C00",
    lineHeight: 29,
    textAlign: "center",
    marginTop: 10,
    marginLeft: 2
  },
  content: {
    fontSize: 14,
    color: "#99999999",
    lineHeight: 15,
    alignContent: "center",
    marginTop: 8,
    paddingHorizontal: 24,
    textAlign: "center"
  },
  continue: {
    fontSize: 16,
    color: "#e62117",
    paddingHorizontal: 24,
    lineHeight: 19,
    marginTop: 16
  },
  goShoppingContainer: {
    width: "100%",
    height: 0.5,
    marginTop: 16,
    backgroundColor: "#ebebeb"
  },
  goShopping: {
    fontSize: 16,
    color: "#666666",
    paddingHorizontal: 24,
    lineHeight: 19,
    marginTop: 15
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: -43
  },
  countryCode: {
    ...FontStyles["DIN-Bold"],
    fontSize: 16,
    color: "#F89C00",
    alignSelf: "flex-end",
    marginBottom: 5
  },
  continueContainer: {
    width: "100%",
    height: 0.5,
    marginTop: 16,
    backgroundColor: "#ebebeb"
  }
});
