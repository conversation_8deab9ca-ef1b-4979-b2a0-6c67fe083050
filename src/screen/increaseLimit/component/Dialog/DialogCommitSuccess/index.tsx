/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-07 19:29
 * @description 提交成功，人工审核
 */

import React, { Component, PureComponent } from "react";
import { View, StyleSheet, Image, Text } from "react-native";
import OutCenterViewContainer from "common/components/DialogContainer/OutOfCenterViewContainer";
import { NativeNavigationModule } from "common/nativeModules";
import { DialogContainer } from "@akulaku-rn/akui-rn/src/components/DialogContainer";
import styles from "./styles";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import PriceComponent from "@akulaku-rn/akui-rn/src/components/PriceComponent";
import { reportClick } from "@akulaku-rn/rn-v4-sdk";
import _ from "lodash";

type State = {};

type Props = {
  continueIncreaseLimit?: Function;
  processId: string;
  [key: string]: any;
};

export default class DialogCommitSuccess extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props);
  }

  continueIncreaseLimit = () => {
    reportClick({
      cn: 16,
      sn: 100633,
      sp: {
        Aku_buttonId: this.props.processId
      }
    });
    NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
      extra: {
        element_id: "pop3004001",
        element_name: "continue to increase",
        Aku_position_id: "02",
        Aku_page_name: this.props.Aku_page_name,
        Aku_page_id: this.props.Aku_page_id,
        pop_id: "pop30040",
        pop_name: "Increase quota Results-audit"
      }
    });
    const { continueIncreaseLimit } = this.props;
    continueIncreaseLimit ? continueIncreaseLimit() : NativeNavigationModule.goBack();
    DialogContainer.dismiss();
  };

  goHome = () => {
    reportClick({
      cn: 17,
      sn: 100633,
      sp: {
        Aku_buttonId: this.props.processId
      }
    });
    NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
      extra: {
        element_id: "pop3004002",
        element_name: "to consume",
        Aku_position_id: "02",
        Aku_page_name: this.props.Aku_page_name,
        Aku_page_id: this.props.Aku_page_id,
        pop_id: "pop30040",
        pop_name: "Increase quota Results-audit"
      }
    });
    NativeNavigationModule.popToHome(0);
  };

  render() {
    NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
      extra: {
        Aku_page_name: this.props.Aku_page_name,
        Aku_page_id: this.props.Aku_page_id,
        pop_id: "pop30040",
        pop_name: "Increase quota Results-audit"
      }
    });
    return (
      <OutCenterViewContainer>
        <View style={styles.container}>
          <Text style={styles.title}>{this.props.t("恭喜！你的提额申请已提交，额度最高可提升")}</Text>
          <View style={{ flexDirection: "row" }}>
            <Text style={styles.countryCode}>{PriceComponent.getCurrency(this.props.store.runtime.countryId)}</Text>
            <Text style={styles.amount}>
              {PriceComponent.priceFormat(200000, this.props.store.runtime.countryId, { needUnit: false })}
            </Text>
          </View>
          <Text style={styles.content}>{this.props.t("审核结果将在1-3个工作日内通知您，请耐心等待")}</Text>
          <View style={styles.continueContainer} />
          <Text style={styles.continue} onPress={this.continueIncreaseLimit}>
            {this.props.t("继续提额")}
          </Text>
          <View style={styles.goShoppingContainer} />
          <Text style={styles.goShopping} onPress={this.goHome}>
            {this.props.t("马上去消费")}
          </Text>
        </View>
        <Image source={require("../../img/commit_success.webp")} style={styles.logo} />
      </OutCenterViewContainer>
    );
  }
}
