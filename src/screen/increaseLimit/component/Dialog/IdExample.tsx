/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-01 16:42
 * @description 证件示例弹窗， 有2种弹窗 手持证件招示例弹窗 证件照示例弹窗
 */
import React, { useState } from "react";
import { View, StyleSheet, TouchableOpacity, Text, Image, ImageSourcePropType, ImageStyle } from "react-native";
import i18next, { TFunction } from "i18next";
import { UrlImage, FontStyles } from "@akulaku-rn/akui-rn";
const styles = StyleSheet.create({
  title: {
    color: "#333333",
    fontSize: 16,
    lineHeight: 21,
    textAlign: "center",
    marginBottom: 16,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  info: { fontSize: 14, lineHeight: 16, color: "#666666", marginTop: 16, textAlign: "center" },
  btnText: {
    lineHeight: 19,
    color: "#E62117",
    fontSize: 16,
    textAlign: "center",
    paddingVertical: 16
  },
  contentContainer: {
    paddingVertical: 16,
    paddingHorizontal: 24
  },
  container: { height: "100%", width: "100%", justifyContent: "center", alignItems: "center" },
  dialogContainer: { backgroundColor: "white", width: 280, borderRadius: 4 },
  borderTop: {
    borderTopWidth: 1,
    borderTopColor: "#EBEBEB"
  },
  holderItemImage: { width: 55, height: 67, backgroundColor: "#F5F5F5" },
  holderItemText: { width: 70, fontSize: 10, lineHeight: 12, color: "#999999", marginTop: 8, textAlign: "center" },
  imageItemImage: { width: 32, height: 32 },
  imageItemText: { width: 70, fontSize: 10, lineHeight: 12, color: "#999999", marginTop: 8, textAlign: "center" }
});

export enum ExampleDialogType {
  IDENTIFICATION, // 证件照
  HOLD_IDENTIFICATION, // 手持证件照
  SIGNATURE // 签名
}

type Props = {
  onConfirm: () => void;
  type: ExampleDialogType;
  t: TFunction;
  countryId: number;
};

export default function ExampleDialog({ type, onConfirm, t, countryId }: Props) {
  const content = {
    [ExampleDialogType.IDENTIFICATION]: (
      <IdentificationComp
        title={t("注意事项")}
        titleTwo={t("正确示例")}
        firstText={t("照片模糊")}
        secondText={t("闪光强烈")}
        thirdText={t("光线太暗")}
        countryId={countryId}
      />
    ),
    [ExampleDialogType.HOLD_IDENTIFICATION]: (
      <HolderIdentification
        title={t("正确示例")}
        firstText={t("身份证模糊")}
        secondText={t("面部遮挡")}
        thirdText={t("光线太暗")}
        content={t("拍照过程会有语音提示，请注意周边环境")}
        countryId={countryId}
      />
    ),
    [ExampleDialogType.SIGNATURE]: <Signature title={t("签名照")} content={t("签名照引导")} countryId={countryId} />
  };
  return (
    <View style={styles.container}>
      <View style={styles.dialogContainer}>
        <View style={styles.contentContainer}>{content[type] || null}</View>
        <TouchableOpacity activeOpacity={0.85} onPress={onConfirm} style={styles.borderTop}>
          <Text style={styles.btnText}>Ok</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

type HolderIdentificationType = {
  firstText: string;
  secondText: string;
  thirdText: string;
  content: string;
  title: string;
  countryId: number;
};
const HolderIdentification = ({
  firstText,
  secondText,
  thirdText,
  content,
  title,
  countryId
}: HolderIdentificationType) => {
  if (countryId === 1) {
    return (
      <View>
        <Text style={styles.title}>{title}</Text>
        <Image style={{ width: 232, height: 303, borderRadius: 4 }} source={require("../../img/img_holdphoto.webp")} />
        <Text style={{ fontSize: 14, color: "#666", lineHeight: 16, marginTop: 16, textAlign: "center" }}>
          {content}
        </Text>
      </View>
    );
  }
  return (
    <>
      <Text style={styles.title}>{title}</Text>
      <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
        <HolderImageItem text={firstText} source={require("../../img/img_blurry.webp")} />
        <HolderImageItem text={secondText} source={require("../../img/img_covered_face.webp")} />
        <HolderImageItem text={thirdText} source={require("../../img/img_dark.webp")} style={{ marginRight: 0 }} />
      </View>
      <Text style={styles.info}>{content}</Text>
    </>
  );
};

type IdentificationCompType = {
  firstText: string;
  secondText: string;
  thirdText: string;
  title: string;
  titleTwo: string;
  countryId: number;
};
const IdentificationComp = ({
  firstText,
  secondText,
  thirdText,
  title,
  titleTwo,
  countryId
}: IdentificationCompType) => {
  if (countryId === 1) {
    return (
      <View>
        <Text style={styles.title}>{titleTwo}</Text>
        <UrlImage
          source={"https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ec_authorize-credit_idphoto.webp"}
          width={232}
          style={{ width: 232, height: 276, borderRadius: 4 }}
        />
      </View>
    );
  }
  return (
    <>
      <Text style={styles.title}>{title}</Text>
      <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
        <ImageItem text={firstText} source={require("common/images/ico_hold.webp")} />
        <ImageItem text={secondText} source={require("common/images/ico_light.webp")} />
        <ImageItem text={thirdText} source={require("common/images/ico_mirror.webp")} style={{ marginRight: 0 }} />
      </View>
    </>
  );
};

const Signature = ({ title, content, countryId }: { title: string; content: string; countryId: number }) => {
  return (
    <>
      <Text style={styles.title}>{title}</Text>
      <Image source={require("common/images/activatecredit_dialog_img_sign.webp")} style={{ height: 100, width: 232 }} />
      <Text style={styles.info}>{content}</Text>
    </>
  );
};

const HolderImageItem = ({
  source,
  text,
  style
}: {
  source: ImageSourcePropType;
  text: string;
  style?: ImageStyle;
}) => {
  return (
    <View style={{ alignItems: "center" }}>
      <Image source={source} style={[styles.holderItemImage, style]} />
      <Text style={styles.holderItemText}>{text}</Text>
    </View>
  );
};

const ImageItem = ({ source, text, style }: { source: ImageSourcePropType; text: string; style?: ImageStyle }) => {
  const [isLoaded, setLoaded] = useState(false);
  return (
    <View style={{ alignItems: "center" }}>
      <Image
        source={source}
        style={[styles.imageItemImage, !isLoaded && { borderRadius: 16, backgroundColor: "#F5F5F5" }, style]}
        onLoadEnd={() => {
          setLoaded(true);
        }}
      />
      <Text style={styles.imageItemText}>{text}</Text>
    </View>
  );
};
