import { PixelRatio } from "react-native";
import Url from "url";
import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import cdnUrl, { PLATFORM } from "./cdnUrl";
import NetInfo, { NetInfoStateType, NetInfoCellularGeneration } from "@react-native-community/netinfo";

const CDNHOST = [
  "cstatic.akulaku.net",
  "drxf4sxe82fjt.cloudfront.net",
  "d12x8ezp3au6m3.cloudfront.net",
  "dev-bimg.akulaku.net",
  "test-bimg.akulaku.net"
];

const scale = (unit: number | undefined) => {
  if (!unit) return 0;
  // 设计稿宽度 360 为准
  return (WINDOW_WIDTH / 360) * unit;
};

enum SPEED {
  FAST = "FAST",
  MEDIUM = "MEDIUM",
  SLOW = "SLOW"
}

interface GlobalEnv {
  SPEED: SPEED;
  DPR: number;
  SUPPORTS_WEBP: boolean;
}

interface ImgQuality {
  FAST: number;
  MEDIUM: number; // cwebp-bin 默认值 75。待测试调优
  SLOW: number;
}

/**
 * 校验cdn平台
 * @param host 域名
 */
function checkCdnPlatform(host: string) {
  const checkres = {
    success: true,
    platform: ""
  };
  for (const key in cdnUrl) {
    // @ts-ignore
    const e = cdnUrl[key];
    if (!e.includes(host)) {
      checkres.success = false;
    } else {
      checkres.success = true;
      checkres.platform = key;
      return checkres;
    }
  }
  return checkres;
}

export async function resolve({
  source: sourceUrl,
  width: logicWidth,
  height: logicHeight,
  isScale
}: {
  source: string;
  width: number | undefined;
  height: number | undefined;
  isScale: boolean;
}) {
  const { hostname, protocol } = Url.parse(sourceUrl);
  // 校验合法链接
  if (!protocol || !protocol.startsWith("http")) {
    console.warn(`sourceUrl(${sourceUrl}) 需要为网络地址`);
    return {};
  }

  // 校验cdn平台
  let cdnPlatform: PLATFORM;

  // 校验是否为合法 cdn 域名
  if (!hostname) {
    console.warn("请传入有效链接");
    return {};
  } else {
    // 校验cdn平台
    const { success, platform } = checkCdnPlatform(hostname);
    if (success) {
      cdnPlatform = platform as PLATFORM;
    } else {
      console.warn(`目前仅支持的cdn裁剪的域名有：${CDNHOST.join(",")}，当前域名为：${hostname}`);
      return {};
    }
  }

  const connectionInfo = await NetInfo.fetch();
  let effectiveType: NetInfoCellularGeneration | null = null;
  if (connectionInfo.type === NetInfoStateType.cellular) {
    effectiveType = connectionInfo.details.cellularGeneration;
  }

  const USER_ENV: GlobalEnv = {
    SPEED: detectNetwork(connectionInfo.type, effectiveType),
    DPR: PixelRatio.get() || 2,
    SUPPORTS_WEBP: true
  };

  // 校验扩展名
  const sourceExt = (resolveExt(sourceUrl) || "").toLowerCase();
  if (!sourceExt) {
    console.warn(`sourceUrl(${sourceUrl}) 不包含有效文件扩展名`);
    return {};
  }
  if (sourceExt !== "jpg" && sourceExt !== "png") {
    console.warn("不支持的格式, 目前仅支持 webp, jpg, png");
    return {};
  }
  const targetExt = USER_ENV.SUPPORTS_WEBP ? "webp" : sourceExt;

  let targetWidth: number | undefined;
  let targetHeight: number | undefined;
  if (logicWidth || logicHeight) {
    // 计算预期的缩放比例，与 设备、网络、宽度区间取整 有关
    const detectSize = Math.max(logicWidth || 0, logicHeight || 0);

    // dpr * 实际尺寸
    const realSize = Math.round(
      (isScale ? scale(detectSize) : detectSize) *
        ({
          FAST: USER_ENV.DPR,
          MEDIUM: Math.min(USER_ENV.DPR, 2),
          SLOW: 1
        } as ImgQuality)[USER_ENV.SPEED]
    );
    // 获取当前尺寸的余数
    const sizeRemainder = realSize % 100;
    let targetSizeRemainder: number;
    // 向上取整至 0 26 50 76 100，不支持任意目标尺寸
    if (sizeRemainder === 0) {
      targetSizeRemainder = 0;
    } else if (sizeRemainder <= 26) {
      targetSizeRemainder = 26;
    } else if (sizeRemainder <= 50) {
      targetSizeRemainder = 50;
    } else if (sizeRemainder <= 76) {
      targetSizeRemainder = 76;
    } else {
      targetSizeRemainder = 100;
    }
    const targetScale = (Math.floor(realSize / 100) * 100 + targetSizeRemainder) / detectSize;

    // 计算实际请求尺寸
    targetWidth = !logicWidth ? undefined : Math.round(logicWidth * targetScale);
    targetHeight = !logicHeight ? undefined : Math.round(logicHeight * targetScale);
  }

  // 计算压缩比例，与格式和网络有关
  let targetQuality;
  switch (targetExt) {
    case "webp":
      targetQuality = ({
        FAST: 90,
        MEDIUM: 80, // cwebp-bin 默认值 75。待测试调优
        SLOW: 60
      } as ImgQuality)[USER_ENV.SPEED];
      break;
    case "jpg":
      targetQuality = ({
        FAST: 90,
        MEDIUM: 80, // mozjpeg 默认 75，通常范围 50-95。待测试调优
        SLOW: 60
      } as ImgQuality)[USER_ENV.SPEED];
      break;
    case "png":
      targetQuality = ({
        FAST: 90,
        MEDIUM: 80, // pngquant 与 mozjpeg 数值含义基本相同，直接套用，待测试调优
        SLOW: 60
      } as ImgQuality)[USER_ENV.SPEED];
      break;
    default:
      break;
  }

  return {
    ext: targetExt,
    width: targetWidth,
    height: targetHeight,
    quality: targetQuality,
    platform: cdnPlatform
  };
}

function resolveExt(url: string) {
  return (/\.(\w+)(\?.+)?$/.exec(url) || [])[1];
}

function detectNetwork(type: string, effectiveType: NetInfoCellularGeneration | null) {
  // rn的有效网络质量判断有问题，目前只有在数据蜂窝下才有234g，wifi下是unknown
  let speedLevel = SPEED.MEDIUM;
  if (type.toLowerCase() === "wifi") {
    speedLevel = SPEED.FAST;
  } else if (type.toLowerCase() === "cellular") {
    switch (effectiveType) {
      case "4g":
        speedLevel = SPEED.MEDIUM;
        break;
      case "3g":
        speedLevel = SPEED.SLOW;
        break;
      default:
        speedLevel = SPEED.SLOW;
        break;
    }
  }
  return speedLevel;
}
