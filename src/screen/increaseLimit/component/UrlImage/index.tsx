import React from "react";
import {
  Image as RNImage,
  ImageBackground,
  ImageProps,
  NativeSyntheticEvent,
  ImageErrorEventData,
  Image,
  View
} from "react-native";
import { resolve } from "./resolve";
import qs from "qs";
import { PLATFORM } from "./cdnUrl";

interface CDNParams {
  w?: number;
  h?: number;
  fmt?: string;
  q?: number;
  fit?: string;
  "x-oss-process"?: string;
}

type P = Omit<ImageProps, "source">;

interface Props extends P {
  /**
   * 完整 url 地址
   */
  source: string;
  /**
   * 逻辑宽度(逻辑像素)，宽高至少需提供一个
   */
  width?: number;
  /**
   * 逻辑高度(逻辑像素)，宽高至少需提供一个
   */
  height?: number;
  /**
   * 裁剪方式，目前仅支持居中
   * clip: center
   */
  clip: string;
  /**
   * 错误网络图片
   */
  failImg?: string;
  /**
   * 错误本地图片
   */
  errorImg?: any;
  /**
   * loading图片
   */
  loadingImg?: any;
  /**
   * 是否为背景图
   */
  isBackground: boolean;
  /**
   * 是否等比缩放
   * default: false
   */
  isScale: boolean;
}

interface States {
  errorImgSource: any;
  targetSource: string;
  key: number;
  loadEnd: boolean;
}

/**
 * UrlImage
 *
 * 网络图片专用组件, 根据用户实际情况加载对应质量的图片
 *
 * 继承于 react-native 的 Image / ImageBackground 组件，可使用 Image / ImageBackground 组件的所有 props
 */
export default class UrlImage extends React.PureComponent<Props, States> {
  imgRef: React.RefObject<unknown>;

  // 按预设逻辑，计算资源使用 CDN 裁剪转换格式后的 url
  // 成功时返回新资源地址，失败时返回 false
  static async resolveNewResource(props: Props) {
    const { source, width, height, isScale, clip } = props;
    if (!source || !/^https?:\/\//.test(source)) {
      return false;
    }
    // TODO 是否对支持的 CDN 地址做检查，不支持的跳过
    if (!width && !height) {
      // eslint-disable-next-line no-console
      console.warn(`Image 宽高均未指定，请尽快处理 source: ${source}`);
    }
    try {
      const params = await resolve({
        source,
        width,
        height,
        isScale
      });
      const [url, query] = source.split("?");
      const sourceParams = qs.parse(query);
      const targetParams: CDNParams = {};

      if (params.platform === PLATFORM.CLOUDFRONT) {
        targetParams.q = params.quality;
        if (params.width) targetParams.w = params.width;
        if (params.height) targetParams.h = params.height;
        if (params.ext) targetParams.fmt = params.ext;
        if (Object.keys(params).length > 0 && clip) targetParams.fit = clip === "center" ? "0" : "1";
      } else if (params.platform === PLATFORM.ALIBABACLOUD) {
        const queryList = [];
        queryList.push("image/resize");
        if (params.width) queryList.push(`,w_${params.width}`);
        if (params.height) queryList.push(`,h_${params.width}`);
        if ((params.width || params.height) && clip) queryList.push(clip === "center" ? ",m_fill" : "");
        if (params.ext) queryList.push(`/format,${params.ext}`);
        queryList.push(`/quality,q_${params.quality}`);
        targetParams["x-oss-process"] = queryList.join("");
      }

      return url + "?" + qs.stringify({ ...sourceParams, ...targetParams });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn(error);
      return false;
    }
  }

  static defaultProps = {
    isBackground: false,
    clip: "nocenter",
    isScale: false
  };

  constructor(props: Props) {
    super(props);
    this.state = {
      errorImgSource: undefined,
      key: 0,
      targetSource: props.source,
      loadEnd: false
    };
    this.imgRef = React.createRef();
  }

  async UNSAFE_componentWillMount() {
    // context 必须在 willMount 时才能取到
    const newResource = await UrlImage.resolveNewResource(this.props);
    if (false !== newResource) {
      this.setState({
        targetSource: newResource
      });
    }
  }

  componentDidMount() {
    // img 行为怪异，若 hydrate 时 browser 计算的 props 与 server 不一致，则不更新渲染(React 对象更新，但 Dom 不更新)
    // 利用此特性，解决 server/browser 计算图片地址不同的问题
    // 是同一张图片，仅 query 不同时，不更新渲染
    // 否则利用 key 变更触发刷新
    // const renderSrc = this.imgRef.current && this.imgRef.current.getAttribute("src");
    // const thisSrc = this.state.targetSource;
    // if (renderSrc && renderSrc.replace(/\?.*$/, "") === thisSrc.replace(/\?.*$/, "")) {
    //   // 确实是同一资源
    //   // no stuff
    //   // this.setState({
    //   //   key: Math.random(),
    //   // });
    // } else {
    //   // 资源已变化，更新
    //   this.setState({
    //     key: Math.random()
    //   });
    // }
  }

  async UNSAFE_componentWillReceiveProps(nextProps: Props) {
    const newResource = await UrlImage.resolveNewResource(nextProps);
    this.setState({
      targetSource: newResource !== false ? newResource : nextProps.source
    });
  }

  render() {
    const { onError, failImg, errorImg, loadingImg, style, isBackground, children, ...props } = this.props;
    const { key, targetSource, errorImgSource, loadEnd } = this.state;
    return !isBackground ? (
      <View>
        <Image source={loadingImg} style={[style, { opacity: loadEnd ? 0 : 1 }]} />
        <RNImage
          {...props}
          source={errorImgSource || { uri: targetSource }}
          // source={errorImgSource||loadingImgSource || { uri: targetSource }}
          style={[style, { position: "absolute" }]}
          onError={(e: NativeSyntheticEvent<ImageErrorEventData>) => {
            failImg &&
              this.setState({
                targetSource: failImg
              });
            errorImg &&
              this.setState({
                errorImgSource: errorImg
              });
            onError && onError(e);
          }}
          onLoadEnd={() => {
            this.setState({
              loadEnd: true
            });
          }}
        />
      </View>
    ) : (
      <ImageBackground
        {...props}
        source={{ uri: targetSource }}
        style={style}
        onError={(e: NativeSyntheticEvent<ImageErrorEventData>) => {
          failImg &&
            this.setState({
              targetSource: failImg
            });
          onError && onError(e);
        }}
      >
        {children}
      </ImageBackground>
    );
  }
}
