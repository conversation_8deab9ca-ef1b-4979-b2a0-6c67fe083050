import { StyleSheet, Platform } from "react-native";
import { p2d } from "common/util";

export default StyleSheet.create({
  container: {
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    paddingTop: 30,
    paddingBottom: 9
  },
  placeholder: {
    color: "#b3b3b3",
    fontSize: 12,
    marginBottom: 8
  },
  fdView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%"
  },

  addressIcon: {
    width: 24,
    height: 24,
    marginRight: 12
  },
  arrowIcon: {
    width: 16,
    height: 16,
    marginRight: 12
  },
  title: {
    fontSize: 14,
    color: "#b3b3b3",
    maxWidth: p2d(240)
  },
  errorView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 3,
    marginBottom: -14.5
  },
  errorText: {
    height: 11,
    fontSize: 10,
    lineHeight: 11,
    color: "#E62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginRight: 4
  }
});
