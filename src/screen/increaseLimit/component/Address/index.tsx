import React, { PureComponent, Component } from "react";
import { Image, View, Text, TouchableOpacity } from "react-native";
import styles from "./styles";
import { NativeAkuAddressModule } from "common/nativeModules";
import { observer } from "mobx-react";
import _ from "lodash";

type Props = {
  setAddress: (text: string, id: number, type: string, key: any) => void;
  id: number;
  containerStyle?: object;
  t: any;
  addrtype: string;
  nonClickable: boolean;
  isOccupation?: boolean;
  placeholder: string;
  store: any;
};

type State = {
  address: string | null;
};
@observer
export default class Address extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    const {
      id,
      store: {
        pageStore: { useQuotaData }
      }
    } = props;
    this.state = {
      address: _.get(useQuotaData, id, null)
    };
  }

  setAddress = async () => {
    const {
      setAddress,
      id,
      addrtype,
      nonClickable,
      t,
      store: {
        pageStore: { useQuotaData }
      }
    } = this.props;

    if (nonClickable) return;
    const address = useQuotaData[id];
    const newAddrtype = parseInt(addrtype) === 5 ? 2 : parseInt(addrtype) === 4 ? 3 : parseInt(addrtype);
    const userInfo = await NativeAkuAddressModule.handleCreditAddress(newAddrtype, address, t("地址"));
    if (userInfo.success) {
      const newData = this.hungary(userInfo.data);
      this.setState({ address: newData });
      setAddress(newData, id, "map", null);
    }
  };

  hungary = (data: any) => {
    const newData = {
      ...data,
      room_number: data.roomNumber
    };
    return JSON.stringify(newData);
  };

  returnAddressText = () => {
    const { address } = this.state;
    const addressObj = address ? JSON.parse(address) : null;
    if (!addressObj) {
      return this.props.placeholder;
    } else {
      const { roomNumber, street, province, city, district, postcode, town, village } = addressObj;
      return `${street ? street : ""} ${roomNumber ? roomNumber : ""} ${town ? town : ""} ${village ? village : ""} ${
        district ? district : ""
      } ${city ? city : ""} ${province ? province : ""} ${postcode ? postcode : ""}`;
    }
  };

  render() {
    const { containerStyle, placeholder } = this.props;
    const { address } = this.state;
    const addressText = this.returnAddressText();
    return (
      <View style={containerStyle}>
        <TouchableOpacity style={[styles.container, !!address && { paddingTop: 8 }]} onPress={this.setAddress}>
          {!!address && <Text style={styles.placeholder}>{placeholder}</Text>}
          <View style={styles.fdView}>
            <Text style={[styles.title, !!address && { color: "#333" }]}>{addressText}</Text>
            <Image style={styles.arrowIcon} source={require("../../img/credit_input_ico_arrow.webp")} />
          </View>
        </TouchableOpacity>
      </View>
    );
  }
}
