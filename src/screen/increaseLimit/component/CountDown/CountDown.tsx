/*
 * @LastEditors: zou yu
 * @Description: 倒计时组件
 * @FilePath: /react-native/src/screen/virtual-services/components/CountDown.tsx
 */

import { FontStyles, FontUtil } from "@akulaku-rn/akui-rn";
import React from "react";
import { Image, Platform, StyleSheet, Text, View } from "react-native";
import LinearGradient from "react-native-linear-gradient";
import { Right } from "native-base";

const MAX_TIME = 24 * 60 * 60 * 1000;

interface Props {
  /**剩余多少毫秒 */
  deadLine: number;
}

const CountDown: React.FC<Props> = ({ deadLine }: Props) => {
  const [lockDeadLine] = React.useState(new Date().getTime() + deadLine);
  const [lastCount, setLastCount] = React.useState(lockDeadLine - new Date().getTime());

  React.useEffect(() => {
    const timer = setInterval(() => {
      const diff = lockDeadLine - new Date().getTime();
      if (diff <= 0) {
        setLastCount(0);
        clearInterval(timer);
      } else {
        const lastTime = diff % MAX_TIME;
        setLastCount(lastTime);
      }
    }, 100);
    return () => {
      clearInterval(timer);
    };
  }, [lockDeadLine]);

  return (
    <View style={styles.container}>
      <Image source={require("./img/time_icon.webp")} style={styles.imageStyle} />
      <Text style={styles.text}>{useHms(lastCount)}</Text>
    </View>
  );
};

export default CountDown;

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 7.5,
    alignSelf: "flex-end"
  },
  imageStyle: {
    width: 16,
    height: 16
  },
  text: {
    marginLeft: 4,
    color: "#2092E5",
    fontSize: 14,
    lineHeight: 15,
    alignSelf: "center",
    textAlign: "center",
    ...FontStyles["rob-medium"],
    ...Platform.select({
      ios: {
        fontWeight: "700"
      }
    })
  }
});

const useHms = (lastMillisecond: number) => {
  const formate = (time: number) => {
    if (time >= 10) {
      return time;
    } else {
      return `0${time}`;
    }
  };

  const parseLeftTime = (leftTime: number) => {
    const ms = Math.floor((leftTime % 1000) / 100);
    leftTime = Math.floor(leftTime / 1000);

    const h = formate(Math.floor(leftTime / (60 * 60)));
    const m = formate(Math.floor(leftTime / 60) % 60);
    const s = formate(leftTime % 60);
    // return `${h} : ${m} : ${s} .${ms}`;
    return `${h} : ${m} : ${s}`;
  };

  return parseLeftTime(lastMillisecond);
};
