import React, { Component } from "react";
import { View } from "react-native";
import SelectItem from "@akulaku-rn/akui-rn/src/components/SelectItem";
import AnimatedTextInput from "../AnimatedTextInput";
import Search from "../Search";
import styles from "./styles";
import Address from "../Address";
import { observer } from "mobx-react";
import MarriageRadioItem from "../Radio/MarriageRadioItem";
import Radio from "../Radio";
import Photo from "../Photo";
import { get } from "lodash";
import { CountryID } from "common/components/BaseContainer/Type";

type Props = {
  pageId: number;
  nextPageHandler?: (isNeedCertUpload: boolean, pageData: Array<object>) => void;
  setWaitArray: (waitArray: any[]) => void;
  t: any;
  nullId: number | null;
  data: any[];
  secondary: any[];
  extraPageData: any[];
  setValue: (text: string | null, id: any, type: string, key: string | null) => void;
  store: any;
  relatedEntryPageInfos: Array<any>;
  type: string;
  sensorLogger: (id?: number) => void;
};

type States = {
  [key: string]: any;
};
@observer
export default class Marriage extends Component<Props, States> {
  selectItems: any[];

  components: any[];

  static defaultProps = {
    extraPageData: []
  };

  constructor(props: Props) {
    super(props);
    this.state = {
      needShowItem: []
    };
    this.selectItems = [];
    this.components = [];
  }

  componentDidMount() {
    this.computeChild();
  }

  computeChild = () => {
    const {
      store: {
        pageStore: { useQuotaData }
      },
      secondary,
      extraPageData,
      nextPageHandler
    } = this.props;
    const data = this.props.data[0];
    if (!!useQuotaData[data.entryId]) {
      const needShowItem = secondary.find(i => {
        return parseInt(i.entryKey) === parseInt(useQuotaData[data.entryId]);
      });
      const findItem = extraPageData.find(i => {
        return parseInt(i.entryKey) === parseInt(useQuotaData[data.entryId]);
      });

      // 获取需要展示的子页数据
      const extraData = findItem ? findItem.relatedPageInfos[0].entityInfos : [];
      nextPageHandler && nextPageHandler(extraData.length > 0, extraData);

      this.setState(
        {
          //由于风控组给的数据结构如此，但是这个数组只会返回一个子项
          needShowItem: needShowItem ? needShowItem.relatedPageInfos[0].entityInfos : []
        },
        () => {
          this.setSelectItemsAndWaitArray();
        }
      );
    }
  };

  openNextComponent = (id: any) => {
    const findIndex = this.selectItems.findIndex(i => i.entryId === id);
    if (!this.selectItems[findIndex + 1]) return;
    const nextId = this.selectItems[findIndex + 1].entryId;
    const nonClickable = this.selectItems[findIndex + 1].nonClickable;
    const {
      store: {
        pageStore: { useQuotaData }
      }
    } = this.props;
    if (useQuotaData[nextId]) return;
    if (nonClickable) return;
    this.components[nextId].onFocus();
  };

  SensorTypeCLICKItem = (id: any) => {
    const { pageId } = this.props;
    // SensorTypeCLICKItem(pageId, id);
    // SensorTypeV3(id);
    // SensorTypeV4(id);
  };

  renderItem = () => {
    const {
      pageId,
      setValue,
      t,
      nullId,
      store: {
        pageStore: { useQuotaData }
      },
      store
    } = this.props;
    const { needShowItem } = this.state;
    const needShowItemView: JSX.Element[] = [];
    needShowItem.map(
      (
        item: {
          type: any;
          pageId: number;
          jsonDownValues: string;
          entryId: number;
          desc: string;
          name: any;
          jsonRule: string;
          subType: string;
          nonClickable: boolean;
        },
        index: number
      ) => {
        const containerStyle = { marginBottom: needShowItem.length - 1 === index ? 106 : 24 };
        const errorText = t("x输入有误", { x: JSON.parse(item.desc).hintInfo || item.name });
        switch (item.type) {
          case "select":
            needShowItemView.push(
              <SelectItem
                ref={view => {
                  this.components[item.entryId] = view;
                }}
                key={index}
                containerStyle={containerStyle}
                data={JSON.parse(item.jsonDownValues)}
                id={item.entryId}
                didSelectedItem={this.didSelectedItem}
                value={useQuotaData[item.entryId]}
                placeholder={JSON.parse(item.desc).hintInfo || item.name}
                nullId={nullId}
                openNextComponent={this.openNextComponent}
                editable={!item.nonClickable}
                onFocus={this.props.sensorLogger}
                nullText={errorText}
              />
            );
            break;
          case "file":
            needShowItemView.push(
              <Photo
                resizeMode={"contain"}
                key={index}
                data={item.desc}
                image={get(useQuotaData, item.entryId, null)}
                jsonRule={item.jsonRule}
                pageId={1}
                containerStyle={[containerStyle, { marginHorizontal: 0, marginTop: index <= 2 ? 8 : 0 }]}
                t={t}
                id={item.entryId}
                setValue={setValue}
                countryId={get(this.props, "store.runtime.countryId", CountryID.ID)}
                nonClickable={item.nonClickable}
              />
            );
            break;
          case "text":
            needShowItemView.push(
              <AnimatedTextInput
                errorText={errorText}
                onFocus={this.props.sensorLogger}
                key={index}
                t={t}
                type={item.type}
                // pageId={pageId}
                containerStyle={containerStyle}
                setValue={setValue}
                value={useQuotaData[item.entryId]}
                placeholder={JSON.parse(item.desc).hintInfo || item.name}
                pattern={JSON.parse(item.jsonRule).pattern}
                // nullId={nullId}
                id={item.entryId}
                editable={!item.nonClickable}
              />
            );
            break;
          case "date":
            needShowItemView.push(
              <SelectItem
                ref={view => {
                  this.components[item.entryId] = view;
                }}
                type={item.type}
                key={index}
                containerStyle={containerStyle}
                data={JSON.parse(item.jsonDownValues)}
                id={item.entryId}
                didSelectedItem={this.didSelectedItem}
                value={useQuotaData[item.entryId]}
                placeholder={JSON.parse(item.desc).hintInfo || item.name}
                nullId={nullId}
                openNextComponent={this.openNextComponent}
                editable={!item.nonClickable}
                onFocus={this.props.sensorLogger}
                nullText={errorText}
              />
            );
            break;
          case "search":
            needShowItemView.push(
              <Search
                onFocus={this.props.sensorLogger}
                value={""}
                editable={true}
                store={this.props.store.pageStore}
                pattern={""}
                t={t}
                // pageId={pageId}
                containerStyle={containerStyle}
                key={index}
                id={item.entryId}
                setValue={setValue}
                // text={useQuotaData[item.entryId]}
                placeholder={JSON.parse(item.desc).hintInfo || item.name}
                // nullId={nullId}
                subType={item.subType}
                // nonClickable={item.nonClickable}
              />
            );
            break;

          case "email":
            needShowItemView.push(
              <AnimatedTextInput
                onFocus={() => {}}
                errorText={errorText}
                key={index}
                t={t}
                type={item.type}
                // pageId={pageId}
                containerStyle={containerStyle}
                setValue={setValue}
                value={useQuotaData[item.entryId]}
                placeholder={JSON.parse(item.desc).hintInfo || item.name}
                pattern={JSON.parse(item.jsonRule).pattern}
                // nullId={nullId}
                id={item.entryId}
                editable={!item.nonClickable}
                isEmail
              />
            );
            break;

          case "number":
            needShowItemView.push(
              <AnimatedTextInput
                onFocus={this.props.sensorLogger}
                errorText={errorText}
                key={index}
                t={t}
                type={item.type}
                // pageId={pageId}
                containerStyle={containerStyle}
                setValue={setValue}
                value={useQuotaData[item.entryId]}
                placeholder={JSON.parse(item.desc).hintInfo || item.name}
                pattern={JSON.parse(item.jsonRule).pattern}
                // nullId={nullId}
                id={item.entryId}
                editable={!item.nonClickable}
              />
            );
            break;

          case "iphone":
            needShowItemView.push(
              <AnimatedTextInput
                onFocus={this.props.sensorLogger}
                errorText={errorText}
                key={index}
                t={t}
                type={item.type}
                // pageId={pageId}
                containerStyle={containerStyle}
                setValue={setValue}
                value={useQuotaData[item.entryId]}
                placeholder={JSON.parse(item.desc).hintInfo || item.name}
                pattern={JSON.parse(item.jsonRule).pattern}
                // nullId={nullId}
                id={item.entryId}
                editable={!item.nonClickable}
              />
            );
            break;

          case "map":
            needShowItemView.push(
              <Address
                key={index}
                t={t}
                // pageId={pageId}
                containerStyle={containerStyle}
                isOccupation
                store={store}
                // nullId={nullId}
                id={item.entryId}
                setAddress={setValue}
                addrtype={JSON.parse(item.jsonRule).Address}
                nonClickable={item.nonClickable}
                placeholder={JSON.parse(item.desc).hintInfo || item.name}
              />
            );
            break;
        }
      }
    );
    return needShowItemView;
  };

  didSelectedItem = (selectedItem: any, id: any, type: string) => {
    const { setValue } = this.props;
    setValue(selectedItem, id, type, null);
  };

  occupationSelected = (selectedItem: any, id: any) => {
    const {
      setValue,
      secondary,
      store: {
        pageStore: { useQuotaData }
      },
      extraPageData,
      nextPageHandler
    } = this.props;
    const setValueFunc = () => setValue(selectedItem, id, this.props.type, null);
    if (selectedItem !== useQuotaData[id]) {
      const needShowItem = secondary.find(i => {
        return i.entryKey === selectedItem;
      });
      const findItem = extraPageData.find(i => {
        return i.entryKey === selectedItem;
      });
      // 获取需要展示的子页数据
      const extraData = findItem ? findItem.relatedPageInfos[0].entityInfos : [];
      nextPageHandler && nextPageHandler(extraData.length > 0, extraData);
      this.setState(
        {
          needShowItem: [] //为了让selectedItem刷新
        },
        () => {
          this.setState(
            {
              //由于风控组给的数据结构如此，但是这个数组只会返回一个子项
              needShowItem: needShowItem ? needShowItem.relatedPageInfos[0].entityInfos : []
            },
            () => {
              // this.emptyData();
              this.setSelectItemsAndWaitArray();
              setValueFunc();
            }
          );
        }
      );
    }
    setValueFunc();
  };

  setSelectItemsAndWaitArray = () => {
    const { setWaitArray } = this.props;
    this.selectItems = [];
    const waitArray: any[] = [];
    this.state.needShowItem.map((item: { type: string; entryId: any }) => {
      if (item.type === "select") {
        this.selectItems.push(item);
      }
      item.entryId && waitArray.push(item.entryId);
    });
    waitArray.unshift(this.props.data[0].entryId);
    setWaitArray(waitArray);
  };

  emptyData = () => {
    const { setValue } = this.props;
    const needEmpty: any[] = [];
    this.state.needShowItem.map((item: { entryId: any }) => {
      needEmpty.push(item.entryId);
    });
    //得到需要清空的id数组，进行遍历清空
    needEmpty.map(item => {
      setValue(null, item, this.props.type, null);
    });
  };

  transformDataToRadioData = (jsonValues: string) => {
    let data: any;
    try {
      data = JSON.parse(jsonValues);
    } catch (e) {
      data = [];
    }
    return data.map((item: any) => {
      return {
        ...item,
        type: item.key
      };
    });
  };

  getTitle = (desc: string) => {
    let data: any;
    try {
      data = JSON.parse(desc);
    } catch (e) {
      data = {};
    }
    return data.hintInfo;
  };

  render() {
    const {
      t,
      nullId,
      store: {
        runtime: { countryId },
        pageStore: { useQuotaData }
      }
    } = this.props;
    const data = this.props.data[0];
    const title = this.getTitle(data.desc);
    return (
      <View style={styles.container}>
        <Radio
          initValue={useQuotaData[data.entryId]}
          title={title}
          onChange={(item: { [key: string]: any }, index: number) => {
            console.log(`index: `, index);
            this.occupationSelected(item.key, data.entryId);
          }}
          id={data.entryId}
          data={this.transformDataToRadioData(data.jsonDownValues)}
          itemComponent={MarriageRadioItem}
        />
        {this.renderItem()}
      </View>
    );
  }
}
