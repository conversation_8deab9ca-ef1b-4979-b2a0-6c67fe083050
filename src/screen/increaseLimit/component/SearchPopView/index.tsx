import React, { Component } from "react";
import { View, Text, DeviceEventEmitter, Image, TouchableOpacity, ScrollView } from "react-native";
import styles from "./styles";
import { NAV_BAR_HEIGHT, WINDOW_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { Shadow } from "@akulaku-rn/akui-rn/src/components/NeomorphShadows";
import { observer } from "mobx-react";
import { SERVICE_TYPES } from "../../tool/client";
import _ from "lodash";

type Props = {
  store: any;
  email?: string;
};

type States = {
  data: Array<any>;
  isLoading: boolean;
};

@observer
export default class SearchPopView extends Component<Props, States> {
  listener: any;

  subType: any;

  value: any;

  constructor(props: Props) {
    super(props);
    this.state = {
      data: [],
      isLoading: true
    };
    this.subType = null;
    this.value = null;
  }

  componentDidMount() {
    this.listener = DeviceEventEmitter.addListener("searchPopView", data => {
      this.fetchData(data);
    });
  }

  componentWillUnmount() {
    this.listener = null;
  }

  fetchData = _.debounce((searchData: { value: any; subType: any }) => {
    const {
      store: { pageStore }
    } = this.props;
    this.setState({
      isLoading: true
    });
    const { value, subType } = searchData;
    this.subType = subType;
    this.value = value;
    const url = subType === "school" ? SERVICE_TYPES.College : SERVICE_TYPES.Discipline;
    pageStore.post(
      url,
      { key: value },
      (response: any) => {
        const { success, data } = response;
        if (success) {
          this.setState({ data, isLoading: false });
        }
      },
      () => {
        this.setState({ isLoading: false });
      }
    );
  }, 500);

  emitter = (data: any) => {
    data.subType = this.subType;
    DeviceEventEmitter.emit("searchText", data);
    this.props.store.pageStore.showSearchPopView = false;
  };

  returnTitle = (name: string) => {
    const value = this.value.replace(/\s+/g, "");
    const data = name.split(value);
    const index = name.indexOf(value);
    let oneText: any = "",
      twoText: any = "",
      threeText: any = "",
      blackIndex = null;
    if (index === -1) {
      oneText = data[0];
    } else if (index === 0) {
      oneText = value;
      twoText = data[0];
      threeText = data.slice(1, data.length + 1);
      blackIndex = 1;
    } else if (index === name.length - value.length) {
      oneText = data[0];
      twoText = data[1];
      threeText = value;
      blackIndex = 3;
    } else {
      oneText = data[0];
      twoText = value;
      threeText = data.slice(1, data.length + 1);
      blackIndex = 2;
    }
    return { oneText, twoText, threeText, blackIndex };
  };

  render() {
    const {
      store: {
        pageStore: { showSearchPopView, searchPageY }
      }
    } = this.props;
    const { data, isLoading } = this.state;
    if (!showSearchPopView) return null;
    if (isLoading) return null;
    return (
      <View style={[styles.container, { top: searchPageY - NAV_BAR_HEIGHT + 28 }]}>
        <Shadow height={WINDOW_HEIGHT / 2} width={WINDOW_WIDTH - 32} style={styles.containerView}>
          {!data.length ? (
            <Text style={styles.nullText}>No relevant results yet~ Please re-enter.</Text>
          ) : (
            <ScrollView>
              {data.map((item, index) => {
                const { oneText, twoText, threeText, blackIndex } = this.returnTitle(item.name);
                return (
                  <TouchableOpacity
                    key={index}
                    style={[styles.item, index === data.length - 1 && { borderBottomWidth: 0 }]}
                    onPress={() => {
                      this.emitter(item);
                    }}
                  >
                    <Text style={styles.itemText}>
                      <Text style={[styles.itemText, blackIndex === 1 && { color: "#333" }]}>{oneText}</Text>
                      <Text style={[styles.itemText, blackIndex === 2 && { color: "#333" }]}>{twoText}</Text>
                      <Text style={[styles.itemText, blackIndex === 3 && { color: "#333" }]}>{threeText}</Text>
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          )}
        </Shadow>
      </View>
    );
  }
}
