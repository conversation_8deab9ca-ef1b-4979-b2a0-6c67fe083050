import { StyleSheet, Platform } from "react-native";
import { WINDOW_WIDTH, FontStyles, WINDOW_HEIGHT } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    position: "absolute",
    left: 16,
    zIndex: 1,
    overflow: "visible"
  },
  containerView: {
    borderRadius: 8,
    shadowColor: "rgba(0,0,0,0.08)",
    shadowRadius: 8,
    height: WINDOW_HEIGHT / 2,
    width: WINDOW_WIDTH - 32,
    backgroundColor: "#fff"
  },
  item: {
    paddingVertical: 17,
    marginLeft: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: "#EBEBEB"
  },
  nullText: {
    alignSelf: "center",
    marginVertical: 24,
    fontSize: 14,
    color: "#b3b3b3"
  },
  itemText: {
    fontSize: 14,
    color: "#999",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  }
});
