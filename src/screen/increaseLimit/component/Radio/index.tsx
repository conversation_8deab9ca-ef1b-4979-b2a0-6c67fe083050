/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-08 17:46
 * @description
 */
import React, { Component } from "react";
import { View, Text } from "react-native";
import { FontStyles } from "@akulaku-rn/akui-rn";

type Props = {
  data: Array<{ [key: string]: any }>;
  itemComponent: any;
  onChange: (item: { [key: string]: any }, index: number) => void;
  title: string;
  initValue: string;
  id: number;
};

export default class Radio extends Component<Props, { selectedIndex: number }> {
  constructor(props: Props) {
    super(props);
    this.state = {
      selectedIndex: props.data.findIndex((item: { [key: string]: any }) => item.value === props.initValue)
    };
  }

  _onPress = (item: { [key: string]: any }, index: number) => () => {
    this.props.onChange && this.props.onChange(item, this.props.id);
    if (this.state.selectedIndex !== index) {
      this.setState({
        selectedIndex: index
      });
    }
  };

  render(): React.ReactNode {
    const { data, itemComponent: ItemComp, title } = this.props;

    // return <View
    //   style={{
    //     flexDirection: "row",
    //     flex: 1
    //   }}
    // >
    //   {data.map((item, index) => {
    //     return (
    //       <ItemComp
    //         key={`${index}`}
    //         isLast={index === data.length - 1}
    //         {...item}
    //         selected={this.state.selectedIndex === index}
    //         onPress={this._onPress(item, index)}
    //       />
    //     );
    //   })}
    // </View>

    return (
      <View style={{ height: 100 }}>
        <Text style={{ color: "#333333", fontSize: 14, ...FontStyles["rob-medium"], marginBottom: 16 }}>{title}</Text>
        <View
          style={{
            flexDirection: "row",
            width: "100%"
          }}
        >
          {data.map((item, index) => {
            return (
              <View key={`${index}`} style={{ flex: 1 }}>
                <ItemComp
                  isLast={index === data.length - 1}
                  {...item}
                  selected={this.state.selectedIndex === index}
                  onPress={this._onPress(item, index)}
                />
              </View>
            );
          })}
        </View>
      </View>
    );
  }
}
