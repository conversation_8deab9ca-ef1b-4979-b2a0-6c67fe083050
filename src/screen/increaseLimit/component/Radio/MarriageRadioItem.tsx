/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-08 17:46
 * @description
 */

import React, { useLayoutEffect } from "react";
import { StyleSheet, TouchableOpacity, Image, Text } from "react-native";

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    borderRadius: 4,
    borderWidth: 1,
    borderColor: "#EEEEEF",
    height: 50,
    flexDirection: "row",
    paddingHorizontal: 12
    // flex: 1
  },
  selectedContainer: {
    borderColor: "#E62117",
    backgroundColor: "#FCEAEA"
  },
  icon: {
    height: 24,
    width: 24,
    marginRight: 4
  },
  text: {
    fontSize: 12,
    color: "#333333",
    flex: 1
  },
  textSelected: { color: "#E62117" }
});

interface Props extends ItemData {
  selected: boolean;
  onPress: () => void;
  isLast: boolean;
}

type ItemData = {
  value: string;
  type: MarriageType;
};

export enum MarriageType {
  MARRIED = "110356601",
  SINGLE = "110356602",
  DIVORCE = "110356603"
}

// export default React.memo(
//   function MarriageRadioItem({ value, onPress, selected, type, isLast }: Props) {
//     const icon = {
//       [MarriageType.MARRIED]: require("./img/icon_married.webp"),
//       [MarriageType.SINGLE]: require("./img/icon_signal.webp"),
//       [MarriageType.DIVORCE]: require("./img/icon_Divorce.webp")
//     };
//
//     return (
//       <TouchableOpacity
//         style={[styles.container, selected && styles.selectedContainer, { marginRight: isLast ? 0 : 5 }]}
//         onPress={onPress}
//       >
//         <Image style={[styles.icon, { tintColor: selected ? "red" : undefined } ]} source={icon[type]} />
//         <Text>{value}</Text>
//       </TouchableOpacity>
//     );
//   },
//   (prevProps, nextProps) => {
//     return prevProps.selected === nextProps.selected;
//   }
// );

export default function MarriageRadioItem({ value, onPress, selected, type, isLast }: Props) {
  const icon = {
    [MarriageType.MARRIED]: {
      selected: require("./img/icon_married_active.webp"),
      unSelected: require("./img/icon_married.webp")
    },
    [MarriageType.SINGLE]: {
      selected: require("./img/icon_signal_active.webp"),
      unSelected: require("./img/icon_signal.webp")
    },
    [MarriageType.DIVORCE]: {
      selected: require("./img/icon_Divorce_active.webp"),
      unSelected: require("./img/icon_Divorce.webp")
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.85}
      style={[styles.container, selected && styles.selectedContainer, { marginRight: isLast ? 0 : 5 }]}
      onPress={onPress}
    >
      <Image style={[styles.icon]} source={icon[type][selected ? "selected" : "unSelected"]} />
      <Text numberOfLines={2} style={[styles.text, selected && styles.textSelected]}>
        {value}
      </Text>
    </TouchableOpacity>
  );
}
