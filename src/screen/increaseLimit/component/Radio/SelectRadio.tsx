/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-08 16:33
 * @description
 */

import React from "react";
import { View, StyleSheet, TouchableOpacity, Image, Text, ViewStyle } from "react-native";
import { MarriageType } from "./MarriageRadioItem";

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    flex: 1
  },
  icon: {
    height: 16,
    width: 16,
    marginRight: 8
  }
});

interface Props extends ItemData {
  selected: boolean;
  onPress: () => void;
  isLast: boolean;
}

type ItemData = {
  text: string;
  type: MarriageType;
  itemStyle?: ViewStyle;
};

export default React.memo(
  function SelectRadio({ onPress, selected, text, itemStyle }: Props) {
    const source = selected ? require("./img/radio_select.webp") : require("./img/radio_unselect.webp");
    return (
      <TouchableOpacity style={[styles.container, itemStyle]} onPress={onPress}>
        <Image style={[styles.icon]} source={source} />
        <Text>{text}</Text>
      </TouchableOpacity>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.selected === nextProps.selected;
  }
);
