import React, { PureComponent } from "react";
import { Text, StyleSheet, Animated, Image, TouchableOpacity, View, Keyboard } from "react-native";

import _ from "lodash";
import { iOS } from "@akulaku-rn/akui-rn";
import PopUpPanel from "@akulaku-rn/akui-rn/src/components/PopUpPanel";
import VerticalList from "@akulaku-rn/akui-rn/src/components/PopUpPanel/components/VerticalList";
import DatePicker from "../DatePicker";

type Props = {
  didSelectedItem: (selectedItem: any, id?: number, type?: any, key?: any) => void;
  openNextComponent?: (id?: number) => void;
  onFocus?: (id?: number) => void;
  data: any;
  value: string;
  placeholder: string;
  id?: number;
  containerStyle?: object;
  type?: string;
  editable?: boolean;
  nullText?: string;
};

type States = {
  selectedItem: string | null;
};

export default class SelectItem extends PureComponent<Props, States> {
  top: Animated.Value;

  fontSize: Animated.Value;

  static defaultProps = {
    editable: true
  };

  constructor(props: Props) {
    super(props);
    const { value } = props;
    this.state = {
      selectedItem: this.returnSelectedItem()
    };
    this.top = new Animated.Value(value ? 8 : 30);
    this.fontSize = new Animated.Value(value ? 12 : 14);
  }

  returnSelectedItem = () => {
    const { value, data, type } = this.props;
    if (type === "date") {
      if (value) {
        return this.formatDate(value);
      } else {
        return null;
      }
    } else {
      const item = data.find((i: { key: any }) => {
        return parseInt(i.key) === parseInt(value);
      });
      if (!!item) {
        return item.value;
      } else {
        return null;
      }
    }
  };

  formatDate = (datetime: any) => {
    const date = new Date(parseInt(datetime));
    const year = date.getFullYear(),
      month = ("0" + (date.getMonth() + 1)).slice(-2);
    // 拼接
    const result = `${month}/${year}`;
    // 返回
    return result;
  };

  onFocus = () => {
    _.debounce(
      () => {
        const { placeholder, editable, onFocus, id } = this.props;
        Keyboard.dismiss();
        !!onFocus && onFocus(id);
        if (!editable) return;
        PopUpPanel.show({
          onClose: this.onBlur,
          title: placeholder,
          childrenComponent: this.childrenComponent()
        });
      },
      300,
      { leading: true, trailing: false }
    )();
  };

  childrenComponent = () => {
    const { data, type } = this.props;
    if (type === "date") {
      return <DatePicker didSelectedItem={this.birthdaydidSelectedItem} />;
    } else {
      return <VerticalList data={data} didSelectedItem={this.didSelectedItem} selectedItem={this.state.selectedItem} />;
    }
  };

  didSelectedItem = (selectedItem: { value: string; key: number }) => {
    const { didSelectedItem, id, openNextComponent, type } = this.props;
    this.setState({ selectedItem: selectedItem.value }, () => {
      this.onBlur();
      PopUpPanel.dismiss();
      didSelectedItem(selectedItem.key, id, type, selectedItem.value);
      openNextComponent && openNextComponent(id);
    });
  };

  birthdaydidSelectedItem = (selectedItem: any) => {
    const { didSelectedItem, id, openNextComponent, type } = this.props;
    const time = selectedItem.getTime();
    const newSelectedItem = this.formatDate(time);
    this.setState({ selectedItem: newSelectedItem }, () => {
      PopUpPanel.dismiss();
      didSelectedItem(time, id, type, null);
      openNextComponent && openNextComponent(id);
    });
  };

  onBlur = () => {
    if (this.state.selectedItem && !!this.state.selectedItem.length) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 8,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 12,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 30,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 14,
          useNativeDriver: false
        })
      ]).start();
    }
  };

  render() {
    const { placeholder, containerStyle, editable } = this.props;
    const { selectedItem } = this.state;
    return (
      <View style={containerStyle}>
        <TouchableOpacity style={styles.container} onPress={this.onFocus}>
          <Text style={[styles.selectedItem, !editable && { color: "#B3B3B3" }]}>{selectedItem}</Text>
          <Image
            style={{ width: 16, height: 16, marginRight: 12 }}
            source={require("common/images/choose_icon.webp")}
          />
          <Animated.View
            style={{
              position: "absolute",
              zIndex: 9,
              top: this.top
            }}
          >
            <Animated.Text
              numberOfLines={1}
              style={{
                fontSize: this.fontSize,
                lineHeight: 16,
                color: "#b3b3b3",
                maxWidth: 300,
                height: 16,
                textAlignVertical: "center"
              }}
            >
              {placeholder}
            </Animated.Text>
          </Animated.View>
        </TouchableOpacity>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    paddingTop: 30,
    paddingBottom: 9,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  selectedItem: {
    fontSize: 14,
    color: "#333",
    maxWidth: "90%"
  },
  errorText: {
    height: 11,
    fontSize: 10,
    lineHeight: 11,
    color: "#E62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginRight: 4
  }
});
