import React, { Component, ReactNode } from "react";
import { NativeSyntheticEvent, TextInput, TextInputKeyPressEventData, Keyboard, View, StyleSheet } from "react-native";

const styles = StyleSheet.create({
  textInput: {
    color: "#333333",
    margin: 0,
    padding: 0,
    fontSize: 16,
    lineHeight: 19,
    height: 19,
    width: 16,
    borderBottomColor: "transparent",
    borderBottomWidth: 1,
    textAlign: "center",
    paddingBottom: 2
  },
  borderBottom: { borderBottomColor: "#333333" }
});

type MultiTextInputProps = {
  data: Array<ItemComp>;
  // onFinish?: (totalString: string) => void
  onChangeText: (totalString: string) => void;
  onFocus?: () => void;
};

export type ItemComp = {
  type: CompType;
  value?: string;
  stateKey?: string;
};

export enum CompType {
  TEXTINPUT,
  TEXT
}

type RefItem = { ref: React.ReactNode; refKey: string };

export default class MixtureTextInput extends Component<MultiTextInputProps, any> {
  constructor(props: any) {
    super(props);
    const { data } = props;
    this.formatDataArray = this.formatData(data);
    const stateData = this.formatDataArray.reduce((result: any, item: ItemComp) => {
      if (item.type === CompType.TEXTINPUT && item.stateKey) {
        return {
          ...result,
          [item.stateKey]: item.value
        };
      }
      return result;
    }, {});
    this.state = {
      ...stateData
    };
    console.log(`this.state: `, this.state);
  }

  formatDataArray: Array<ItemComp> = [] as Array<ItemComp>;

  textInputRefs: Array<RefItem> = [];

  onChangeText = (key: string) => (text: string) => {
    this.setState({ [key]: text.substr(0, 1) });
    const index = this.textInputRefs.findIndex((item: { ref: React.ReactNode; refKey: string }) => {
      return item.refKey === key;
    });
    if (!this.state[key] && !!text) {
      // 没有值变有值
      if (index < this.textInputRefs.length - 1) {
        !(this.textInputRefs[index + 1].ref as TextInput).isFocused() &&
          (this.textInputRefs[index + 1].ref as TextInput).focus();
      } else {
        (this.textInputRefs[index].ref as TextInput).blur();
        Keyboard.dismiss();
      }
    }
    let haveEmpty = false;
    this.formatDataArray.forEach((item: ItemComp) => {
      if (!haveEmpty && item.type === CompType.TEXTINPUT) {
        if (item.stateKey !== key) {
          if (!this.state[item.stateKey || ""]) haveEmpty = true;
        } else {
          if (!text) haveEmpty = true; // text 没有值为true
        }
      }
    });
    if (!haveEmpty) {
      // this.props.onFinish && this.props.onFinish(this.getAllString(key, text));
    }
    this.props.onChangeText && this.props.onChangeText(this.getAllString(key, text));
  };

  onFocus = () => {
    const item = this.textInputRefs.find((item: RefItem) => {
      return !this.state[item.refKey];
    });
    // @ts-ignore
    item && item.ref.focus();
    this.props.onFocus && this.props.onFocus();
  };

  onKeyPress = (key: string) => (e: NativeSyntheticEvent<TextInputKeyPressEventData>) => {
    console.log(`e.nativeEvent.key: `, e.nativeEvent.key);
    const index = this.textInputRefs.findIndex((item: { ref: React.ReactNode; refKey: string }) => {
      return item.refKey === key;
    });
    if (e.nativeEvent.key === "Backspace") {
      if (!!this.state[this.textInputRefs[index].refKey]) {
        this.setState({ [key]: "" });
      } else if (index > 0) {
        const item = this.textInputRefs[index - 1];
        this.setState({ [item.refKey]: "" });
        (this.textInputRefs[index - 1].ref as TextInput).focus();
      }
    }
    if (/^[0-9]$/.test(e.nativeEvent.key)) {
      if (!!this.state[key]) {
        this.setState({ [key]: e.nativeEvent.key });
      }
      if (index < this.textInputRefs.length - 1) {
        !(this.textInputRefs[index + 1].ref as TextInput).isFocused() &&
          (this.textInputRefs[index + 1].ref as TextInput).focus();
      }
    }
  };

  pushTextInputRef = (key: string) => (textInput: ReactNode) => {
    const haveItem = this.textInputRefs.some((item: RefItem) => item.refKey === key);
    if (!haveItem) {
      this.textInputRefs.push({ ref: textInput, refKey: key });
    }
  };

  textInput: any = null;

  formatData = (data: Array<ItemComp>): Array<ItemComp> => {
    return data.map((item: ItemComp, index: number) => {
      return {
        ...item,
        stateKey: `${index}`
      };
    });
  };

  getAllString = (currentItemKey: string, currentText: string): string => {
    return this.formatDataArray.reduce((result: string, item: ItemComp) => {
      if (item.type === CompType.TEXT) {
        return `${result}${item.value}`;
      } else {
        if (item.stateKey === currentItemKey) {
          return `${result}${currentText}`;
        }
        return `${result}${this.state[item.stateKey || ""]}`;
      }
    }, "");
  };

  render() {
    const data = this.formatDataArray;
    return (
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        {data.map((item: ItemComp, index) => {
          const marginStyle = { marginLeft: index === 0 ? 0 : 4 };
          switch (item.type) {
            case CompType.TEXTINPUT:
              return (
                !!item.stateKey && (
                  <TextInput
                    selectionColor={"#E62117"}
                    key={item.stateKey}
                    editable={true}
                    onKeyPress={this.onKeyPress(item.stateKey)}
                    style={[styles.textInput, styles.borderBottom, marginStyle]}
                    maxLength={1}
                    onFocus={this.onFocus}
                    value={this.state[item.stateKey]}
                    onChangeText={this.onChangeText(item.stateKey)}
                    ref={this.pushTextInputRef(item.stateKey)}
                    keyboardType={"numeric"}
                  />
                )
              );
            default:
              // return <Text style={[{ fontSize: 16, lineHeight: 19, borderBottomColor: "#333333", borderBottomWidth: 1 }, marginStyle]}>{item.value}</Text>
              const stylesDefault = styles.textInput;
              const isSymbol = /\D/.test(item.value || "");
              // if () {
              //   Reflect.deleteProperty(stylesDefault, "width");
              //   stylesDefault.width = 4;
              // }
              return (
                <TextInput
                  key={item.stateKey}
                  style={[stylesDefault, marginStyle, isSymbol && { width: 4 }]}
                  maxLength={1}
                  value={item.value}
                  editable={false}
                />
              );
          }
        })}
      </View>
    );
  }
}

// const TextInputWithLine = (props: any) => {
//   return (
//     <View>
//       <TextInput {...props} />
//       <View style={{ height: 1, backgroundColor: "#333333", width: "100%" }} />
//     </View>
//   );
// };
