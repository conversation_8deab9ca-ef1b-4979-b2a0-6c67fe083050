/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-09 20:53
 * @description
 */

import React from "react";
import MixtureTextInput, { CompType, ItemComp } from "./index";
import { CountryID } from "common/components/BaseContainer/Type";
import { Image, Text, View, ViewStyle, StyleSheet } from "react-native";
import { FontStyles } from "@akulaku-rn/akui-rn";

const transformDataByCountryId = (countryId: CountryID): { data: Array<ItemComp>; tip: string } => {
  const map: { [key: string]: { data: Array<ItemComp>; tip: string } } = {
    [`${CountryID.ID}`]: {
      data: [
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXT,
          value: "."
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXT,
          value: "."
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXT,
          value: "."
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXT,
          value: "-"
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXT,
          value: "."
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        },
        {
          type: CompType.TEXTINPUT,
          value: ""
        }
      ],
      tip: "Masukkan dengan format: ************-000.000"
    },
    [`${CountryID.MY}`]: {
      data: [
        {
          type: CompType.TEXT,
          value: "3"
        }
      ],
      tip: "The format of number entered is: ************-000.000"
    },
    [`${CountryID.PH}`]: {
      data: [
        {
          type: CompType.TEXT,
          value: "3"
        }
      ],
      tip: "The format of number entered is: ************-000.000"
    },
    [`${CountryID.VN}`]: {
      data: [
        {
          type: CompType.TEXT,
          value: "3"
        }
      ],
      tip: "Quy tắc nhập mã là: ************-000.000"
    }
  };
  return map[countryId];
};

const transformDataByInitValue = (initValue: string): Array<ItemComp> => {
  const splitArr = initValue.split("");
  return splitArr.map((item: string) => {
    if (/^\d$/.test(item)) {
      return {
        type: CompType.TEXTINPUT,
        value: item
      };
    } else {
      return {
        type: CompType.TEXT,
        value: item
      };
    }
  });
};

type Props = {
  countryId: CountryID;
  title: string;
  // onFinish: (taxNo: string, id: string, type: string, key: null) => void;
  onChangeText: (taxNo: string, id: string, type: string, key: null) => void;
  initValue?: string;
  type: string;
  id: string;
  style?: ViewStyle;
  onFocus?: (id: string) => void;
};

export default function TaxMixtureTextInput({
  countryId,
  title,
  onChangeText,
  initValue,
  id,
  type,
  style,
  onFocus
}: Props) {
  // const TypeData = transformDataByCountryId(countryId);
  const TypeData = transformDataByCountryId(CountryID.ID);

  const data = !initValue ? TypeData.data : transformDataByInitValue(initValue || "");
  console.log(`data: `, data);
  console.log(`initValue: `, initValue);

  return (
    <View style={style}>
      <Text style={styles.title}>{title}</Text>
      <MixtureTextInput
        onFocus={() => {
          onFocus && onFocus(id);
        }}
        onChangeText={(taxNo: string) => {
          onChangeText(taxNo, id, type, null);
        }}
        // onFinish={(taxNo: string) => {
        //   onFinish(taxNo, id, type, null);
        // }}
        data={data}
      />
      <View style={styles.bottomInfoContainer}>
        <Image source={require("../img/feedback_ico.webp")} style={styles.bottomInfoImage} />
        <Text style={styles.bottomInfoText}>{TypeData.tip}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  title: {
    marginBottom: 16,
    fontSize: 14,
    color: "#333333",
    ...FontStyles["rob-medium"]
  },
  bottomInfoContainer: {
    flexDirection: "row",
    marginTop: 16
  },
  bottomInfoImage: {
    height: 12,
    width: 12,
    marginRight: 4
  },
  bottomInfoText: {
    fontSize: 12,
    color: "#666666"
  }
});
