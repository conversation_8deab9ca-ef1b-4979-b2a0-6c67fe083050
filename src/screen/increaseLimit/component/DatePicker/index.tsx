import { FontStyles, iOS, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { Wheel } from "@akulaku-rn/akui-rn/src/components/Teaset";
import React, { PureComponent, Component } from "react";
import { View, StyleSheet, Dimensions, Text, TouchableOpacity } from "react-native";
import { withTranslation } from "common/services/i18n";

type Props = {
  didSelectedItem: any;
  t?: any;
};

type State = {
  date: Date;
  yearIndex: number;
  monthsIndex: number;
};
@withTranslation("increase-limit")
export default class DatePicker extends Component<Props, State> {
  years: number[];

  months: number[];

  date: Date;

  constructor(props: Readonly<Props>) {
    super(props);
    this.state = {
      date: new Date(),
      yearIndex: 0,
      monthsIndex: 0
    };
    this.years = [];
    for (let i = 1970; i <= 2100; ++i) this.years.push(i);
    this.months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
    this.date = new Date();
  }

  componentDidMount() {
    this.initializeDate();
  }

  initializeDate = () => {
    const { date } = this.state;
    date.setDate(1);
    const year = date.getFullYear(),
      month = date.getMonth();
    this.setState({ date, monthsIndex: this.months.indexOf(month + 1), yearIndex: this.years.indexOf(year) });
  };

  onDateChange(year: number, month: number) {
    const { date } = this.state;
    date.setFullYear(year);
    date.setMonth(month);
    this.date = date;
    this.setState({ date, monthsIndex: this.months.indexOf(month + 1), yearIndex: this.years.indexOf(year) });
  }

  didSelectedItem = () => {
    const { didSelectedItem } = this.props;
    didSelectedItem(this.date);
  };

  render() {
    const { date, yearIndex, monthsIndex } = this.state;
    const year = date.getFullYear(),
      month = date.getMonth();
    return (
      <View>
        <View style={styles.main}>
          <Wheel
            style={styles.wheelStyle}
            itemStyle={styles.itemStyle}
            holeStyle={styles.holeStyle}
            items={this.months}
            index={monthsIndex}
            onChange={(index: any) => this.onDateChange(year, this.months[index] - 1)}
          />
          <Wheel
            style={styles.wheelStyle}
            itemStyle={styles.itemStyle}
            holeStyle={styles.holeStyle}
            items={this.years}
            index={yearIndex}
            onChange={(index: any) => this.onDateChange(this.years[index], month)}
          />
        </View>
        <View style={styles.buttonView}>
          <TouchableOpacity style={styles.button} onPress={this.didSelectedItem}>
            <Text style={styles.buttonText}>{this.props.t("提交")}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  main: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "red"
  },
  wheelStyle: {
    height: 233,
    width: WINDOW_WIDTH / 2
  },
  itemStyle: {
    textAlign: "center",
    fontSize: 20
  },
  holeStyle: {
    height: 45
  },
  buttonView: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#fff"
  },
  button: {
    backgroundColor: "#E62117",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 4
  },
  buttonText: {
    fontSize: 16,
    color: "#fff",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  }
});
