import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Animated,
  DeviceEventEmitter,
  Image,
  Keyboard,
  findNodeHandle,
  UIManager,
  TouchableOpacity
} from "react-native";
import { inject, observer } from "mobx-react";

type Props = {
  setValue: (text: string, id: number, type: any, key: any) => void;
  value: string;
  id: number;
  placeholder: string;
  onFocus: (id: number) => void;
  containerStyle?: object;
  pattern: any;
  t: any;
  isEmail?: boolean;
  store?: any;
  keyboardType?: any;
  type?: string;
  editable: boolean;
  errorText: string;
};

type States = {
  value: string;
  isfocus: boolean;
  error: boolean;
};
@inject("store")
@observer
export default class AnimatedTextInput extends Component<Props, States> {
  textInputRef: any;

  top: Animated.Value;

  fontSize: Animated.Value;

  keyboardDidHideListener: any;

  listener: any;

  timer: any;

  constructor(props: Props) {
    super(props);
    this.state = {
      value: props.type === "iphone" && props.value ? props.value.replace(/(\d{4})(?=\d)/g, "$1 ") : props.value,
      isfocus: false,
      error: false
    };
    this.top = new Animated.Value(props.value ? 8 : 30);
    this.fontSize = new Animated.Value(props.value ? 12 : 14);
    this.timer = null;
    this.listener = null;
  }

  componentDidMount() {
    const { setValue, id, isEmail, type } = this.props;
    if (isEmail) {
      this.listener = DeviceEventEmitter.addListener("setEmail", email => {
        const newText = this.state.value + email;
        this.setState({ value: newText }, () => {
          this.onEndEditing();
          this.onBlur();
        });
        setValue(newText, id, type, null);
      });
    } else {
      this.listener = DeviceEventEmitter.addListener("setValue", data => {
        if (id === data.id) {
          this.setState({ value: data.value }, () => {
            this.onEndEditing();
            this.onBlur();
          });
          setValue(data.value, id, type, null);
        }
      });
    }
    this.keyboardDidHideListener = Keyboard.addListener("keyboardDidHide", this.keyboardDidHide);
    this.onEndEditing();
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    this.listener && this.listener.remove();
    this.keyboardDidHideListener.remove();
  }

  keyboardDidHide = () => {
    this.textInputRef && this.textInputRef.blur();
  };

  onFocus = () => {
    const { id, onFocus } = this.props;
    onFocus(id);
    this.onFocusAnimation();
  };

  onFocusAnimation = () => {
    Animated.parallel([
      Animated.timing(this.top, {
        toValue: 8,
        duration: 200,
        useNativeDriver: false
      }),
      Animated.timing(this.fontSize, {
        toValue: 12,
        duration: 200,
        useNativeDriver: false
      })
    ]).start();
    this.setState({
      isfocus: true
    });
  };

  onBlur = () => {
    this.setState({ isfocus: false });
    if (this.state.value && !!this.state.value.length) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 8,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 12,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 30,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 14,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
    }
  };

  onChangeText = (text: string) => {
    const { setValue, id, isEmail, type } = this.props;
    let nowText = text;
    if (type === "iphone") {
      nowText = nowText.replace(/(\d{4})(?=\d)/g, "$1 ");
    }
    this.setState({ value: nowText, error: false });
    setValue(nowText, id, type, null);
    if (isEmail) {
      const spstr = nowText.split("");
      const showEmailTips = spstr[spstr.length - 1] === "@";
      if (showEmailTips) {
        Keyboard.dismiss();
        setTimeout(() => {
          this.props.store.pageStore.showEmailTips = true;
        }, 500);
      } else {
        this.props.store.pageStore.showEmailTips = false;
      }
    }
  };

  onEndEditing = () => {
    const { pattern, type } = this.props;
    let text = this.state.value;
    if (type === "iphone") {
      while (text && text.indexOf(" ") >= 0) {
        // 电话号码 去掉空格
        text = text.replace(" ", "");
      }
    }
    let error = false;
    if (!!pattern && !!text) {
      const reg = new RegExp(pattern);
      error = !reg.test(text);
    }
    this.setState({ error });
  };

  clickText = () => {
    if (!this.props.editable) return;
    this.textInputRef.focus();
  };

  onLayout = () => {
    if (!this.props.isEmail) return;
    const handle = findNodeHandle(this.textInputRef);
    if (handle) {
      this.timer = setInterval(() => {
        UIManager.measure(handle, (x, y, width, height, pageX, pageY) => {
          const {
            store: { pageStore }
          } = this.props;
          pageStore.emailPageY = pageY;
        });
      }, 500);
    }
  };

  clear = () => {
    this.setState(
      {
        value: ""
      },
      () => {
        this.onBlur();
        const { setValue, id, type } = this.props;
        this.setState({ value: "" });
        setValue("", id, type, null);
        this.keyboardDidHide();
      }
    );
  };

  render() {
    const { placeholder, containerStyle, type, editable, errorText } = this.props;
    const { value, isfocus, error } = this.state;
    return (
      <View style={containerStyle}>
        <View
          style={[
            styles.container,
            isfocus && { borderBottomColor: "#666" },
            error && { borderBottomColor: "#e62117" }
          ]}
        >
          <TextInput
            ref={textInputRef => {
              this.textInputRef = textInputRef;
            }}
            onEndEditing={this.onEndEditing}
            editable={editable}
            selectionColor={"#e62117"}
            numberOfLines={1}
            style={[styles.textInput, !editable && { color: "#b3b3b3" }]}
            maxLength={100}
            underlineColorAndroid="transparent"
            onChangeText={this.onChangeText}
            onFocus={this.onFocus}
            onBlur={this.onBlur}
            value={value}
            hitSlop={{ top: 10, bottom: 10 }}
            keyboardType={type && (type === "number" || type === "iphone") ? "numeric" : "default"}
          />
          {!!value && !!value.length && (
            <TouchableOpacity onPress={this.clear} hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}>
              <Image style={styles.clearIcon} source={require("common/images/icon_clear_3.webp")} />
            </TouchableOpacity>
          )}
          <Animated.View
            style={{
              position: "absolute",
              zIndex: 9,
              top: this.top
            }}
          >
            <Animated.Text
              onPress={this.clickText}
              style={{ fontSize: this.fontSize, lineHeight: 16, color: "#B3B3B3" }}
            >
              {placeholder}
            </Animated.Text>
          </Animated.View>
        </View>
        {error && (
          <View style={styles.errorView}>
            <Image style={styles.errorIcon} source={require("common/images/error_icon.webp")} />
            <Text style={styles.errorText}>{errorText}</Text>
          </View>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    paddingTop: 30,
    paddingBottom: 9
  },
  textInput: {
    height: 16,
    padding: 0,
    fontSize: 14,
    textAlign: "left",
    textAlignVertical: "center",
    color: "#333",
    lineHeight: 16,
    width: "90%"
  },
  errorView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8
  },
  errorText: {
    height: 11,
    fontSize: 10,
    lineHeight: 11,
    color: "#E62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginRight: 4
  },
  clearIcon: {
    width: 12,
    height: 12,
    marginRight: 12
  }
});
