/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-10 17:10
 * @description
 */
import { StyleSheet } from "react-native";
import { FontStyles, WINDOW_WIDTH, FontUtil } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    width: WINDOW_WIDTH,
    backgroundColor: "#fff"
  },
  title: {
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    fontSize: 16,
    color: "#282B2E",
    fontWeight: "700",
    marginTop: 16,
    marginLeft: 16,
    marginBottom: 16
  },
  line: {
    borderColor: "#E2E5E9",
    height: 0,
    borderTopWidth: StyleSheet.hairlineWidth,
    margin: 0.5
  },
  maxQuota: {
    fontFamily: FontUtil.getFontFamily("robot-regular"),
    fontSize: 12,
    color: "#999999",
    lineHeight: 14,
    alignSelf: "flex-start",
    marginLeft: 16,
    marginBottom: 8
  },
  divider: {
    width: WINDOW_WIDTH,
    height: StyleSheet.hairlineWidth,
    backgroundColor: "#ebebeb",
    marginLeft: 64
  },
  moreContainer: {
    width: WINDOW_WIDTH,
    height: StyleSheet.hairlineWidth,
    backgroundColor: "#ebebeb"
  },
  moreHighlight: {
    width: "100%",
    height: 40,
    alignItems: "center",
    justifyContent: "center"
  },
  moreText: {
    fontSize: 14,
    color: "#666666",
    lineHeight: 15
  }
});
