/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 10:26
 * @description
 */

import React from "react";
import { View, StyleSheet, Text, TouchableOpacity, TouchableHighlight } from "react-native";
import { Category, INCREASE_LIMIT_STATUS } from "../../model/IncreaseLimitInfo";
import IncreaseLimitItem from "../IncreaseLimitItem";
import { NativeNavigationModuleModel } from "common/nativeModules/router/nativeNavigationModule";
import { TFunction } from "i18next";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import styles from "./styles";
import { reportClick } from "@akulaku-rn/rn-v4-sdk";
import { COUNTRY_CODE } from "common/components/BaseContainer/Type";

type State = {};

type Props = {
  category: Category;
  navigation: NativeNavigationModuleModel;
  t: TFunction;
  countryCode: COUNTRY_CODE;
};

export default function IncreaseLimitGroup(props: Props) {
  let title;
  switch (props.category.type) {
    case 2:
      title = props.t("补充资料提额");
      break;
    case 3:
      title = props.t("账号授权提额");
      break;
    case 4:
      title = props.t("其他信息提额");
      break;
  }
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.line} />
      {props.category.processes.map((item, index) => {
        return (
          <>
            <IncreaseLimitItem {...item} {...props} position={index + 1} countryCode={props.countryCode} />
            {index === props.category.processes.length - 1 ? null : <View style={styles.divider} />}
          </>
        );
      })}
      {props.category.hasMore ? (
        <>
          <View style={styles.moreContainer} />
          <TouchableHighlight
            style={styles.moreHighlight}
            underlayColor={"#f5f5f5"}
            onPress={() => {
              reportClick({
                cn: 4,
                sn: 100630,
                ext: {
                  Aku_channelName: props.category.type
                }
              });
              NativeSensorModule.sensorLogger(SensorType.CLICK, {
                element_id: "9120107",
                element_name: "see more",
                module_id: "01",
                module_name: "credit limit",
                position_id: "07",
                page_name: "credit limit detail page",
                page_id: "912",
                extra: {
                  Aku_channelName: props.category.type
                }
              });
              props.navigation.navigate({
                screen: "IncreaseLimitByInfo",
                params: {
                  categoryType: props.category.type
                }
              });
            }}
          >
            <Text style={styles.moreText}>{props.t("查看更多") + ">>"}</Text>
          </TouchableHighlight>
        </>
      ) : null}
    </View>
  );
}
