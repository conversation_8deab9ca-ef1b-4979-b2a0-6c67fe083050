import { StyleSheet } from "react-native";
import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    paddingTop: 20,
    marginHorizontal: 16
  },
  touch: {
    backgroundColor: "#F3F3F3",
    borderRadius: 4,
    overflow: "hidden"
  },
  desc: {
    fontSize: 14,
    color: "#333",
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    marginBottom: 12
  },
  image: {
    width: WINDOW_WIDTH - 32,
    height: (20 / 39) * (WINDOW_WIDTH - 32)
  },
  errorView: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    position: "absolute",
    paddingLeft: 8,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    paddingVertical: 6,
    backgroundColor: "#FCEAEA"
  },
  errorText: {
    fontSize: 10,
    lineHeight: 11,
    color: "#E62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginRight: 4
  },
  watermark: {
    width: 63,
    height: 63,
    position: "absolute",
    left: 7,
    bottom: 8
  }
});
