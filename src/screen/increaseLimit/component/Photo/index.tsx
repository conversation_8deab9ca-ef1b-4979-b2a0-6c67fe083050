import React, { Component } from "react";
import { View, Text, Image, TouchableOpacity, ImageResizeMode } from "react-native";
import styles from "./styles";
import DialogContainer from "../Dialog/DialogContainer";
import { NativeImageCaptureModule } from "common/nativeModules";
import { iOS } from "@akulaku-rn/akui-rn";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import PreViewDialog from "../Dialog/PreViewDialog";
import { observer, inject } from "mobx-react";
import _ from "lodash";

type Props = {
  store?: any;
  data: any;
  image: string;
  id: number;
  pageId: number;
  containerStyle?: object;
  t: any;
  setValue: (text: string, id: number, type: any, key: any) => void;
  countryId: number;
  jsonRule: any;
  showDialog: boolean;
  nonClickable: boolean;
  resizeMode: ImageResizeMode;
};

type States = {
  image: string | null;
  imgError: boolean;
};

enum ExampleDialogType {
  IDENTIFICATION, // 证件照
  HOLD_IDENTIFICATION, // 手持证件照
  SIGNATURE // 签名
}
@inject("store")
@observer
export default class Photo extends Component<Props, States> {
  static defaultProps = {
    showDialog: true,
    resizeMode: "contain"
  };

  constructor(props: Props) {
    super(props);
    this.state = {
      image: this.getImageLocalPath(props.image),
      imgError: false
    };
  }

  _showImage = (id: number, type: ExampleDialogType, source: any) => {
    DialogContainer.show({
      renderContent: (
        <PreViewDialog
          t={this.props.t}
          source={source}
          onReTake={() => this._finishChoosePhoto(id, type)}
          onConfirm={() => DialogContainer.dismiss()}
        />
      )
    });
  };

  didSelectedItem = _.debounce(
    () => {
      const { id, nonClickable } = this.props;
      if (nonClickable) return;
      const jsonRule = JSON.parse(this.props.jsonRule);
      let type = ExampleDialogType.SIGNATURE;
      if (jsonRule.isIcImg) {
        type = ExampleDialogType.IDENTIFICATION;
      }
      if (jsonRule.isHoldIcImg) {
        type = ExampleDialogType.HOLD_IDENTIFICATION;
      }
      if (jsonRule.isSignatureImg) {
        type = ExampleDialogType.SIGNATURE;
      }
      // 查看图片功能
      if (this.state.image) {
        this._showImage(id, type, this.getImageLocalPath(this.state.image));
        return;
      }
      this._finishChoosePhoto(id, type);
    },
    500,
    { leading: true, trailing: false }
  );

  _finishChoosePhoto = async (id: number, type: ExampleDialogType) => {
    if (this.props.showDialog) {
      DialogContainer.dismiss();
    }
    const {
      setValue,
      countryId,
      store: { pageStore }
    } = this.props;
    //如果不加延迟会导致原生闪回RN
    setTimeout(async () => {
      // @ts-ignore
      let image = null;
      let response;
      switch (type) {
        case ExampleDialogType.IDENTIFICATION:
          response = await NativeImageCaptureModule.go2Photo(1, "instalment_credit", id);
          break;
        case ExampleDialogType.HOLD_IDENTIFICATION:
          response = await NativeImageCaptureModule.go2Face(2, "instalment_credit", id);
          break;
        case ExampleDialogType.SIGNATURE:
          response = await NativeImageCaptureModule.takePhoto();
          break;
        default:
          break;
      }
      if (response.success) {
        const waitImgs = [
          iOS ? { entryId: id, base64: response.data.base64 } : { entryId: id, filePath: response.data.filePath }
        ];
        const uploadResponse = await pageStore.uploadImages(waitImgs, countryId);
        if (uploadResponse.success) {
          image = uploadResponse.data[0];
        } else {
          Toast.show("upload img error", {
            position: 0
          });
        }
      } else {
        !!response.errMsg &&
          Toast.show(response.errMsg, {
            position: 0
          });
      }
      if (image) {
        const finalPath = this.getImageLocalPath(image);
        setValue(image, id, "file", null);
        this.setState({ image: finalPath, imgError: false });
      }
    }, 500);
  };

  getImageLocalPath = (path: string | { base64: string; filePath: string }) => {
    if (!path) return null;
    if (typeof path === "string") {
      return path;
    } else {
      if (iOS) {
        return `data:image/png;base64,${path.base64}`;
      } else {
        return `file://${path.filePath}`;
      }
    }
  };

  onError = () => {
    this.setState({
      imgError: true
    });
  };

  returnUri = () => {
    const { image, imgError } = this.state;
    if (imgError) {
      return require("common/images/img_failed.webp");
    }
    if (image) {
      return { uri: image };
    }
    const data = JSON.parse(this.props.data);
    if (data.placeholder1) {
      return { uri: data.placeholder1 };
    } else {
      return require("common/images/img_failed.webp");
    }
  };

  returnResizeMode = () => {
    const { imgError, image } = this.state;
    const { resizeMode } = this.props;
    const data = JSON.parse(this.props.data);
    if (!!resizeMode) return resizeMode;
    if (!image) {
      return "contain";
    }
    if (imgError || !data.placeholder1) {
      return "contain";
    }
    const jsonRule = JSON.parse(this.props.jsonRule);
    if (jsonRule.isIcImg) {
      return "cover";
    }
    if (jsonRule.isHoldIcImg) {
      return "contain";
    }
    if (jsonRule.isSignatureImg) {
      return "cover";
    }
  };

  returnWatermark = () => {
    const { countryId } = this.props;
    switch (countryId) {
      case 1:
        return require("common/images/watermark_in.png");
      case 2:
        return require("common/images/watermark_ms.png");
      case 3:
        return require("common/images/watermark_ph.png");
      case 4:
        return require("common/images/watermark_vn.png");
    }
  };

  render() {
    const { containerStyle } = this.props;
    const data = JSON.parse(this.props.data);
    const { image, imgError } = this.state;
    const uri = this.returnUri();
    const resizeMode = this.returnResizeMode();
    const watermark = this.returnWatermark();
    return (
      <View style={[styles.container, containerStyle]}>
        <Text style={styles.desc}>{data.hintInfo}</Text>
        <TouchableOpacity
          style={[styles.touch, !image && { backgroundColor: "#EDF4FF" }]}
          onPress={this.didSelectedItem}
        >
          <Image resizeMode={resizeMode} source={uri} style={styles.image} onError={this.onError} />
          {!!image && !imgError && <Image style={styles.watermark} source={watermark} />}
        </TouchableOpacity>
      </View>
    );
  }
}
