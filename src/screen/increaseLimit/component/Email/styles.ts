import { StyleSheet, Platform } from "react-native";

export default StyleSheet.create({
  container: {
    backgroundColor: "white",
    flex: 1
  },
  inputContainer: {
    flexDirection: "row",
    width: "100%",
    height: 54,
    marginTop: 26
  },
  input: {
    flex: 1,
    marginLeft: 16,
    marginRight: 16,
    borderBottomColor: "#d9d9d9",
    borderBottomWidth: 1,
    fontSize: 14,
    padding: 0,
    alignSelf: "flex-end",
    paddingBottom: 6,
    height: 33,
    paddingRight: 120
  },
  clearContainer: {
    position: "absolute",
    right: 100,
    bottom: 0,
    padding: 13
  },
  clearImg: {
    width: 12,
    height: 12
  },
  button: {
    fontSize: 10,
    color: "#ffffff",
    lineHeight: 11
  },
  errorContainer: {
    flexDirection: "row",
    marginTop: 8,
    overflow: "hidden",
    height: 13
  },
  errorImg: {
    width: 12,
    height: 12,
    marginLeft: 16,
    marginRight: 4
  },
  errorText: {
    fontSize: 11,
    color: "#e62117",
    lineHeight: 13
  },
  explainContainer: {
    backgroundColor: "#F5F5F5",
    marginHorizontal: 16,
    marginTop: 68,
    padding: 16,
    borderRadius: 4
  },
  explainText1: {
    fontSize: 12,
    color: "#666666",
    lineHeight: 14
  },
  explainText2: {
    fontSize: 12,
    color: "#999999",
    lineHeight: 13,
    marginTop: 8
  },
  animatView: {
    position: "absolute",
    zIndex: 9
  },
  animatText: {
    color: "#999999",
    marginLeft: 16,
    lineHeight: 16
  },
  touchable: {
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    right: 24,
    bottom: 9,
    width: 69,
    height: 24
  }
});
