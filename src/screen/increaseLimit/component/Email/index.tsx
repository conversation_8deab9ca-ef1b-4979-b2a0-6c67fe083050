/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-08 18:04
 * @description 邮箱认证
 */

import React, { PureComponent } from "react";
import {
  View,
  TextInput,
  Image,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  Animated
} from "react-native";
// @ts-ignore
import i18next, { TFunction } from "i18next";
import { observer } from "mobx-react";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { NativeNetworkModuleV2 } from "common/nativeModules";
import styles from "./styles";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import DialogSendEmailSuccess from "../Dialog/DialogSendEmailSuccess";
import { DialogContainer } from "common/components/DialogContainer";
import NativeConfigModule from "common/nativeModules/basics/nativeConfigModule";
import { AKButton, AKButtonType } from "common/components/AKButton";

type Props = {
  navigation: any;
  store: { [key: string]: any };
  t: TFunction;
  jsonRule: string;
};

type State = {
  [key: string]: any;
};

@observer
export default class IncreaseLimitByEmail extends PureComponent<Props, State> {
  top: Animated.Value;

  fontSize: Animated.Value;

  timeout: any;

  lastSecond: number;

  input: any;

  constructor(props: any) {
    super(props);
    this.state = {
      increaseLimitPageInfo: null,
      inputWordLength: 0,
      inputWordError: false,
      inputWord: "",
      countSecond: 0,
      loading: true,
      isError: false,
      caretHidden: false
    };
    this.top = new Animated.Value(20);
    this.fontSize = new Animated.Value(14);
    this.lastSecond = 0;
  }

  async componentDidMount() {
    this.input.focus();
    const {
      data: { deviceBrand, osVersionCode }
    } = await NativeConfigModule.getEnvInfoSilent();
    this.setState({
      caretHidden: deviceBrand === "Xiaomi" && osVersionCode === 29
    });
  }

  componentWillUnmount() {
    this.timeout && clearTimeout(this.timeout);
  }

  /**倒计时**/
  countSecond = () => {
    this.setState({
      countSecond: this.lastSecond
    });
    if (this.lastSecond > 0) {
      this.timeout = setTimeout(() => {
        this.lastSecond--;
        this.countSecond();
      }, 1000);
    }
  };

  postEmail = async (email: string) => {
    const { t } = this.props;
    try {
      const response = await NativeNetworkModuleV2.post("/capi/credit/common/email/send", { email: email });
      NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
        element_id: "pop3007101",
        position_id: "01",
        page_name: "quota submission page",
        page_id: "915",
        extra: {
          pop_id: "pop30071",
          pop_name: "email sucess-popup"
        }
      });
      if (response.success) {
        DialogContainer.show({
          renderContent: <DialogSendEmailSuccess t={t} email={this.state.inputWord} />
        });
      } else {
        NativeToast.showMessage(response.errMsg);
      }
    } catch (e) {
      console.log(e);
      NativeToast.showMessage(t("邮件发送失败请重试"));
    } finally {
    }
  };

  send = () => {
    const { t, jsonRule } = this.props;
    const reg = new RegExp(JSON.parse(jsonRule).pattern);
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "9150104",
      element_name: "Send",
      module_id: "01",
      module_name: "quota",
      position_id: "04",
      page_name: "quota submission page",
      page_id: "915"
    });
    this.setState({
      inputWordError: !reg.test(this.state.inputWord)
    });
    if (this.state.countSecond > 0 || this.state.inputWordLength === 0 || !reg.test(this.state.inputWord)) return;
    this.lastSecond = 120;
    this.countSecond();
    this.postEmail(this.state.inputWord);
  };

  clearContainer = () => {
    if (this.state.textInputFocus) {
      this.setState({
        inputWordLength: 0,
        inputWordError: false,
        inputWord: ""
      });
    }
  };

  onChangeText = (text: string) => {
    this.setState({
      inputWordLength: text.trimLeft().length,
      inputWord: text,
      inputWordError: false
    });
  };

  onFocus = () => {
    this.setState({
      textInputFocus: true
    });
    if (!this.state.inputWordLength) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 10,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
    }
  };

  onBlur = () => {
    this.setState({
      textInputFocus: false
    });
    if (!this.state.inputWordLength) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 27,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 14,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
    }
  };

  render() {
    const { t } = this.props;
    return (
      <TouchableWithoutFeedback style={styles.container} onPress={Keyboard.dismiss}>
        <View>
          <View style={styles.inputContainer}>
            <TextInput
              caretHidden={this.state.caretHidden}
              ref={c => (this.input = c)}
              style={styles.input}
              autoFocus={false}
              placeholderTextColor="#999"
              selectionColor="#E62117"
              onChangeText={this.onChangeText}
              onBlur={this.onBlur}
              onFocus={this.onFocus}
            >
              {this.state.inputWord}
            </TextInput>
            <Animated.View style={[styles.animatView, { top: this.top }]}>
              <Animated.Text style={[styles.animatText, { fontSize: this.fontSize }]}>{t("企业邮箱")}</Animated.Text>
            </Animated.View>
            {this.state.inputWordLength > 0 && (
              <TouchableOpacity style={styles.clearContainer} onPress={this.clearContainer}>
                <Image
                  source={require("common/images/icon_clear_3.webp")}
                  style={[styles.clearImg, { opacity: this.state.textInputFocus ? 1 : 0 }]}
                />
              </TouchableOpacity>
            )}
            <AKButton
              onPress={this.send}
              disabled={this.state.countSecond > 0 || this.state.inputWordLength === 0 || this.state.inputWordError}
              text={this.state.countSecond > 0 ? this.state.countSecond + "S" : t("发送")}
              type={AKButtonType.B1_1_4}
              style={{
                position: "absolute",
                right: 24,
                bottom: 9,
                width: 69
              }}
            />
          </View>

          {this.state.inputWordError ? (
            <View style={styles.errorContainer}>
              <Image source={require("../../img/icon_error.webp")} style={styles.errorImg} />
              <Text style={styles.errorText}>{t("邮箱格式有误")}</Text>
            </View>
          ) : null}

          <View style={styles.explainContainer}>
            <Text style={styles.explainText1}>{t("如何验证企业邮箱?")}</Text>
            <Text style={styles.explainText2}>
              {t("1输入您的企业邮箱,并点击发送,以获取验证链接;2我们将会发送验证链接到您的邮箱中,邮箱中点击验证;")}
            </Text>
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  }
}
