import React, { Component } from "react";
import {
  View,
  StyleSheet,
  TextInput,
  Animated,
  DeviceEventEmitter,
  Keyboard,
  findNodeHandle,
  UIManager,
  TouchableOpacity,
  Image
} from "react-native";
import { inject, observer } from "mobx-react";
import { autorun } from "mobx";

type Props = {
  setValue: (text: string | null, id: number, type: any, key: any) => void;
  value: string;
  id: number;
  placeholder: string;
  onFocus: (id: number) => void;
  containerStyle?: object;
  pattern: any;
  t: any;
  store: any;
  subType: any;
  keyboardType?: any;
  type?: string;
  editable: boolean;
};

type States = {
  value: string;
  isfocus: boolean;
  error: boolean;
};
@inject("store")
@observer
export default class Search extends Component<Props, States> {
  textInputRef: any;

  top: Animated.Value;

  fontSize: Animated.Value;

  keyboardDidHideListener: any;

  listener: any;

  timer: any;

  constructor(props: Props) {
    super(props);
    this.state = {
      value: props.value,
      isfocus: false,
      error: false
    };
    this.top = new Animated.Value(props.value ? 8 : 30);
    this.fontSize = new Animated.Value(props.value ? 12 : 14);
    this.timer = null;
    this.listener = null;
  }

  componentDidMount() {
    this.keyboardDidHideListener = Keyboard.addListener("keyboardDidHide", this.keyboardDidHide);
    this.listener = DeviceEventEmitter.addListener("searchText", data => {
      if (data.subType === this.props.subType) {
        this.setState({ value: data.name }, () => {
          const { setValue, id, type } = this.props;
          setValue(data, id, type, null);
          this.onBlur();
        });
      }
    });
    const {
      id,
      store: { pageStore }
    } = this.props;
    autorun(() => {
      const { nowOnFocusid, useQuotaData } = pageStore;
      if (nowOnFocusid !== id && !this.state.isfocus) {
        if (!useQuotaData[id] || !useQuotaData[id].name || useQuotaData[id].name !== this.state.value) {
          const value = !!useQuotaData[id] ? useQuotaData[id].name : "";
          this.setState(
            {
              value
            },
            () => {
              this.onBlurAnimation();
            }
          );
        }
      }
    });
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    this.listener && this.listener.remove();
    this.keyboardDidHideListener.remove();
  }

  keyboardDidHide = () => {
    this.textInputRef && this.textInputRef.blur();
  };

  onFocus = () => {
    const {
      id,
      subType,
      onFocus,
      store: { pageStore }
    } = this.props;
    const { value } = this.state;
    pageStore.nowOnFocusid = id;
    onFocus(id);
    this.onFocusAnimation();
    const handle = findNodeHandle(this.textInputRef);
    if (handle) {
      UIManager.measure(handle, (x, y, width, height, pageX, pageY) => {
        pageStore.searchPageY = pageY;
        if (value && value.length > 0) {
          const data = {
            value: this.state.value,
            subType
          };
          pageStore.showSearchPopView = true;
          DeviceEventEmitter.emit("searchPopView", data);
        } else {
          pageStore.showSearchPopView = false;
        }
      });
      this.timer = setInterval(() => {
        UIManager.measure(handle, (x, y, width, height, pageX, pageY) => {
          pageStore.searchPageY = pageY;
        });
      }, 1000);
    }
  };

  onFocusAnimation = () => {
    Animated.parallel([
      Animated.timing(this.top, {
        toValue: 8,
        duration: 200,
        useNativeDriver: false
      }),
      Animated.timing(this.fontSize, {
        toValue: 12,
        duration: 200,
        useNativeDriver: false
      })
    ]).start();
    this.setState({
      isfocus: true
    });
  };

  onBlurAnimation = () => {
    if (this.state.value && !!this.state.value.length) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 8,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 12,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 30,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 14,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
    }
  };

  onBlur = () => {
    this.setState({ isfocus: false });
    this.timer && clearTimeout(this.timer);
    this.onBlurAnimation();
  };

  onChangeText = (text: string) => {
    const {
      subType,
      store: { pageStore }
    } = this.props;
    const nowText = text;
    this.setState({ value: nowText, error: false });
    const data = {
      value: text,
      subType
    };
    pageStore.showSearchPopView = true;
    DeviceEventEmitter.emit("searchPopView", data);
  };

  clickText = () => {
    if (!this.props.editable) return;
    this.textInputRef.focus();
  };

  clear = () => {
    this.setState(
      {
        value: ""
      },
      () => {
        this.onBlur();
        const { setValue, id, type } = this.props;
        this.setState({ value: "" });
        setValue(null, id, type, null);
        this.keyboardDidHide();
      }
    );
  };

  render() {
    const { placeholder, containerStyle, type, editable } = this.props;
    const { value, isfocus } = this.state;
    return (
      <View style={containerStyle}>
        <View style={[styles.container, isfocus && { borderBottomColor: "#666" }]}>
          <TextInput
            ref={textInputRef => {
              this.textInputRef = textInputRef;
            }}
            editable={editable}
            selectionColor={"#e62117"}
            numberOfLines={1}
            style={[styles.textInput, !editable && { color: "#b3b3b3" }]}
            maxLength={100}
            underlineColorAndroid="transparent"
            onChangeText={this.onChangeText}
            onFocus={this.onFocus}
            onBlur={this.onBlur}
            value={value}
            hitSlop={{ top: 10, bottom: 10 }}
            keyboardType={type && (type === "number" || type === "iphone") ? "numeric" : "default"}
          />
          {!!value && !!value.length && (
            <TouchableOpacity onPress={this.clear} hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}>
              <Image style={styles.clearIcon} source={require("common/images/icon_clear_3.webp")} />
            </TouchableOpacity>
          )}
          <Animated.View
            style={{
              position: "absolute",
              zIndex: 9,
              top: this.top
            }}
          >
            <Animated.Text
              onPress={this.clickText}
              style={{ fontSize: this.fontSize, lineHeight: 16, color: "#B3B3B3" }}
            >
              {placeholder}
            </Animated.Text>
          </Animated.View>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    paddingTop: 30,
    paddingBottom: 9
  },
  textInput: {
    height: 16,
    padding: 0,
    fontSize: 14,
    textAlign: "left",
    textAlignVertical: "center",
    color: "#333",
    lineHeight: 16,
    width: "90%"
  },
  errorView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 3,
    marginBottom: -14.5
  },
  errorText: {
    height: 11,
    fontSize: 10,
    lineHeight: 11,
    color: "#E62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginRight: 4
  },
  clearIcon: {
    width: 12,
    height: 12,
    marginRight: 12
  }
});
