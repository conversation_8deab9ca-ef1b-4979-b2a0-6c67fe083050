/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-10 11:50
 * @description
 */
import { StyleSheet } from "react-native";
import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    flexDirection: "row",
    width: WINDOW_WIDTH,
    paddingTop: 16,
    paddingBottom: 16
  },
  img: {
    width: 36,
    height: 36,
    marginLeft: 16,
    marginRight: 12
  },
  title: {
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    fontSize: 14,
    color: "#333333",
    lineHeight: 16,
    width: 178
  },
  rowContainer: {
    flexDirection: "row",
    marginTop: 8,
    width: 178,
    flexWrap: "wrap"
  },
  text2: {
    fontSize: 12,
    color: "#989FA9",
    lineHeight: 14
  },
  price: {
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    fontSize: 12,
    color: "#6E737D",
    lineHeight: 14
  },
  text4: {
    fontSize: 12,
    alignSelf: "center",
    textAlign: "right",
    marginLeft: 16,
    flex: 1,
    lineHeight: 13,
    marginRight: 32,
    textAlignVertical: "center"
  },
  imgArrow: {
    width: 16,
    height: 16,
    position: "absolute",
    right: 16,
    alignSelf: "center"
  }
});
