/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-08 14:56
 * @description 提额Item组件
 */

import React from "react";
import { AsyncStorage, Image, StyleSheet, Text, TouchableHighlight, TouchableOpacity, View } from "react-native";
import { INCREASE_LIMIT_STATUS, INCREASE_LIMIT_TYPE, Item } from "../../model/IncreaseLimitInfo";
import { DialogContainer } from "common/components/DialogContainer";
import { AuthPageType, AuthSource } from "../../../auth/types";
import DialogPrompt from "../Dialog/DialogPrompt";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import UrlImage from "../UrlImage";
import styles from "./styles";
import { TFunction } from "i18next";
import { NativeNavigationModule } from "common/nativeModules";
import { reportClick } from "@akulaku-rn/rn-v4-sdk";
import { PriceComponent } from "@akulaku-rn/akui-rn";
import { GlobalRuntime } from "common/constant";
import { COUNTRY_CODE } from "common/components/BaseContainer/Type";
import { CardChannelScene } from "common/screen/card-manage-center/type";

type Props = Item & {
  position: number;
  fromHome?: boolean;
  t: TFunction;
  countryCode: COUNTRY_CODE;
};

export function ToRepayItem(props: { t: TFunction }) {
  return (
    <TouchableHighlight
      underlayColor={"#f5f5f5"}
      onPress={() => {
        NativeSensorModule.sensorLogger(SensorType.CLICK, {
          element_id: "9120108",
          element_name: "to repay",
          module_id: "01",
          module_name: "credit limit",
          position_id: "08",
          page_name: "credit limit detail page",
          page_id: "912"
        });
        reportClick({
          cn: 6,
          sn: 100630
        });
        NativeNavigationModule.navigate({
          screen: "billscreen",
          params: {}
        });
      }}
    >
      <View style={styles.container}>
        <Image source={require("../../img/icon_repayment.webp")} width={36} height={36} style={styles.img} />
        <View>
          <Text style={styles.title}>{props.t("逾期还款")}</Text>
          <View style={styles.rowContainer}>
            <Text style={styles.text2}>{props.t("还清账单后才能提额噢~")}</Text>
          </View>
        </View>
        <Text style={[styles.text4, { color: "#e62117" }]}>{props.t("去还款")}</Text>
        <Image source={require("../img/arrow_light.webp")} style={styles.imgArrow} />
      </View>
    </TouchableHighlight>
  );
}

export default function IncreaseLimitItem(props: Props) {
  const extra = {
    Aku_buttonId: props.id,
    Aku_buttonName: props.title,
    Aku_buttonStatus: props.status,
    Aku_channelName: props.type,
    positionid: props.position
  };

  const event = {
    element_id: props.fromHome ? "9120106" : "9140102",
    element_name: "go finish",
    module_id: "01",
    module_name: props.fromHome ? "credit limit" : "sub",
    position_id: props.fromHome ? "06" : "02",
    page_name: props.fromHome ? "credit limit detail page" : "credit limit detail sub-page",
    page_id: props.fromHome ? "912" : "914"
  };

  let text2, text4;
  let statusColor;
  let clickAble = true;
  switch (props.status) {
    case INCREASE_LIMIT_STATUS.INCREASE_AVAILABLE:
    case INCREASE_LIMIT_STATUS.REJECT:
      statusColor = "#F32823";
      text2 = props.t("最高可提额");
      text4 = props.t("去提额\n");
      break;
    case INCREASE_LIMIT_STATUS.ON_REVEIW:
      statusColor = "#7fb800";
      text2 = props.t("最高可提额");
      text4 = props.t("审核中");
      clickAble = false;
      break;
    case INCREASE_LIMIT_STATUS.UPDATE_AVAILABLE:
      statusColor = "#ff9933";
      text2 = props.t("最高可提额");
      text4 = props.t("可更新");
      break;
    case INCREASE_LIMIT_STATUS.FINISH:
      statusColor = "#999999";
      if (props.daysBeforeUpdatable === 0) {
        statusColor = "#ff9933";
        text4 = props.t("可更新");
      } else if (props.daysBeforeUpdatable > 0 && props.daysBeforeUpdatable <= 7) {
        text4 = props.t("6天后可更新", { x: props.daysBeforeUpdatable + "" });
      } else {
        text4 = props.t("已完成");
      }
      text2 = props.t("已提额");
      clickAble = false;
      break;
    case INCREASE_LIMIT_STATUS.REACH_THE_UPPER_LIMIT:
      statusColor = "#999999";
      text2 = props.t("最高可提额");
      text4 = props.t("已达上限");
      clickAble = false;
      break;
    default:
      statusColor = "#e62117";
      text2 = props.t("还清账单后才能提额噢~");
      text4 = props.t("去还款");
      break;
  }

  function jump() {
    NativeNavigationModule.navigate({
      screen: "IncreaseInfomationPage",
      params: {
        processId: props.id,
        title: props.title,
        status: props.status,
        itemType: props.itemType,
        jumpSource: 0,
        path: props.path
      }
    });
  }

  async function onConfirm() {
    AsyncStorage.setItem("firstIntoAuthorization", "1");
    await DialogContainer.dismiss();
    if (props.type === INCREASE_LIMIT_TYPE.AUTHORIZATION) {
      jump2Auth(props);
    } else {
      jump();
    }
  }

  function jump2Auth(props: any) {
    NativeNavigationModule.navigate({
      screen: "AuthPage",
      params: {
        jumpLink:
          "ak://m.akulaku.com/1602?screen=/increaseLimit/increaseAuthCallback&processId=" +
          (props.processId || props.id),
        source: AuthSource.INCREASE_LIMIT,
        processId: props.processId || props.id,
        type: props.type,
        name: props.title,
        path: props.path,
        extra: {
          ...extra,
          page_id: "915",
          page_name: "quota submission page",
          path: props.path
        }
      }
    });
  }

  const onPress = async () => {
    reportClick({
      cn: props.fromHome ? 5 : 1,
      sn: props.fromHome ? 100630 : 100632,
      ...extra
    });
    const notfirstIn = await AsyncStorage.getItem("firstIntoAuthorization");
    if (props.status === INCREASE_LIMIT_STATUS.ON_REVEIW) {
      NativeToast.showMessage(props.t("资料正在审核中"));
      return;
    } else if (
      props.status === INCREASE_LIMIT_STATUS.FINISH &&
      (props.daysBeforeUpdatable > 7 || props.daysBeforeUpdatable < 0)
    ) {
      NativeToast.showMessage(props.t("该提额项已完成"));
      return;
    } else if (
      props.status === INCREASE_LIMIT_STATUS.FINISH &&
      props.daysBeforeUpdatable > 0 &&
      props.daysBeforeUpdatable <= 7
    ) {
      NativeToast.showMessage(props.t("改提额项x天后可更新", "" + props.daysBeforeUpdatable));
      return;
    } else if (props.status === INCREASE_LIMIT_STATUS.REACH_THE_UPPER_LIMIT) {
      return;
    }

    if (props.type === 1) {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9120108",
        element_name: "to repay",
        module_id: "01",
        module_name: "credit limit",
        position_id: "08",
        page_name: "credit limit detail page",
        page_id: "912"
      });
      reportClick({
        cn: 6,
        sn: 100630
      });
      NativeNavigationModule.navigate({
        screen: "billscreen",
        params: {}
      });
    }
    NativeSensorModule.sensorLogger(SensorType.CLICK, Object.assign(event, { extra: extra }));
    if (props.itemType === "electronic_signature") {
      // props.navigation.navigate({ url: "ak://m.akulaku.com/48?source=4" });
      NativeNavigationModule.navigate({
        screen: "hetong",
        params: {
          title: props.title,
          source: 4
        }
      });
    }
    if (
      props.type === INCREASE_LIMIT_TYPE.AUTHORIZATION &&
      props.status === INCREASE_LIMIT_STATUS.INCREASE_AVAILABLE &&
      !notfirstIn
    ) {
      DialogContainer.show({
        renderContent: <DialogPrompt onConfirm={onConfirm} {...props} {...extra} {...event} />
      });
    } else if (props.type === INCREASE_LIMIT_TYPE.AUTHORIZATION) {
      jump2Auth(props);
    } else if (props.itemType === "binding_bank_card") {
      NativeNavigationModule.navigate({
        screen: "AddBankCardScreen",
        params: {
          cardScene: CardChannelScene.IncreaseLimit,
          redirectUrl: "ak://m.akulaku.com/1602?screen=/auth-result&title=" + props.title
        }
      });
    } else {
      jump();
    }
  };
  return (
    <TouchableHighlight underlayColor={"#f5f5f5"} onPress={onPress}>
      <View style={styles.container}>
        <UrlImage
          source={props.imgLink}
          errorImg={require("../../img/failed.webp")}
          loadingImg={require("../../img/img_loading.webp")}
          width={36}
          height={36}
          style={styles.img}
        />
        <View>
          <Text style={styles.title}>{props.title}</Text>
          <View style={styles.rowContainer}>
            <Text style={styles.text2}>{text2 + " "}</Text>
            <Text style={styles.price}>
              {PriceComponent.priceFormat(props.maxIncreaseQuotaAmount, GlobalRuntime.countryId)}
            </Text>
          </View>
        </View>
        <Text style={[styles.text4, { color: statusColor, opacity: clickAble ? 1 : 0.4 }]}>{text4}</Text>
        <Image
          source={clickAble ? require("../img/arrow_deep.webp") : require("../img/arrow_light.webp")}
          style={styles.imgArrow}
        />
      </View>
    </TouchableHighlight>
  );
}
