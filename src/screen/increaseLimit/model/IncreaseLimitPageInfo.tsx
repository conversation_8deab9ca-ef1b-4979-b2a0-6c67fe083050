/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 18:06
 * @description
 */

import React from "react";
import { View, StyleSheet } from "react-native";

export interface EntityInfo {
  desc: string;
  entryId: number;
  jsonRule: string;
  name: string;
  pageId: number;
  required: boolean;
  type: string;
  subType: string;
  jsonWhere: string;
  jsonDownValues: string[];
  lastValue: string;
  iconUrl: string;
  rejectMsg: string;
  nonClickable: boolean;
}

export interface EntityInfo2 {
  desc: string;
  entryId: number;
  jsonRule: string;
  name: string;
  pageId: number;
  required: boolean;
  type: string;
  subType: string;
  jsonWhere: string;
  jsonDownValues: string[];
  lastValue: string;
  iconUrl: string;
  rejectMsg: string;
  nonClickable: boolean;
}

export interface RelatedPageInfo {
  description: string;
  entityInfos: EntityInfo2[];
  id: number;
  inconUrl: string;
  pageLevel: number;
  step: number;
  subTitle: string;
}

export interface SubPageInfo {
  pageId: number;
  entryKey: number;
  relatedPageInfos: RelatedPageInfo[];
}

export interface RelatedEntryPageInfo {
  entryId: number;
  subPageInfo: SubPageInfo[];
}

export interface IncreaseLimitPageInfo {
  desc: string;
  entityInfos: EntityInfo[];
  groupType: string;
  faq: string[];
  relatedEntryPageInfos: RelatedEntryPageInfo[];
}
