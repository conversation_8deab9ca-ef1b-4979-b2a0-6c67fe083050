/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-08 10:25
 * @description 用户额度信息
 */
export default interface UserLimitInfo {
  //总额度
  totalCredit: string;

  //已用额度
  usedCredit: string;

  //可用额度
  availableCredit: string;

  //固定额度
  fixedCredit: string;

  //临时额度
  contingentCredit: string;

  //已提升额度
  promotedCredit: string;

  //额度历史开关
  detailSwitch: false;

  //冻结额度
  frozenCredit: string;

  //Installment产品标签
  // installmentTags: string[];

  //通用额度
  commonCredit: string;

  //临时额度失效时间
  contingentCreditExpireTime: string;

  //补充资料提额额度
  furtherInfoCredit: string;

  //是否展示
  // display: boolean;

  //PayLater可用额度
  payLaterAvailableCredit: string;

  //PayLater产品标签
  // payLaterTags: string[];

  //免息总额度
  payLaterTotalCredit: string;

  //分期总额度
  installmentTotalCredit: string;

  //分期可用额度
  installmentAvailableCredit: string;

  //是否有临时额度
  hasContingentCredit: boolean;

  //已用临时额度
  usedContingentCredit: string;

  //可用临时额度
  availableContingentCredit: string;
  // 是否显示免息额度
  // showPayLater?: boolean;

  contingentEquityDetail?: ContingentEquityDetail | null; // 临额权益卡片信息【为空时不出现引导】
}

export interface ContingentEquityDetail {
  status: number;
  expireTime: string | null;
  usedContingentCredit: string;
}
