import React, { Component } from "react";
import {
  Animated,
  BackHandler,
  EmitterSubscription,
  Image,
  ImageBackground,
  NativeEventSubscription,
  Linking,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Text,
  TouchableOpacity,
  View
} from "react-native";
import { TFunction } from "i18next";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import { Category } from "../model/IncreaseLimitInfo";
import { NativeNavigationModule } from "common/nativeModules";
import IncreaseLimitGroup from "../component/IncreaseLimitGroup";
import HorizontalItem from "./component/HorizontalItem";
import styles from "./styles";
import { ADTitle, ADImage } from "common/components/ADGroup/BottomAD";
import { AkuNativeEventEmitter } from "common/nativeModules";
import { getCurrentScreen } from "common/constant";
import { ScreenEventName } from "common/nativeModules/emitter/nativeEventEmitter";
import _, { debounce, get, isNil } from "lodash";
import { ScreenSensorEvent } from "common/nativeModules/sdk/nativeSensroModule";
import { PriceComponent, WINDOW_WIDTH, NetworkErrorComponent, NAV_BAR_HEIGHT, Android } from "@akulaku-rn/akui-rn";
import ActivityAdPopup from "common/components/ADGroup/ActivityAdPopup";
import { ResAD } from "common/components/ADGroup/helpers/types";
import { NativeNavigationModuleModel } from "common/nativeModules/router/nativeNavigationModule";
import { runtimeType } from "common/components/BaseContainer/Type";
import RuntimeStore from "common/store/Runtime";
import {
  ExposeEvent,
  HOCExposerScrollView,
  PageConfig,
  reportClick as reportClickV4,
  reporter,
  ScrollViewExposer
} from "@akulaku-rn/rn-v4-sdk";
import { reportClick } from "common/components/ADGroup/helpers";
import CountDown from "../component/CountDown/CountDown";
import { TimeFormat } from "common/util";
import { getAdList } from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/helpers";
import Svg, { Polyline } from "react-native-svg";
import HorizontalListAd from "./component/HorizontalListAd";
import NumberRoller from "./component/NumberRoller";
import IncreaseLimitStoreV2 from "./store";
import CreditLineCenter from "./eventTracking";
import DefaultComponent from "./component/DefaultComponent";
import TipsCards from "./component/tipsCards";
import NavTopBar from "./component/NavTopBar";
import PayLaterPremiumCard from "./component/PayLaterPremium";
import { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import { DialogContainer } from "@akulaku-rn/akulaku-ec-common/src/components/DialogContainer";
import HintDailog from "./component/HintDailog";

interface NavParams {
  gobackNum: number;
  screen: string;
}

type Props = {
  navigation: NativeNavigationModuleModel;
  store: { pageStore: IncreaseLimitStoreV2; runtime: RuntimeStore & runtimeType; navParams: NavParams };
  t: TFunction;
  configSensorEvent: (event: ScreenSensorEvent) => void;
  // configPageInfo: (config: PageConfig, isSupportV3: boolean) => void;
  configPageInfo: (config: PageConfig, isSupportV3: boolean, isInit?: boolean) => void;
};

type State = {
  tipsAdsData: ResAD[] | [];
  isShowTips: boolean;
};

@RootView({
  withI18n: [
    "increase-limit",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json")
    }
  ],
  store: IncreaseLimitStoreV2
})
@withTranslation("increase-limit")
@inject("store")
@observer
export default class IncreaseLimitHomeV2 extends Component<Props, State> {
  listOffsetY: Animated.Value;

  exposer: ScrollViewExposer;

  screenEventListener?: EmitterSubscription;

  BackHandlerListener?: NativeEventSubscription;

  timer?: number;

  offY = 0;

  constructor(props: Props) {
    super(props);
    this.screenEventListener = AkuNativeEventEmitter.addListener(getCurrentScreen(), this.handleScreenEvent);
    this.props.store.runtime.setPaddingBottom(false);
    this.props.configSensorEvent &&
      this.props.configSensorEvent({
        page_id: "912",
        page_name: "credit limit detail page"
      });
    this.state = {
      tipsAdsData: [],
      isShowTips: true
    };
    this.listOffsetY = new Animated.Value(0);
    this.listOffsetY.addListener(v => {
      this.offY = v.value;
    });
    //v4埋点
    props.configPageInfo({ sn: 100630 }, true);

    this.exposer = new ScrollViewExposer({
      reporter: {
        expose: (event: ExposeEvent) => {
          reporter.expose(event);
          //V4曝光同时上报神策
          event.li.map(e => {
            const cn = get(e, "cn", 0);
            if (cn >= 14 && cn <= 17) {
              const adData = this.props.store.pageStore.adDataMap.get(cn);
              adData && CreditLineCenter.onExposeAd(cn - 14, adData);
            }
          });
        }
      }
    });
  }

  componentDidMount() {
    this.fetchData();
    ActivityAdPopup.show({
      adData: { adGroupId: 199 },
      imgWidth: 300,
      imgHeight: 345,
      adExpose: this.activityAdExpose,
      adClick: this.activityAdClick
    });
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    this.screenEventListener && this.screenEventListener.remove();
  }

  handleScreenEvent = debounce(
    (event: { eventName: ScreenEventName }) => {
      if (event.eventName === ScreenEventName.ON_ENTER) {
        if (this.exposer) {
          this.exposer.caculateExposeItems();
          this.exposer.startExpose();
        }
        this.BackHandlerListener = BackHandler.addEventListener("hardwareBackPress", () => {
          this.onBackPress();
          return true;
        });
      } else if (event.eventName === ScreenEventName.ON_LEAVE) {
        this.exposer.endExpose();
        this.BackHandlerListener && this.BackHandlerListener.remove();
      }
    },
    100,
    {
      leading: true,
      trailing: false
    }
  );

  activityAdExpose = (adData?: ResAD) => {
    CreditLineCenter.onExposeActivityAd(adData);
    const v4data = reporter.generateExposeEvent([
      {
        cn: 10,
        eit: 2,
        ei: {
          spot_id: get(adData, "spotId", ""),
          ad_id: get(adData, "id", ""),
          image_id: get(adData, "creatives[0].id", "")
        }
      }
    ]);
    reporter.expose(v4data);
  };

  activityAdClick = (adData?: ResAD) => {
    const url: string = _.get(adData, "creatives[0].destUrl");
    if (url.includes("play.google.com")) {
      Linking.canOpenURL("http://play.google.com/store/apps/details?id=io.silvrr.installment")
        .then(supported => {
          if (supported) {
            Linking.openURL("http://play.google.com/store/apps/details?id=io.silvrr.installment");
          } else {
            NativeNavigationModule.navigate({
              url: "https://play.google.com/store/apps/details?id=io.silvrr.installment"
            });
          }
        })
        .catch(err => console.error("", err));
    } else {
      NativeNavigationModule.navigate({ url: url });
    }
    CreditLineCenter.onClickActivityAd(adData);
    reportClickV4({
      sn: 100630,
      cn: 10,
      bct: 8,
      bi: {
        spot_id: get(adData, "spotId", ""),
        ad_id: get(adData, "id", ""),
        image_id: get(adData, "creatives[0].id", "")
      }
    });
  };

  fetchData = async () => {
    const {
      store: { pageStore }
    } = this.props;
    const data = await getAdList(333); // 获取顶部广告
    this.setState({ tipsAdsData: data });
    pageStore.fetchData().then(() => {
      this.props.configPageInfo(
        {
          sn: 100630
        },
        true
      );
    });
  };

  onUsedPress = () => {
    reportClickV4({
      cn: 2,
      sn: 100630
    });
    CreditLineCenter.onUsedClick();
    this.jump2IncreaseLimitDetail();
  };

  onRightPress = () => {
    reportClickV4({
      cn: 9,
      sn: 100630
    });
    CreditLineCenter.onClickRightTitle();
    this.jump2IncreaseLimitDetail();
  };

  // 关闭额度中心顶部广告提示
  onCloseTips = () => {
    this.setState({ isShowTips: false });
  };

  // 跳转广告详情
  onToTipsInfoPage = () => {
    reportClick(this.state.tipsAdsData[0].id);
    CreditLineCenter.onClickTipsAdsCard(this.state.tipsAdsData[0]);
    NativeNavigationModule.navigate({ url: this.state.tipsAdsData[0].creatives[0].destUrl });
  };

  onBackPress = () => {
    const {
      store: {
        navParams: { gobackNum }
      }
    } = this.props;
    CreditLineCenter.onClickBack();
    if (!isNil(gobackNum)) {
      NativeNavigationModule.popTo(parseInt(gobackNum + "") + 1);
    } else {
      NativeNavigationModule.goBack();
    }
  };

  jump2IncreaseLimitDetail = (initialPage?: number) => {
    this.props.navigation.navigate({
      screen: "IncreaseLimitDetail",
      params: {
        initialPage: initialPage
      }
    });
  };

  callbackfn = (categoryItem: Category, index: number) => {
    return (
      <>
        {index ? <View style={styles.IncreaseLimitGroupDivider} /> : null}
        <IncreaseLimitGroup
          key={`${index}`}
          category={categoryItem}
          countryCode={this.props.store.runtime.countryCode}
          {...this.props}
        />
      </>
    );
  };

  //底部广告位V4点击埋点 (曝光字段在广告组件内部处理,因为数据在组件内获取)
  getADV4ClickData = (index: number, adData: ResAD) => {
    return {
      sn: 100630,
      cn: +`${14 + index}`,
      bct: 8,
      bi: {
        spot_id: get(adData, "spotId", ""),
        ad_id: get(adData, "id", ""),
        image_id: get(adData, "creatives[0].id", "")
      }
    };
  };

  renderBottomAD = () => {
    const { pageStore } = this.props.store;
    const columnWidth = (WINDOW_WIDTH - 16 - 18 - 6) / 2; /* 一拖二广告位 左间距16 右间距18 列间隔6 */
    const viewStyles = !!this.props.store.pageStore.adDataMap.size ? {} : { padding: 0 };
    return (
      <>
        <View style={[styles.bottomADContainer, viewStyles]}>
          {!this.props.store.pageStore.adDataMap.size ? (
            <Image
              style={styles.bottomDefaultImage}
              source={{
                uri:
                  "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ak_ec_user/default_image/icon_uncredited.webp"
              }}
            />
          ) : null}
          <ADTitle
            adGroupId={221}
            style={styles.bottomADTitleText}
            cn={14}
            onClick={adData => {
              CreditLineCenter.onClickAD(0, adData, false);
              this.exposer.click(this.getADV4ClickData(0, adData));
            }}
            saveAdData={pageStore.saveAdData}
            uniqueItemKey={"increase limit scroll view"}
            identifier={"bottom ad banner 1"}
          />
          <View style={styles.bottomADLeftWrapper}>
            <ADImage
              adGroupId={222}
              imgWidth={columnWidth}
              imgHeigh={(columnWidth / 160) * 206}
              style={styles.bottomADLeft}
              cn={15}
              onClick={adData => {
                CreditLineCenter.onClickAD(1, adData);
                this.exposer.click(this.getADV4ClickData(1, adData));
              }}
              saveAdData={pageStore.saveAdData}
              uniqueItemKey={"increase limit scroll view"}
              identifier={"bottom ad banner 2"}
            />
            <View style={styles.bottomADRightWrapper}>
              <ADImage
                adGroupId={223}
                imgWidth={columnWidth}
                imgHeigh={(columnWidth / 160) * 100}
                style={styles.bottomADRight}
                cn={16}
                onClick={adData => {
                  CreditLineCenter.onClickAD(2, adData);
                  this.exposer.click(this.getADV4ClickData(2, adData));
                }}
                saveAdData={pageStore.saveAdData}
                uniqueItemKey={"increase limit scroll view"}
                identifier={"bottom ad banner 3"}
              />
              <ADImage
                adGroupId={224}
                imgWidth={columnWidth}
                imgHeigh={(columnWidth / 160) * 100}
                style={styles.bottomADRight}
                cn={17}
                onClick={adData => {
                  CreditLineCenter.onClickAD(3, adData);
                  this.exposer.click(this.getADV4ClickData(3, adData));
                }}
                saveAdData={pageStore.saveAdData}
                uniqueItemKey={"increase limit scroll view"}
                identifier={"bottom ad banner 4"}
              />
            </View>
          </View>
        </View>
      </>
    );
  };

  onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    this.listOffsetY.setValue(event.nativeEvent.contentOffset.y);
  };

  horizontalListAdOnExpose = (adData: ResAD, index: number) => {
    CreditLineCenter.onExposeHorizontalListAd(adData, index);
  };

  horizontalListOnClick = (adData: ResAD, index: number) => {
    NativeNavigationModule.navigate({ url: adData.creatives[0].destUrl });
    CreditLineCenter.onClickHorizontalListAd(adData, index);
    reportClickV4({
      sn: 100630,
      cn: 13,
      bct: 8,
      bi: {
        spot_id: get(adData, "spotId", ""),
        ad_id: get(adData, "id", ""),
        image_id: get(adData, "creatives[0].id", "")
      },
      ext: {
        positionid: index + 1
      }
    });
  };

  render() {
    const {
      t,
      store: {
        pageStore: { loading, isError, isShowUncredited, userLimitInfo, increaseLimitInfo }
      },
      navigation
    } = this.props;
    let content = null;
    let bottomContent = null;
    if (loading) {
      content = <Loading />;
    } else if (isError) {
      content = (
        <View style={{ marginTop: NAV_BAR_HEIGHT, flex: 1 }}>
          <NetworkErrorComponent
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            buttonText={t("global:刷新")}
            onRetryPress={this.fetchData}
          />
        </View>
      );
    } else if (isShowUncredited) {
      content = (
        <View style={{ marginTop: NAV_BAR_HEIGHT, flex: 1 }}>
          <DefaultComponent
            containerStyle={{ marginHorizontal: 24 }}
            image={
              "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ak_ec_user/default_image/icon_uncredited.webp"
            }
            message={t("您还未授信或授信未通过哦，请您先申请授信~")}
            buttonText={t("申请授信")}
            onRetryPress={() => {
              if (Android) {
                navigation.popToTop();
                NativeNavigationModule.navigate({
                  screen: "AuthorizeCreditApplyInfo"
                });
              } else {
                NativeNavigationModule.navigate({
                  screen: "AuthorizeCreditApplyInfo",
                  params: { gestureEnabled: false },
                  popNumber: 0
                });
              }
            }}
          />
        </View>
      );
    } else {
      bottomContent = (
        <>
          {increaseLimitInfo && increaseLimitInfo.groups && increaseLimitInfo.groups.length > 0 ? (
            <>
              <View style={styles.functionContainer}>
                <Image
                  style={{
                    width: 31,
                    height: 1
                  }}
                  source={require("./images/icon_left_gray_line.webp")}
                />
                <Text style={styles.functionText}>{t("提额专区")}</Text>
                <Image
                  style={{
                    width: 31,
                    height: 1
                  }}
                  source={require("./images/icon_right_gray_line.webp")}
                />
              </View>
              {/**电商授权各授权项入口 */}
              {increaseLimitInfo.groups.map(this.callbackfn)}
            </>
          ) : null}
          {this.renderBottomAD()}
        </>
      );
      content = (
        <View style={{ marginTop: NAV_BAR_HEIGHT }}>
          <HOCExposerScrollView
            uniqKey={"increase limit scroll view"}
            exposer={this.exposer}
            contentContainerStyle={styles.scrollContainer}
            scrollEventThrottle={this.offY > 135 ? 0 : 16}
            onScroll={this.onScroll}
            bounces={false}
            overScrollMode={"never"}
          >
            {this.state.tipsAdsData.length > 0 &&
            this.state.tipsAdsData[0].creatives.length > 0 &&
            this.state.isShowTips ? (
              <TipsCards
                tipsText={this.state.tipsAdsData[0]?.creatives[0]?.slogan ?? ""}
                tipsAdsData={this.state.tipsAdsData[0]}
                onClose={this.onCloseTips}
                onTipsPress={this.onToTipsInfoPage}
                adsId={this.state.tipsAdsData[0].id}
              />
            ) : null}
            {this.LimitHeader()}
            <HorizontalListAd
              adGroup={201}
              imageWidth={WINDOW_WIDTH - 110}
              imageHeight={(WINDOW_WIDTH - 110) * 0.56}
              onExpose={this.horizontalListAdOnExpose}
              onClick={this.horizontalListOnClick}
            />
            {bottomContent}
          </HOCExposerScrollView>
        </View>
      );
    }
    return (
      <View style={styles.container}>
        <NavTopBar
          right={t("额度明细")}
          onRightPress={this.onRightPress}
          title={t("提额专区")}
          animatedOffsetY={this.listOffsetY}
          onBackPress={this.onBackPress}
        />
        {content}
      </View>
    );
  }

  deadLine = () => {
    const {
      t,
      store: {
        runtime: { countryCode },
        pageStore: { userLimitInfo }
      }
    } = this.props;
    const timeStamp = Date.parse(new Date().toString());
    if (+userLimitInfo.availableContingentCredit <= 0) {
      return null;
    }
    // eslint-disable-next-line eqeqeq
    if (timeStamp < +userLimitInfo.contingentCreditExpireTime && +userLimitInfo.availableContingentCredit == 0) {
      //未过期且已用完
      return null;
    } else {
      if (
        +userLimitInfo.contingentCreditExpireTime > timeStamp &&
        +userLimitInfo.contingentCreditExpireTime - timeStamp < 24 * 60 * 60 * 1000
      ) {
        return <CountDown deadLine={+userLimitInfo.contingentCreditExpireTime - timeStamp} />;
      } else {
        if (+userLimitInfo.contingentCreditExpireTime - timeStamp > 0) {
          return (
            <Text style={styles.textTimeTilte}>
              {t("失效日期：") +
                TimeFormat(new Date(+userLimitInfo.contingentCreditExpireTime), "YYYY-MM-DD", countryCode)}
            </Text>
          );
        } else {
          return (
            <Text style={styles.textTime}>
              {t("失效日期：") +
                TimeFormat(new Date(+userLimitInfo.contingentCreditExpireTime), "YYYY-MM-DD", countryCode)}
            </Text>
          );
        }
      }
    }
  };

  available = () => {
    const {
      t,
      store: {
        runtime: { countryId },
        pageStore: { userLimitInfo }
      }
    } = this.props;
    const timeStamp = Date.parse(new Date().toString());
    if (timeStamp < +userLimitInfo.contingentCreditExpireTime && +userLimitInfo.availableContingentCredit > 0) {
      //未过期且可用额度>0
      return (
        <View>
          <Text style={styles.usableText}>{t("可用")}</Text>
          <Text style={styles.usableAmount}>
            {PriceComponent.priceFormat(userLimitInfo.availableContingentCredit, countryId, { needUnit: true })}
          </Text>
        </View>
      );
    } else if (timeStamp < +userLimitInfo.contingentCreditExpireTime && +userLimitInfo.availableContingentCredit <= 0) {
      //未过期且可用额度=0
      return (
        <Text style={styles.allAmountTitle}>
          {t("共RpXX，已用完", {
            X: PriceComponent.priceFormat(userLimitInfo.contingentCredit, countryId, { needUnit: true })
          })}
        </Text>
      );
    } else {
      return (
        <Text style={styles.allAmountTitle}>
          {t("已透支Rp{{X}}，还清后恢复额度", {
            X: PriceComponent.priceFormat(userLimitInfo.usedContingentCredit, countryId, { needUnit: true })
          })}
        </Text>
      );
    }
  };

  //额度头部
  LimitHeader = () => {
    const {
      t,
      store: {
        runtime: { countryCode, countryId },
        pageStore: { userLimitInfo, isShowAvailable, complianceConfig }
      }
    } = this.props;
    const timeStamp = Date.parse(new Date().toString());

    const right_x = WINDOW_WIDTH - 16;
    const center_x = WINDOW_WIDTH / 2;
    const priceNumber = PriceComponent.priceFormat(userLimitInfo.availableCredit, countryId, {
      needUnit: false
    });
    const arrNumber = Array.from(priceNumber);

    const isShowLimitInfo =
      timeStamp >= +userLimitInfo.contingentCreditExpireTime && +userLimitInfo.usedContingentCredit === 0;
    return (
      <View style={{ alignItems: "center" }}>
        <View style={styles.bnplView}>
          <View style={styles.bnplGroup}>
            <Text style={styles.availableCredit}>{t("可用额度") + "(Rp)"}</Text>
            <View style={styles.contextView}>
              <TouchableOpacity
                onPress={() => {
                  CreditLineCenter.onClickAvailableCredit();
                  this.jump2IncreaseLimitDetail();
                }}
              >
                {priceNumber === "0" || !isShowAvailable ? (
                  <Text style={styles.availableCreditValue}>{priceNumber}</Text>
                ) : (
                  <View style={{ flexDirection: "row", justifyContent: "center" }}>
                    {arrNumber.map((item, index) => {
                      return (
                        <NumberRoller
                          key={index.toString() + item}
                          isUp={!(index % 2)}
                          originalNuber={item}
                          currentNumber={parseInt(item)}
                        />
                      );
                    })}
                  </View>
                )}
              </TouchableOpacity>
              {userLimitInfo.showNewCustomerTip && !complianceConfig.is_new ? (
                <TouchableOpacity
                  style={styles.imageIcon}
                  onPress={() => {
                    DialogContainer.show({
                      renderContent: <HintDailog t={t} onClick={() => DialogContainer.dismiss()} />
                    });
                  }}
                >
                  <Image style={styles.imageIcon} source={require("./images/icon_hint.webp")} />
                </TouchableOpacity>
              ) : null}
            </View>
          </View>
          <Svg width={"100%"} height={"5"}>
            <Polyline
              points={`16,5 ${center_x - 5},5 ${center_x},0 ${center_x + 5},5 ${right_x},5`}
              stroke="#E2E5E9"
              strokeWidth="0.5"
              fill="#00000000"
            />
          </Svg>
          <View style={styles.horizonViewBg}>
            <View style={styles.horizontalItemView}>
              <HorizontalItem
                textTitle={t("总额度")}
                textAmount={PriceComponent.priceFormat(userLimitInfo.totalCredit, countryId, { needUnit: true })}
              />
            </View>
            <View style={styles.horizontalItemDivider} />
            <View style={styles.horizontalItemView}>
              <HorizontalItem
                textTitle={t("已用额度")}
                textAmount={PriceComponent.priceFormat(userLimitInfo.usedCredit, countryId, { needUnit: true })}
                onPress={this.onUsedPress}
              />
            </View>
          </View>

          {//已过期且已用额度为0,不展示临时额度模块,否则展示
          isShowLimitInfo ? null : (
            <ImageBackground source={require("../img/limit_bg.webp")} style={styles.limitbg}>
              <View style={styles.limitView}>
                <Text style={styles.limitTitle}>{t("临时额度")}</Text>
                {this.deadLine()}
              </View>
              <View style={styles.limitView}>
                {this.available()}
                {timeStamp < +userLimitInfo.contingentCreditExpireTime &&
                +userLimitInfo.availableContingentCredit > 0 ? (
                  <Text style={styles.allLimitAmount}>
                    {t("共Rp{{X}}", {
                      X: PriceComponent.priceFormat(userLimitInfo.contingentCredit, countryId, { needUnit: true })
                    })}
                  </Text>
                ) : null}
              </View>
            </ImageBackground>
          )}
          {!!userLimitInfo.contingentEquityDetail ? (
            <PayLaterPremiumCard
              t={t}
              isShowLimitInfo={isShowLimitInfo}
              status={userLimitInfo.contingentEquityDetail?.status}
              usedContingentCredit={
                userLimitInfo.contingentEquityDetail?.usedContingentCredit === null
                  ? "0"
                  : userLimitInfo.contingentEquityDetail?.usedContingentCredit
              }
              countryId={countryId}
              expireTime={
                userLimitInfo.contingentEquityDetail?.expireTime === null
                  ? 0
                  : parseInt(userLimitInfo.contingentEquityDetail?.expireTime)
              }
            />
          ) : null}
        </View>
        {isShowAvailable ? this.quotaAvailable() : null}
      </View>
    );
  };

  quotaAvailable = () => {
    const {
      store: {
        pageStore: { userLimitInfo, updateisShowAvailable },
        runtime: { countryId, countryCode }
      }
    } = this.props;

    this.timer && clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      updateisShowAvailable();
    }, 5000);

    const adjustDetail = userLimitInfo.adjustDetail;
    if (adjustDetail !== null && adjustDetail !== undefined) {
      return (
        <View style={styles.availableView}>
          <TouchableOpacity
            onPress={() => {
              CreditLineCenter.onClickQuotaAvailable();
              this.jump2IncreaseLimitDetail(1);
            }}
          >
            <View style={styles.paylaterArrow} />
            <View style={styles.available}>
              <Text style={styles.availableValue}>
                {"+" + PriceComponent.priceFormat(adjustDetail?.credit, countryId, { needUnit: true }) + " "}
                <Text style={[styles.availableValue, { fontWeight: "400" }]}>
                  {TimeFormat(
                    new Date(adjustDetail?.time ? +adjustDetail?.time : new Date()),
                    "MM/DD/YYYY",
                    countryCode
                  )}
                </Text>
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      );
    } else {
      return null;
    }
  };
}
