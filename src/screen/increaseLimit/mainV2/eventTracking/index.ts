import { ResAD } from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/helpers/types";
import NativeSensorModule, { SensorEvent, SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { get } from "lodash";

const baseParams: SensorEvent = {
  page_name: "credit limit detail page",
  page_id: "912"
};

const CreditLineCenter = {
  onClickTipsAdsCard: (adData: ResAD): void => {
    // 顶部广告的点击上报
    NativeSensorModule.reportSence(SensorType.CLICK, {
      element_id: "9120116",
      element_name: "ad",
      module_name: "credit limit",
      module_id: "01",
      position_id: "16",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", "") //标签id
      },
      ...baseParams
    });
  },
  onExposeTipsAdsCard: (adData: ResAD): void => {
    NativeSensorModule.reportSence(SensorType.EXPOSE, {
      element_id: "9120116",
      element_name: "ad",
      module_name: "credit limit",
      module_id: "01",
      position_id: "16",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", "") //标签id
      },
      ...baseParams
    });
  },
  onClickAD: (index: number, adData: ResAD, isImage = true): void => {
    // 顶部广告的点击上报
    NativeSensorModule.reportSence(SensorType.CLICK, {
      element_id: `${9120112 + index}`,
      element_name: `banner${1 + index}`,
      module_id: "01",
      module_name: "credit limit",
      position_id: `${12 + index}`,
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: isImage ? get(adData, "creatives[0].id", "") : "" //图片id
      },
      ...baseParams
    });
  },
  onExposeAd: (index: number, adData: ResAD, isImage = true): void => {
    // 顶部广告的点击上报
    NativeSensorModule.reportSence(SensorType.EXPOSE, {
      element_id: `${9120112 + index}`,
      element_name: `banner${1 + index}`,
      module_id: "01",
      module_name: "credit limit",
      position_id: `${12 + index}`,
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: isImage ? get(adData, "creatives[0].id", "") : "" //图片id
      },
      ...baseParams
    });
  },
  onExposeActivityAd: (adData?: ResAD): void => {
    NativeSensorModule.reportSence(SensorType.POPVIEW, {
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: get(adData, "creatives[0].id", ""), //图片id
        pop_id: "pop30097",
        pop_name: "credit limit-popup"
      },
      ...baseParams
    });
  },
  onClickActivityAd: (adData?: ResAD): void => {
    NativeSensorModule.reportSence(SensorType.POPCLICK, {
      element_id: "pop3009701",
      element_name: "ad",
      position_id: "01",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: get(adData, "creatives[0].id", ""), //图片id
        pop_id: "pop30097",
        pop_name: "credit limit-popup"
      },
      ...baseParams
    });
  },
  onUsedClick: (): void => {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      element_id: "9120104",
      element_name: "used",
      module_id: "01",
      module_name: "credit limit",
      position_id: "04",
      ...baseParams
    });
  },
  onClickRightTitle: (): void => {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      element_id: "9120101",
      element_name: "amount details",
      module_id: "01",
      module_name: "credit limit",
      position_id: "01",
      ...baseParams
    });
  },
  onClickBack: (): void => {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      element_id: "9120102",
      element_name: "return",
      module_id: "01",
      module_name: "credit limit",
      position_id: "02",
      ...baseParams
    });
  },
  onExposeHorizontalListAd: (adData: ResAD, index: number): void => {
    NativeSensorModule.reportSence(SensorType.EXPOSE, {
      ...baseParams,
      element_id: "9120111",
      element_name: "banner",
      module_id: "01",
      module_name: "credit limit",
      position_id: "11",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: get(adData, "creatives[0].id", ""), //图片id
        positionid: index + 1
      }
    });
  },
  onClickHorizontalListAd: (adData: ResAD, index: number): void => {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      ...baseParams,
      element_id: "9120111",
      element_name: "banner",
      module_id: "01",
      module_name: "credit limit",
      position_id: "11",
      extra: {
        Aku_links: get(adData, "creatives[0].destUrl", ""), //跳转地址
        Aku_adPositionid: get(adData, "spotId", ""), //广告位id
        Aku_advertisingID: get(adData, "id", ""), //广告id
        Aku_label_id: get(adData, "tagId", ""), //标签id
        Aku_image_id: get(adData, "creatives[0].id", ""), //图片id
        positionid: index + 1
      }
    });
  },
  onClickQuotaAvailable: (): void => {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      ...baseParams,
      element_id: "9120118",
      element_name: "credit record",
      module_id: "01",
      module_name: "credit limit",
      position_id: "18"
    });
  },
  onClickAvailableCredit: (): void => {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      element_id: "9120117",
      element_name: "available credit",
      module_id: "01",
      module_name: "credit limit",
      position_id: "17",
      ...baseParams
    });
  },
  onClickGet: (): void => {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      element_id: "9120501",
      element_name: "get",
      module_id: "05",
      module_name: "large online",
      position_id: "01",
      ...baseParams
    });
  },
  onClickToUse: (): void => {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      element_id: "9120502",
      element_name: "to use",
      module_id: "05",
      module_name: "large online",
      position_id: "02",
      ...baseParams
    });
  },
  onClickQuestion: (): void => {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      element_id: "9120503",
      element_name: "question",
      module_id: "05",
      module_name: "large online",
      position_id: "03",
      ...baseParams
    });
  }
};

export default CreditLineCenter;
