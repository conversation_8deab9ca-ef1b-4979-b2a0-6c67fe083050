import React, { Component } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { CenterViewContainer } from "common/components/DialogContainer";
import { TFunction } from "i18next";
import NativeToast from "@akulaku-rn/akulaku-ec-common/src/nativeModules/basics/nativeUIModule/Toast";
import FontStyles from "@akulaku-rn/akui-rn/src/components/FontUtil/FontStyles";

const styles = StyleSheet.create({
  content: {
    fontSize: 14,
    color: "#282B2E",
    marginTop: 32,
    alignSelf: "center",
    textAlign: "center",
    marginHorizontal: 24
  },
  confirm: {
    ...FontStyles["rob-medium"],
    fontSize: 16,
    color: "#F32823",
    alignSelf: "center",
    paddingVertical: 15
  },
  divider: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: "#E2E5E9",
    marginTop: 24
  }
});

type HintDailogProps = {
  t: TFunction;
  onClick?: () => void;
};

const HintDailog = (props: HintDailogProps): JSX.Element => {
  const { t, onClick } = props;
  return (
    <CenterViewContainer contentStyle={{ borderRadius: 12 }}>
      <Text style={styles.content}>
        {t(
          "先买后付服务由PT Fintek Digital Indonesia (Kredito) 提供，该公司由 Otoritas Jasa Keuangan许可并接受其监管。"
        )}
        {/**合规整改V2暂时屏蔽 */}
        {/* <Text
          onPress={() => {
            NativeToast.showMessage(t("你的AFI额度预授信通过，AFI额度在正式授信完成后可用"));
          }}
        >
          {t("点击查看更多细节")}
        </Text> */}
      </Text>
      <View style={styles.divider} />
      <TouchableOpacity onPress={() => onClick && onClick()}>
        <Text style={styles.confirm}>{t("我知道了")}</Text>
      </TouchableOpacity>
    </CenterViewContainer>
  );
};

export default React.memo(HintDailog);
