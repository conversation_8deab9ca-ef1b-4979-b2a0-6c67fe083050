import { StyleSheet } from "react-native";
import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    width: WINDOW_WIDTH - 32,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 20,
    marginTop: 12
  },
  imageV: {
    width: 156,
    height: 100,
    position: "absolute",
    top: 0,
    right: 0
  },
  imageIcon: {
    width: 16,
    height: 16,
    marginRight: 6
  },
  title: {
    color: "#282B2E",
    fontSize: 14,
    ...FontStyles["roboto-bold"],
    marginRight: 6
  },
  hintText: {
    fontSize: 14,
    color: "#6E737D"
  },
  timeTitle: {
    fontSize: 12,
    color: "#282B2E"
  },
  waitingHint: {
    color: "#282B2E",
    fontSize: 12,
    marginBottom: 16
  },
  akButton: {
    width: WINDOW_WIDTH - 64,
    height: 32
  },
  timeView: {
    flexDirection: "row",
    marginTop: 18,
    marginBottom: 10
  }
});
