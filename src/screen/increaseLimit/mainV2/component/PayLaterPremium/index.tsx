import AKButton, { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { TFunction } from "i18next";
import React from "react";
import { View, TouchableOpacity, Text, Image } from "react-native";
import LinearGradient from "react-native-linear-gradient";
import styles from "./styles";
import { EquityStatus } from "../../constents/index";
import AKDialog, { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import NativeNavigationModule from "@akulaku-rn/akulaku-ec-common/src/nativeModules/router/nativeNavigationModule";
import CountDown from "../CountDown/CountDown";
import PriceComponent from "@akulaku-rn/akui-rn/src/components/PriceComponent";
import CreditLineCenter from "../../eventTracking";
import { iOS } from "@akulaku-rn/akui-rn";

type IProps = {
  t: TFunction;
  isShowLimitInfo: boolean;
  status: EquityStatus;
  onClickDrawDown?: () => void;
  onClickUsed?: () => void;
  expireTime: number;
  countryId: number;
  usedContingentCredit?: string;
};

const PayLaterPremiumCard: React.FC<IProps> = (props: IProps) => {
  const {
    t,
    isShowLimitInfo,
    status,
    onClickDrawDown,
    onClickUsed,
    expireTime,
    countryId,
    usedContingentCredit
  } = props;
  let content = null;
  const onClickTips = () => {
    AKDialog.show({
      type: DialogType.C3_1_1,
      desc: t("Paylater Premiun额度仅线上CicilMall 3-20jutajuta的商品可用"),
      positiveText: t("知道了")
    });
    CreditLineCenter.onClickQuestion();
  };

  switch (status) {
    case EquityStatus.WAITING:
      content = (
        <>
          <Text style={styles.waitingHint}>
            {t("你有个Paylater Premium的领取资格，额度最高20JT，快来看看能领多少额度吧")}
          </Text>
          <AKButton
            onPress={() => {
              CreditLineCenter.onClickGet();
              // 因为原生IOS和android的首页tab实现不一样，所以这里需要做差异
              NativeNavigationModule.popToHome(iOS ? 2 : 4);
              onClickDrawDown && onClickDrawDown();
            }}
            text={t("领取")}
            type={AKButtonType.B1_1_2}
            style={styles.akButton}
          />
        </>
      );
      break;
    case EquityStatus.USED:
      content = (
        <TouchableOpacity
          onPress={() => {
            CreditLineCenter.onClickToUse();
            NativeNavigationModule.popToHome(iOS ? 2 : 4);
            onClickDrawDown && onClickDrawDown();
          }}
        >
          <Text style={styles.hintText}>{t("最高20JT|最长12期|0首付")}</Text>
          <View style={styles.timeView}>
            <Text style={styles.timeTitle}>{t("权益有效期")}</Text>
            <CountDown endTime={expireTime} />
          </View>
          <AKButton
            onPress={() => {
              NativeNavigationModule.popToHome(iOS ? 2 : 4);
              onClickUsed && onClickUsed();
            }}
            text={t("去使用")}
            type={AKButtonType.B1_1_2}
            style={styles.akButton}
          />
        </TouchableOpacity>
      );
      break;
    case EquityStatus.PENDING_REPAYMENT:
      content = (
        <>
          <Text style={[styles.waitingHint, { marginBottom: 0 }]}>
            {t("已超额使用,还清超额使用额度后,可开始恢复可用额度", {
              X: PriceComponent.priceFormat(usedContingentCredit ?? "0", countryId, { needUnit: true })
            })}
          </Text>
        </>
      );
      break;
  }

  return (
    <LinearGradient
      start={{ x: 0.25, y: 0.25 }}
      end={{ x: 0.75, y: 0.75 }}
      colors={["#FFF2F0", "#EBEDFF"]}
      style={[styles.container, { marginTop: isShowLimitInfo ? 20 : 12 }]}
    >
      <Image style={styles.imageV} source={require("../../images/icon_v.webp")} />
      <View style={{ flexDirection: "row" }}>
        <Image style={styles.imageIcon} source={require("../../images/icon_paylater_premium.webp")} />
        <Text style={styles.title}>{t("Paylater Premium")}</Text>
        <TouchableOpacity
          onPress={() => {
            onClickTips();
          }}
        >
          <Image style={styles.imageIcon} source={require("../../images/icon_tips.webp")} />
        </TouchableOpacity>
      </View>
      <View style={{ marginTop: 10 }}>{content}</View>
    </LinearGradient>
  );
};

export default PayLaterPremiumCard;
