import React, { Component } from "react";
import { TextStyle, View, ViewStyle } from "react-native";
import Piece from "./Piece";
import Tick from "./Ticker";
import styles from "./styles/index.styles";

interface TickerProps {
  tickerNum: number;
  height: number;
  text: string;
  textStyle: TextStyle;
  rotateTime: number;
  style: ViewStyle;
}

class Ticker extends Component<TickerProps> {
  static defaultProps = {
    // 默认滚动时间
    rotateTime: 3000,
    tickerNum: 3,
    height: 20
  };

  constructor(props: TickerProps) {
    super(props);
  }

  render() {
    // 获取文本内容,子组件,样式,滚动时长
    const { height, text, textStyle, style, rotateTime, tickerNum } = this.props;

    //绘制了一个隐藏的text组件，是为了测量在当前样式下，绘制出的数字高度值
    return (
      <View style={[styles.row, { height }, style]}>
        {stringNumberRenderer(height, tickerNum, text, textStyle, rotateTime, resourceData)}
      </View>
    );
  }
}

//我们需要对当前的文本做切割得到包含文本中每个字符的字符数组，
//遍历切割后的字符数组，取出每一个字符，判断是否是数字，不是数字就直接绘制文本，
//Piece是封装的直接用text进行文本绘制的组件，如果是数字就绘制数字动画组件，
//Tick是封装的单个数字动画绘制的组件。
const stringNumberRenderer = (
  height: number,
  tickerNum: number,
  textContext: string,
  textStyle: any,
  rotateTime: number,
  rotateItems: string[]
) => {
  //传入的数字总长度
  const length = textContext.length;
  const rollLength = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];

  // 切割子组件文本内容遍历
  return splitText(textContext).map((piece, i) => {
    //取单个字符，如果不是数字,直接绘制文本
    if (!isNumber(piece)) {
      return <Piece key={i} style={{ height }} textStyle={textStyle} textTilte={piece} height={height} />;
    } else {
      return (
        <Tick
          rollNum={i}
          duration={rotateTime}
          key={i}
          text={piece}
          textStyle={textStyle}
          height={height}
          rotateItems={rotateItems}
        />
      );
    }
    // if (!isNumber(piece))
    //   return <Piece key={i} style={{ height }} textStyle={textStyle} textTilte={piece} height={height} />;

    // //前面数字不滚动，直接显示，在交界点显示一个“，”号，然后只滚动后面的tickerNum位数数字
    // if (length > tickerNum) {
    //   // 这里控制前面几个数字不滚动
    //   //前面数字不滚动，直接显示
    //   if (i < length - tickerNum) {
    //     return <Piece key={i} style={{ height }} textStyle={textStyle} textTilte={piece} height={height} />;
    //   } else {
    //     return (
    //       <Tick
    //         rollNum={rollLength[i - (length - tickerNum)]}
    //         duration={rotateTime}
    //         key={i}
    //         text={piece}
    //         textStyle={textStyle}
    //         height={height}
    //         rotateItems={rotateItems}
    //       />
    //     );
    //   }
    // } else {
    //   // 不足三位数字滚动的逻辑
    //   return (
    //     <Tick
    //       rollNum={rollLength[tickerNum - length + i]}
    //       duration={rotateTime}
    //       key={i}
    //       text={piece}
    //       textStyle={textStyle}
    //       height={height}
    //       rotateItems={rotateItems}
    //     />
    //   );
    // }
  });
};

// 指定范围创建数组
const range = (length: number) => Array.from({ length }, (x, i) => i);
// 切割
const splitText = (text = "") => (text + "").split("");
// 是十进制数字判断
const isNumber = (text = "") => !isNaN(parseInt(text));
const isString = (text = "") => typeof text === "string";
// 创建"0","1","2","3","4"..."9"的数组,默认绘制数据
const resourceData = [
  "0",
  "1",
  "2",
  "3",
  "4",
  "5",
  "6",
  "7",
  "8",
  "9",
  "0",
  "1",
  "2",
  "3",
  "4",
  "5",
  "6",
  "7",
  "8",
  "9"
];

export { resourceData as numberRange };
export default Ticker;
