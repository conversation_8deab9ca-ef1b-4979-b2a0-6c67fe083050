import React, { Component } from "react";
import { View, Animated } from "react-native";
import Piece from "./Piece";

type TickPorps = {
  rollNum: number;
  duration: number;
  text: string;
  textStyle: any;
  height: number;
  rotateItems: string[];
};

type TickStatus = {
  animation: Animated.Value;
};

const getAnimationStyle = (animation: Animated.Value) => {
  return {
    transform: [
      {
        translateY: animation
      }
    ]
  };
};

/**
 * getPosition这个方法是用来计算目标数字的y轴坐标值，
 * 根据当前数字在数组中的下标乘以测量出的数字文本绘制高度取负值，得出坐标值。
 * @param {*} param0
 */
const getPosition = ({ text, items, height }: { text: string; items: string[]; height: number }) => {
  // 获得文本在数组的下标
  return parseInt(text) * height * -1;
};

export default class Tick extends Component<TickPorps, TickStatus> {
  constructor(props: TickPorps) {
    super(props);
    /**
     * 创建动画初始值
     * @type {{animation: Animated.Value}}
     */
    this.state = {
      animation: new Animated.Value(
        getPosition({
          text: "" + (parseInt(this.props.text) + 10 - this.props.rollNum),
          items: this.props.rotateItems,
          height: this.props.height
        })
      )
    };
  }

  componentDidMount() {
    const nowValue = parseInt(this.props.text);
    const now = nowValue + 10 - this.props.rollNum;

    const init = getPosition({
      text: "" + now,
      items: this.props.rotateItems,
      height: this.props.height
    });

    this.setState({
      animation: new Animated.Value(init)
    });
  }

  getDerivedStateFromProps(nextProps: any) {
    const nowValue = parseInt(nextProps.text);
    const now = nowValue + 10 - nextProps.rollNum;
    const init = getPosition({
      text: "" + now,
      items: this.props.rotateItems,
      height: this.props.height
    });

    this.setState({
      animation: new Animated.Value(init)
    });
  }

  componentDidUpdate(prevProps: any) {
    const { height, duration, rotateItems, text } = this.props;
    //第一次进来，不执行动画
    const endValue = getPosition({
      text: "" + (parseInt(this.props.text) + 10),
      items: rotateItems,
      height
    });
    // 数字变化,用当前动画值和变化后的动画值进行插值,并启动动画
    Animated.timing(this.state.animation, {
      toValue: endValue,
      duration,
      useNativeDriver: true
    }).start();
  }

  render() {
    const { animation } = this.state;
    const { textStyle, height, rotateItems } = this.props;
    return (
      <View style={{ height }}>
        <Animated.View style={getAnimationStyle(animation)}>
          {/*遍历数字范围数组绘制数字*/}
          {rotateItems.map((v, i) => (
            <Piece key={i} style={{ height }} textStyle={textStyle} textTilte={v} height={height} />
          ))}
        </Animated.View>
      </View>
    );
  }
}
