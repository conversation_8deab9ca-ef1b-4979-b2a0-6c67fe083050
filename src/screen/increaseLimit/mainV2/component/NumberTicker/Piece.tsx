import React from "react";
import { Text, View } from "react-native";

type IProps = {
  textTilte: string | string[];
  style: any;
  height: number;
  textStyle: any;
};

const Piece: React.FC<IProps> = (props: IProps) => {
  const { textTilte, style, height, textStyle } = props;
  return (
    <View style={style}>
      <Text style={[textStyle, { height }]}>{textTilte}</Text>
    </View>
  );
};

export default Piece;
