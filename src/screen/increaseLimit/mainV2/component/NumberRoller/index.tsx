import FontStyles from "@akulaku-rn/akui-rn/src/components/FontUtil/FontStyles";
import React, { useEffect, useRef, useState } from "react";
import { StyleSheet, Text, Animated, Easing } from "react-native";

type IProps = {
  isUp: boolean;
  timeOut?: number;
  currentNumber: number;
  originalNuber: string;
};

const NumberRoller: React.FC<IProps> = (props: IProps) => {
  const [number, setNumber] = useState(props.currentNumber);
  const translateY = useRef(new Animated.Value(0)).current;
  const numberRef = useRef(number);
  const animationRef = useRef<Animated.CompositeAnimation | null>(null);

  useEffect(() => {
    numberRef.current = number; // 保存最新的number值

    const animation = Animated.timing(translateY, {
      toValue: props.isUp ? -0.5 : 0.5,
      duration: 60,
      easing: Easing.linear,
      useNativeDriver: false
    });

    const resetAnimation = Animated.timing(translateY, {
      toValue: 0,
      duration: 0,
      easing: Easing.linear,
      useNativeDriver: false
    });

    const loopAnimation = () => {
      animationRef.current = Animated.sequence([animation, resetAnimation]);

      animationRef.current.start(({ finished }) => {
        if (finished) {
          const newNumber = numberRef.current === 9 ? 0 : numberRef.current + 1;
          setNumber(newNumber);
          numberRef.current = newNumber; // 更新numberRef.current的值

          if (newNumber === props.currentNumber) {
            animationRef.current?.stop();
          } else {
            loopAnimation();
          }
        }
      });
    };

    // 这里之所以没0，0不需要滚动
    const numberText = [1, 2, 3, 4, 5, 6, 7, 8, 9];

    // 是否为数字
    const isNumber = numberText.some(item => item === props.currentNumber);

    if (isNumber) {
      setTimeout(() => {
        loopAnimation();
      }, props.timeOut ?? 0);
    }

    return () => {
      animationRef.current?.stop();
    };
  }, []);
  return (
    <Animated.View style={[styles.numberContainer, { transform: [{ translateY }] }]}>
      <Text style={styles.numberText}>{!isNaN(number) ? number : props.originalNuber}</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center"
  },
  numberContainer: {
    height: 40,
    overflow: "hidden",
    justifyContent: "center"
  },
  numberText: {
    ...FontStyles["roboto-bold"],
    fontSize: 32,
    color: "#282B2E",
    alignSelf: "center"
  }
});

export default NumberRoller;
