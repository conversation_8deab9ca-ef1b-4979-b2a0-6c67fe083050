import AkButton, { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import React, { PureComponent } from "react";
import { Image, View, Text, ImageStyle, ViewStyle } from "react-native";
import styles from "./styles";

export type BaseProps = {
  headTitle?: string;
  message?: string;
  buttonText?: string;
  onRetryPress?: () => void;
  containerStyle?: ViewStyle;
  image?: string;
  imageStyle?: ImageStyle;
  isShowBottomButton?: boolean;
  isFunctionPage?: boolean;
};

export default class DefaultComponent extends PureComponent<BaseProps> {
  renderBottomButton = (): JSX.Element | null => {
    const { onRetryPress, buttonText } = this.props;
    return <AkButton type={AKButtonType.B1_1_3} text={buttonText || ""} onPress={onRetryPress} style={styles.button} />;
  };

  renderImage = () => {
    const { imageStyle, image } = this.props;
    let imageSource = {
      uri: "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ak_ec_user/default_image/icon_uncredited.webp"
    };
    if (!!image) {
      imageSource = { uri: image };
      return <Image style={[styles.image, !!imageStyle && imageStyle]} source={imageSource} />;
    }
    return null;
  };

  render() {
    const { message, containerStyle, headTitle, isFunctionPage } = this.props;
    return (
      <View style={[isFunctionPage ? styles.functionContainer : styles.container, containerStyle]}>
        {this.renderImage()}
        {!!headTitle && <Text style={[styles.headTitle, !message && { marginBottom: 12 }]}>{headTitle}</Text>}
        {!!message && <Text style={styles.message}>{message}</Text>}
        {this.renderBottomButton()}
      </View>
    );
  }
}
