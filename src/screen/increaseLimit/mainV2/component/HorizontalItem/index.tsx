import React from "react";
import { View, TouchableOpacity, Text } from "react-native";
import styles from "./styles";

type IProps = {
  textTitle: string;
  textAmount: string;
  onPress?: () => void;
};

const HorizontalItem: React.FC<IProps> = (props: IProps) => {
  const { textAmount, textTitle, onPress } = props;
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => {
        onPress && onPress();
      }}
    >
      <View style={styles.containerView}>
        <Text style={styles.amount}>{textAmount}</Text>
        <Text style={styles.title}>{textTitle}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default HorizontalItem;
