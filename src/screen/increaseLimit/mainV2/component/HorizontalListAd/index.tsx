import React, { PureComponent } from "react";
import {
  Animated,
  EmitterSubscription,
  Image,
  NativeScrollEvent,
  NativeSyntheticEvent,
  TouchableOpacity,
  View
} from "react-native";
import _ from "lodash";
import {
  ExposeE<PERSON>,
  reporter,
  ScrollViewExposer,
  ExposerScrollViewItem,
  HOCExposerScrollView
} from "@akulaku-rn/rn-v4-sdk";
import { ResAD } from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/helpers/types";
import { getAdList } from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/helpers";
import AkuNativeEventEmitter, {
  ScreenEventName
} from "@akulaku-rn/akulaku-ec-common/src/nativeModules/emitter/nativeEventEmitter";
import { getCurrentScreen } from "@akulaku-rn/akulaku-ec-common/src/constant";
import styles from "./styles";

type State = {
  adList: Array<ResAD>;
  hideAd: boolean; //隐藏广告
  moveX: Animated.Value; //横向移动距离
  index: number; //当前页面
  isFirst: boolean; // 是否第一次加载
};

type Props = {
  /**
   * 广告位id
   */
  adGroup: number;

  /**
   * 广告曝光回调
   */
  onExpose?: (adData: ResAD, index: number) => void;

  /**
   * 广告点击回调
   */
  onClick?: (adData: ResAD, index: number) => void;

  imageWidth: number;

  imageHeight: number;

  imgSpacing?: number;
};

export default class HorizontalListAd extends PureComponent<Props, State> {
  screenEventListener?: EmitterSubscription;

  exposer?: ScrollViewExposer;

  constructor(props: Props) {
    super(props);
    this.state = {
      adList: [],
      hideAd: false,
      moveX: new Animated.Value(0),
      index: 0,
      isFirst: false
    };
  }

  async componentDidMount() {
    const { adGroup, imageWidth, imgSpacing = 8, onExpose } = this.props;
    const data = await getAdList(adGroup);
    if (data && data.length > 0) {
      this.setState({
        adList: data,
        hideAd: false
      });
    } else {
      this.setState({
        hideAd: true
      });
    }
    this.screenEventListener = AkuNativeEventEmitter.addListener(getCurrentScreen(), event => {
      if (event.eventName === ScreenEventName.ON_ENTER) {
        if (this.getExposer()) {
          this.getExposer().caculateExposeItems();
          this.getExposer().startExpose();
        }
      } else if (event.eventName === ScreenEventName.ON_LEAVE) {
        this.getExposer().endExpose();
      }
    });
  }

  private index = 0;

  getExposer = (): ScrollViewExposer => {
    if (this.exposer) return this.exposer;
    this.exposer = new ScrollViewExposer({
      needExtraField: true,
      reporter: {
        expose: (event: ExposeEvent) => {
          reporter.expose(event);
        }
      }
    });
    return this.exposer;
  };

  onMomentumScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { imageWidth, imgSpacing = 8 } = this.props;
    this.index = Math.ceil(event.nativeEvent.contentOffset.x / (imageWidth + imgSpacing));
    this.setState({
      index: this.index
    });
  };

  renderAdItem = (resAd: ResAD, index: number, adList: ResAD[]) => {
    const { imageWidth, imageHeight, imgSpacing = 8, onExpose } = this.props;
    if (!this.state.isFirst) {
      this.setState({
        isFirst: true
      });
      onExpose && onExpose(adList[index], index);
    }
    return (
      <View style={{ backgroundColor: "#FFF" }} key={index}>
        <ExposerScrollViewItem
          identifier={`${index}`}
          uniqItemKey={"horizontalListAd"}
          bizInfo={{
            sn: 100630,
            cn: 13,
            bct: 2,
            ei: {
              spot_id: _.get(resAd, "spotId", ""),
              ad_id: _.get(resAd, "id", ""),
              image_id: _.get(resAd, "creatives[0].id", "")
            },
            ext: {
              index: index
            }
          }}
        >
          <TouchableOpacity
            onPress={() => {
              const { onClick } = this.props;
              const { adList } = this.state;
              onClick && onClick(adList[index], index);
            }}
            activeOpacity={1}
          >
            <Image
              source={{ uri: resAd.creatives[0].image }}
              style={[
                {
                  marginRight: index === adList.length - 1 ? 16 : 0,
                  width: imageWidth,
                  height: imageHeight
                },
                styles.adImage
              ]}
            />
          </TouchableOpacity>
        </ExposerScrollViewItem>
      </View>
    );
  };

  render() {
    const { adList, hideAd } = this.state;
    const { imageWidth, imgSpacing = 8 } = this.props;
    return !hideAd ? (
      <View style={styles.container}>
        <HOCExposerScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          pagingEnabled={true}
          snapToInterval={imageWidth + imgSpacing}
          decelerationRate={"fast"}
          scrollEventThrottle={16}
          style={{
            flexDirection: "row"
          }}
          exposer={this.getExposer()}
          uniqKey={"horizontalListAd"}
          onMomentumScrollEnd={this.onMomentumScrollEnd}
        >
          {adList.map(this.renderAdItem)}
        </HOCExposerScrollView>
      </View>
    ) : null;
  }
}
