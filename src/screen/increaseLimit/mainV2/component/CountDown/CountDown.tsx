import React, { PureComponent } from "react";
import { Text } from "react-native";

interface TimeInfo {
  // 天,一般用来做数学计算的
  day: number;
  //小时,一般用来做数学计算的
  hours: number;
  // 补零后的小时，一般用来做UI显示的字段
  hoursStr: string;
  // 分钟,一般用来做数学计算的
  minutes: number;
  // 补零后的分，一般用来做UI显示的字段
  minutesStr: string;
  // 秒,一般用来做数学计算的
  seconds: number;
  // 补零后的秒，一般用来做UI显示的字段
  secondsStr: string;
  // 毫秒
  milliseconds?: number;
  // 补零后的毫秒
  millisecondsStr?: string;
}

interface IProps {
  endTime: number; // 倒计时的初始时（时间戳）
}

interface IState {
  timeInfo: TimeInfo;
}

export default class CountDown extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    let remainTime = 0;
    const nowTime = Date.now();
    remainTime = ((props.endTime || 0) - nowTime) / 1000;
    this.state = { timeInfo: this.countdown(remainTime) };
  }

  componentDidMount(): void {
    this.handleStart();
  }

  componentWillUnmount(): void {
    if (this.timerID) {
      clearInterval(this.timerID);
    }
  }

  // 数字小于10时，前面补0
  formate(time: number): string {
    return `${time < 10 ? "0" : ""}${time}`;
  }

  countdown(remainTime: number): TimeInfo {
    // 剩余时间不能小于0
    remainTime = remainTime < 0 ? 0 : remainTime;
    const day = Math.floor(remainTime / (24 * 60 * 60));
    const hours = Math.floor((remainTime / (60 * 60)) % 24);
    const hoursStr = this.formate(hours);
    const minutes = Math.floor((remainTime / 60) % 60);
    const minutesStr = this.formate(minutes);
    const seconds = Math.floor(remainTime % 60);
    const secondsStr = this.formate(seconds);

    const timeInfo: TimeInfo = {
      day,
      hours,
      hoursStr,
      minutes,
      minutesStr,
      seconds,
      secondsStr
    };
    return timeInfo;
  }

  timerID = 0;

  // 开启倒计时
  handleStart = () => {
    const nowTime = Date.now();
    if (this.props.endTime && this.props.endTime - nowTime > 0) {
      this.timerID = window.setInterval(() => {
        let remainTime = 0;
        const nowTime = Date.now();
        remainTime = (this.props.endTime - nowTime) / 1000;
        if (remainTime < 0) {
          clearInterval(this.timerID);
          // TODO 倒计时结束时，回掉函数
          // this.props.onTimeOver && this.props.onTimeOver();
        } else {
          this.setState({
            timeInfo: this.countdown(remainTime)
          });
        }
      }, 1000);
    }
  };

  render(): JSX.Element {
    return (
      <>
        <Text
          style={{ fontSize: 12, color: "#F32823" }}
        >{`${this.state.timeInfo.day}d : ${this.state.timeInfo.hoursStr}h : ${this.state.timeInfo.minutesStr}m : ${this.state.timeInfo.secondsStr}s `}</Text>
      </>
    );
  }
}
