import { action, observable } from "mobx";
import Basic from "common/store/Basic";
import NativeToast, { TOAST_POSITION } from "common/nativeModules/basics/nativeUIModule/Toast";
import { ResAD } from "common/components/ADGroup/helpers/types";
import { errCode } from "../constents";
import * as API from "../constents/api";
import UserLimitInfo from "../model/UserLimitInfo";
import { IncreaseLimit } from "../model/IncreaseLimitInfo";
import { NetworkResponse } from "@akulaku-rn/akulaku-ec-common/src/nativeModules/network/nativeNetworkModule";
import { getConfig } from "common/commonConfig";
export default class IncreaseLimitStoreV2 extends Basic {
  @observable loading = true;

  @observable isError = false;

  @observable isShowUncredited = false;

  @observable adDataMap: Map<number, ResAD> = new Map();

  @observable isShowAvailable = false;

  @observable userLimitInfo: UserLimitInfo = {
    //总额度
    totalCredit: "0",

    //已用额度
    usedCredit: "0",

    //可用额度
    availableCredit: "0",

    //临时额度
    contingentCredit: "0",

    //临时额度失效时间
    contingentCreditExpireTime: "0",

    usedContingentCredit: "0",

    availableContingentCredit: "0",

    adjustDetail: null,

    contingentEquityDetail: null,
    showNewCustomerTip: false
  };

  complianceConfig: { [key: string]: string | boolean } = {
    link: "",
    IN: "",
    EN: "",
    is_new: false
  };

  @observable increaseLimitInfo: IncreaseLimit = {
    overdue: false,
    groups: []
  };

  @action("合规演示需要修改部分UI和协议，通过配置中心来配置是否开启")
  getConfigCompliance = async () => {
    const res = await getConfig(4031);
    if (!!res) {
      this.complianceConfig = res;
    }
  };

  @action saveAdData = (cn: number, data: ResAD) => {
    this.adDataMap.set(cn, data);
  };

  @action updateisShowAvailable = () => {
    this.isShowAvailable = false;
  };

  // 新版额度中心获取额度信息
  @action
  getQuotaCenterDetal = async () => {
    const responseUserLimitInfo: NetworkResponse<UserLimitInfo> = await this.io.post(API.GET_QUOTA_CENTER_DETAIL, {});
    if (responseUserLimitInfo.success) {
      this.loading = false;
      if (!!responseUserLimitInfo.data) {
        this.userLimitInfo = responseUserLimitInfo.data;
      }
      if (!!responseUserLimitInfo.data?.adjustDetail) {
        this.isShowAvailable = true;
      }
    } else if (responseUserLimitInfo.errCode === errCode.UNCREDITED) {
      // 未授信的处理
      this.isShowUncredited = true;
    } else {
      this.isError = true;
      responseUserLimitInfo &&
        NativeToast.showMessage(responseUserLimitInfo.errMsg, { position: TOAST_POSITION.CENTER });
    }
  };

  @action
  getByGroupGetList = async () => {
    const responseIncreaseLimitInfo: NetworkResponse<IncreaseLimit> = await this.io.post(API.GET_BY_GROUP_LIST, {});
    if (responseIncreaseLimitInfo.success) {
      if (!!responseIncreaseLimitInfo.data) {
        this.increaseLimitInfo = responseIncreaseLimitInfo.data;
      }
    } else {
      responseIncreaseLimitInfo &&
        NativeToast.showMessage(responseIncreaseLimitInfo.errMsg, { position: TOAST_POSITION.CENTER });
    }
  };

  @action
  fetchData = async () => {
    this.loading = true;
    this.isError = false;
    try {
      await this.getConfigCompliance();
      this.getQuotaCenterDetal();
      this.getByGroupGetList();
    } catch (e) {
      console.log(e);
      this.isError = true;
    } finally {
      this.loading = false;
    }
  };
}
