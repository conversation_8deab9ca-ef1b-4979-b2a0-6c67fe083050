/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-08 10:33
 * @description 提额信息
 */
export enum INCREASE_LIMIT_STATUS {
  INCREASE_AVAILABLE, //可提额
  ON_REVEIW, //风控审核中
  REJECT, //风控拒绝
  UPDATE_AVAILABLE, //可更新
  FINISH, //完成
  REACH_THE_UPPER_LIMIT //达到提额上限
}

export enum INCREASE_LIMIT_TYPE {
  OVERDUE = 1,
  INFORMATION,
  AUTHORIZATION,
  OTHER
}

export interface Item {
  imgLink: string;
  maxIncreaseQuotaAmount: number;
  id: number;
  status: number; //0:可提额，1：风控审核中，2：风控拒绝，3：可更新，4：完成 (对应INCREASE_LIMIT_STATUS)
  title: string;
  daysBeforeUpdatable: number;
  type: number;
  itemType: string;
  path: string;
}

export interface Category {
  hasMore: boolean;
  processes: Item[];
  type: number; //2：资料提额， 3：账号授权， 4：其它提额
}

export interface IncreaseLimitInfo {
  processes: Item[];
}

export interface IncreaseLimit {
  overdue: boolean;
  groups?: Category[];
}
