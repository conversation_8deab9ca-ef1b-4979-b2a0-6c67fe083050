export default interface UserLimitInfo {
  //总额度
  totalCredit: string;

  //已用额度
  usedCredit: string;

  //可用额度
  availableCredit: string;

  //临时额度
  contingentCredit: string;

  //临时额度失效时间
  contingentCreditExpireTime: string;

  //已用临时额度
  usedContingentCredit: string;

  //可用临时额度
  availableContingentCredit: string;

  adjustDetail?: AdjustDetail | null;

  contingentEquityDetail?: ContingentEquityDetail | null; // 临额权益卡片信息【为空时不出现引导】
  showNewCustomerTip?: boolean;
}

export interface AdjustDetail {
  time: string;
  totalCredit: string;
  credit: string;
  jumpLink: string;
}

export interface ContingentEquityDetail {
  status: number;
  expireTime: string | null;
  usedContingentCredit: string;
}
