/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-08 17:01
 * @description 额度明细
 */
import { stringify } from "qs";
import { isEmpty } from "lodash";

export interface ItemDetails {
  quotaType: number;
  quota: number;
}

export interface Record {
  amount: number;
  createdMillis: number;
  desc: string;
  icon: string;
  type: string;
  month: string;
  itemDetails: ItemDetails[];
}

export interface LimitDetailInfo {
  isLast: boolean;
  records: Record[];
  pageNo: number;
  pageSize: number;
  totalRecords: number;
}

export function getSectionData(limitDetailInfo: LimitDetailInfo) {
  const sections: { data: Record[]; title: string }[] = [];
  let data: Record[] = [];
  let title = "";
  limitDetailInfo.records &&
    limitDetailInfo.records.forEach((value, index) => {
      //title相同，属于同一组，直接添加到当前data里面
      if (title === value.month) {
        data.push(value);
      } else {
        //title不同，属于不同组，新建一组title和data
        if (!isEmpty(title)) {
          //非第一次遍历，先把缓存的一组title和data添加到sections
          sections.push({
            data: data,
            title: title
          });
        }
        title = value.month;
        data = [];
        data.push(value);
      }
      if (index === limitDetailInfo.records.length - 1) {
        sections.push({
          data: data,
          title: title
        });
      }
    });
  return sections;
}
