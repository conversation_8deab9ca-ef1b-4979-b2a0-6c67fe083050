import { StyleSheet } from "react-native";
import { WINDOW_WIDTH, FontStyles } from "@akulaku-rn/akui-rn";

const columnWidth = (WINDOW_WIDTH - 16 - 18 - 6) / 2;
const columnHeight = (columnWidth / 160) * 206;

export default StyleSheet.create({
  horizonViewBg: {
    flexDirection: "row",
    marginTop: 20
  },
  horizontalItemDivider: {
    width: 0.5,
    height: 32,
    alignSelf: "center",
    backgroundColor: "#E2E5E9"
  },
  horizontalItemView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center"
  },
  IncreaseLimitGroupDivider: {
    width: WINDOW_WIDTH,
    height: 8,
    backgroundColor: "#EFF2F6"
  },
  container: {
    backgroundColor: "#EFF2F6",
    flex: 1
  },
  bottomADContainer: {
    padding: 16,
    marginTop: 12,
    marginBottom: 40,
    width: "100%",
    backgroundColor: "#FFFFFF",
    alignItems: "center"
  },
  bottomDefaultImage: {
    height: 150,
    width: WINDOW_WIDTH - 94,
    marginVertical: 50
  },
  bottomADTitleText: {
    fontSize: 18,
    lineHeight: 21,
    ...FontStyles["rob-medium"],
    marginBottom: 8
  },
  bottomADLeft: {
    width: columnWidth,
    height: columnHeight,
    marginRight: 6,
    backgroundColor: "red",
    borderRadius: 6,
    overflow: "hidden"
  },
  bottomADLeftWrapper: {
    flexDirection: "row",
    width: "100%"
  },
  bottomADRightWrapper: {
    width: columnWidth,
    justifyContent: "space-between"
  },
  bottomADRight: {
    width: columnWidth,
    height: (columnWidth / 160) * 100,
    backgroundColor: "lightblue",
    borderRadius: 6,
    overflow: "hidden"
  },
  scrollContainer: {
    alignItems: "center",
    backgroundColor: "#00000000"
  },
  functionContainer: {
    width: WINDOW_WIDTH,
    flexDirection: "row",
    paddingVertical: 12,
    backgroundColor: "#EFF2F6",
    alignItems: "center",
    justifyContent: "center"
  },
  functionLine: {
    width: 32,
    height: 1,
    backgroundColor: "#c8ced4"
  },
  functionImg: {
    width: 20,
    height: 20,
    marginLeft: 10
  },
  functionText: {
    color: "#282B2E",
    fontSize: 12,
    marginRight: 9,
    marginLeft: 9
  },
  bnplView: {
    width: WINDOW_WIDTH,
    marginBottom: 12,
    paddingBottom: 20,
    backgroundColor: "#fff",
    justifyContent: "center",
    alignItems: "center"
  },
  bnplGroup: {
    alignSelf: "center",
    marginTop: 30,
    marginBottom: 20
  },
  availableCredit: {
    fontSize: 14,
    color: "#282B2E",
    alignSelf: "center",
    marginTop: 4
  },
  availableCreditValue: {
    ...FontStyles["roboto-bold"],
    fontSize: 36,
    color: "#282B2E",
    alignSelf: "center"
  },
  imageIcon: {
    width: 16,
    height: 16,
    marginLeft: 1
  },
  totalView: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: WINDOW_WIDTH - 33,
    height: 16,
    paddingHorizontal: 16,
    backgroundColor: "#00000000"
  },
  availableView: {
    position: "absolute",
    marginTop: 75
  },
  contextView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 4
  },
  paylaterArrow: {
    width: 0,
    height: 0,
    borderStyle: "solid",
    borderWidth: 6,
    alignSelf: "center",
    borderTopColor: "#00000000", //下箭头颜色
    borderLeftColor: "#00000000", //右箭头颜色
    borderBottomColor: "#FF8833", //上箭头颜色
    borderRightColor: "#00000000", //左箭头颜色,
    marginTop: 12
  },
  available: {
    borderRadius: 15,
    backgroundColor: "#FF8833",
    paddingVertical: 6,
    paddingHorizontal: 12
  },
  availableValue: {
    ...FontStyles["roboto-bold"],
    fontSize: 12,
    fontWeight: "700",
    color: "#ffffff"
  },
  textTimeTilte: {
    fontSize: 12,
    color: "#2092E5",
    alignSelf: "center"
  },
  textTime: {
    fontSize: 12,
    color: "#6E737D",
    alignSelf: "center"
  },
  usableText: {
    color: "#6E737D",
    fontSize: 14
  },
  usableAmount: {
    color: "#333333",
    fontSize: 14,
    marginTop: 4,
    ...FontStyles["DIN-Bold"]
  },
  allAmountTitle: {
    fontSize: 12,
    color: "#282B2E"
  },
  limitbg: {
    width: WINDOW_WIDTH - 32,
    height: ((WINDOW_WIDTH - 32) * 206) / 656,
    paddingHorizontal: 16,
    paddingTop: 25,
    paddingBottom: 16,
    marginTop: 20,
    justifyContent: "space-between"
  },
  limitView: {
    flexDirection: "row",
    justifyContent: "space-between"
  },
  allLimitAmount: {
    color: "#282B2E",
    fontSize: 12,
    alignSelf: "flex-end"
  },
  limitTitle: {
    color: "#282B2E",
    fontSize: 14,
    ...FontStyles["DIN-Bold"]
  }
});
