import React, { Component } from "react";
import { FlatList, KeyboardAvoidingView, View, Text, DeviceEventEmitter } from "react-native";
import i18next, { TFunction } from "i18next";
import RootView from "common/components/Layout/RootView";
import store from "../store";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import NavigationBar from "@akulaku-rn/akui-rn/src/components/NavigationBar";
import styles from "./styles";
import { Loading, NAV_BAR_HEIGHT, NetworkErrorComponent, WINDOW_HEIGHT } from "@akulaku-rn/akui-rn";
import Address from "../component/Address";
import { ComponentType } from "../model/ComponentType";
import _, { isEqual } from "lodash";
import { SERVICE_TYPES } from "../tool/client";
import Photo from "../component/Photo";
import BottomButton from "../component/BottomButton";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import AnimatedTextInput from "../component/AnimatedTextInput";
import IncreaseLimitByEmail from "../component/Email";
import { SensorTypeCLICKItem, SensorTypeCLICKNext } from "../tool/sensorLogger";
import Search from "../component/Search";
import SearchPopView from "../component/SearchPopView";
import SelectItem from "../component/SelectItem";
import { DialogContainer } from "@akulaku-rn/akui-rn/src/components/DialogContainer";
import DialogCommitSuccess from "../component/Dialog/DialogCommitSuccess";
import TaxMixtureTextInput from "../component/MixtureTextInput/TaxMixtureTextInput";
import { CountryID } from "common/components/BaseContainer/Type";
import Marriage from "../component/Marriage";
import DialogTopLimit from "../component/Dialog/DialogTopLimit";
import { NativeNavigationModule } from "common/nativeModules";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";

type Props = {
  navigation: any;
  store: any;
  t: TFunction;
  configSensorEvent: any;
  configPageInfo: any;
};

type State = {
  refreshing: boolean;
  loadFailed: boolean;
  data: any;
  shadow: boolean;
  isInputDone: boolean;
  relatedEntryPageInfos: Array<any>;
};

@RootView({
  withI18n: [
    "increase-limit",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      ms: require("../i18n/ms.json"),
      vi: require("../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("increase-limit")
@inject("store")
@observer
export default class IncreaseInfomationPage extends Component<Props, State> {
  components: any;

  selectItems: any;

  allItems: any;

  waitArray: any;

  sumbitLoading: boolean;

  constructor(props: any) {
    super(props);
    this.state = {
      refreshing: true,
      loadFailed: false,
      data: null,
      shadow: false,
      isInputDone: false,
      relatedEntryPageInfos: []
    };
    const {
      store: {
        navParams: { processId }
      },
      configPageInfo
    } = props;
    configPageInfo({ sn: 100633, sp: { Aku_buttonId: processId } }, false);
    //所有selectView--ref
    this.components = [];
    //所有selectView的子项数据
    this.selectItems = [];
    //当前页面所有需要提交的数据
    this.waitArray = [];
    //当前页面所有的选项
    this.allItems = [];
    //是否正在提交中
    this.sumbitLoading = false;
    this.getPageData();
    this.props.configSensorEvent({
      page_name: "quota submission page",
      page_id: "915"
    });
  }

  getPageData = async () => {
    const {
      navParams,
      pageStore,
      runtime: { uid }
    } = this.props.store;
    if (navParams.itemType === "electronic_signature") {
      this.setState({
        refreshing: false
      });
      return;
    }
    pageStore.post(
      SERVICE_TYPES.PageInfo,
      { processId: navParams.processId },
      (response: any) => {
        const {
          data: { entityInfos, relatedEntryPageInfos },
          success
        } = response;
        if (success) {
          entityInfos &&
            entityInfos.map((item: { type: string; entryId: number; jsonRule: string }) => {
              if (!!(ComponentType as any)[item.type]) {
                this.waitArray.push(item.entryId);
              }
              if (item.type === ComponentType.select || item.type === ComponentType.date) {
                this.selectItems.push(item);
              }
              this.allItems.push(item);
            });
          ///用来把子集数据放到allItems
          relatedEntryPageInfos &&
            relatedEntryPageInfos.map((child: { subPageInfo: { relatedPageInfos: { entityInfos: any[] }[] }[] }) => {
              child.subPageInfo.map((item: { relatedPageInfos: { entityInfos: any[] }[] }) => {
                item.relatedPageInfos[0].entityInfos.map((event: any) => {
                  this.allItems.push(event);
                });
              });
            });
          ///
          // this.setLastValue(this.allItems);
          this.setState({
            data: entityInfos,
            relatedEntryPageInfos: relatedEntryPageInfos,
            refreshing: false,
            loadFailed: false
          });
        } else {
          this.setState({
            loadFailed: true,
            refreshing: false
          });
        }
      },
      () => {
        this.setState({
          loadFailed: true,
          refreshing: false
        });
      }
    );
  };

  keyExtractor = (item: any, index: number) => index.toString();

  setComponentRef = (ref: any, id: number) => {
    this.components[id] = ref;
  };

  setValue = (text: string | null, id: any, type: string, key: string | null) => {
    const {
      store: {
        pageStore,
        runtime: { uid }
      }
    } = this.props;
    pageStore.useQuotaData[id] = text;
    pageStore.useQuotaOtherData[id] = {
      type,
      key
    };
    this.isInputDone();
  };

  sensorLogger = (id: any) => {
    const item = this.allItems.find((i: { entryId: number }) => {
      return i.entryId === id;
    });
    if (!item) {
      console.warn(`find Item is ${item}`);
      return;
    } // 如果item为 false/undefined/null/0 返回
    const {
      store: {
        navParams: { processId, title, status }
      }
    } = this.props;
    const data = {
      fieldID: id,
      fieldName: JSON.parse(item.desc).hintInfo || item.name,
      fieldType: item.type,
      required: !!item.required ? "1" : "0"
    };
    SensorTypeCLICKItem(processId, title, status, data);
  };

  isInputDone = () => {
    const {
      store: { pageStore }
    } = this.props;
    let breakBoolen = false;
    // 遍历当前页有的资料项
    for (const entryId of this.waitArray) {
      let useQuotaItemData = _.cloneDeep(pageStore.useQuotaData[entryId]);
      const item = this.allItems.find((i: { entryId: number }) => {
        return i.entryId === entryId;
      });
      if (item.type === "text" || item.type === "email" || item.type === "number" || item.type === "iphone") {
        useQuotaItemData = useQuotaItemData && useQuotaItemData.replace(/ /g, "");
      }
      if (!useQuotaItemData) {
        // 该资料项未填写
        const findItem = this.allItems.find((i: any) => {
          return i.entryId === entryId;
        });
        if (findItem.required) {
          breakBoolen = true;
          break;
        }
      } else {
        const childData = this.allItems.find((i: any) => i.entryId === entryId);
        let value = pageStore.useQuotaData[entryId];
        if (childData && childData.type === "iphone") {
          while (value.indexOf(" ") >= 0) {
            // 电话号码 去掉空格
            value = value.replace(" ", "");
          }
        }
        // 判断资料项正则
        if (childData && childData.jsonRule && JSON.parse(childData.jsonRule).pattern) {
          const reg = new RegExp(JSON.parse(childData.jsonRule).pattern);
          const error = !reg.test(value);
          if (error) {
            breakBoolen = true;
            break;
          }
        }
      }
    }
    this.setState({
      isInputDone: !breakBoolen
    });
  };

  openNextComponent = (id: any) => {
    const findIndex = this.selectItems.findIndex((i: { entryId: any }) => i.entryId === id);
    if (!this.selectItems[findIndex + 1]) return;
    const nextId = this.selectItems[findIndex + 1].entryId;
    const nonClickable = this.selectItems[findIndex + 1].nonClickable;
    const {
      store: {
        pageStore: { useQuotaData }
      }
    } = this.props;
    if (!!useQuotaData[nextId]) return;
    if (nonClickable) return;
    this.components[nextId].onFocus();
  };

  renderItem = ({ item, index }: { item: any; index: number }) => {
    const {
      store,
      store: {
        navParams,
        runtime: { countryId },
        pageStore: { useQuotaData }
      },
      t
    } = this.props;
    const containerStyle = { marginBottom: 12 };
    const value = _.get(useQuotaData, item.entryId, null);
    const type = item.type;
    const selectData = !!item.jsonDownValues && JSON.parse(item.jsonDownValues);
    const placeholder = (!!item.desc && JSON.parse(item.desc).hintInfo) || item.name;
    const pattern = !!item.jsonRule && JSON.parse(item.jsonRule).pattern;
    const errorText = t("x输入有误", { x: placeholder });
    switch (item.type) {
      case "select":
        if (item.subType === "marital_status") {
          return (
            <Marriage
              setValue={this.setValue}
              setWaitArray={this.setWaitArray}
              // setValue={this.setValue}
              secondary={this.state.relatedEntryPageInfos[0].subPageInfo}
              pageId={123}
              nullId={12}
              // extraPageData={}
              t={this.props.t}
              store={this.props.store}
              // id={item.entryId}
              data={this.state.data}
              relatedEntryPageInfos={this.state.relatedEntryPageInfos}
              type={type}
              sensorLogger={this.sensorLogger}
            />
          );
        }
        return (
          <View key={index} style={styles.listItem}>
            <SelectItem
              ref={view => {
                this.setComponentRef(view, item.entryId);
              }}
              containerStyle={containerStyle}
              data={selectData}
              id={item.entryId}
              type={type}
              didSelectedItem={this.setValue}
              value={value}
              placeholder={placeholder}
              openNextComponent={this.openNextComponent}
              editable={!item.nonClickable}
              onFocus={this.sensorLogger}
            />
          </View>
        );
      case "date":
        return (
          <View key={index} style={styles.listItem}>
            <SelectItem
              ref={view => {
                this.setComponentRef(view, item.entryId);
              }}
              containerStyle={containerStyle}
              data={selectData}
              id={item.entryId}
              type={type}
              didSelectedItem={this.setValue}
              value={value}
              placeholder={placeholder}
              openNextComponent={this.openNextComponent}
              editable={!item.nonClickable}
              onFocus={this.sensorLogger}
            />
          </View>
        );
      case "text":
        if (item.subType === "tax_card_number") {
          return (
            <View key={index} style={styles.listItem}>
              <TaxMixtureTextInput
                onFocus={this.sensorLogger}
                style={{ marginTop: 20 }}
                key={index}
                countryId={CountryID.ID}
                onChangeText={this.setValue}
                type={type}
                title={placeholder}
                id={item.entryId}
                initValue={item.lastValue}
              />
            </View>
          );
        }
        return (
          <View key={index} style={styles.listItem}>
            <AnimatedTextInput
              t={t}
              key={index}
              containerStyle={containerStyle}
              setValue={this.setValue}
              value={value}
              type={type}
              placeholder={placeholder}
              pattern={pattern}
              id={item.entryId}
              editable={!item.nonClickable}
              onFocus={this.sensorLogger}
              errorText={errorText}
            />
          </View>
        );
      case "email":
        return (
          <View key={index} style={styles.listItem}>
            <AnimatedTextInput
              t={t}
              key={index}
              containerStyle={containerStyle}
              setValue={this.setValue}
              value={value}
              type={type}
              placeholder={placeholder}
              pattern={pattern}
              id={item.entryId}
              editable={!item.nonClickable}
              onFocus={this.sensorLogger}
              errorText={errorText}
            />
          </View>
        );
      case "number":
        return (
          <View key={index} style={styles.listItem}>
            <AnimatedTextInput
              t={t}
              key={index}
              containerStyle={containerStyle}
              setValue={this.setValue}
              value={value}
              type={type}
              placeholder={placeholder}
              pattern={pattern}
              id={item.entryId}
              editable={!item.nonClickable}
              onFocus={this.sensorLogger}
              errorText={errorText}
            />
          </View>
        );
      case "iphone":
        return (
          <View key={index} style={styles.listItem}>
            <AnimatedTextInput
              t={t}
              key={index}
              containerStyle={containerStyle}
              setValue={this.setValue}
              value={value}
              type={type}
              placeholder={placeholder}
              pattern={pattern}
              id={item.entryId}
              editable={!item.nonClickable}
              onFocus={this.sensorLogger}
              errorText={errorText}
            />
          </View>
        );
      case "map":
        return (
          <View key={index} style={styles.listItem}>
            <Address
              t={t}
              store={store}
              containerStyle={containerStyle}
              id={item.entryId}
              setAddress={this.setValue}
              addrtype={JSON.parse(item.jsonRule).Address}
              nonClickable={item.nonClickable}
              placeholder={placeholder}
            />
          </View>
        );

      case "file":
        return (
          <Photo
            key={index}
            data={item.desc}
            image={value}
            jsonRule={item.jsonRule}
            pageId={1}
            containerStyle={[containerStyle, navParams.processId === "126377695491391490" && { marginBottom: 32 }]}
            t={t}
            id={item.entryId}
            setValue={this.setValue}
            countryId={countryId}
            nonClickable={item.nonClickable}
          />
        );
      case "search":
        return (
          <View key={index} style={styles.listItem}>
            <Search
              t={t}
              key={index}
              store={store}
              containerStyle={containerStyle}
              setValue={this.setValue}
              value={value}
              type={type}
              placeholder={placeholder}
              pattern={pattern}
              id={item.entryId}
              editable={!item.nonClickable}
              onFocus={this.sensorLogger}
              subType={item.subType}
            />
          </View>
        );
      case "tip":
        return <Text style={styles.tipText}>{placeholder}</Text>;
      default:
        return null;
    }
  };

  onContentSizeChange = (w: number, h: number) => {
    if (WINDOW_HEIGHT - NAV_BAR_HEIGHT - 64 < h) {
      this.setState({
        shadow: true
      });
    }
  };

  setWaitArray = (waitArray: any) => {
    this.waitArray = waitArray;
  };

  next = () => {
    const {
      store: {
        navParams: { processId, title, status },
        pageStore
      },
      t
    } = this.props;

    const entries: any[] = [];
    // 遍历当前页有的资料项
    for (const entryId of this.waitArray) {
      //逻辑层跟UI层的用户输入错误分离开了的，这边是做为一个逻辑层的输入错误，拦截提交
      let value = pageStore.useQuotaData[entryId];
      if (!value) return;
      const childData = this.allItems.find((i: any) => i.entryId === entryId);
      const entriesKey = !!pageStore.useQuotaOtherData[entryId] ? pageStore.useQuotaOtherData[entryId].key : null;
      const entriesType = !!pageStore.useQuotaOtherData[entryId] ? pageStore.useQuotaOtherData[entryId].type : null;
      if (childData && childData.type === "iphone") {
        while (value.indexOf(" ") >= 0) {
          // 电话号码 去掉空格
          value = value.replace(" ", "");
        }
      }
      const isUrl = childData.type === "file" && typeof value === "string";
      // 收集文件信息
      if (childData && childData.type === "file" && !isUrl) {
        entries.push({ entryId, value: value.key, key: entriesKey, type: entriesType });
      } else if (childData && childData.type === "search") {
        entries.push({ entryId, value: value.id, key: entriesKey, type: entriesType });
      } else {
        entries.push({ entryId, value, key: entriesKey, type: entriesType });
      }
    }
    if (this.sumbitLoading) {
      return;
    }
    SensorTypeCLICKNext(processId, title, status, "1");
    const waitSumbit = {
      processId,
      entries
    };
    console.log(waitSumbit);

    this.dataSubmit(waitSumbit);
  };

  dataSubmit = (data: object) => {
    const {
      t,
      store: {
        navParams: { processId, title, status },
        pageStore
      }
    } = this.props;
    Loading.show();
    this.sumbitLoading = true;
    pageStore.post(
      SERVICE_TYPES.Submit,
      data,
      (response: any) => {
        DeviceEventEmitter.emit("increaseLimitUpdate");
        const { success, errMsg } = response;
        this.sumbitLoading = false;
        Loading.dismiss();
        if (isEqual(response && response.errCode, "UA.0057")) {
          DialogContainer.show({
            renderContent: <DialogTopLimit t={t} msg={errMsg} />
          });
        } else if (success) {
          DialogContainer.show({
            renderContent: <DialogCommitSuccess processId={processId} {...this.props} />
          });
        } else {
          NativeToast.showMessage(errMsg);
        }
      },
      (error: any) => {
        this.sumbitLoading = false;
        Loading.dismiss();
        const { errMsg } = error;
        NativeToast.showMessage(errMsg);
      }
    );
  };

  returnMain = () => {
    const {
      navigation,
      store,
      store: { navParams, pageStore },
      t
    } = this.props;
    const { data } = this.state;
    switch (navParams.itemType) {
      case "company_email":
        return <IncreaseLimitByEmail store={store} navigation={navigation} t={t} jsonRule={data && data[0].jsonRule} />;
      case "electronic_signature":
        break;
      default:
        return (
          <>
            <FlatList
              bounces={false}
              keyExtractor={this.keyExtractor}
              data={data}
              renderItem={this.renderItem}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps={"handled"}
              onContentSizeChange={this.onContentSizeChange}
            />
            <SearchPopView store={store} />
          </>
        );
    }
  };

  onBackPress = () => {
    const {
      store: {
        navParams: { processId, title, status, jumpSource, path }
      }
    } = this.props;
    NativeNavigationModule.goBack();
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      page_name: "quota submission page",
      element_id: "9150101",
      element_name: "return",
      module_id: "01",
      module_name: "quota",
      position_id: "01",
      page_id: "915",
      extra: {
        Aku_buttonId: processId,
        Aku_buttonName: title,
        Aku_buttonStatus: status,
        Aku_jumpSource: jumpSource,
        Aku_path: path
      }
    });
  };

  render() {
    const {
      store: {
        navParams: { itemType, title }
      },
      t
    } = this.props;
    const { shadow, isInputDone, refreshing, loadFailed } = this.state;
    const showButton = itemType !== "company_email";
    if (refreshing) {
      return (
        <View>
          <Loading containerStyle={styles.loadingView} />
        </View>
      );
    }
    if (loadFailed) {
      return (
        <View style={styles.container}>
          <NavigationBar onBackPress={this.onBackPress} />
          <NetworkErrorComponent
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            containerStyle={styles.container}
            onRetryPress={this.getPageData}
            buttonText={t("global:刷新")}
          />
        </View>
      );
    }
    return (
      <View style={styles.container}>
        <NavigationBar title={title} onBackPress={this.onBackPress} />
        <KeyboardAvoidingView style={styles.main}>
          {this.returnMain()}
          {showButton && <BottomButton isInputDone={isInputDone} text={t("提交")} next={this.next} shadow={shadow} />}
        </KeyboardAvoidingView>
      </View>
    );
  }
}
