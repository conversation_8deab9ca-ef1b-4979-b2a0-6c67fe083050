import Basic from "common/store/Basic";
import { action, observable } from "mobx";
import UserLimitInfo from "../../model/UserLimitInfo";

/**
 * Create by <PERSON><PERSON>xiao on 2022-08 10:36
 * @description
 */
export default class IncreaseLimitStore extends Basic {
  @observable loading = true;

  @observable isError = false;

  @observable userLimitInfo: UserLimitInfo = {
    //总额度
    totalCredit: "0",

    //已用额度
    usedCredit: "0",

    //可用额度
    availableCredit: "0",

    //固定额度
    fixedCredit: "0",

    //临时额度
    contingentCredit: "0",

    //已提升额度
    promotedCredit: "0",

    //额度历史开关
    detailSwitch: false,

    //冻结额度
    frozenCredit: "0",

    //Installment产品标签
    // installmentTags: [],

    //通用额度
    commonCredit: "0",

    //临时额度失效时间
    contingentCreditExpireTime: "0",

    //补充资料提额额度
    furtherInfoCredit: "0",

    //是否展示
    // display: false,

    //PayLater可用额度
    payLaterAvailableCredit: "0",

    //PayLater产品标签
    // payLaterTags: [],

    payLaterTotalCredit: "0",

    installmentTotalCredit: "0",

    installmentAvailableCredit: "0",

    hasContingentCredit: false,

    usedContingentCredit: "0",

    availableContingentCredit: "0"
  };

  @action
  fetchData = async () => {
    this.loading = true;
    this.isError = false;
    try {
      const responseUserLimitInfo = await this.io.post("/capi/credit/account/user-quota", {});
      if (!responseUserLimitInfo.data) {
        this.isError = true;
        return;
      }
      this.userLimitInfo = responseUserLimitInfo.data;
    } catch (e) {
      this.isError = true;
    } finally {
      this.loading = false;
    }
  };
}
