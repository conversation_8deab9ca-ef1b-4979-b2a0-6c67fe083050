/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022-08 09:53
 * @description 临时额度结果页
 */

import React, { Component } from "react";
import { Image, ScrollView, StyleSheet, Text, View } from "react-native";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import store from "./store";
import { FontStyles, NetworkErrorComponent, PriceComponent, WINDOW_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import RecommendList, { RecommendScene } from "common/components/RecommendList";
import ImageAd from "common/components/ADGroup/ImageAd";
import { RecommendItemModel } from "common/components/RecommendListItem/model";
import { NativeNavigationModule } from "nativeModules/index";
import { GlobalRuntime } from "common/constant";
import { TFunction } from "i18next";
import NativeSensorModule, { SensorEvent, SensorType } from "nativeModules/sdk/nativeSensroModule";
import { reporter } from "@akulaku-rn/rn-v4-sdk";

const styles = StyleSheet.create({});

type State = {};

type Props = {
  navigation: any;
  store: { [key: string]: any; pageStore: store };
  t: TFunction;
  configSensorEvent: any;
};

@RootView({
  withI18n: [
    "increase-limit",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("increase-limit")
@inject("store")
@observer
export default class TemporaryResult extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.props.configSensorEvent({
      page_id: "1233",
      page_name: "quota success page"
    });
    reporter.setPageConfig({ sn: 202166 });
    reporter.setUri(props?.store?.navParams?.screen ?? "TemporaryResult");
  }

  componentDidMount() {
    this.fetchData();
  }

  fetchData = () => {
    const {
      store: { pageStore }
    } = this.props;
    setTimeout(() => {
      pageStore.fetchData();
    }, 2000);
  };

  onItemClick = (item: RecommendItemModel, index: number) => {
    if (item.spuId) {
      NativeNavigationModule.navigate({
        url: `ak://m.akulaku.com/1301?id=${item.spuId}&countryCode=${GlobalRuntime.countryCode}&skuId=${item.skuId}`
      });
    }
  };

  _getExtPitInfo = () => {
    return {
      cn: 1
    };
  };

  _getSensorReportData = (): SensorEvent => {
    return {
      element_id: "12330104",
      element_name: "recommend",
      page_id: "1233",
      page_name: "quota success page",
      module_id: "01",
      module_name: "quota",
      position_id: "04"
    };
  };

  render() {
    const {
      t,
      store: {
        navParams,
        runtime: { countryId },
        pageStore: { loading, isError, userLimitInfo }
      }
    } = this.props;
    let content = null;
    if (loading) {
      content = (
        <View
          style={{
            position: "absolute",
            height: WINDOW_HEIGHT,
            width: WINDOW_WIDTH,
            flex: 1,
            backgroundColor: "#fff"
          }}
        >
          <Image
            source={require("../img/temporary_top.webp")}
            style={{
              width: WINDOW_WIDTH,
              height: (WINDOW_WIDTH * 372) / 1080
            }}
          />
          <Image
            source={require("../img/temporary_bottom.webp")}
            style={{
              position: "absolute",
              bottom: 0,
              width: WINDOW_WIDTH,
              height: (WINDOW_WIDTH * 621) / 1080
            }}
          />
          <Image
            source={require("../img/temporary_loading.gif")}
            style={{
              alignSelf: "center",
              marginTop: 40,
              width: 100,
              height: 100
            }}
          />
          <Image
            source={require("../img/loading_text.webp")}
            style={{
              alignSelf: "center",
              marginTop: 20,
              width: 119,
              height: (119 * 46) / 353
            }}
          />
        </View>
      );
    } else if (isError) {
      content = (
        <NetworkErrorComponent
          message={t("global:啊，网络故障导致我们的距离好远啊!")}
          buttonText={t("global:刷新")}
          onRetryPress={this.fetchData}
        />
      );
    } else {
      content = (
        <RecommendList
          renderHeader={this.renderHeader()}
          screen={navParams.screen}
          RecommendScene={RecommendScene.TEMPORARY_INCREASELIMIT_RESULT}
          uniqKey={"temporaryIncreaselimitResult"}
          onItemClick={this.onItemClick}
          title={t("向您推荐")}
          getExtPitInfo={this._getExtPitInfo}
          getSensorReportData={this._getSensorReportData}
        />
      );
    }
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: "#fff"
        }}
      >
        {loading ? null : (
          <Text
            style={{
              fontSize: 14,
              color: "#6E737D",
              alignSelf: "flex-end",
              marginTop: 48,
              marginRight: 12
            }}
            onPress={() => {
              NativeSensorModule.sensorLogger(SensorType.CLICK, {
                element_id: "12330101",
                element_name: "done",
                module_id: "01",
                position_id: "01",
                page_id: "1233"
              });
              NativeNavigationModule.goBack();
            }}
          >
            {t("完成")}
          </Text>
        )}
        {content}
      </View>
    );
  }

  private renderHeader() {
    const {
      t,
      store: {
        navParams,
        runtime: { countryId },
        pageStore: { loading, isError, userLimitInfo }
      }
    } = this.props;
    return () => {
      return (
        <>
          <View
            style={{
              backgroundColor: "#fff",
              paddingBottom: 30,
              borderBottomLeftRadius: 8,
              borderBottomRightRadius: 8
            }}
          >
            <Image
              source={require("../img/get_success.webp")}
              style={{
                width: 140,
                height: (140 * 345) / 420,
                alignSelf: "center",
                marginTop: 22
              }}
            />
            <Text
              style={{
                ...FontStyles["DIN-Bold"],
                fontSize: 26,
                color: "#F32823",
                alignSelf: "center",
                marginTop: 10
              }}
            >
              {PriceComponent.priceFormat(userLimitInfo.contingentCredit, countryId, { needUnit: true })}
            </Text>
            <Text
              style={{
                color: "#6E737D",
                fontSize: 15,
                alignSelf: "center",
                marginTop: 4
              }}
            >
              {t("有效期30天")}
            </Text>
            <Text
              style={{
                ...FontStyles["rob-medium"],
                color: "#282B2E",
                fontSize: 14,
                alignSelf: "center",
                textAlign: "center",
                marginTop: 12
              }}
            >
              {t("恭喜您领取到临时额度")}
            </Text>
          </View>
          <View
            style={{
              backgroundColor: "#fff",
              borderRadius: 8,
              marginTop: 12,
              marginBottom: 10
            }}
          >
            <ImageAd
              adGroup={315}
              imageWidth={WINDOW_WIDTH - 32}
              imageHeight={((WINDOW_WIDTH - 32) * 100) / 336}
              wrapperStyle={{
                alignSelf: "center",
                marginTop: 12,
                marginHorizontal: 12
              }}
              onExpose={adData => {
                NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
                  element_id: "12330102",
                  element_name: "ad1",
                  page_id: "1233",
                  page_name: "quota success page",
                  module_id: "01",
                  module_name: "quota",
                  position_id: "02",
                  extra: {
                    advertisingID: adData.id,
                    image_id: adData.creatives[0].id,
                    adPositionid: adData.spotId,
                    links: adData.creatives[0].destUrl,
                    label_id: adData.tagId || ""
                  }
                });
              }}
              onClick={adData => {
                NativeSensorModule.sensorLogger(SensorType.CLICK, {
                  element_id: "12330102",
                  element_name: "ad1",
                  page_id: "1233",
                  page_name: "quota success page",
                  module_id: "01",
                  module_name: "quota",
                  position_id: "02",
                  extra: {
                    advertisingID: adData.id,
                    image_id: adData.creatives[0].id,
                    adPositionid: adData.spotId,
                    links: adData.creatives[0].destUrl,
                    label_id: adData.tagId || ""
                  }
                });
              }}
            />
            <ImageAd
              adGroup={317}
              imageWidth={WINDOW_WIDTH - 32}
              imageHeight={((WINDOW_WIDTH - 32) * 100) / 336}
              wrapperStyle={{
                marginTop: 8,
                alignSelf: "center",
                marginBottom: 12,
                marginHorizontal: 12
              }}
              onExpose={adData => {
                NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
                  element_id: "12330103",
                  element_name: "ad2",
                  page_id: "1233",
                  page_name: "quota success page",
                  module_id: "01",
                  module_name: "quota",
                  position_id: "03",
                  extra: {
                    advertisingID: adData.id,
                    image_id: adData.creatives[0].id,
                    adPositionid: adData.spotId,
                    links: adData.creatives[0].destUrl,
                    label_id: adData.tagId || ""
                  }
                });
              }}
              onClick={adData => {
                NativeSensorModule.sensorLogger(SensorType.CLICK, {
                  element_id: "12330103",
                  element_name: "ad2",
                  page_id: "1233",
                  page_name: "quota success page",
                  module_id: "01",
                  module_name: "quota",
                  position_id: "03",
                  extra: {
                    advertisingID: adData.id,
                    image_id: adData.creatives[0].id,
                    adPositionid: adData.spotId,
                    links: adData.creatives[0].destUrl,
                    label_id: adData.tagId || ""
                  }
                });
              }}
            />
          </View>
        </>
      );
    };
  }
}
