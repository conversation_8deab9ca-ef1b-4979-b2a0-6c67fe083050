/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-08 17:37
 * @description 额度明细-使用明细
 */

import React, { Component } from "react";
import { View, StyleSheet, Text, SectionList, SectionListRenderItemInfo, Image, SectionListData } from "react-native";
import i18next, { TFunction } from "i18next";
import store from "../store";
import { inject, observer } from "mobx-react";
import LimitItem from "../component/LimitItem";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import DefaultComponent from "common/components/DefaultComponent";
import { getSectionData, Record } from "../../model/LimitDetailInfo";
import { stringify } from "qs";
import LimitDetailStore from "../store";
import { NAV_BAR_HEIGHT, NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import ListFooterComponent from "@akulaku-rn/akui-rn/src/components/ListFooterComponent";
import NotDataComponent from "@akulaku-rn/akui-rn/src/components/NotDataComponent";
import styles from "./styles";
import { NativeNavigationModuleModel } from "common/nativeModules/router/nativeNavigationModule";
import RuntimeStore from "common/store/Runtime";
import { runtimeType } from "common/components/BaseContainer/Type";
import { ScreenSensorEvent } from "common/nativeModules/sdk/nativeSensroModule";
import { get } from "lodash";
import { PageConfig } from "@akulaku-rn/rn-v4-sdk";

type Props = {
  navigation: NativeNavigationModuleModel;
  store: { pageStore: LimitDetailStore; runtime: RuntimeStore & runtimeType };
  t: TFunction;
  configSensorEvent: (event: ScreenSensorEvent) => void;
  configPageInfo: (config: PageConfig, isSupportV3?: boolean) => void;
};

type State = {};

@inject("store")
@observer
export default class UsageDetailScreen extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    //v4埋点
    props.configPageInfo({ sn: 100634 }, true);
  }

  async componentDidMount() {
    await this.fetchData();
    this.props.store.runtime.setPaddingBottom(false);
  }

  pageNumber = 1;

  renderItem = ({ item, index }: { item: Record; index: number }) => {
    let bottom;
    const {
      t,
      store: {
        runtime: { countryCode, countryId }
      }
    } = this.props;
    return (
      <LimitItem
        t={t}
        detailType={1}
        amount={item.amount}
        createdMillis={item.createdMillis}
        desc={item.desc}
        icon={item.icon}
        type={item.type}
        countryCode={countryCode}
        countryId={countryId}
      />
    );
  };

  renderSectionHeader = (info: { section: SectionListData<Record, { data: Record[]; title: string }> }) => {
    return (
      <>
        <View style={styles.devider12} />
        <View
          style={{
            width: "100%",
            backgroundColor: "white"
          }}
        >
          <Text style={styles.title}>{get(info, "section.title", "")}</Text>
        </View>
        <View style={styles.devider1} />
      </>
    );
  };

  fetchData = () => {
    const {
      store: { pageStore }
    } = this.props;
    pageStore.fetchUsageDetail(this.pageNumber);
  };

  onEndReached = () => {
    const {
      store: {
        pageStore,
        pageStore: { limitDetailInfoUsage }
      }
    } = this.props;
    if (!limitDetailInfoUsage.isLast) {
      setTimeout(() => {
        pageStore.fetchUsageDetail(++this.pageNumber);
      }, 200);
    }
  };

  keyExtractor = (item: Record, index: number) => "" + index;

  onRetryPress = () => {
    this.fetchData();
  };

  listFooterComponent = (isLast: boolean) => {
    const { t } = this.props;
    let footer;
    if (!isLast) {
      footer = <ListFooterComponent isLoding={true} title={t("加载中")} />;
    } else {
      footer = <ListFooterComponent title={t("没有更多数据了")} />;
    }
    return <View style={{ backgroundColor: "#f5f5f5" }}>{footer}</View>;
  };

  itemSeparatorComponent = () => <View style={styles.itemSeparator} />;

  render() {
    const {
      t,
      store: {
        pageStore: { loadingUsage, isErrorUsage, limitDetailInfoUsage }
      }
    } = this.props;
    let content = null;
    if (loadingUsage) {
      content = (
        <View style={styles.loadingContainer}>
          <Loading />
        </View>
      );
    } else if (isErrorUsage) {
      content = (
        <NetworkErrorComponent
          message={t("global:啊，网络故障导致我们的距离好远啊!")}
          buttonText={t("global:刷新")}
          onRetryPress={this.onRetryPress}
        />
      );
    } else if (limitDetailInfoUsage.records.length === 0) {
      content = (
        <NotDataComponent
          message={t("无信用额度使用记录")}
          image={
            "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ec_country_id_Limit_76117685d30c65bebcd0200a559fa54b_noLimitData.png"
          }
        />
      );
    } else {
      content = (
        <SectionList
          sections={getSectionData(limitDetailInfoUsage)} //数据源
          keyExtractor={this.keyExtractor}
          renderItem={this.renderItem}
          renderSectionHeader={this.renderSectionHeader}
          onEndReachedThreshold={0.1}
          onEndReached={this.onEndReached}
          ListFooterComponent={this.listFooterComponent(limitDetailInfoUsage.isLast)}
          // @ts-ignore
          ListFooterComponentStyle={{
            flex: 1,
            justifyContent: "flex-end",
            backgroundColor: "#f5f5f5"
          }} //使footer在SectionList(FlatList)未被填满时在最底部
          contentContainerStyle={{
            flexGrow: 1,
            backgroundColor: "white"
          }} //设置SectionList(FlatList)的contentSize最小值等于SectionList(FlatList)的大小（宽高）
          ItemSeparatorComponent={this.itemSeparatorComponent}
        ></SectionList>
      );
    }
    return <View style={styles.container}>{content}</View>;
  }
}
