/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 19:33
 * @description
 */
import { StyleSheet } from "react-native";
import { FontStyles } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  devider12: {
    width: "100%",
    height: 12,
    backgroundColor: "#F5F5F5"
  },
  title: {
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    fontSize: 16,
    color: "#999999",
    lineHeight: 50,
    marginLeft: 16
  },
  itemSeparator: {
    width: "100%",
    height: StyleSheet.hairlineWidth,
    backgroundColor: "#ebebeb",
    marginLeft: 16
  },
  devider1: {
    width: "100%",
    height: StyleSheet.hairlineWidth,
    backgroundColor: "#ebebeb"
  },
  img: {
    width: 150,
    height: 150,
    alignSelf: "center",
    marginTop: 109
  },
  text: {
    fontSize: 14,
    color: "#999999",
    lineHeight: 16,
    alignSelf: "center",
    marginLeft: 45,
    marginRight: 45,
    textAlign: "center"
  },
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5"
  },
  loadingContainer: {
    alignItems: "center",
    flex: 1,
    flexDirection: "row"
  }
});
