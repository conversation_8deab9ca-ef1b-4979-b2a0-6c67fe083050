/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-07 22:21
 * @description
 */

import React from "react";
import { View, StyleSheet } from "react-native";
import Basic from "common/store/Basic";
import { action, observable } from "mobx";
import { LimitDetailInfo, Record } from "../../model/LimitDetailInfo";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import { isEmpty } from "lodash";

export default class LimitDetailStore extends Basic {
  @observable loadingUsage = true;

  @observable isErrorUsage = false;

  @observable limitDetailInfoUsage: LimitDetailInfo = {
    isLast: false,
    records: [],
    pageNo: 0,
    pageSize: 0,
    totalRecords: 0
  };

  @observable loadingAdjustment = true;

  @observable isErrorAdjustment = false;

  @observable limitDetailInfoAdjustment: LimitDetailInfo = {
    isLast: false,
    records: [],
    pageNo: 0,
    pageSize: 0,
    totalRecords: 0
  };

  @action
  fetchUsageDetail = async (pageNumber: number) => {
    let showLoading = false;
    try {
      if (this.isErrorUsage) {
        Loading.show();
        showLoading = true;
        this.isErrorUsage = false;
      }
      const res = await this.io.post("/capi/credit/quota/used-list", {
        pageNo: pageNumber,
        pageSize: 10
      });
      if (!res.data) {
        this.loadingUsage = false;
        return;
      }
      if (this.limitDetailInfoUsage.records.length > 0) {
        this.limitDetailInfoUsage.records = this.limitDetailInfoUsage.records.concat(res.data.records);
      } else {
        this.limitDetailInfoUsage = res.data;
      }
      this.limitDetailInfoUsage.isLast = res?.data?.isLast;
      this.loadingUsage = false;
    } catch (e) {
      this.isErrorUsage = true;
      this.loadingUsage = false;
    } finally {
      if (showLoading) Loading.dismiss();
    }
  };

  @action
  fetchAdjustmentDetail = async (pageNumber: number) => {
    let showLoading = false;
    try {
      if (this.isErrorAdjustment) {
        Loading.show();
        showLoading = true;
        this.isErrorAdjustment = false;
      }
      const res = await this.io.post("/capi/credit/quota/adjusted-list", {
        pageNo: pageNumber,
        pageSize: 10
      });
      if (!res.data) {
        this.loadingAdjustment = false;
        return;
      }
      if (this.limitDetailInfoAdjustment.records.length > 0) {
        this.limitDetailInfoAdjustment.records = this.limitDetailInfoAdjustment.records.concat(res.data.records);
      } else {
        this.limitDetailInfoAdjustment = res.data;
      }
      this.limitDetailInfoAdjustment.isLast = res?.data?.isLast;
      this.loadingAdjustment = false;
    } catch (e) {
      this.isErrorAdjustment = true;
      this.loadingAdjustment = false;
    } finally {
      if (showLoading) Loading.dismiss();
    }
  };
}
