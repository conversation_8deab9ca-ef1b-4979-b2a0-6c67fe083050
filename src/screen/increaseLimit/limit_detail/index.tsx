/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-06 20:52
 * 额度明细
 */

import React, { Component } from "react";
import { StyleSheet, View, Text } from "react-native";
import { TFunction } from "i18next";
import RootView from "common/components/Layout/RootView";
import store from "./store";
import LimitDetailStore from "./store";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import { FontStyles, FontUtil, NavigationBar, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { tabsStyle, tabStyle } from "../tool/baseStyle";
import NativeSensorModule, { ScreenSensorEvent, SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import UsageDetailScreen from "./usage_detail";
import AdjustmentDetailScreen from "./adjustment_detail";
import NativeNavigationModule, {
  NativeNavigationModuleModel
} from "common/nativeModules/router/nativeNavigationModule";
import RuntimeStore from "common/store/Runtime";
import { runtimeType } from "common/components/BaseContainer/Type";
import { PageConfig, reportClick } from "@akulaku-rn/rn-v4-sdk";
import { Tab, TabBar, Tabs } from "common/components/AKTabView";
import { TabBarProps, TabType } from "common/components/AKTabView/types";

export enum LIMIT_DETAIL_TYPE {
  USAGE,
  ADJUSTMENT
}

type Props = {
  navigation: NativeNavigationModuleModel;
  store: { navParams: { initialPage?: number }; pageStore: LimitDetailStore; runtime: RuntimeStore & runtimeType };
  t: TFunction;
  configSensorEvent: (event: ScreenSensorEvent) => void;
  configPageInfo: (config: PageConfig, isSupportV3?: boolean) => void;
};

type State = {};

const styles = StyleSheet.create({
  scrollTab: {
    height: 40,
    backgroundColor: "#FFF",
    borderBottomWidth: 0
  },
  tabContainer: {
    width: WINDOW_WIDTH,
    justifyContent: "center",
    alignItems: "center"
  },
  title: {
    fontSize: 14,
    ...FontStyles["rob-medium"],
    color: "#999",
    paddingHorizontal: 16
  },
  tab: {
    backgroundColor: "#E62117",
    height: 2,
    width: WINDOW_WIDTH / 2 - 48
  }
});

@RootView({
  withI18n: [
    "increase-detail",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("increase-detail")
@inject("store")
@observer
export default class IncreaseLimitDetail extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
  }

  componentDidMount() {
    NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
      page_id: "913",
      page_name: "amount details page"
    });
  }

  componentWillUnmount() {
    NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
      page_id: "913",
      page_name: "amount details page"
    });
  }

  onBackPress = () => {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "9130103",
      element_name: "return",
      module_id: "01",
      module_name: "details",
      position_id: "03",
      page_name: "amount details page",
      page_id: "913"
    });
    NativeNavigationModule.goBack();
  };

  onChangeTab = ({ i, from }: { i: number; from: number }) => {
    if (from === 0) {
      reportClick({
        cn: 1,
        sn: 100634
      });
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9130102",
        element_name: "usage details",
        module_id: "01",
        module_name: "details",
        position_id: "02",
        page_name: "amount details page",
        page_id: "913"
      });
    } else {
      reportClick({
        cn: 2,
        sn: 100634
      });
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9130101",
        element_name: "adjustment details",
        module_id: "01",
        module_name: "details",
        position_id: "01",
        page_name: "amount details page",
        page_id: "913"
      });
    }
  };

  renderTabBar = (props: TabBarProps): React.ReactElement | null | undefined => {
    return <TabBar {...props} style={styles.scrollTab} underlineStyle={styles.tab} />;
  };

  _renderTab = (tabProps: TabType, activeTab: number) => {
    const isActive = tabProps.index === activeTab;
    return <Text style={[styles.title, isActive ? { color: "#E62117" } : null]}>{tabProps.title}</Text>;
  };

  render() {
    const {
      t,
      store: { navParams }
    } = this.props;

    return (
      <View style={{ flex: 1 }}>
        <NavigationBar title={t("额度明细")} onBackPress={this.onBackPress} />
        <Tabs
          initialPage={navParams.initialPage ?? 0}
          {...tabsStyle}
          onChangeTab={this.onChangeTab}
          renderTabBar={this.renderTabBar}
        >
          <Tab key="installment" title={t("使用明细")} {...tabStyle} renderTab={this._renderTab}>
            <UsageDetailScreen {...this.props} />
          </Tab>
          <Tab key="installment" {...tabStyle} title={t("调整明细")} renderTab={this._renderTab}>
            <AdjustmentDetailScreen {...this.props} />
          </Tab>
        </Tabs>
      </View>
    );
  }
}
