/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 19:33
 * @description
 */
import { StyleSheet } from "react-native";
import { FontStyles } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  devider12: {
    width: "100%",
    height: 12,
    backgroundColor: "#F5F5F5"
  },
  title: {
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    fontSize: 16,
    color: "#999999",
    lineHeight: 50,
    marginLeft: 16
  },
  itemSeparator: {
    width: "100%",
    height: StyleSheet.hairlineWidth,
    backgroundColor: "#ebebeb",
    marginLeft: 16
  },
  devider1: {
    width: "100%",
    height: StyleSheet.hairlineWidth,
    backgroundColor: "#ebebeb"
  },
  sectionHeaderContainer: {
    width: "100%",
    backgroundColor: "white"
  },
  historyOpacity: {
    borderRadius: 30,
    borderWidth: 1,
    borderColor: "#E62117",
    width: 139,
    marginTop: 16,
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    height: 32
  },
  historyText: {
    fontSize: 12,
    color: "#E62117",
    lineHeight: 13
  },
  noDataImg: {
    width: 150,
    height: 150,
    alignSelf: "center",
    marginTop: 109
  },
  noDataText: {
    fontSize: 14,
    color: "#999999",
    lineHeight: 16,
    alignSelf: "center",
    marginLeft: 45,
    marginRight: 45,
    textAlign: "center"
  },
  viewHistoryOpacity: {
    borderRadius: 30,
    borderWidth: 1,
    borderColor: "#B3B3B3",
    width: 139
  },
  viewHistoryText: {
    fontSize: 14,
    color: "#333333",
    height: 15,
    alignSelf: "center",
    marginTop: 12,
    marginBottom: 12
  },
  footerContainer: {
    backgroundColor: "#f5f5f5",
    alignItems: "center"
  },
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5"
  },
  loadingContainer: {
    alignItems: "center",
    flex: 1,
    flexDirection: "row"
  }
});
