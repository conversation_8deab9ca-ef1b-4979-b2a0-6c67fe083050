/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-08 17:37
 * @description 额度明细-调整明细
 */

import React, { Component } from "react";
import { SectionList, Text, View } from "react-native";
import { TFunction } from "i18next";
import LimitDetailStore from "../store";
import { inject, observer } from "mobx-react";
import LimitItem from "../component/LimitItem";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import { getSectionData, Record } from "../../model/LimitDetailInfo";
import { NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import NotDataComponent from "@akulaku-rn/akui-rn/src/components/NotDataComponent";
import ListFooterComponent from "@akulaku-rn/akui-rn/src/components/ListFooterComponent";
import styles from "./styles";
import { NativeNavigationModuleModel } from "common/nativeModules/router/nativeNavigationModule";
import { runtimeType } from "common/components/BaseContainer/Type";
import RuntimeStore from "common/store/Runtime";
import { ScreenSensorEvent } from "common/nativeModules/sdk/nativeSensroModule";

type Props = {
  navigation: NativeNavigationModuleModel;
  store: { pageStore: LimitDetailStore; runtime: RuntimeStore & runtimeType };
  t: TFunction;
  configSensorEvent: (event: ScreenSensorEvent) => void;
};

type State = {};

@inject("store")
@observer
export default class AdjustmentDetailScreen extends Component<Props, State> {
  async componentDidMount() {
    await this.fetchData();
    this.props.store.runtime.setPaddingBottom(false);
  }

  pageNumber = 1;

  renderItem = ({ item, index }: { item: Record; index: number }) => {
    const {
      t,
      store: {
        runtime: { countryCode, countryId }
      }
    } = this.props;
    return (
      <LimitItem
        t={t}
        detailType={2}
        amount={item.amount}
        createdMillis={item.createdMillis}
        desc={item.desc}
        icon={item.icon}
        type={item.type}
        countryCode={countryCode}
        countryId={countryId}
      />
    );
  };

  renderSectionHeader = ({ section: { title } }: any) => {
    return (
      <>
        <View style={styles.devider12} />
        <View style={styles.sectionHeaderContainer}>
          <Text style={styles.title}>{title}</Text>
        </View>
        <View style={styles.devider1} />
      </>
    );
  };

  fetchData = () => {
    const {
      store: { pageStore }
    } = this.props;
    pageStore.fetchAdjustmentDetail(this.pageNumber);
  };

  onEndReached = () => {
    const {
      store: {
        pageStore,
        pageStore: { limitDetailInfoAdjustment }
      }
    } = this.props;
    if (!limitDetailInfoAdjustment.isLast) {
      const callback = () => {
        pageStore.fetchAdjustmentDetail(++this.pageNumber);
      };
      setTimeout(callback, 200);
    }
  };

  listFooterComponent = (isLast: boolean) => {
    const { t } = this.props;
    let footer;
    if (!isLast) {
      footer = <ListFooterComponent isLoding={true} title={t("数据加载中")} />;
    } else {
      footer = <ListFooterComponent title={t("没有更多数据了")} />;
    }
    return <View style={styles.footerContainer}>{footer}</View>;
  };

  keyExtractor = (item: any, index: number) => "" + index;

  itemSeparatorComponent = () => <View style={styles.itemSeparator} />;

  render() {
    const {
      t,
      store: {
        pageStore: { loadingAdjustment, isErrorAdjustment, limitDetailInfoAdjustment }
      }
    } = this.props;
    let content = null;
    if (loadingAdjustment) {
      content = (
        <View style={styles.loadingContainer}>
          <Loading />
        </View>
      );
    } else if (isErrorAdjustment) {
      content = (
        <NetworkErrorComponent
          message={t("global:啊，网络故障导致我们的距离好远啊!")}
          buttonText={t("global:刷新")}
          onRetryPress={this.fetchData}
        />
      );
    } else if (limitDetailInfoAdjustment && limitDetailInfoAdjustment.totalRecords === 0) {
      content = (
        <NotDataComponent
          message={t("无额度调整记录")}
          image={
            "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ec_country_id_Limit_76117685d30c65bebcd0200a559fa54b_noLimitData.png"
          }
        />
      );
    } else {
      content = (
        // @ts-ignore
        <SectionList
          sections={getSectionData(limitDetailInfoAdjustment)} //数据源
          keyExtractor={this.keyExtractor}
          renderItem={this.renderItem}
          renderSectionHeader={this.renderSectionHeader}
          onEndReachedThreshold={0.1}
          onEndReached={this.onEndReached}
          ItemSeparatorComponent={this.itemSeparatorComponent}
          ListFooterComponent={this.listFooterComponent(limitDetailInfoAdjustment.isLast)}
          // @ts-ignore
          ListFooterComponentStyle={{
            flex: 1,
            justifyContent: "flex-end",
            backgroundColor: "#f5f5f5"
          }} //使footer在SectionList(FlatList)未被填满时在最底部
          contentContainerStyle={{
            flexGrow: 1,
            backgroundColor: "white"
          }} //设置SectionList(FlatList)的contentSize最小值等于SectionList(FlatList)的大小（宽高）
        ></SectionList>
      );
    }

    return <View style={styles.container}>{content}</View>;
  }
}
