/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-08 17:44
 * @description
 */

import React, { Component } from "react";
import { View, StyleSheet, Image, Text, TouchableOpacity } from "react-native";
import { ItemDetails, Record } from "../../model/LimitDetailInfo";
import { COUNTRY_CODE } from "common/components/BaseContainer/Type";
import { TimeFormat } from "common/util";
import { PriceComponent } from "@akulaku-rn/akui-rn";
import styles from "./styles";
import { GlobalRuntime } from "common/constant";
import { TFunction } from "i18next";

export default function LimitItem(item: {
  t: TFunction;
  amount: number;
  createdMillis: number;
  desc: string;
  icon: string;
  type: string;
  countryCode: COUNTRY_CODE;
  countryId: number;
  detailType: number; //详情类型,1-->使用明细,2-->变更明细
}) {
  const amountColor = item.amount > 0 ? "#e62117" : "#7fb800";
  const amountPrefix = item.amount > 0 ? "+Rp" : "-Rp";
  return (
    <View
      style={{
        paddingHorizontal: 16,
        paddingVertical: 24
      }}
    >
      <View
        style={{
          flexDirection: "row"
        }}
      >
        <Image
          source={{ uri: item.icon }}
          style={{
            width: 16,
            height: 16
          }}
        />
        <Text style={styles.type} ellipsizeMode={"tail"} numberOfLines={1}>
          {item.type}
        </Text>
        <Text style={styles.createMillis}>
          {TimeFormat(Number.parseInt(item.createdMillis + ""), "DD/MM/YYYY hh:mm:ss", item.countryCode)}
        </Text>
      </View>
      <View
        style={{
          flexDirection: "row",
          marginTop: 12
        }}
      >
        <Text style={styles.desc} ellipsizeMode={"tail"}>
          {item.desc}
        </Text>
        <Text
          style={[
            styles.price,
            {
              color: amountColor,
              right: 0
            }
          ]}
        >
          {amountPrefix +
            PriceComponent.priceFormat(Math.abs(item.amount), GlobalRuntime.countryId, { needUnit: false })}
        </Text>
      </View>
    </View>
  );
}
