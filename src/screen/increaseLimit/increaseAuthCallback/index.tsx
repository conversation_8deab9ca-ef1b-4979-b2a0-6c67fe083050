/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022-03 17:20
 * @description 提额授权回调页
 */

import React, { Component } from "react";
import { View } from "react-native";
import CountLoading from "../../loading";
import { get } from "lodash";
import { NativeNavigationModule } from "common/nativeModules";
import { inject } from "mobx-react";
import store from "./store";
import RootView from "common/components/Layout/RootView";
import { AuthSource } from "../../auth/types";

type State = {};

type Props = {
  store: {
    pageStore: store;
    navParams: {
      jumpSource?: number; //跳转源 0-->提额, 1-->交易链路, 2-->广告, 3-->现金贷, 4-->现金分期
      jumpLink?: string; //如果有传入链接,授权登录成功跳转链接
      dataString?: string; //授权成功跳转需要携带的数据
      name?: string; //授权页面标题
      source?: AuthSource; // 是否是提额
      processId?: string;
      type: string; // 接口参数type
      path?: string; // 电商必须要有
      extra: string;
      sessiondId: string;
      account: string;
      title: string; //标题
    };
    [key: string]: any;
  };
};

@RootView({
  store
})
@inject("store")
export default class IncreaseAuthCallback extends Component<Props, State> {
  async componentDidMount() {
    CountLoading.show();
    const {
      store: {
        navParams: { processId, sessiondId, account, extra, title }
      }
    } = this.props;
    let extraParse: Record<string, string>;
    try {
      extraParse = JSON.parse(extra);
    } catch (e) {
      console.log(`e: `, e);
      extraParse = {};
    }
    console.log("zhangxiao", "processId-->" + processId);
    if (!processId) {
      CountLoading.dismiss();
      NativeNavigationModule.navigate({
        screen: "ResultPage",
        params: {
          gestureEnabled: false,
          status: 2,
          extra: {
            ...extraParse
          }
        }
      });
      return;
    }
    const entryRes = await this.props.store.pageStore.getEntryId(processId);
    if (!entryRes.success) {
      CountLoading.dismiss();
      NativeNavigationModule.navigate({
        screen: "ResultPage",
        params: {
          gestureEnabled: false,
          status: 2,
          extra: {
            ...extraParse
          }
        }
      });
      return;
    }
    const entriesPre = get(entryRes, "data.entityInfos", []);
    const entries = entriesPre.map((item: any, index: number) => {
      return {
        entryId: item.entryId,
        type: "auth",
        value: index === 0 ? sessiondId : account
      };
    });
    const increaseLimitRes = await this.props.store.pageStore.increaseLimit({
      processId,
      entries
    });
    if (!increaseLimitRes.success) {
      CountLoading.dismiss();
      NativeNavigationModule.navigate({
        screen: "ResultPage",
        params: {
          title: title,
          gestureEnabled: false,
          status: 2,
          extra: {
            ...JSON.parse(extra)
          }
        }
      });
      return;
    }
    const p1 = new Promise((resolve, reject) => {
      setTimeout(() => {
        reject("failed");
      }, 5000);
    });
    const p2 = this.props.store.pageStore.queryIncreaseStatus({
      asiApplicationId: increaseLimitRes.data.asiApplicationId
    });
    Promise.race([p1, p2])
      .then((queryRes: any) => {
        if (queryRes?.success) {
          switch (
            queryRes.data.status //申请状态，0,未申请过, 1申请中，7申请失败， 8 申请成功
          ) {
            case 0:
            case 7:
              NativeNavigationModule.navigate({
                screen: "ResultPage",
                params: {
                  title: title,
                  gestureEnabled: false,
                  status: 2,
                  extra: {
                    ...JSON.parse(extra)
                  }
                }
              });
              break;
            case 1:
              NativeNavigationModule.navigate({
                screen: "ResultPage",
                params: {
                  title: title,
                  gestureEnabled: false,
                  status: 0,
                  extra: {
                    ...JSON.parse(extra)
                  }
                }
              });
              break;
            case 8:
              NativeNavigationModule.navigate({
                screen: "ResultPage",
                params: {
                  title: title,
                  gestureEnabled: false,
                  status: 1,
                  increaseQuota: queryRes.data?.increaseQuota, //提升额度
                  extra: {
                    ...JSON.parse(extra)
                  }
                }
              });
              break;
          }
        } else {
          CountLoading.dismiss();
          NativeNavigationModule.navigate({
            screen: "ResultPage",
            params: {
              title: title,
              gestureEnabled: false,
              status: 2,
              extra: {
                ...JSON.parse(extra)
              }
            }
          });
          return;
        }
      })
      .catch(() => {
        CountLoading.dismiss();
        NativeNavigationModule.navigate({
          screen: "ResultPage",
          params: {
            title: title,
            gestureEnabled: false,
            status: 2,
            extra: {
              ...JSON.parse(extra)
            }
          }
        });
      });
  }

  render() {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: "#E5E5E5"
        }}
      />
    );
  }
}
