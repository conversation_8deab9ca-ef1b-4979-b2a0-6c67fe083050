/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022-04 14:57
 * @description 提额授权回调
 */

import React from "react";
import { View, StyleSheet, DeviceEventEmitter } from "react-native";
import Basic from "common/store/Basic";
import { action } from "mobx";
import { INCREASE_LIMIT, INCREASE_PAGE_INFO, QUERY_INCREASE_STATUS } from "../../../auth/api";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { isEqual } from "lodash";
import CountLoading from "../../../loading";

export default class IncreaseAuthCallbackStore extends Basic {
  getEntryId = (processId: string): Promise<{ success: boolean; [key: string]: any }> => {
    return new Promise(resolve => {
      this.io.POST(
        INCREASE_PAGE_INFO,
        { processId },
        (response: any) => {
          const { data, success, errCode, errMsg } = response;
          if (!success) {
          }
          NativeToast.showMessage(errMsg);
          resolve(response);
        },
        (error: any) => {
          const { data, success, code, message } = error;
          NativeToast.showMessage(error.errorMsg || error.message);
          resolve({
            success,
            errCode: code,
            errMsg: message
          });
        }
      );
    });
  };

  /**
   * 发起提额申请
   * @param params   value： 授权成功的sessionId
   * */
  increaseLimit = (params: {
    processId: string;
    entries: Array<{ entryId: number; type: string; value: string }>;
  }): Promise<{ success: boolean; [key: string]: any }> => {
    return new Promise(resolve => {
      this.io.POST(
        INCREASE_LIMIT,
        params,
        (response: any) => {
          DeviceEventEmitter.emit("increaseLimitUpdate");
          const { data, success, errCode, errMsg } = response;
          if (isEqual(errCode, "UA.0057")) {
            CountLoading.dismiss();
            // DialogContainer.show({
            //   renderContent: <DialogTopLimit t={this.t} msg={errMsg} />
            // });
          } else if (!success) {
            CountLoading.dismiss();
            NativeToast.showMessage(errMsg);
          }
          resolve(response);
        },
        (error: any) => {
          CountLoading.dismiss();
          const { data, success, code, message } = error;
          NativeToast.showMessage(error.errorMsg || error.message);
          resolve({
            success,
            errCode: code,
            errMsg: message
          });
        }
      );
    });
  };

  /**
   * 查询提额状态
   * @param processId
   */
  queryIncreaseStatus = (params: { asiApplicationId: string }): Promise<{ success: boolean; [key: string]: any }> => {
    return new Promise(resolve => {
      this.io.POST(
        QUERY_INCREASE_STATUS,
        params,
        (response: any) => {
          CountLoading.dismiss();
          resolve(response);
        },
        (error: any) => {
          const { data, success, code, message } = error;
          CountLoading.dismiss();
          resolve({
            success,
            errCode: code,
            errMsg: message
          });
        }
      );
    });
  };
}
