/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-06 20:52
 * 资料提额
 */

import React, { Component } from "react";
import { Image, ImageBackground, ScrollView, Text, View } from "react-native";
import { TFunction } from "i18next";
import RootView from "common/components/Layout/RootView";
import store from "./store";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import NavigationBar from "@akulaku-rn/akui-rn/src/components/NavigationBar";
import { INCREASE_LIMIT_TYPE, Item } from "../model/IncreaseLimitInfo";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import DefaultComponent from "common/components/DefaultComponent";
import styles from "./styles";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";
import IncreaseLimitItem from "../component/IncreaseLimitItem";
import { COUNTRY_CODE } from "common/components/BaseContainer/Type";
import { NetworkErrorComponent, UrlImage } from "@akulaku-rn/akui-rn";
import PerformanceMonitor from "common/performanceMonitor/PerformanceMonitor";

type Props = {
  navigation: any;
  store: { [key: string]: any; pageStore: store };
  t: TFunction;
  configSensorEvent: any;
};

type State = {
  [key: string]: any;
};

@RootView({
  withI18n: [
    "increase-info",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("increase-info")
@inject("store")
@observer
export default class IncreaseLimitByInfo extends Component<Props, State> {
  constructor(props: any) {
    super(props);
    const {
      store: {
        navParams: { categoryType }
      }
    } = this.props;
    PerformanceMonitor.trackSettingScreenSub("categoryType_" + categoryType);
    this.props.store.runtime.setPaddingBottom(false);
    //v4埋点
    props.configPageInfo({ sn: 100632 }, true);
  }

  componentDidMount() {
    this.fetchData();
    NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
      page_id: "914",
      page_name: "credit limit detail sub-page",
      extra: {
        Aku_channelName: this.props.store.navParams.categoryType
      }
    });
  }

  componentWillUnmount() {
    NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
      page_id: "914",
      page_name: "credit limit detail sub-page",
      extra: {
        Aku_channelName: this.props.store.navParams.categoryType
      }
    });
  }

  fetchData = () => {
    const {
      store: {
        pageStore,
        navParams: { categoryType }
      }
    } = this.props;
    pageStore.fetchData(categoryType);
  };

  onBackPress = () => {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "9140101",
      element_name: "return",
      module_id: "01",
      module_name: "sub",
      position_id: "01",
      page_name: "credit limit detail sub-page",
      page_id: "914",
      extra: {
        Aku_channelName: this.props.store.navParams.categoryType
      }
    });
    NativeNavigationModule.goBack();
  };

  callbackfn = (item: Item, index: number) => {
    return (
      <IncreaseLimitItem
        {...item}
        key={`${index}`}
        position={index}
        t={this.props.t}
        countryCode={this.props.store.runtime.countryCode}
      />
    );
  };

  render() {
    const {
      t,
      store: {
        navParams: { categoryType },
        pageStore: { loading, isError, increaseLimitInfo },
        runtime: { countryCode }
      }
    } = this.props;
    let navigationTitle;
    let headerImg = "";
    let bannerText;
    switch (categoryType) {
      case INCREASE_LIMIT_TYPE.INFORMATION:
        PerformanceMonitor.trackSettingScreenSub("INFORMATION");
        navigationTitle = t("资料提额");
        headerImg =
          countryCode === COUNTRY_CODE.ID
            ? "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/information_img_bg_in.png"
            : "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/information_img_bg_en.png";
        bannerText = t("补充资料提额");
        break;
      case INCREASE_LIMIT_TYPE.AUTHORIZATION:
        PerformanceMonitor.trackSettingScreenSub("AUTHORIZATION");
        navigationTitle = t("账户授权提额");
        headerImg =
          countryCode === COUNTRY_CODE.ID
            ? "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/account_img_bg_in.png"
            : "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/account_img_bg_en.png";
        bannerText = t("账号授权提额");
        break;
      case INCREASE_LIMIT_TYPE.OTHER:
        PerformanceMonitor.trackSettingScreenSub("OTHER");
        navigationTitle = t("其他信息提额");
        headerImg =
          countryCode === COUNTRY_CODE.ID
            ? "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/consumptionincrease_img_bg_in.png"
            : "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/consumptionincrease_img_bg_en.png";
        bannerText = t("其他信息提额");
        break;
    }

    let content;
    if (loading) {
      content = <Loading />;
    } else if (isError) {
      content = (
        <NetworkErrorComponent
          message={t("global:啊，网络故障导致我们的距离好远啊!")}
          buttonText={t("global:刷新")}
          onRetryPress={this.fetchData}
        />
      );
    } else {
      content = (
        <ScrollView>
          <UrlImage source={headerImg} isBackground={true} style={styles.headerImg}>
            <View
              style={{
                flexDirection: "row"
              }}
            ></View>
            <View style={styles.bannerBottomContainer}>
              <Image source={require("../img/icon_prompt.webp")} style={styles.bannerBottomImg} />
              <Text style={styles.bannerBottomText}>
                {t("我们将为您提供数据安全防护以及对您的个人信息进行隐私加密")}
              </Text>
            </View>
          </UrlImage>
          <View style={styles.listContainer}>
            {increaseLimitInfo && increaseLimitInfo.processes && increaseLimitInfo.processes.map(this.callbackfn)}
          </View>
          <View style={styles.functionContainer}>
            <View style={styles.functionLine} />
            <Text style={[styles.functionText, { fontSize: 10, color: "#989FA9" }]}>{t("没有更多了")}</Text>
            <View style={styles.functionLine} />
          </View>
        </ScrollView>
      );
    }
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: "#f5f5f5"
        }}
      >
        <NavigationBar title={navigationTitle} onBackPress={this.onBackPress} />
        {content}
      </View>
    );
  }
}
