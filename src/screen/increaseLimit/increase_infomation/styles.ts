/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-09 19:13
 * @description
 */
import { StyleSheet } from "react-native";
import { WINDOW_WIDTH, FontStyles } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  headerImg: {
    width: WINDOW_WIDTH,
    height: WINDOW_WIDTH * 0.462,
    paddingLeft: 16
  },
  bannerText: {
    ...StyleSheet.flatten(FontStyles["DIN-Bold"]),
    fontSize: 13,
    color: "#ffffff",
    lineHeight: 14,
    width: 220,
    marginTop: 21
  },
  countryCode: {
    ...StyleSheet.flatten(FontStyles["DIN-Bold"]),
    fontSize: 14,
    color: "#ffffff",
    lineHeight: 16,
    alignSelf: "flex-end",
    bottom: 2,
    marginRight: 5
  },
  bannerBottomContainer: {
    flexDirection: "row",
    position: "absolute",
    bottom: 0,
    paddingLeft: 16,
    paddingBottom: 20,
    paddingTop: 8,
    backgroundColor: "rgba(255,255,255,0.19)",
    width: WINDOW_WIDTH
  },
  bannerBottomImg: {
    width: 16,
    height: 16,
    marginRight: 12,
    alignSelf: "center"
  },
  bannerBottomText: {
    fontSize: 10,
    color: "rgba(255,255,255,0.80)",
    lineHeight: 11,
    textAlignVertical: "center",
    width: WINDOW_WIDTH - 60
  },
  listContainer: {
    width: WINDOW_WIDTH,
    backgroundColor: "#fff",
    top: -12,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12
  },
  functionContainer: {
    width: WINDOW_WIDTH,
    height: 40,
    flexDirection: "row",
    backgroundColor: "#F5F5F5",
    alignItems: "center",
    justifyContent: "center"
  },
  functionLine: {
    width: 32,
    height: 1,
    backgroundColor: "#c8ced4"
  },
  functionText: {
    color: "#3C444C",
    fontSize: 14,
    marginRight: 12,
    marginLeft: 6
  }
});
