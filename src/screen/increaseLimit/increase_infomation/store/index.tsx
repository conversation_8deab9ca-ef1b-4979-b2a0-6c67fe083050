/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-07 22:21
 * @description
 */

import React from "react";
import Basic from "common/store/Basic";
import { action, observable } from "mobx";
import { IncreaseLimitInfo } from "../../model/IncreaseLimitInfo";
import NativeEventReportModule, { ReportScene } from "common/nativeModules/basics/nativeEventReportModule";

export default class Store extends Basic {
  @observable loading = true;

  @observable isError = false;

  @observable increaseLimitInfo: IncreaseLimitInfo = {
    processes: []
  };

  @action
  fetchData = async (categoryType: number) => {
    this.loading = true;
    this.isError = false;
    try {
      const allDeviceInfo = await NativeEventReportModule.getAllDeviceInfo(ReportScene.SUBMIT_CREDIT_AUTH);

      const responseIncreaseLimitInfo = await this.io.post("/capi/credit/increase-quota/process/get-list", {
        deviceInfo: JSON.stringify(allDeviceInfo),
        // deviceInfo: '{"device_id":"sainefljaflf"}',
        type: categoryType
      });
      if (!responseIncreaseLimitInfo.data) {
        this.isError = true;
        return;
      }
      this.increaseLimitInfo = responseIncreaseLimitInfo.data;
    } catch (e) {
      console.log(e);
      this.isError = true;
    } finally {
      this.loading = false;
    }
  };
}
