import { StyleSheet } from "react-native";
import FontStyles from "@akulaku-rn/akui-rn/src/components/FontUtil/FontStyles";
import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 32,
    paddingHorizontal: 24
  },
  title: {
    ...FontStyles["rob-medium"],
    fontSize: 16,
    color: "#282B2E",
    marginTop: 12,
    textAlign: "center"
  },
  content: {
    fontSize: 14,
    color: "#6E737D",
    marginTop: 12,
    textAlign: "center"
  },
  touch: {
    width: WINDOW_WIDTH - 64,
    height: 44,
    borderRadius: 8,
    backgroundColor: "#FF5353",
    marginTop: 24,
    alignItems: "center",
    justifyContent: "center"
  },
  gobackText: {
    ...FontStyles["rob-medium"],
    fontSize: 16,
    color: "#FFFFFF"
  },
  amount: {
    color: "#e62117",
    ...FontStyles["rob-medium"],
    fontSize: 14
  }
});
