/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2021-06 11:48
 * @description 结果页
 */

import React, { Component } from "react";
import { BackHand<PERSON>, DeviceEventEmitter, Image, NativeEventSubscription, Text, View } from "react-native";
import { NavigationBar, PriceComponent } from "@akulaku-rn/akui-rn";
import { get } from "lodash";
import RootView from "common/components/Layout/RootView";
import store from "../store";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import { TFunction } from "i18next";
import { NativeNavigationModule } from "common/nativeModules";
import { ScreenSensorEvent } from "common/nativeModules/sdk/nativeSensroModule";
import { AKButton, AKButtonType } from "common/components/AKButton";
import { PageConfig } from "@akulaku-rn/rn-v4-sdk";
import AuthSensorManager, { getExtraBaseParams, setBaseParmas } from "../tool/AuthSensorManager";
import styles from "./styles";

type State = {};

type Props = {
  t: TFunction;
  store: {
    runtime: { countryId: number };
    navParams: {
      title: string;
      status: number; //0:提交成功,1:提额成功,2:提额失败
      increaseQuota: number; //提升额度
      extra: { [key: string]: any };
    };
  };
  configSensorEvent: (event: ScreenSensorEvent) => void;
  configPageInfo: (config: PageConfig, isSupportV3?: boolean) => void;
};

@RootView({
  withI18n: [
    "Auth-result",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      ms: require("../i18n/ms.json"),
      vi: require("../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("Auth-result")
@inject("store")
@observer
export default class ResultPage extends Component<Props, State> {
  backHandler?: NativeEventSubscription;

  constructor(props: Props) {
    super(props);
    const {
      store: { navParams }
    } = this.props;
    const status = get(this.props.store, "navParams.status", 0);
    switch (status) {
      case 0:
        this.pageStatus = "review";
        break;
      case 1:
        this.pageStatus = "success";
        break;
      case 2:
        this.pageStatus = "fail";
        break;
    }
    setBaseParmas(navParams.extra);
    this.props.configSensorEvent({
      // ...getPageBaseParams(),
      page_id: "1000",
      page_name: "Increase quota result page",
      extra: { ...getExtraBaseParams(), Aku_PageStatus: this.pageStatus }
    });
    this.props.configPageInfo(AuthSensorManager.v4.getResultScreenParams());
  }

  pageStatus = "";

  goback = () => {
    AuthSensorManager.sc.clickReturnCenter(this.pageStatus);
    AuthSensorManager.v4.clickBack();
    NativeNavigationModule.popToScreen("/increaseLimit");
  };

  onBackPress = () => {
    AuthSensorManager.sc.clickBackOnResultPage(this.pageStatus);
    NativeNavigationModule.popToScreen("/increaseLimit");
  };

  componentDidMount() {
    DeviceEventEmitter.emit("increaseLimitUpdate");
    this.backHandler = BackHandler.addEventListener("hardwareBackPress", () => {
      this.onBackPress();
      return true;
    });
  }

  componentWillUnmount() {
    this.backHandler && this.backHandler.remove();
  }

  render() {
    const {
      t,
      store: {
        runtime: { countryId },
        navParams: { increaseQuota = 0, title }
      }
    } = this.props;
    const status = get(this.props.store, "navParams.status", 0);
    let image;
    let headline;
    let content = null;
    switch (status) {
      case 0:
        image = require("../img/icon_succeed.webp");
        headline = t("请等待提额结果");
        content = (
          <Text style={styles.content}>{t("AKULAKU 已经收到了您提交的信息，当前正在审核中。请您耐心等待")}</Text>
        );
        break;
      case 1:
        image = require("../img/icon_credit_limit_succeed.webp");
        headline = t("恭喜，提额成功啦！");
        if (increaseQuota > 0) {
          content = (
            <Text style={styles.content}>
              {t("分期额度提升")}
              <Text style={styles.amount}>
                {PriceComponent.priceFormat(increaseQuota, countryId, { needUnit: true })}
              </Text>
            </Text>
          );
        }
        break;
      case 2:
        image = require("../img/icon_fail.webp");
        headline = t("提额失败");
        content = <Text style={styles.content}>{t("很抱歉您的提额未成功，请尝试其他提额方式")}</Text>;
        break;
    }

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: "#fff"
        }}
      >
        <NavigationBar title={title} onBackPress={this.onBackPress} />
        <View style={styles.container}>
          <Image
            source={image}
            style={{
              width: 159,
              height: 110
            }}
          />
          <Text style={styles.title}>{headline}</Text>
          {content ? <Text style={styles.content}>{content}</Text> : null}
          <AKButton
            onPress={this.goback}
            text={t("返回额度中心")}
            type={AKButtonType.B1_1_3}
            style={{
              marginTop: 20
            }}
          />
        </View>
      </View>
    );
  }
}
