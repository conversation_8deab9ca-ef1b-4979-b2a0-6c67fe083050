import React, { PureComponent, Component } from "react";
import { View, Text, TouchableOpacity, Image, TextInput, DeviceEventEmitter, SectionList } from "react-native";
import { NavigationBar } from "@akulaku-rn/akui-rn";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import store from "../../../store";
import _ from "lodash";
import { inject, observer } from "mobx-react";
import styles from "./styles";
import api from "../../../store/api";
import { EnterLeavePage } from "../../../tool/EventTracking";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";

type Props = {
  t: any;
  store: any;
  navigation: any;
  configPageInfo: any;
};

type State = {
  titile: string;
  data: any[];
};

@RootView({
  store,
  keyApi: ["/capi/credit/search/industry"]
})
@withTranslation("OrderIDApplyInfo")
@inject("store")
@observer
export default class CreditInitialsSearch extends Component<Props, State> {
  flatListRef: any;

  textInputRef: any;

  searchKey: any;

  constructor(props: Props) {
    super(props);
    const {
      store: { navParams }
    } = props;
    this.state = {
      titile: "",
      data: []
    };
    this.searchKey = "";
    this.textInputRef = null;
    props.configPageInfo({ sn: 300020, sp: { status: navParams.isFirstApply ? "first" : "not_first" } }, false);
  }

  componentDidMount() {
    this.featchCompanyData();
  }

  didSelectedItem = (data: { name: string }) => {
    const {
      store: {
        navParams: { pageType, isFirstApply, id }
      }
    } = this.props;
    const params = {
      data,
      id
    };
    DeviceEventEmitter.emit("setValue", params);
    const enterPageData = { pageType, isFirst: isFirstApply, enter: true };
    EnterLeavePage(enterPageData);
    this.props.navigation.goBack();
  };

  featchCompanyData = () => {
    const {
      store: {
        pageStore,
        runtime: { countryId }
      }
    } = this.props;
    const url = api.SEARCH_INDUSTRY;
    pageStore.post(
      url,
      { key: this.searchKey, countryId },
      (response: any) => {
        const data = this.formatData(response.data);
        this.setState({ data });
      },
      (error: any) => {
        const { message } = error;
        Toast.show(message, {
          position: 0
        });
      }
    );
  };

  formatData = (data: any[]) => {
    //把数据处理为SectionList可用数据
    let initialsArray = [];
    for (let i = 0, len = data.length; i < len; i++) {
      const initials = data[i].name.substr(0, 1).toUpperCase();
      initialsArray.push(initials);
    }
    initialsArray = _.uniq(initialsArray).sort();
    const DataArray: any[] = [];
    for (let i = 0, len = initialsArray.length; i < len; i++) {
      const newData: any[] = [];
      for (let k = 0, length = data.length; k < length; k++) {
        if (data[k].name.substr(0, 1).toUpperCase() === initialsArray[i]) {
          newData.push(data[k]);
        }
        if (k + 1 === length) {
          DataArray.push({
            key: initialsArray[i],
            data: newData
          });
        }
      }
    }
    return DataArray;
  };

  onChangeText = (searchKey: string) => {
    this.searchKey = searchKey;
    _.debounce(() => {
      this.featchCompanyData();
    }, 500)();
  };

  renderItem = ({ item, index, section }: { item: any; index: number; section: any }) => {
    const {
      store: { navParams }
    } = this.props;
    return <Button data={item} key={index} lastValue={navParams.lastValue} didSelectedItem={this.didSelectedItem} />;
  };

  renderSectionHeader = ({ section: { key } }: { section: { key: string } }) => {
    return <Text style={styles.section}>{key}</Text>;
  };

  render() {
    const {
      t,
      store: { navParams }
    } = this.props;
    const { data } = this.state;
    return (
      <View style={styles.container}>
        <NavigationBar title={navParams.title} />
        {!!data.length && (
          <SectionList
            style={{ backgroundColor: "#fff" }}
            showsVerticalScrollIndicator={false}
            renderItem={this.renderItem}
            // @ts-ignore
            renderSectionHeader={this.renderSectionHeader}
            stickySectionHeadersEnabled={true}
            sections={data}
            keyExtractor={(item, index) => item + index}
          />
        )}
      </View>
    );
  }
}

type SelectProps = {
  data: any;
  didSelectedItem: any;
  lastValue: string;
};
class Button extends PureComponent<SelectProps> {
  selectItem = () => {
    const { data, didSelectedItem } = this.props;
    didSelectedItem(data);
  };

  render() {
    const { data } = this.props;
    return (
      <TouchableOpacity style={styles.button} onPress={this.selectItem}>
        <Text style={[styles.buttonTitle]}>{data.name}</Text>
      </TouchableOpacity>
    );
  }
}
