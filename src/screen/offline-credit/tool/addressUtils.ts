/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/tool/addressUtils.ts
 */

import { NativeNavigationModule } from "@akulaku-rn/akulaku-ec-common/src/nativeModules";
import { DeviceEventEmitter } from "react-native";
import { EVENT_SELECT_ADDRESS } from "../../../constant";

export interface CityInfo {
  province: string;
  provinceId: string;
  city: string;
  cityId: string;
}

export interface ResponseAddress {
  success: boolean;
  data: CityInfo | null;
}

export const handleCityAddress = async (cityInfo: CityInfo) => {
  NativeNavigationModule.navigate({
    screen: "SelectAddress",
    params: { addressInfo: cityInfo }
  });
  return new Promise<ResponseAddress>(resolve => {
    const listener = DeviceEventEmitter.addListener(EVENT_SELECT_ADDRESS, (result: ResponseAddress) => {
      listener.remove();
      resolve(result);
    });
  });
};
