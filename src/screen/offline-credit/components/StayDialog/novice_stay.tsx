import { reportExpose, reportClick } from "common/components/ADGroup/helpers";
import { ResAD } from "common/components/ADGroup/helpers/types";
import { Android, UrlImage } from "@akulaku-rn/akui-rn";
import { get, isNil } from "lodash";
import React, { PureComponent } from "react";
import { View, TouchableOpacity, StyleSheet, Animated, BackHandler } from "react-native";
import RootSiblings from "react-native-root-siblings";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { NativeNavigationModule } from "common/nativeModules";
import store from "../../store";
import { reportClick as reportClickV4 } from "@akulaku-rn/rn-v4-sdk";
import { NoviceStaySensor } from "../../tool/EventTracking";

type Options = {
  options: any;
  store: pageStoreModel<store>;
  data: ResAD;
};

type Props = {
  options: any;
  store: pageStoreModel<store>;
  data: ResAD;
};

export default class NoviceStay extends PureComponent<Props> {
  opacity: Animated.Value;

  showAni: Animated.CompositeAnimation;

  dismissAni: Animated.CompositeAnimation;

  backHandlerListener: any;

  static show(options: Options) {
    NoviceStay.dialogArr.push(
      new RootSiblings(
        (
          <NoviceStay
            ref={(comp: any) => {
              if (comp) {
                NoviceStay.refArr.push(comp);
              }
            }}
            key={"AKDialog"}
            {...options}
          />
        )
      )
    );
  }

  static dismiss() {
    if (NoviceStay.dialogArr.length >= 1) {
      const ref = this.refArr.pop();
      ref && ref.dismiss();
      // @ts-ignore
      const window = NoviceStay.dialogArr.pop();
      window && window.destroy();
    }
  }

  constructor(props: any) {
    super(props);
    this.opacity = new Animated.Value(0);
    this.showAni = Animated.spring(this.opacity, {
      toValue: 1,
      useNativeDriver: true
    });
    this.dismissAni = Animated.spring(this.opacity, {
      toValue: 0,
      useNativeDriver: true
    });
  }

  componentDidMount(): void {
    const { data } = this.props;
    this.showAni.start();
    reportExpose(data.id);
    NoviceStaySensor.noviceStayPopView({
      pop_id: "rn100004",
      pop_name: "Recall reminder",
      adPositionid: data.spotId,
      advertisingID: data.id,
      image_id: data.creatives[0].id
    });
    if (Android) {
      this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
    }
  }

  componentWillUnmount() {
    if (Android) {
      this.backHandlerListener && this.backHandlerListener.remove();
    }
  }

  static refArr: Array<any> = [];

  static dialogArr: Array<RootSiblings> = [];

  listenerArr: Array<any> = [];

  onBackPress = () => {
    const { data } = this.props;
    reportClick(data.id);
    reportClickV4({
      sn: 300097,
      cn: 1001,
      bct: 8,
      bi: { spot_id: data.spotId, ad_id: data.id, image_id: data.creatives[0].id }
    });
    NoviceStaySensor.noviceStayPopClickImprove({
      pop_id: "rn100004",
      pop_name: "Recall reminder",
      adPositionid: data.spotId,
      advertisingID: data.id,
      image_id: data.creatives[0].id
    });
    NoviceStay.dismiss();
    return true;
  };

  dismiss() {
    this.props.options.onClose();
    this.dismissAni.start(() => {
      this.setState({
        visible: false
      });
    });
  }

  onNegativePress = () => {
    const {
      data,
      store: {
        navParams: { gobackNum }
      }
    } = this.props;
    reportClick(data.id);
    reportClickV4({
      sn: 300097,
      cn: 1002,
      bct: 8,
      bi: { spot_id: data.spotId, ad_id: data.id, image_id: data.creatives[0].id }
    });
    NoviceStaySensor.noviceStayPopClickGiveUp({
      pop_id: "rn100004",
      pop_name: "Recall reminder",
      adPositionid: data.spotId,
      advertisingID: data.id,
      image_id: data.creatives[0].id
    });
    if (!isNil(gobackNum)) {
      NativeNavigationModule.popTo(parseInt(gobackNum + "") + 1);
    } else {
      NativeNavigationModule.goBack();
    }
  };

  render() {
    const { data } = this.props;
    return (
      <Animated.View
        style={[
          styles.container,
          {
            opacity: this.opacity
          }
        ]}
      >
        <UrlImage style={styles.adImg} source={get(data, "creatives[0].image")} />
        <View style={styles.buttonView}>
          <TouchableOpacity onPress={this.onBackPress} style={styles.button1} />
          <TouchableOpacity onPress={this.onNegativePress} style={styles.button2} />
        </View>
      </Animated.View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    alignItems: "center",
    justifyContent: "center"
  },
  adImg: {
    width: 300,
    height: 343
  },
  buttonView: {
    marginTop: -82,
    alignItems: "center"
  },
  button1: {
    width: 183,
    height: 36,
    marginBottom: 6
  },
  button2: {
    width: 118,
    height: 25
  }
});
