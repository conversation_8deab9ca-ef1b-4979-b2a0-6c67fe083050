/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/components/SelectItem/ConfirmInfo.tsx
 */

import { ScrollView, StyleProp, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from "react-native";
import React, { useEffect, useState } from "react";
import { AKButton, FontStyles, isShapedIOS, WINDOW_HEIGHT } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";

const DATA = [
  "国籍",
  "姓名-预览",
  "身份证号码",
  "出生地（城市）",
  "出生日期",
  "性别",
  "居住地址",
  "身份证地址",
  "职业类型",
  "每月收入",
  "收入来源"
];

interface Props {
  t: any;
  onPressReset: () => void;
  onPressContinue: () => void;
}

const ConfirmInfo = ({ t, onPressReset, onPressContinue }: Props) => {
  const _renderItem = (text: string, index: number) => {
    return (
      <View key={`ConfirmInfo_${index}`} style={styles.dotView}>
        <View style={styles.dot} />
        <Text style={styles.cell}>{t(text)}</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView>
        <Text style={styles.title}>
          {t("若您有多种职业，请填写有收入的职业类型。如您可能同时是劳动者，家庭主妇，职员，内容创作者等多种职业。")}
        </Text>

        <View style={styles.text}>
          <Text style={styles.cell}>{t("资金提供者信息（预览）")}</Text>
          {DATA.map((item, index) => _renderItem(item, index))}
        </View>
      </ScrollView>
      <View style={styles.bottom}>
        <CountDownBtn onPress={onPressContinue} title={t("继续-预览")} style={{ flex: 1, marginRight: 12 }} />
        <AKButton onPress={onPressReset} style={{ flex: 1 }} text={t("重新选择")} type={AKButtonType.B1_1_2} />
      </View>
      {isShapedIOS && <View style={{ height: 40 }} />}
    </View>
  );
};

interface BtnProps {
  title: string;
  style: StyleProp<ViewStyle>;
  onPress: () => void;
}

const CountDownBtn = ({ title, style, onPress }: BtnProps) => {
  const [count, setCount] = useState(3);
  useEffect(() => {
    let initCount = 3;
    const interval = setInterval(() => {
      initCount--;
      if (initCount <= 0) {
        clearInterval(interval);
      }
      setCount(initCount);
    }, 1000);
  }, []);

  return (
    <TouchableOpacity
      onPress={() => {
        if (count) return;
        onPress();
      }}
      style={[styles.btn, style, { borderColor: count === 0 ? "#6E737D" : "#E2E5E9" }]}
    >
      <Text style={{ fontSize: 16, color: count ? "#989FA9" : "#282B2E", ...FontStyles["rob-medium"] }}>
        {count === 0 ? title : `${title}(${count}s)`}
      </Text>
    </TouchableOpacity>
  );
};

export default ConfirmInfo;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    maxHeight: WINDOW_HEIGHT * 0.85 - 66
  },
  dotView: { flexDirection: "row", alignItems: "center", marginTop: 12 },
  dot: { width: 4, height: 4, borderRadius: 2, marginRight: 12, backgroundColor: "#6E737D" },
  text: { padding: 16, backgroundColor: "#EFF2F6", borderRadius: 12 },
  btn: {
    height: 40,
    borderWidth: 1,
    borderRadius: 22,
    paddingHorizontal: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  title: {
    fontSize: 11,
    color: "#6E737D",
    marginBottom: 12
  },
  bottom: {
    marginTop: 12,
    paddingBottom: 16,
    flexDirection: "row"
  },
  cell: {
    fontSize: 14,
    ...FontStyles["rob-medium"],
    color: "#282B2E"
  }
});
