import { StyleSheet } from "react-native";
import { FontStyles } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    alignItems: "center"
  },
  topView: {
    marginTop: 68,
    marginBottom: 6,
    width: "100%",
    alignItems: "center"
  },
  img: {
    width: 136,
    height: 90
  },
  btnStyle: {
    position: "absolute",
    right: 14,
    top: 40,
    padding: 8,
    justifyContent: "center",
    alignItems: "center"
  },
  buttonTitle: {
    fontSize: 16,
    color: "#333",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  title: {
    fontSize: 16,
    color: "#333",
    marginHorizontal: 68,
    textAlign: "center",
    marginBottom: 12,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  reason: {
    fontSize: 14,
    color: "#999",
    marginBottom: 16,
    paddingHorizontal: 16,
    textAlign: "center"
  },
  redReason: {
    color: "#E62117",
    fontSize: 14,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  adView: {
    paddingHorizontal: 16,
    backgroundColor: "#F5F5F5",
    width: "100%",
    paddingTop: 16
  },
  wrapperStyle: {
    marginBottom: 12,
    borderRadius: 4,
    overflow: "hidden"
  }
});
