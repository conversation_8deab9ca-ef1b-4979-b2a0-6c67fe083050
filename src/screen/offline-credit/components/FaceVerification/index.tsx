import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";
import React, { PureComponent } from "react";
import { View, Text, Image, StyleSheet } from "react-native";

type Props = {
  t: TFunction;
};

export default class FaceVerification extends PureComponent<Props, {}> {
  render() {
    const { t } = this.props;
    return (
      <>
        <View style={styles.container}>
          <Image style={styles.img} source={require("../../img/imgFaceverification.webp")} />
          <Text style={styles.headerText}>{t("最后一步，为确保信息安全，请刷脸核身")}</Text>
          <Text style={styles.headerTitle}>{t("刷脸识别时请正对手机，屏幕会出现闪烁")}</Text>
          <View style={styles.line} />
        </View>
        <View style={styles.tipsArrView}>
          <Tips title={t("不要太暗")} img={require("../../img/imgtoodark.webp")} />
          <Tips title={t("不要遮挡面部")} img={require("../../img/imgcoverface.webp")} />
        </View>
        <View style={[styles.tipsArrView, { marginTop: 16 }]}>
          <Tips title={t("不要晃动")} img={require("../../img/imgdontshake.webp")} />
          <Tips title={t("保持衣着整洁")} img={require("../../img/img_keep_dressed.png")} />
        </View>
      </>
    );
  }
}

const Tips = (props: any) => {
  const { title, img } = props;
  return (
    <View style={styles.tipsView}>
      <Image style={styles.tipImg} source={img} />
      <View style={styles.containerTitle}>
        <Text style={styles.tipsTitle}>{title}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 23
  },
  headerText: {
    fontSize: 16,
    color: "#333",
    marginBottom: 6,
    textAlign: "center",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  headerTitle: {
    fontSize: 12,
    color: "#999",
    paddingBottom: 20,
    textAlign: "center"
  },
  line: {
    height: StyleSheet.hairlineWidth,
    width: "100%",
    backgroundColor: "#EBEBEB",
    marginBottom: 20
  },
  headerTips: {
    fontSize: 12,
    color: "#979696"
  },
  img: {
    height: 140,
    width: 140,
    alignSelf: "center",
    marginBottom: 20
  },
  tipsArrView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  tipsView: {
    alignItems: "center",
    flexDirection: "row",
    width: (WINDOW_WIDTH - 52) / 2
  },
  tipImg: {
    width: 56,
    height: 56,
    marginBottom: 8
  },
  tipsTitle: {
    fontSize: 12,
    color: "#666"
  },
  containerTitle: {
    flex: 1,
    marginLeft: 8
  }
});
