/* eslint-disable @typescript-eslint/no-empty-function */
import React, { Component } from "react";
import { View } from "react-native";
import { observer } from "mobx-react";
import { ItemType } from "../../dict/ComponentType";
import DynamicComponent from "../DynamicComponent";
import { NewEntityInfos } from "../../tool/DataHandling";
import { TFunction } from "i18next";
import store from "../../store";
import { pageStoreModel } from "common/components/BaseContainer/Type";

type Props = {
  setWaitArray: (waitArray: any[], id: number) => void;
  t: TFunction;
  missingId: number | null;
  data: NewEntityInfos;
  setRef: (uid: number, ref: any) => void;
  setValue: (text: string, id: number, type: string, key: string) => void;
  store: pageStoreModel<store>;
  isFirstApply: boolean;
  allItems: NewEntityInfos[];
  testType: number;
};

type States = {
  needShowItem: any[];
  loading: boolean;
  lastValue: string;
};
@observer
export default class Associated extends Component<Props, States> {
  selectItems: NewEntityInfos[];

  constructor(props: Props) {
    super(props);
    this.state = {
      lastValue: props.data.lastValue,
      needShowItem: [],
      loading: false
    };
    this.selectItems = [];
  }

  componentDidMount() {
    this.computeChild();
  }

  computeChild = () => {
    const {
      store: { pageStore }
    } = this.props;
    const data = this.props.data;
    if (!!pageStore.useCreditData[data.id]) {
      const needShowItem: NewEntityInfos[] = this.getShowItems(parseInt(pageStore.useCreditData[data.id]));

      this.setSelectItemsAndWaitArray(needShowItem);
      this.setState({
        //由于风控组给的数据结构如此，但是这个数组只会返回一个子项
        needShowItem: needShowItem
      });
    }
  };

  renderItem = () => {
    if (this.state.loading) return null;
    const { t, missingId, isFirstApply, store, allItems, setRef, testType } = this.props;
    const { needShowItem } = this.state;
    const needShowItemView: JSX.Element[] = [];
    needShowItem.map((item: NewEntityInfos, index: number) => {
      const containerStyle = {
        marginBottom: store.pageStore.newUi ? 18 : needShowItem.length - 1 === index ? 42 : 24
      };
      needShowItemView.push(
        <View onLayout={() => {}} ref={_ref => setRef(item.id, _ref)}>
          <DynamicComponent
            key={index}
            data={item}
            t={t}
            setValue={this.didSelectedItem}
            containerStyle={containerStyle}
            missingId={missingId}
            isFirstApply={isFirstApply}
            selectItems={this.selectItems}
            store={store}
            allItems={allItems}
            testType={testType}
          />
        </View>
      );
    });
    return needShowItemView;
  };

  didSelectedItem = (selectedItem: any, id: any, type: any, key: any) => {
    const { setValue } = this.props;
    setValue(selectedItem, id, type, key);
  };

  occupationSelected = (value: string, id: number, type: string, key: string) => {
    this.setState({ loading: true }, () => {
      const { setValue } = this.props;

      const Value = parseInt(value);
      if (Value !== parseInt(this.state.lastValue)) {
        const needShowItem: NewEntityInfos[] = this.getShowItems(Value);

        this.setSelectItemsAndWaitArray(needShowItem);
        this.setState({
          //由于风控组给的数据结构如此，但是这个数组只会返回一个子项
          needShowItem: needShowItem,
          lastValue: value
        });
        setValue(value, id, type, key);
      }
      this.setState({ loading: false });
    });
  };

  setSelectItemsAndWaitArray = (needShowItem: any[]) => {
    const { setWaitArray, data } = this.props;
    const waitArray: any[] = [];
    this.selectItems = [];

    needShowItem.map((item: NewEntityInfos) => {
      if (item.type === "select") {
        this.selectItems.push(item);
      }
      if (item.type as ItemType) {
        waitArray.push(item);
      }
    });
    setWaitArray(waitArray, data.id);
  };

  getShowItems = (id: number) => {
    const {
      store: { pageStore }
    } = this.props;
    const data = this.props.data;
    const showItems: NewEntityInfos[] = [];
    // 如果发现图片，图片后面的资料项 都需要等图片选择完成 再展示
    if (data.keyOptions && data.keyOptions[id]) {
      const allItemIds = new Set(data.keyOptions[id]);
      this.props.allItems.forEach(element => {
        if (allItemIds.has(element.id)) {
          element.regex = element.regexMap ? element.regexMap[id] : element.regex;
          element.type = element.typeMap ? element.typeMap[id] : element.type;
          element.title = element.placeholderMap ? element.placeholderMap[id] : element.title;
          showItems.push(element);
        }
      });
    }
    return showItems;
  };

  render() {
    const { t, missingId, isFirstApply, store, allItems, setRef, testType } = this.props;
    const data = this.props.data;
    return (
      <View>
        <View onLayout={() => {}} ref={_ref => setRef(data.id, _ref)}>
          <DynamicComponent
            data={data}
            t={t}
            setValue={this.occupationSelected}
            containerStyle={{ marginBottom: store.pageStore.newUi ? 18 : 24 }}
            missingId={missingId}
            isFirstApply={isFirstApply}
            selectItems={this.selectItems}
            store={store}
            allItems={allItems}
            testType={testType}
          />
        </View>
        {this.renderItem()}
      </View>
    );
  }
}
