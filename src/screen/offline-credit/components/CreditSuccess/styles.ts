import { StyleSheet } from "react-native";
import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    alignItems: "center",
    backgroundColor: "#fff"
  },
  img: {
    marginHorizontal: 48,
    marginTop: 40,
    width: 216,
    height: 160
  },
  title: {
    fontSize: 24,
    marginTop: 20,
    marginBottom: 12,
    color: "#333",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  text1: {
    width: WINDOW_WIDTH - 48,
    fontSize: 16,
    color: "#6C6C69",
    marginBottom: 8
  },
  text2: {
    width: WINDOW_WIDTH - 48,
    fontSize: 16,
    color: "#6C6C69",
    marginBottom: 25
  },
  touch: {
    paddingHorizontal: 32,
    paddingVertical: 10,
    borderRadius: 4,
    backgroundColor: "#E62117"
  },
  touchText: {
    fontSize: 16,
    color: "#fff"
  }
});
