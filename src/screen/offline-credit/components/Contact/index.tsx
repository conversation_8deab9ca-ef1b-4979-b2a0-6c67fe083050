import React, { PureComponent } from "react";
import { View, Image, TouchableOpacity, DeviceEventEmitter, Text } from "react-native";
import AnimatedTextInput from "../AnimatedTextInput";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { NativeSystemJumpModule } from "common/nativeModules";
import store from "../../store";
import { NewEntityInfos } from "../../tool/DataHandling";
import { EnterLeavePage, SensorTypeClickContacts } from "../../tool/EventTracking";
import styles from "./styles";
import { ItemType } from "../../dict/ComponentType";
type Props = {
  setValue: (text: string, id: any, type: any, key: any) => void;
  store: pageStoreModel<store>;
  containerStyle?: object;
  missingId: number | null;
  t: any;
  data: any;
  isFirstApply: boolean;
  allItems: NewEntityInfos[];
  testType: number;
};

export default class ContactComponent extends PureComponent<Props> {
  getNumber = async () => {
    const { setValue, data, isFirstApply, allItems, testType } = this.props;
    SensorTypeClickContacts(isFirstApply);
    const enterPageData = { pageType: data.pageType, isFirst: isFirstApply, enter: false, testType };
    EnterLeavePage(enterPageData);
    const userInfo = await NativeSystemJumpModule.chooseContact();
    enterPageData.enter = true;
    EnterLeavePage(enterPageData);
    if (userInfo.success) {
      const { name, phone } = userInfo.data;
      let nowPhone = phone.replace(/[^0-9]/gi, "");
      nowPhone = nowPhone.replace(/(\d{4})(?=\d)/g, "$1 ");
      const nameItem = allItems.find((i: NewEntityInfos) => {
        return i.type === ItemType.contact;
      });
      const phoneItem = allItems.find((i: NewEntityInfos) => {
        return i.type === ItemType.iphone;
      });
      if (!!nameItem && nameItem.editable) {
        const data = {
          id: nameItem.id,
          value: name
        };
        DeviceEventEmitter.emit("setValue", data);
      }
      if (!!phoneItem && phoneItem.editable) {
        const data = {
          id: phoneItem.id,
          value: nowPhone
        };
        DeviceEventEmitter.emit("setValue", data);
      }
    }
  };

  render() {
    const { data, setValue, missingId, t, isFirstApply, containerStyle, store } = this.props;
    return (
      <View>
        <AnimatedTextInput
          data={data}
          t={t}
          setValue={setValue}
          containerStyle={containerStyle}
          textInputStyle={store.pageStore.newUi ? styles.newContactText : styles.contactText}
          missingId={missingId}
          isFirstApply={isFirstApply}
          store={store}
          hideCleanButton={true}
        />
        {store.pageStore.newUi ? (
          <View
            style={{
              position: "absolute",
              right: 0,
              top: 0,
              bottom: 18,
              width: 140,
              paddingRight: 16,
              justifyContent: "center",
              alignItems: "flex-end"
            }}
          >
            <TouchableOpacity
              hitSlop={{ left: 16, top: 16, right: 16, bottom: 16 }}
              style={styles.useContactViewNew}
              onPress={this.getNumber}
            >
              <Image style={styles.useContactIconNew} source={require("./img/credit_icon_contact.webp")} />
              <Text style={styles.useContactTextNew}>{t("使用通讯录")}</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity
            hitSlop={{ left: 16, top: 16, right: 16, bottom: 16 }}
            style={styles.useContactView}
            onPress={this.getNumber}
          >
            <Image style={styles.useContactIcon} source={require("./img/icon_usecontacts.webp")} />
            <Text style={styles.useContactText}>{t("使用通讯录")}</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }
}
