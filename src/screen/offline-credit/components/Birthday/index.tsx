import React, { PureComponent } from "react";
import { Text, ScrollView, StyleSheet, View, FlatList, Image, TouchableOpacity } from "react-native";
import { WINDOW_WIDTH, WINDOW_HEIGHT } from "@akulaku-rn/akui-rn";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import Tab from "./Tab";
import _ from "lodash";

type Props = {
  didSelectedItem: any;
  // MM/DD/YYYY
  selectedItem: any;
  overflowYear: number;
};
type State = {
  month: string | null;
  date: string | null;
  year: string | null;
  activeIndex: number;
};
export default class BirthdayList extends PureComponent<Props, State> {
  tabArray: Array<any>;

  scrollView: any;

  constructor(props: any) {
    super(props);
    const selectArray = props.selectedItem ? props.selectedItem.split("/") : [];
    let month = null,
      date = null,
      year = null;
    if (selectArray.length === 3) {
      month = selectArray[0];
      date = selectArray[1];
      year = selectArray[2];
    }
    // 初始状态
    this.state = {
      month: month,
      date: date,
      year: year,
      activeIndex: 0
    };
    this.tabArray = [
      { value: "Year", type: "year" },
      { value: "Month", type: "month" },
      { value: "Date", type: "date" }
    ];
  }

  didSelectedItem = (type: string, value: string) => {
    const state: any = {};
    state[type] = value;
    this.setState(state, () => {
      let index;
      switch (type) {
        case "year":
          index = 1;
          break;
        case "month":
          index = 2;
          break;
        case "date":
          index = null;
          break;
      }
      if (!!index) {
        this.setSelectTab(index);
        this.setState({ activeIndex: index });
      } else {
        this.props.didSelectedItem(this.state);
      }
    });
  };

  setSelectTab = (selectTab: number) => {
    const { activeIndex, year, month, date } = this.state;
    if (activeIndex === selectTab) return;
    if (activeIndex === 0 && !year) {
      Toast.show("Please select year");
      return;
    }
    if (activeIndex === 1 && !month) {
      Toast.show("Please select month");
      return;
    }
    this.setState({ activeIndex: selectTab });
    this.scrollView && this.scrollView.scrollToIndex({ index: selectTab, animated: false });
  };

  keyExtractor = (item: any) => item.value;

  renderComponent = ({ item }: { item: { type: any; value: string } }) => {
    // @ts-ignore
    const selectedItem = this.state[item.type];
    const data = this.returnData(item.type);
    return (
      <ListDetail type={item.type} data={data} selectedItem={selectedItem} didSelectedItem={this.didSelectedItem} />
    );
  };

  returnData = (type: string) => {
    let data: Array<any> = [];
    switch (type) {
      case "month":
        for (let i = 1; i <= 12; i++) {
          data.push(this.PrefixZero(i.toString(), 2));
        }
        break;
      case "date":
        data = this.returnDay();
        break;
      case "year":
        const yearNow = new Date().getFullYear() + this.props.overflowYear;
        for (let i = yearNow; i >= 1900; i--) {
          data.push(i.toString());
        }
        break;
    }
    return data;
  };

  returnDay = () => {
    const { month, year } = this.state;
    const intMonth = parseInt(month || "");
    let dayCount = 28;
    const data: Array<any> = [];
    switch (intMonth) {
      case 1:
      case 3:
      case 5:
      case 7:
      case 8:
      case 10:
      case 12:
        dayCount = 31;
        break;
      case 4:
      case 6:
      case 9:
      case 11:
        dayCount = 30;
        break;
      case 2:
        if (
          (parseInt(year as string) % 4 === 0 && parseInt(year as string) % 100 !== 0) ||
          parseInt(year as string) % 400 === 0
        ) {
          dayCount = 29;
        }
        break;
    }

    for (let i = 1; i <= dayCount; i++) {
      data.push(this.PrefixZero(i.toString(), 2));
    }
    return data;
  };

  PrefixZero = (num: any, n: number) => {
    return (Array(n).join("0") + num).slice(-n);
  };

  render() {
    return (
      <View>
        <Tab data={this.tabArray} setSelectTab={this.setSelectTab} activeIndex={this.state.activeIndex} />
        <FlatList
          ref={(ref: any) => {
            this.scrollView = ref;
          }}
          scrollEnabled={false}
          bounces={false}
          horizontal
          keyExtractor={this.keyExtractor}
          extraData={this.state}
          data={this.tabArray}
          pagingEnabled
          renderItem={this.renderComponent}
          initialScrollIndex={0}
          showsHorizontalScrollIndicator={false}
        />
      </View>
    );
  }
}

type ListDetailProps = {
  type: string;
  data: Array<string>;
  selectedItem: any;
  didSelectedItem: any;
};
class ListDetail extends PureComponent<ListDetailProps> {
  render() {
    const { data, selectedItem, type, didSelectedItem } = this.props;
    return (
      <ScrollView
        style={{ width: WINDOW_WIDTH, paddingLeft: 12, flex: 1, height: WINDOW_HEIGHT * 0.7 }}
        bounces={false}
      >
        {data.map((item, index) => {
          const isSelected = selectedItem === item;
          return <Item key={index} data={item} type={type} isSelected={isSelected} didSelectedItem={didSelectedItem} />;
        })}
      </ScrollView>
    );
  }
}

type ItemProps = {
  type: string;
  data: any;
  isSelected: boolean;
  didSelectedItem: any;
};
class Item extends PureComponent<ItemProps> {
  didSelectedItem = () => {
    const { data, didSelectedItem, type } = this.props;
    didSelectedItem(type, data);
  };

  render() {
    const { isSelected, data } = this.props;
    return (
      <TouchableOpacity onPress={this.didSelectedItem} style={styles.button}>
        <Text style={[styles.title, isSelected && { color: "#E62117" }]}>{data}</Text>
        {isSelected && (
          <Image style={{ width: 24, height: 24, marginRight: 12 }} source={require("./choose_icon.png")} />
        )}
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    height: 50,
    width: "100%",
    borderBottomColor: "#EBEBEB",
    borderBottomWidth: 0.5,
    backgroundColor: "#fff"
  },
  title: {
    fontSize: 14,
    color: "#333",
    marginLeft: 4
  }
});
