import { isShapedIOS } from "@akulaku-rn/akui-rn";
import React, { PureComponent } from "react";
import { Text, ScrollView, StyleSheet, Image, TouchableOpacity, StyleProp, ViewStyle } from "react-native";
type Props = {
  data: any[];
  didSelectedItem: any;
  selectedItem: any;
  style?: StyleProp<ViewStyle>;
  renderItem?: (item: any, index: number) => React.ReactElement | null;
};
type State = {
  selectedItem: any;
};
export default class VerticalList extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props);
    this.state = {
      selectedItem: props.selectedItem
    };
  }

  didSelectedItem = (selectedItem: { value: string; id: number }) => {
    const { didSelectedItem } = this.props;
    this.setState({
      selectedItem: selectedItem.value
    });
    didSelectedItem(selectedItem);
  };

  render() {
    const { data, renderItem, style } = this.props;
    const { selectedItem } = this.state;
    return (
      <ScrollView
        style={[styles.container, style]}
        contentContainerStyle={{ paddingBottom: isShapedIOS ? 40 : 0 }}
        bounces={false}
      >
        {data.map((item, index) => {
          const isSelected = selectedItem === item.value;
          return renderItem ? (
            renderItem(item, index)
          ) : (
            <Item key={index} data={item} isSelected={isSelected} didSelectedItem={this.didSelectedItem} />
          );
        })}
      </ScrollView>
    );
  }
}

type ItemProps = {
  data: any;
  isSelected: boolean;
  didSelectedItem: any;
};
export class Item extends PureComponent<ItemProps> {
  didSelectedItem = () => {
    const { data, didSelectedItem } = this.props;
    didSelectedItem(data);
  };

  render() {
    const { isSelected, data } = this.props;
    return (
      <TouchableOpacity accessibilityLabel={String(data.key)} onPress={this.didSelectedItem} style={styles.button}>
        <Text style={[styles.title, isSelected && { color: "#E62117" }]}>{data.value}</Text>
        {/* {isSelected && (
          <Image style={{ width: 24, height: 24, marginRight: 12 }} source={require("../img/select_icon.png")} />
        )} */}
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingLeft: 16
  },
  button: {
    flexDirection: "row",
    justifyContent: "space-between",
    height: 50,
    alignItems: "center",
    width: "100%",
    borderBottomColor: "#E2E5E9",
    borderBottomWidth: 0.5,
    backgroundColor: "#fff"
  },
  title: {
    fontSize: 14
  }
});
