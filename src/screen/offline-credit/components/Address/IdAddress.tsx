import React, { Component } from "react";
import _ from "lodash";
import { NewEntityInfos } from "../../tool/DataHandling";
import { TFunction } from "i18next";
import store from "../../store";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { observer } from "mobx-react";

import Address from "./index";

type Props = {
  setValue: (text: string, id: number, type: string, key: string) => void;
  data: NewEntityInfos;
  missingId: number | null;
  containerStyle?: object;
  t: TFunction;
  store: pageStoreModel<store>;
  isFirstApply: boolean;
  testType: number;
};

type State = {
  address: string | null;
  showError: boolean;
};
@observer
export default class IdAddress extends Component<Props, State> {
  render() {
    return <Address {...this.props} disableUseNewAddress />;
  }
}
