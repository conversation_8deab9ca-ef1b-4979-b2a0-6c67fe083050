import React, { Component } from "react";
import { Image, View, Text, TouchableOpacity, DeviceEventEmitter, NativeModules } from "react-native";
import styles from "./styles";
import _ from "lodash";
import { EnterLeavePage, SensorTypeClickItem, V4ClickItem } from "../../tool/EventTracking";
import { NewEntityInfos } from "../../tool/DataHandling";
import { TFunction } from "i18next";
import store from "../../store";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { NativeAkuAddressModule, NativeNavigationModule } from "common/nativeModules";
import { observer } from "mobx-react";
import AddressConfirm from "./AddressConfirm";

// 居住地址 id
const ADDRESS_LIVE = 1101020;
// 身份证地址 id
const ADDRESS_ID = 1101008;

type Props = {
  setValue: (text: string, id: number, type: string, key: string) => void;
  data: NewEntityInfos;
  missingId: number | null;
  containerStyle?: object;
  t: TFunction;
  store: pageStoreModel<store>;
  isFirstApply: boolean;
  testType: number;
  disableUseNewAddress?: boolean;
};

type State = {
  address: string | null;
  showError: boolean;
};
@observer
export default class Address extends Component<Props, State> {
  listener: any;

  constructor(props: Props) {
    super(props);
    const {
      store: { pageStore },
      data: { id, needModified }
    } = props;
    this.state = {
      address: pageStore.useCreditData[id],
      showError: needModified
    };
  }

  componentDidMount() {
    this.listener = DeviceEventEmitter.addListener("setValue", data => {
      const {
        setValue,
        data: { id, pageType },
        isFirstApply,
        testType
      } = this.props;
      if (data.id === id) {
        const addressStr = JSON.stringify(data.address);
        setValue(addressStr, id, "map", "");
        this.setState({ address: addressStr === "{}" ? null : addressStr });
        const enterPageData = { pageType, isFirst: isFirstApply, enter: true, testType };
        EnterLeavePage(enterPageData);
      }
    });
  }

  componentWillUnmount() {
    this.listener && this.listener.remove();
  }

  hasNewAddress(): boolean {
    return Boolean(NativeModules["AkuAddressModule"]["handleCreditAddressNew"]);
  }

  setValue = _.debounce(
    async () => {
      const {
        data: { id, pageType, editable, title },
        isFirstApply,
        setValue,
        testType,
        disableUseNewAddress,
        store: { pageStore }
      } = this.props;

      if (!editable) return;
      const { address } = this.state;
      SensorTypeClickItem(pageType, id);
      const v4Data = { entryid: id, type: "address", isFirst: isFirstApply };
      V4ClickItem(v4Data);
      const enterPageData = { pageType, isFirst: isFirstApply, enter: false, testType };
      EnterLeavePage(enterPageData);
      let userInfo;
      if (!disableUseNewAddress && pageStore.newAddressSubassembly && this.hasNewAddress()) {
        userInfo = await NativeAkuAddressModule.handleCreditAddressNew(2, address || "", title, 2);
      } else {
        userInfo = await NativeAkuAddressModule.handleCreditAddress(2, address || "", title, 2);
      }
      enterPageData.enter = true;
      EnterLeavePage(enterPageData);
      if (userInfo.success) {
        const newData = this.hungary(userInfo.data);
        setValue(newData, id, "map", "");
        this.setState({ address: newData, showError: false });
        if (pageStore.newUi && id === ADDRESS_ID) {
          this.onPressAddressType(true);
        }
      }
    },
    500,
    { leading: true, trailing: false }
  );

  hungary = (data: any) => {
    const newData = {
      ...data,
      room_number: data.roomNumber
    };
    return JSON.stringify(newData);
  };

  returnAddressText = () => {
    const { address } = this.state;
    let addressObj = null;
    if (!!address) {
      try {
        addressObj = JSON.parse(address);
      } catch (e) {
        console.log("returnAddressText Address e", e);
        addressObj = null;
      }
    }
    if (!addressObj) {
      return this.props.data.title;
    } else {
      const { roomNumber, street, province, city, district, postcode, town, village } = addressObj;
      return `${street ? street : ""} ${roomNumber ? roomNumber : ""} ${town ? town : ""} ${village ? village : ""} ${
        district ? district : ""
      } ${city ? city : ""} ${province ? province : ""} ${postcode ? postcode : ""}`;
    }
  };

  onPressAddressType = (isSelectTrue: boolean) => {
    this.props.store.pageStore.isReuseAddress = isSelectTrue;
    const address = isSelectTrue ? this.props.store.pageStore.useCreditData[ADDRESS_ID] : "{}";
    this.props.setValue(address, ADDRESS_LIVE, "map", "");
    let addressData = {};
    try {
      addressData = JSON.parse(address);
    } catch (e) {
      addressData = {};
    }
    const data = {
      id: ADDRESS_LIVE,
      address: addressData
    };

    DeviceEventEmitter.emit("setValue", data);
  };

  renderOldAddress = () => {
    const {
      data: { id, title },
      missingId
    } = this.props;
    const { address } = this.state;
    const addressText = this.returnAddressText();
    return (
      <TouchableOpacity
        style={[styles.container, missingId === id && !address && { borderBottomColor: "#e62117" }]}
        onPress={this.setValue}
      >
        <View style={styles.fdView}>
          <Image style={styles.addressIcon} source={require("common/images/credit_input_ico_address.webp")} />

          <Text style={[styles.title, !!address && styles.hasTitle]}>{addressText}</Text>
        </View>
        <Image style={styles.arrowIcon} source={require("../../img/credit_input_ico_arrow.webp")} />
        {!!address && <Text style={styles.placeholderText}>{title}</Text>}
      </TouchableOpacity>
    );
  };

  renderNewAddress = () => {
    const {
      data: { id, title },
      missingId
    } = this.props;
    const { address, showError } = this.state;
    const addressText = this.returnAddressText();
    return (
      <TouchableOpacity
        style={[styles.containerNew, missingId === id && !address && { borderBottomColor: "#e62117" }]}
        onPress={this.setValue}
      >
        <View style={styles.fdView}>
          {!!address ? <Text style={[styles.newTitle, !!address && styles.hasTitle]}>{addressText}</Text> : null}
        </View>
        <Image style={styles.arrowIconNew} source={require("../../img/credit_arrow_new.webp")} />
        <Text style={!!address ? styles.placeholderTextFouces : styles.placeholderText2}>{title}</Text>
      </TouchableOpacity>
    );
  };

  render() {
    const {
      data: { id, title, needModified, pageType, fieldName, editable, showAddressConfirm },
      missingId,
      containerStyle,
      t,
      store: { pageStore, navParams }
    } = this.props;
    const { address, showError } = this.state;
    const addressText = this.returnAddressText();
    if (pageType === 5 && !pageStore.showHideComponent) return null;
    if (id === ADDRESS_LIVE && pageStore.isReuseAddress && pageStore.newUi && editable) return null;
    return (
      <View style={containerStyle}>
        {pageStore.newUi ? this.renderNewAddress() : this.renderOldAddress()}
        {((missingId === id && !address) || showError) && (
          <View style={styles.errorView}>
            <Text style={styles.errorText}>{!!needModified ? t("请修改") : t("此项未填写")}</Text>
          </View>
        )}
        {id === ADDRESS_ID && address && showAddressConfirm ? (
          <AddressConfirm t={t} isSelectTrue={pageStore.isReuseAddress} onPress={this.onPressAddressType} />
        ) : null}
      </View>
    );
  }
}
