import { NewEntityInfos } from "../tool/DataHandling";
import { ItemType } from "./ComponentType";

export interface ProcessData {
  data: Data;
  errCode: string;
  errMsg: string;
  isFromCache: boolean;
  success: boolean;
  sysTime: number;
}

// export interface Data {
//   id: string;
//   subTitle: string;
//   step: number;
//   description: string;
//   inconUrl: string;
//   pageLevel: number;
//   type: string;
//   groupInfos: GroupInfoList[];
//   needAuthProtocol: boolean;
//   defaultCheck: boolean;
//   asiApplicationId: string;
//   phoneNumber: string;
//   emergencyPhoneId: number;
// }
export interface Data {
  title: string;
  bannerImg: string;
  groupInfos: GroupInfoList[];
  pageNo: number;
  asiApplicationId: string;
}

export interface GroupInfoList {
  title: string;
  type: string;
  entries: EntryInfoList[];
}

export interface HandleGroupInfoList {
  title: string;
  type: string;
  entries: NewEntityInfos[];
  showFoldView?: boolean;
}

export interface RelatedEntryPageList {
  entryId: number;
  subPageInfoList: SubPageInfoList[];
}

export interface SubPageInfoList {
  pageId: string;
  entryKey: number;
  pageInfo: PageInfo;
}

export interface PageInfo {
  entryKey: number;
  jsonEntryIds: string;
  pageLevel: number;
  entries: EntryInfoList[];
}

export interface EntryInfoList {
  entryId: number;
  value: string | null;
  fieldName: string;
  placeholder: string;
  //text、date、email、select、pic、search_company、contact】
  type: ItemType;
  //是否必填
  required: boolean;
  //正则表达式字符串(下拉/搜索类资料不需要匹配正则)
  regex: string;
  //是否敏感
  sensitive: boolean;
  //是否可改
  modifiable: boolean;
  //提示是否需要修改 （主要用于部分授信被拒，提示语）
  needModified: boolean;
  options: { key: number; value: string }[];
  keyOptions: {
    [key: number]: number[];
  };
  linkedOptions: {
    [key: number]: {
      [key: number]: number[];
    };
  };
  regexMap: { [key: number]: string } | null;
  typeMap: { [key: number]: ItemType } | null;
  placeholderMap: { [key: number]: string } | null;
  // pageType: number; //1 个人信息页 2 紧急联系人页 3 工作信息页 4 工作照片页 5 证件照片页 6 声纹页 7 账号授权页 8 税卡页 9 刷脸识别页
}
// export interface EntryInfoList {
//   entryId: number;
//   name: string;
//   desc: string;
//   type: ItemType;
//   jsonWhere: string;
//   required: boolean;
//   sensitive: boolean;
//   jsonDownValues: string[];
//   lastValue: string;
//   nonClickable: boolean;
//   kycStatus: boolean;
//   needModified: boolean;
//   pageType: number; //1 个人信息页 2 紧急联系人页 3 工作信息页 4 工作照片页 5 证件照片页 6 声纹页 7 账号授权页 8 税卡页 9 刷脸识别页
// }
