/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/dict/GrayRelease.ts
 */

export interface FrameI {
  top: number;
  fontSize: number;
}

export interface TitleInfo {
  focus: FrameI;
  blur: FrameI;
}

export const getTitleInfo = (isGray: boolean): TitleInfo => {
  return isGray
    ? { focus: { top: 7, fontSize: 12 }, blur: { top: 22, fontSize: 16 } }
    : { focus: { top: 0, fontSize: 12 }, blur: { top: 24, fontSize: 14 } };
};
