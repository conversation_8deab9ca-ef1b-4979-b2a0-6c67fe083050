import { TextStyle, Platform } from "react-native";
import { FontStyles } from "@akulaku-rn/akui-rn";

// ["Roboto-Bold", "Roboto-Light", "Roboto-Medium", "didot_bold", "din_bold", "opensans"]
export enum FontWeightNameMappings {
  Light = "300",
  Normal = "400",
  Regular = "400",
  Medium = "500",
  SemiBold = "600",
  DemiBold = "600",
  Bold = "700"
}

enum AndroidFontFamily {
  RobotoBold = "Roboto-Bold",
  RobotoLight = "Roboto-Light",
  RobotoMedium = "Roboto-Medium",
  DidotBold = "didot_bold",
  DinBold = "din_bold",
  Opensans = "opensans"
}

export default function fontWeightTrans(fw: FontWeightNameMappings, isDin = false): TextStyle {
  if (Platform.OS === "ios") {
    return {
      fontWeight: fw
    };
  }
  let style: TextStyle = {};
  switch (fw) {
    case FontWeightNameMappings.Light:
      style = {
        fontFamily: AndroidFontFamily.RobotoLight
      };
      break;
    case FontWeightNameMappings.Normal:
    case FontWeightNameMappings.Regular:
      break;
    case FontWeightNameMappings.Medium:
      style = FontStyles["rob-medium"];
      break;
    case FontWeightNameMappings.SemiBold:
    case FontWeightNameMappings.DemiBold:
      style = {};
      break;
    case FontWeightNameMappings.Bold:
      style = FontStyles["roboto-bold"];
      break;
    default:
      break;
  }
  return style;
}
