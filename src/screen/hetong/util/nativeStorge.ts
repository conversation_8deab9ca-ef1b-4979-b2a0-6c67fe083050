import { Platform, NativeModules } from "react-native";
import _ from "lodash";

const SP_KEY_SHOW_GUIDE_ANDROID = "electric_sign_guide_show";
const SP_KEY_SHOW_GUIDE_IOS = "isSigned";

import * as Sentry from "@sentry/react-native";
import NativeUserInfoModule from "common/nativeModules/bussinse/nativeUserInfoModule";
import nativeKvStoreModule from "common/nativeModules/basics/nativeKvStoreModule";
import ESignatureConstants from "../constants";
import { getCurrentScreen } from "common/constant";
import AkConsole from "common/util/AkConsole/AkConsole";

async function getUserInfo() {
  let info;
  try {
    info = await NativeUserInfoModule.getUserInfo();
    AkConsole("hetong", "info: ", info);
    info = info.data;
  } catch (e) {
    Sentry.customReport(e, ESignatureConstants.MODULE_NAME);
  }
  return info;
}

async function needShowGuideFlag() {
  const info = await getUserInfo();
  if (!info.uid) {
    return true;
  }
  const re = await nativeKvStoreModule.getBoolean(SP_KEY_SHOW_GUIDE_IOS + info.uid);
  AkConsole("hetong", "SOTRE_KEY: getBoolean ", SP_KEY_SHOW_GUIDE_IOS + info.uid);
  AkConsole("hetong", re, "re");
  return Boolean(_.get(re, "data.value"));
}

async function setShowGuideFlag() {
  const info = await getUserInfo();
  if (!info.uid) {
    return;
  }
  nativeKvStoreModule.putBoolean(SP_KEY_SHOW_GUIDE_IOS + info.uid, true);
  AkConsole("hetong", "SOTRE_KEY: putBoolean", SP_KEY_SHOW_GUIDE_IOS + info.uid);
}

const getEmail = async () => {
  const info = await getUserInfo();
  AkConsole("hetong", `getEmail info: `, info);
  return info.email;
};

export { needShowGuideFlag, setShowGuideFlag, getEmail };
