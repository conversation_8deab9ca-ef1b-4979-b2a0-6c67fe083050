/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-01 15:00
 * @description 处理电子合同埋点
 */
import NativeSensorModule, { SensorEvent, SensorType } from "common/nativeModules/sdk/nativeSensroModule";

const baseParams: SensorEvent = {
  page_name: "Electronic signature page",
  page_id: "1006"
};

export enum UserStatus {
  USER_SIGNED = "Signed",
  USER_UNSIGNED = "Unsigned"
}

const UserStatusMap = {
  [UserStatus.USER_SIGNED]: "Signed",
  [UserStatus.USER_UNSIGNED]: "Unsigned"
};

export default {
  sensorPageEnter(userStatus: UserStatus | null) {
    NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
      ...baseParams,
      extra: {
        ...(userStatus && { UserStatus: userStatus })
      }
    });
  },
  sensorPageLeave(userStatus: UserStatus) {
    NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
      ...baseParams,
      extra: {
        UserStatus: userStatus
      }
    });
  },

  sensorClickHowSign(userStatus: UserStatus) {
    // 点击如何签名
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060101",
      element_name: "how to sign",
      module_id: "01",
      module_name: "sign",
      position_id: "01",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },

  sensorClickFinishSign(userStatus: UserStatus) {
    // 点击完善签名
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060102",
      element_name: "perfect signature",
      module_id: "01",
      module_name: "sign",
      position_id: "02",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },

  /*  邮箱弹窗相关埋点  */
  sensorExposeEmailDialog(userStatus: UserStatus) {
    // 邮箱确认弹框-曝光
    NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
      element_id: "10060201",
      element_name: "popup-email-exposure",
      module_id: "02",
      module_name: "popup-email",
      position_id: "01",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },

  sensorFocusEmail(userStatus: UserStatus) {
    // 输入邮箱
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060202",
      element_name: "email-input email",
      module_id: "02",
      module_name: "popup-email",
      position_id: "02",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },

  sensorClickEmailConfirm(userStatus: UserStatus) {
    // 点击confirm
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060203",
      element_name: "email-confirm",
      module_id: "02",
      module_name: "popup-email",
      position_id: "03",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },
  sensorClickEmailClose(userStatus: UserStatus) {
    // 点击“叉”
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060204",
      element_name: "email-close",
      module_id: "02",
      module_name: "popup-email",
      position_id: "04",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },

  /* 提示框相关埋点 */
  sensorExposeTipDialog(userStatus: UserStatus) {
    // 提示确认弹框-曝光
    NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
      element_id: "10060301",
      element_name: "popup-prompt-exposure",
      module_id: "03",
      module_name: "popup-prompt",
      position_id: "01",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },
  sensorClickTipDetail(userStatus: UserStatus) {
    // 点击详情
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060302",
      element_name: "prompt-detail",
      module_id: "03",
      module_name: "popup-prompt",
      position_id: "02",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },
  sensorClickTipConfirm(userStatus: UserStatus) {
    // 点击confirm
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060303",
      element_name: "prompt-confirm",
      module_id: "03",
      module_name: "popup-prompt",
      position_id: "03",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },
  sensorClickTipClose(userStatus: UserStatus) {
    // 点击“叉”
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060304",
      element_name: "prompt-close",
      module_id: "03",
      module_name: "popup-prompt",
      position_id: "04",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },
  sensorClickTipModify(userStatus: UserStatus) {
    // 点击修改
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "********",
      element_name: "prompt-change",
      module_id: "03",
      module_name: "popup-prompt",
      position_id: "05",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },

  /* 账号注册弹框相关埋点 */
  sensorExposeRegisterDialog(userStatus: UserStatus) {
    // 账号注册弹框-曝光
    NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
      element_id: "4500401",
      element_name: "popup-account registration-exposure",
      module_id: "04",
      module_name: "popup-account registration",
      position_id: "01",
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },

  sensorClickRegisterConfirm(userStatus: UserStatus) {
    // 点击“我知道了”
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "********",
      element_name: "got it ",
      module_id: "01",
      module_name: "register",
      position_id: "01",
      page_id: "451",
      page_name: "Register is in progress",
      extra: {
        Aku_UserStatus: UserStatus.USER_UNSIGNED
      }
    });
  },

  sensorExposeGuideDialog(n: number, userStatus: UserStatus) {
    const index = 2 * n - 1;
    NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
      element_id: `1006050${index}`,
      element_name: `popup-guide-exposure ${n}`,
      module_id: "05",
      module_name: "popup-guide",
      position_id: `0${index}`,
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },

  sensorClickGuideNextStep(n: number, userStatus: UserStatus) {
    const index = 2 * n;
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: `1006050${index}`,
      element_name: `guide-next step ${n}`,
      module_id: "05",
      module_name: "popup-guide",
      position_id: `0${index}`,
      extra: { UserStatus: UserStatusMap[userStatus] },
      ...baseParams
    });
  },

  sensorSignPageClickNext(userStatus: UserStatus) {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10090101",
      element_name: "next step",
      module_id: "01",
      module_name: "third",
      position_id: "01",
      extra: { UserStatus: UserStatusMap[userStatus] },
      page_id: "453",
      page_name: "Third login page"
    });
  },

  sensorSignPageNextTipDialog() {
    NativeSensorModule.sensorLogger(SensorType.EXPOSE, {
      element_id: "10090201",
      element_name: "ppup-incomplete signature",
      module_id: "02",
      module_name: "incomplete signature",
      position_id: "01",
      extra: { UserStatus: UserStatusMap[UserStatus.USER_UNSIGNED] },
      page_id: "453",
      page_name: "Third login page"
    });
  },

  sensorSignPageNextTipDialogClickConfirm() {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10090202",
      element_name: "incomplete signature-sign now",
      module_id: "02",
      module_name: "incomplete signature",
      position_id: "02",
      extra: { UserStatus: UserStatusMap[UserStatus.USER_UNSIGNED] },
      page_id: "453",
      page_name: "Third login page"
    });
  },

  sensorSignPageNextTipDialogClickSkip() {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10090203",
      element_name: "incomplete signature-skip",
      module_id: "02",
      module_name: "incomplete signature",
      position_id: "03",
      extra: { UserStatus: UserStatusMap[UserStatus.USER_UNSIGNED] },
      page_id: "453",
      page_name: "Third login page"
    });
  },

  sensorSignResultGoSign() {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10100101",
      element_name: "to sign",
      module_id: "01",
      module_name: "electronic signature",
      position_id: "01",
      extra: { UserStatus: UserStatusMap[UserStatus.USER_UNSIGNED] },
      page_name: "Go to the signature",
      page_id: "1010"
    });
  },

  sensorSignResultViewSign() {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10100102",
      element_name: "view signature",
      module_id: "01",
      module_name: "electronic signature",
      position_id: "02",
      extra: { UserStatus: UserStatusMap[UserStatus.USER_SIGNED] },
      page_name: "Go to the signature",
      page_id: "1010"
    });
  },

  sensorExposeReConfirmDialog(userStatus: UserStatus) {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060401",
      element_name: "email fail-exp",
      module_id: "04",
      module_name: "email fail",
      extra: { userStatus },
      position_id: "01",
      page_name: "Electronic signature page",
      page_id: "1006"
    });
  },

  sensorExposeRegisterFailDialog(reason: string) {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "10060501",
      element_name: "register fail-exp",
      module_id: "05",
      module_name: "register fail",
      position_id: "01",
      extra: { reason, UserStatus: UserStatusMap[UserStatus.USER_UNSIGNED] },
      page_name: "Electronic signature page",
      page_id: "1006"
    });
  }
};
