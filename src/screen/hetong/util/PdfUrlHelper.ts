/**
 * Create by z<PERSON><PERSON><PERSON> on 2021-05 16:06
 * @description
 */
import DeviceInfoHelper from "common/util/DeviceInfoHelper";

export default class PdfUrlHelper {
  static async getUrl(url: string) {
    const deviceInfoRes = await DeviceInfoHelper.getEnvInfoSilentWithCache();
    if (!deviceInfoRes.success) {
      return "";
    }
    const domain = deviceInfoRes.data.webLoaderUrl;
    if (!domain) {
      return "";
    }
    const encodeUrl = encodeURIComponent(url);
    return `${domain}/ec-basic/previewFile?pdfurl=${encodeUrl}&scale=2.0&isPage=0`;
  }
}
