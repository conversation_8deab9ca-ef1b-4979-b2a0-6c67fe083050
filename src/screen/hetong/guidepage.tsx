import * as React from "react";
import { View, ScrollView } from "react-native";

import { NavigationBar, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

import styles from "./styles";

import { inject, observer } from "mobx-react";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";

import AutoHeightImage from "./components/AutoHeightImage";

import store from "./store";
import { ElectricSignPlatformType } from "./types";
import ESignatureConstants from "./constants";
import { Type } from "common/components/BaseContainer";
import ContractPdfStore from "./store/contractPdf";
import AkConsole from "common/util/AkConsole/AkConsole";

@RootView({
  withI18n: [
    ESignatureConstants.MODULE_NAME,
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json"),
      vi: require("./i18n/vi.json"),
      ms: require("./i18n/ms.json")
    }
  ],
  store
})
@withTranslation(ESignatureConstants.MODULE_NAME)
@inject("store")
@observer
class GuidePage extends React.Component<Type.Props<ContractPdfStore>> {
  constructor(props: Type.Props<ContractPdfStore>) {
    super(props);
    const {
      store: { navParams }
    } = this.props;
    const { userStatus } = navParams;
    this.props.configSensorEvent({
      page_name: "Long picture guidelines",
      page_id: "1008",
      extra: { Aku_UserStatus: userStatus }
    });
  }

  state = {
    loading: true
  };

  loadingOverFlag = 4;

  imageLoadOver = () => {
    AkConsole("hetong", this.loadingOverFlag);
    if (this.loadingOverFlag === 1) {
      this.setState({ loading: false });
    } else {
      this.loadingOverFlag = this.loadingOverFlag - 1;
    }
  };

  render() {
    const {
      t,
      store: { navParams }
    } = this.props;
    const { type } = navParams;

    AkConsole("hetong", type);

    const ds =
      type === ElectricSignPlatformType.DIGISIGN
        ? ESignatureConstants.imgMap.digisign
        : ESignatureConstants.imgMap.other;

    const content = (
      <ScrollView
        style={{
          flex: 1
        }}
        contentContainerStyle={{
          alignItems: "stretch"
        }}
      >
        {ds.map((item: string) => {
          return (
            <AutoHeightImage
              width={WINDOW_WIDTH}
              key={item}
              source={{
                uri: `${ESignatureConstants.GUIDE_IMAGE_BASE_URL}${item}`
              }}
              onLoad={this.imageLoadOver}
            />
          );
        })}
      </ScrollView>
    );

    return (
      <View style={styles.container}>
        <NavigationBar title={t("electric_sign_long_guide_title")} />
        {content}
      </View>
    );
  }
}

export default GuidePage;
