import React, { Component } from "react";
import { DeviceEventEmitter, EmitterSubscription, Image, Text, TouchableOpacity, View } from "react-native";
import { inject, observer } from "mobx-react";
import { debounce } from "lodash";

import { setShowGuideFlag } from "./util/nativeStorge";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import { RegisterStatus, SignResultPageType } from "./types";
import { BlockBtn, BottomModal, GuideModal, HtmlContainer, TopTips } from "./components";
import { Loading, NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import { NavigationBar } from "@akulaku-rn/akui-rn";
import NetWorkError from "common/components/DefaultComponent";
import styles, { headerStyles } from "./styles";
import SensorManager, { UserStatus } from "./util/SensorManager";
import AkuNativeEventEmitter from "common/nativeModules/emitter/nativeEventEmitter";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { ContractSignStatus } from "./types";
import { SIGN_PAGE_BACK_EVENT } from "./types";
import ESignatureConstants from "./constants";
import ESignatureStore from "./store";
import { Type } from "common/components/BaseContainer";

/**
 * NOTICE：更改电子合同的业务代码，需要同步到空包分支， 空包分支也有电子合同的业务代码
 * */

@RootView({
  withI18n: [
    ESignatureConstants.MODULE_NAME,
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json"),
      vi: require("./i18n/vi.json"),
      ms: require("./i18n/vi.json")
    }
  ],
  store: ESignatureStore,
  keyApi: ["/capi/personal/user/e-signatures/register/info"]
})
@withTranslation(ESignatureConstants.MODULE_NAME)
@inject("store")
@observer
class HeTong extends Component<Type.Props<ESignatureStore>> {
  screenEventListener?: EmitterSubscription;

  checkBeforeImproveDeb: () => Promise<void> | undefined;

  constructor(props: Type.Props<ESignatureStore>) {
    super(props);
    this.checkBeforeImproveDeb = debounce(this.checkBeforeImprove, 300);
    this.navToPage = debounce(this.navToPage, 300, { leading: true, trailing: false });
  }

  componentDidMount() {
    const {
      store: {
        pageStore,
        navParams: { source, title, screen },
        runtime: { countryCode }
      },
      navigation,
      t
    } = this.props;

    // android 会传title、 iOS 不会
    this.props.store.pageStore.updateSourceAndTitle(Number(source), title);
    this.props.store.pageStore.setCountryCode(countryCode);
    this.fetchRemoteData(true);

    this.signPageBackListener = DeviceEventEmitter.addListener(SIGN_PAGE_BACK_EVENT, () => {
      this.props.store.pageStore.isFromSignPage = true;
    });
  }

  componentWillUnmount(): void {
    this.screenEventListener && this.screenEventListener.remove();
    this.signPageBackListener && this.signPageBackListener.remove();
    this.props.store.pageStore.timer && clearTimeout(this.props.store.pageStore.timer);
  }

  signPageBackListener: EmitterSubscription | null = null;

  fetchRemoteData = async (fromEnter: boolean) => {
    const {
      store: {
        pageStore,
        navParams: { screen }
      }
    } = this.props;
    await pageStore.fetchRemoteData(fromEnter);
    // 神策埋点事件
    setTimeout(() => {
      if (!this.screenEventListener) {
        this.screenEventListener = AkuNativeEventEmitter.addListener(screen, event => {
          if (pageStore.loading || pageStore.showError) {
            return;
          }
          if (event.eventName === "onEnter") {
            // NativeLoggerModule.sensorCustomLogger("ActivityRnView", ReportData("Enter"));
            if (!pageStore.modalVisible) {
              this.fetchRemoteData(false);
            }
            // SensorManager.sensorPageEnter(pageStore.userStatus);
          } else if (event.eventName === "onLeave") {
            SensorManager.sensorPageLeave(pageStore.userStatus);
          } else {
            console.warn(`[RootView]: ${screen} unknown screen event`);
          }
        });
      }
    }, 800);
  };

  checkBeforeImprove = async () => {
    const {
      store: { pageStore },
      navigation
    } = this.props;

    const { source, electricSignPlatform, title } = pageStore;

    Loading.show({ useBack: true });
    const result = await pageStore.checkBeforeImprove();

    if (!result.success) {
      Loading.dismiss();
      return;
    }
    const { electricSign, signRegist } = result;

    // 已经签名
    if (
      electricSign &&
      electricSign.signStatus &&
      (electricSign.signStatus === ContractSignStatus.SIGNED ||
        electricSign.signStatus === ContractSignStatus.SIGNED_BY_USER)
    ) {
      if (electricSign.docUrl) {
        pageStore.navigateContractPdfPage(electricSign.docUrl);
      } else {
        NativeToast.showMessage(`electricSign.docUrl is ${electricSign.docUrl}`);
      }
      Loading.dismiss();
      return;
    }

    SensorManager.sensorClickFinishSign(UserStatus.USER_UNSIGNED);

    // 已经注册
    if (signRegist && signRegist.status === RegisterStatus.REGISTED) {
      const improveSignRes = await pageStore.improveElectricSign();
      console.log(`improveSignRes: `, improveSignRes);
      const { success, url } = improveSignRes;
      Loading.dismiss();
      if (success && url) {
        pageStore.navigateSignPage(url);
      }
      return;
    }

    // 注册中
    if (signRegist && signRegist.status === RegisterStatus.REGISTING) {
      Loading.dismiss();
      // pageStore.changeModalVisible(true, "SignInProcess");
      pageStore.navigateResultPage(SignResultPageType.REEGEISTERING);
      return;
    }

    // 未注册， 或者注册失败可以重试会到这里
    Loading.dismiss();
    pageStore.updateEmail(signRegist ? signRegist.registerEmail : "");
    pageStore.changeModalVisible(true, "ConfirmEmail");
  };

  tipsOnClose = () => {
    const {
      store: { pageStore }
    } = this.props;

    pageStore.changeTipVisible(false);
  };

  navToPage = () => {
    const {
      navigation,
      store: { pageStore }
    } = this.props;

    navigation.navigate({
      screen: "electricSignScreenGuide",
      params: {
        type: pageStore.electricSignPlatform,
        userStatus: pageStore.userStatus
      }
    });
    SensorManager.sensorClickHowSign(pageStore.userStatus);
  };

  close = () => {
    const {
      store: { pageStore }
    } = this.props;
    switch (pageStore.modalType) {
      case "ConfirmEmail": // 邮箱弹窗 点击 "叉"
        SensorManager.sensorClickEmailClose(UserStatus.USER_UNSIGNED);
        break;
      case "Signature": // 提示确认弹窗 点击 "叉"
        SensorManager.sensorClickTipClose(UserStatus.USER_UNSIGNED);
        break;
    }
    pageStore.changeModalVisible(false);
  };

  closeGuideModal = () => {
    const {
      store: { pageStore }
    } = this.props;
    setShowGuideFlag();
    pageStore.changeGuideModalVisible(false);
  };

  renderHeader() {
    const {
      store: {
        pageStore: { headerIcon, headerSignState }
      },
      t
    } = this.props;
    let tSource;
    switch (headerIcon) {
      case "electricSigned":
        tSource = require("./images/electric_signed.webp");
        break;
      case "notCompleteSign":
        tSource = require("./images/not_complete_sign.webp");
        break;
      case "notSign":
        tSource = require("./images/not_sign.webp");
        break;
      default:
        tSource = require("./images/ic_default_avatar.webp");
        break;
    }

    return (
      <View style={headerStyles.header}>
        {/* <TouchableOpacity
          onPress={() => {
            this.props.store.pageStore.changeModalVisible(true, "SignInProcess");
          }}
        >
          <Image source={tSource} style={headerStyles.leftIcon} />
        </TouchableOpacity> */}
        <Image source={tSource} style={headerStyles.leftIcon} />
        <View style={headerStyles.rightBlock}>
          <Text style={headerStyles.statusText}>{t(headerSignState)}</Text>
          <Text numberOfLines={2} style={headerStyles.intro}>
            {t("授权akulaku签署并管理您的债务合同")}
          </Text>
          <TouchableOpacity onPress={this.navToPage} style={headerStyles.jumpBlock}>
            <Image source={require("./images/edit.webp")} style={headerStyles.jumpIcon} />
            <Text style={headerStyles.jumpText}>{t("electric_sign_long_guide_btn")}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  render() {
    const {
      t,
      store: {
        pageStore,
        navParams: { title }
      }
    } = this.props;

    const {
      showGuideModal,
      tipVisible,
      loading,
      showError,
      modalVisible,
      btnText,
      modalType,
      electricSign
    } = pageStore;

    let content;
    if (loading) {
      content = <Loading useBack />;
    } else if (showError) {
      content = (
        <NetworkErrorComponent
          message={t("global:啊，网络故障导致我们的距离好远啊!")}
          onRetryPress={() => {
            this.fetchRemoteData(false);
          }}
          buttonText={t("global:刷新")}
        />
      );
    } else {
      content = (
        <>
          {tipVisible && <TopTips txt={t("signature_tips")} onClose={this.tipsOnClose} />}
          {this.renderHeader()}
          <View style={styles.segmentLine} />
          <HtmlContainer
            userName={(electricSign && electricSign.userName) || ""}
            authDate={(electricSign && electricSign.authDate) || ""}
            userIdNo={(electricSign && electricSign.userIdNo) || ""}
            address={(electricSign && electricSign.address) || ""}
            style={{ flex: 1, backgroundColor: "#fff" }}
          />
          <BlockBtn onPress={this.checkBeforeImprove} text={t(btnText)} />
        </>
      );
    }

    return (
      <View style={styles.container}>
        <NavigationBar title={title || t("借款合同")} />
        {content}
        {showGuideModal && (
          <View style={styles.guideModal}>
            <GuideModal onClose={this.closeGuideModal} />
          </View>
        )}
        <BottomModal
          onConfirm={this.checkBeforeImprove}
          t={t}
          type={modalType}
          visible={modalVisible}
          onClose={this.close}
        />
      </View>
    );
  }
}

export default HeTong;
