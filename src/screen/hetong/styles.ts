import { StyleSheet } from "react-native";
import fontWeightTrans, { FontWeightNameMappings } from "./util/fontTrans";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    alignItems: "stretch",
    position: "relative"
  },
  segmentLine: {
    height: 12,
    backgroundColor: "#f5f5f5"
  },
  btnContainer: {
    height: 56,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
    paddingHorizontal: 12
  },
  btn: {
    height: 40,
    backgroundColor: "#E62117",
    alignSelf: "stretch",
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center"
  },
  btnText: {
    fontSize: 16,
    color: "#fff"
  },
  guideModal: {
    alignItems: "center",
    justifyContent: "center",
    zIndex: 10,
    position: "absolute",
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0,0,0,.5)"
  }
});

const headerStyles = StyleSheet.create({
  header: {
    height: 111,
    backgroundColor: "#fff",
    flexDirection: "row",
    paddingTop: 16,
    paddingLeft: 16,
    paddingRight: 16
  },
  leftIcon: {
    width: 40,
    height: 40,
    marginTop: 2
  },
  rightBlock: {
    marginLeft: 12,
    flex: 1
  },
  statusText: {
    fontSize: 16,
    color: "#333"
  },
  intro: {
    fontSize: 12,
    color: "#999",
    marginVertical: 8,
    alignSelf: "stretch"
  },
  jumpBlock: {
    flexDirection: "row",
    alignItems: "center"
  },
  jumpIcon: {
    width: 12,
    height: 13,
    marginTop: 1,
    marginRight: 4
  },
  jumpText: {
    color: "#e62117",
    fontSize: 12
  }
});

const signResultPageStyle = StyleSheet.create({
  container: {
    // flex: 1,
    backgroundColor: "#fff"
    // alignItems: "center"
  },
  image: {
    marginTop: 84,
    width: 152,
    height: 151
  },
  h2: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    color: "#333",
    fontSize: 16,
    marginTop: 15,
    marginBottom: 12
  },
  h3: {
    color: "#666",
    fontSize: 14,
    textAlign: "center",
    alignSelf: "stretch",
    paddingHorizontal: 53,
    marginBottom: 24
  },
  btn: {
    backgroundColor: "#E62117",
    height: 40,
    alignSelf: "stretch",
    // paddingHorizontal: 80,
    marginHorizontal: 80,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 4
  },
  btnText: {
    color: "#fff",
    fontSize: 16
  }
});

export { headerStyles, signResultPageStyle };

export default styles;
