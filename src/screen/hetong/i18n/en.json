{"电子签名": "E-signature", "借款合同": "Loan Contract", "完善签名": "Improve Signature", "未签名": "No signature yet", "指引": "Guidelines", "授权akulaku签署并管理您的债务合同": "Authorize <PERSON><PERSON><PERSON><PERSON> to sign and manage your debt contract", "邮箱确认": "Confirm Email Address", "输入邮箱以完成注册": "Enter email to complete registration", "用于接受您的电子签名账户信息": "For receiving your E-signature account information", "为保障您的账号安全，邮箱务必是您本人邮箱": "For your account security, please make sure the email provided belongs to you", "确认": "Confirm", "邮箱": "Email", "请输入邮箱": "Please enter your email", "您输入的邮箱有误": "Incorrect email input", "提示": "Tips", "您将被指引至privy页面": "You will be directed to the page of privy", "您的数据将提供给privy,用于创建电子签名账户": "Your information will be provided to privy to create E-signature account", "账户信息": "Account information", "您可以使用邮箱查看账号信息": "You can check the account information via your email", "修改": "Modify", "详情": "Details>>", "下一步": "Next", "签名未完成": "Incomplete signature", "跳过": "<PERSON><PERSON>", "若您跳过，您最终可以重新从Bills进入完善签名": "If you have this step skipped, you still can go to Bills again and have your signature improved", "账号注册中": "Registration is in process now", "Akulaku已将您的信息提交注册，正在受理中，您可稍后重试": " <PERSON><PERSON><PERSON><PERSON> has submitted your information for registration and is being processed. You can try again later", "现在签名": "Sign now", "我知道了": "Got it", "只差一步！快去签名吧": "Only one step left, go ahead and sign", "去签名": "Sign now", "签名已完成，您可以随时查看签名哟~": "Signature has already completed, and you can have it viewed anytime", "查看签名": "View your signature", "已签名": "Already signed", "仅差一步！": "Only one step left", "注册失败": "Register failed", "很抱歉，注册失败": "Sorry, register failed", "通过邮箱注册，privyid和password，将会发送至您的邮箱": "Register by email, privyid and password will be sent to your email", "下一张": "Next one", "邮箱查看Privyid": "View Privyid in email", "点击签名": "Click to sign", "点击附上签名": "Click to attach the signature", "点击获取验证码": "Click to get verification code", "怎么签名？": "How to sign", "1、邮箱注册，请务必保证您的邮箱正确": "1. For e-mail register, please ensure  your email address is correct", "邮箱务必是您的正确邮箱": "The email address must be correct", "2、注册成功后，privyid账号信息将会发送至您的邮箱您可前往邮箱查看": "2. After registered successful, privyid account information will be sent to you email You can check in email box", "账号信息已发送至您的邮箱": "Account information has been sent to your email", "3、进入privyid签名页面，点击签名": "3. Enter privyid signature page, click to sign", "4、请在签名区域，写上您的名字": "4. Please sign your name in sign area", "签名": "sign", "点击保存": "Click to save", "5、在合同底部点击附上您的签名": "5. Please attach your signature in the bottom of the contract", "6、获取验证码，填写发送后，签名完成": "6. After receive verification code, fill in and send out, the signature is completed", "签名完成": "Signature completed", "获取验证码": "Receive verification code", "您将被引导至Digisign签名页面": "You will be led to Digisign sign page", "您的数据将提供给Digisign，用于创建电子签名帐户": "Your data will be provided to Digsign to create e-signature account", "Akulaku已将您的信息提交注册，正在受理中，您可1分钟后再次点击完善签名。（59s）": "<PERSON><PERSON><PERSON><PERSON> has submit your registered information and processing it, you can click again to complete the signature after 1 minute. (59s) ", "邮箱最大30位": "max. 30-digits of e-mail length", "合同标题": "Loan Contract", "signature_tips": "Add e-signature to increase credit score and enjoy more benefits", "electric_sign_long_guide_title": "Cara Ttd digital?", "electric_sign_long_guide_btn": "Cara Ttd digital>>", "您的电子合同正在签署流程中，请于 xxx 后再次尝试。": "Your electronic contract is in the process of signing. Please try again after {{time}}.", "您重试的次数已到上限，请联系在线客服反馈。": "Your number of retries have reached the upper limit. Please contact online customer service for feedback.", "重新确认电子邮箱": "Reconfirm Email", "注册失败弹窗标题": "Registration Failed", "失败原因": "Reason For Failure", "您已使用xx在digisign平台注册，无法使用其他邮箱再次注册": "You have used {{email}} to register on the Digisign platform, and cannot register again using another email address", "请检查以下邮箱地址，确认无误后可重新提交进行电子签名服务": "Please check the following email address and resubmit for electronic signature service after confirming that it is correct", "您的手机号xxx在digisign平台已注册，请联系digisign平台进行更改": "Your mobile number {{phone}} has been registered on the Digisign platform. Please contact the Digisign platform to change", "确认邮箱以便完成注册": "Complete the email to complete the registration", "您的KYC信息校验未通过，请联系客服处理。": "Your KYC information verification failed. Please contact customer service for processing.", "网络延迟，请重新尝试。": "Network delay. Please contact customer service"}