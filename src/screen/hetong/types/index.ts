/**
 * @description 电子合同类型定义文件
 */

export const SIGN_PAGE_BACK_EVENT = "SIGN_PAGE_BACK_EVENT";

export interface BaseResponse<T> {
  errCode: string;
  errMsg: string;
  success: boolean;
  sysTime: number;
  data: T;
}

export enum SignResultPageType {
  SIGNED = 1, // 签名
  REEGEISTERING = 2, // 注册中
  UN_SIGNED = 3, // 未签名
  LOADING = 4 // DIGSIGN 回调后 需要先上传数据
}

export enum RegisterStatus {
  UNREGISET = 1,
  REGISTING = 2,
  REGISTED = 3,
  REGIST_FAIL = 4,
  REGISTER_BY_OTHER_CHANNEL,
  REGISTER_FAIL_WAIT_CONFIRM
}

export enum ElectricSignPlatformType {
  DIGISIGN = 2,
  PRIVY = 1
}

export interface ElectricSignRegist {
  registerEmail: string;
  status: number;
  thirdPartyType: number;
  registerByOtherWay: boolean;
  hadRegisteredEmail: string;
  rejectReason: string;
  errCode: string;
  allFailed: boolean;
}

export enum ContractSignStatus {
  NO_COMPLETE = 0,
  RECOMPLETE = 90,
  SIGNING = 99,
  SIGNED = 100,
  SIGNED_BY_USER = 101
}

export enum API {
  ELECTRIC_SIGNATURE_IMPROVE_SIGN = "/capi/personal/user/e-signatures/auth-contract/process", // 电子签名 - 完善合同(授权书合同)
  ELECTRIC_SIGNATURE_POLICY = "/capi/personal/user/e-signatures/auth-contract/info/get", // 电子签名 - 获取合同(授权书合同)
  // ELECTRIC_SIGNATURE_PLATFORM = "/capi/personal/user/e-signatures/third-party/info/get", // 电子签名 - 平台分流信息
  ELECTRIC_SIGNATURE_REGIST_STATUS = "/capi/personal/user/e-signatures/register/info", // 电子签名 - 注册信息查询
  ELECTRIC_SIGNATURE_REGIST = "/capi/personal/user/e-signatures/register", // 电子签名 - 注册
  ELECTRIC_SIGNATURE_UPLOAD_DIGSIGN_MSG = "/capi/personal/user/e-signatures/digi-sign/call-back-result"
}

export interface ElectricSign {
  contractId: number;
  contractNo: string;
  userName: string;
  userIdNo: string;
  address: string;
  authDate: string;
  signStatus: number;
  redirect: string;
  docUrl: string;
}

export type HeaderIconType = "notSign" | "notCompleteSign" | "electricSigned" | "default";
export type checkBeforeImprove = {
  electricSign?: ElectricSign;
  signRegist?: ElectricSignRegist;
};

export enum LoadStatus {
  LOADING,
  SUCCESS,
  FAIL
}

export type ResultDataMap = {
  [key: string]: {
    title: string;
    info: string;
    image: number;
    buttonText: string;
    onButtonClick: () => void;
    pageSensorData: {
      page_name: string;
      page_id: string;
      extra: {
        Aku_UserStatus: string;
      };
    };
  };
};

export type SignPageNavParams = {
  url: string;
  title: string;
  platform: string;
  source: string;
};

// 完善状态,0-未完善 90-重新完善(异常场景可以重试获取接口) 99-签名中 100-已完善 101-只有个人签名
export enum AuthContractProcessStatus {
  FETCH_ERROR = -1, // 前端自己定义的状态，用来标志网络请求错误
  NO_COMPLETE = 0, // 未完善
  PROCESSING = 10, // 后台异步处理中
  RE_COMPLETE = 90, // 需要重新完善， 重新走流程
  SIGNING = 99, // 签名中， 这个时候已经生成了三方URL
  ALL_COMPLETED = 100, // 签名已完成
  PERSONAL_COMPLETED = 101
}

export type AuthContractProcessData = {
  redirect?: string;
  status: number;
  overLimitFlag: boolean;
  retryNextBeginTime: number;
};

export enum RegisterError {
  EMAIL_EXIST = "10820040012101",
  REGISTER_PHONE_EXIST = "10820040012102", // 电话号码已经注册
  REGISTER_VERIFY_PHOTO_FAIL = "10820040012103" // 验证授信资料失败 - 大部分原因是证件照和手持证件照审核不通过
}

export type modalType =
  | "IncompleteSignature"
  | "AboutSignature"
  | "ConfirmEmail"
  | "Signature"
  | "SignInProcess"
  | "ReConfirmEmail";

export enum PageRegisterStatus {
  UN_REGISTER, // 未注册
  REGISTER_SUCCESS, // 注册成功
  REGISTER_FAIL_WAIT_CONFIRM, // 需要用户确认邮箱
  REGISTER_FAIL_PHONE_EXIST, // 注册电话号码已经存在
  REGISTER_FAIL_VERIFY_KYC, // 用户授信信息验证失败（一般都是证件照没验证通过）
  REGISTER_FAIL_OTHER_ERROR,
  REGISTERING
}

export enum DialogHadShow {
  HAD_SHOW = "1",
  NO_SHOW = "0"
}
