/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-06 11:38
 * @description
 */

import React, { Component } from "react";
import { View, StyleSheet, UIManager, BackHandler, Text } from "react-native";
import PDFView from "react-native-view-pdf";
import {
  Loading,
  NavigationBar,
  WINDOW_HEIGHT,
  WINDOW_WIDTH,
  NAV_BAR_HEIGHT,
  Android,
  NetworkErrorComponent
} from "@akulaku-rn/akui-rn";
import i18next, { TFunction } from "i18next";
import { withTranslation } from "common/services/i18n";
import RootView from "common/components/Layout/RootView";
import { inject, observer } from "mobx-react";
import store from "./store/contractPdf";
import { SensorEvent } from "common/nativeModules/sdk/nativeSensroModule";
import { UserStatus } from "./util/SensorManager";
import ESignatureConstants from "./constants";
import ContractPdfStore from "./store/contractPdf";
import { Type } from "common/components/BaseContainer";
import { LoadStatus } from "./types";
import { WebView } from "react-native-webview";
import DeviceInfoHelper from "common/util/DeviceInfoHelper";
import { NativeNavigationModule } from "common/nativeModules";
import AkConsole from "common/util/AkConsole/AkConsole";
import { getCurrentScreen } from "common/constant";
import { UserRouteName } from "../../split-package-config";

const styles = StyleSheet.create({
  overlayContainer: {
    position: "absolute",
    height: "100%",
    width: WINDOW_WIDTH,
    justifyContent: "center",
    alignItems: "center"
  }
});

type Props = {
  t: TFunction;
  url: string;
  store: ContractPdfStore;
  configSensorEvent: (event: SensorEvent) => void;
};

@RootView({
  withI18n: [
    ESignatureConstants.MODULE_NAME,
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json"),
      vi: require("./i18n/vi.json"),
      ms: require("./i18n/vi.json")
    }
  ],
  store
})
@withTranslation(ESignatureConstants.MODULE_NAME)
@inject("store")
@observer
export default class ContractPdfPage extends Component<Type.Props<ContractPdfStore>> {
  constructor(props: Type.Props<ContractPdfStore>) {
    super(props);
    const { configSensorEvent } = this.props;
    configSensorEvent({
      page_id: "1011",
      page_name: "Contract page",
      extra: { Aku_UserStatus: UserStatus.USER_SIGNED }
    });
  }

  componentDidMount(): void {
    BackHandler.addEventListener("hardwareBackPress", this.onBack);
  }

  componentWillUnmount(): void {
    BackHandler.removeEventListener("hardwareBackPress", this.onBack);
  }

  onBack = () => {
    NativeNavigationModule.popToScreen(UserRouteName.Hetong);
    return true;
  };

  pdfRef: WebView | null = null;

  onLoad = () => {
    AkConsole("hetong", "onLoad");
    this.props.store.pageStore.changeLoadStatus(LoadStatus.SUCCESS);
  };

  onError = () => {
    AkConsole("hetong", "onError");
    this.props.store.pageStore.changeLoadStatus(LoadStatus.FAIL);
  };

  renderOverlayContent = () => {
    switch (this.props.store.pageStore.loadStatus) {
      case LoadStatus.LOADING:
        return (
          <View style={styles.overlayContainer}>
            <Loading useBack />
          </View>
        );
      case LoadStatus.FAIL:
        return (
          <View style={styles.overlayContainer}>
            <NetworkErrorComponent
              message={this.props.t("global:啊，网络故障导致我们的距离好远啊!")}
              containerStyle={{ backgroundColor: "#FFFFFF", marginTop: -NAV_BAR_HEIGHT }}
              onRetryPress={() => {
                if (!this.pdfRef) return;
                // @ts-ignore
                this.pdfRef.reload();
              }}
              buttonText={this.props.t("global:刷新")}
            />
          </View>
        );
      case LoadStatus.SUCCESS:
      default:
        return null;
    }
  };

  getPDFRef = (pdf: WebView | null) => {
    this.pdfRef = pdf;
  };

  render() {
    const {
      store: {
        navParams: { url }
      }
    } = this.props;
    console.log(`url: `, url);
    return (
      <View style={{ flex: 1, backgroundColor: "#FFFFFF" }}>
        {/* Some Controls to change PDF resource */}
        <View style={{ zIndex: 2 }}>
          <NavigationBar title={this.props.t("合同标题")} onBackPress={this.onBack} />
        </View>
        <View style={{ flex: 1 }}>
          <WebView
            ref={this.getPDFRef}
            style={{ flex: 1 }}
            source={{ uri: url }}
            onLoad={this.onLoad}
            onError={this.onError}
          />
          {this.renderOverlayContent()}
        </View>
      </View>
    );
  }
}
