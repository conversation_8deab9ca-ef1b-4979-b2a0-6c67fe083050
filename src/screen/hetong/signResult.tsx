/**
 * @description 注册中 & 签名中 & 已签名 公用页面
 *
 * */

import * as React from "react";
import { Component } from "react";
import { BackHandler, Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import * as Types from "./types";
import { ResultDataMap, SignResultPageType } from "./types";
import { inject, observer } from "mobx-react";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import { FontStyles, Loading, NavigationBar, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

import i18next, { TFunction } from "i18next";
import SensorManager, { UserStatus } from "./util/SensorManager";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";
import ESignatureConstants from "./constants";
import SignResultStore from "./store/signresult";
import { Type } from "common/components/BaseContainer";

const styles = StyleSheet.create({
  outContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FFF"
  },
  container: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 32
  },
  image: {
    height: 150,
    width: 150,
    marginBottom: 15
  },
  title: {
    fontSize: 16,
    lineHeight: 19,
    color: "#333333",
    marginBottom: 12,
    ...FontStyles["rob-medium"]
  },
  info: {
    fontSize: 14,
    lineHeight: 16,
    color: "#666666",
    textAlign: "center",
    marginBottom: 24
  },
  button: {
    paddingHorizontal: 30,
    minWidth: 142,
    height: 40,
    backgroundColor: "#E62117",
    borderRadius: 4,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center"
  },
  buttonText: {
    fontSize: 16,
    lineHeight: 19,
    color: "#FFFFFF"
  }
});

@RootView({
  withI18n: [
    ESignatureConstants.MODULE_NAME,
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json"),
      vi: require("./i18n/vi.json"),
      ms: require("./i18n/vi.json")
    }
  ],
  store: SignResultStore
})
@withTranslation(ESignatureConstants.MODULE_NAME)
@inject("store")
@observer
class SignResult extends Component<Type.Props<SignResultStore>> {
  constructor(props: Type.Props<SignResultStore>) {
    super(props);
    const {
      store: {
        pageStore,
        navParams: { type, source, platform, title, digsignData }
      },
      navigation
    } = this.props;
    pageStore.setNavParams({ type, source, platform, title, digsignData });
    this.pageSensor();
  }

  componentDidMount(): void {
    if (this.props.store.pageStore.type === SignResultPageType.LOADING) {
      this.props.store.pageStore.uploadDigsignData(this.pageSensor);
    }
    BackHandler.addEventListener("hardwareBackPress", this.onBack);
  }

  componentWillUnmount(): void {
    BackHandler.removeEventListener("hardwareBackPress", this.onBack);
  }

  pageSensor = () => {
    const {
      store: { pageStore },
      configSensorEvent,
      t
    } = this.props;
    const data = this.getDataMap(t, pageStore.type);
    data && configSensorEvent(data.pageSensorData);
  };

  onBack = () => {
    switch (this.props.store.pageStore.type) {
      case SignResultPageType.LOADING:
      case SignResultPageType.SIGNED:
        NativeNavigationModule.popTo(2);
        break;
      default:
        NativeNavigationModule.goBack();
        break;
    }
    return true;
  };

  getDataMap = (t: TFunction, type: SignResultPageType) => {
    const dataMap: ResultDataMap = {
      [SignResultPageType.SIGNED]: {
        title: t("已签名"),
        info: t("签名已完成，您可以随时查看签名哟~"),
        image: require("./images/sign_conplete.webp"),
        buttonText: t("查看签名"),
        onButtonClick: () => {
          SensorManager.sensorSignResultViewSign();
        },
        pageSensorData: {
          page_name: "Go to the signature",
          page_id: "1010",
          extra: { Aku_UserStatus: UserStatus.USER_SIGNED }
        }
      },
      [SignResultPageType.REEGEISTERING]: {
        title: t("账号注册中"),
        info: t("Akulaku已将您的信息提交注册，正在受理中，您可稍后重试"),
        image: require("./images/processnow.webp"),
        buttonText: t("我知道了"),
        onButtonClick: () => {
          SensorManager.sensorClickRegisterConfirm(UserStatus.USER_UNSIGNED);
        },
        pageSensorData: {
          page_name: "Register is in progress",
          page_id: "451",
          extra: { Aku_UserStatus: UserStatus.USER_UNSIGNED }
        }
      },
      [SignResultPageType.UN_SIGNED]: {
        title: t("仅差一步！"),
        info: t("只差一步！快去签名吧"),
        image: require("./images/laststep.webp"),
        buttonText: t("完善签名"),
        onButtonClick: () => {
          SensorManager.sensorSignResultGoSign();
        },
        pageSensorData: {
          page_name: "Go to the signature",
          page_id: "1010",
          extra: { Aku_UserStatus: UserStatus.USER_UNSIGNED }
        }
      }
    };
    return dataMap[type];
  };

  onButtonPress = (sensorFunc: () => void) => async () => {
    const {
      store: { navParams, pageStore }
    } = this.props;
    sensorFunc && sensorFunc();
    if (this.props.store.pageStore.type === SignResultPageType.SIGNED) {
      // 请求查看合同信息接口
      this.props.store.pageStore.viewContract();
    } else {
      this.onBack();
    }
  };

  onBackPress = () => {
    // this.props.navigation.popToHome(3);
  };

  render() {
    const {
      store: {
        navParams,
        pageStore: { type }
      },
      t
    } = this.props;
    let content;
    const { title = "" } = navParams;
    const pageData = this.getDataMap(t, type);
    if (type === SignResultPageType.LOADING) {
      content = <Loading useBack />;
    } else {
      content = (
        <View style={styles.container}>
          <Image style={styles.image} source={pageData.image} />
          <Text style={styles.title}>{pageData.title}</Text>
          <Text style={styles.info}>{pageData.info}</Text>
          <View style={{ flexDirection: "row" }}>
            <TouchableOpacity
              style={styles.button}
              activeOpacity={0.85}
              onPress={this.onButtonPress(pageData.onButtonClick)}
            >
              <Text style={styles.buttonText}>{pageData.buttonText}</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return (
      <View style={styles.outContainer}>
        <View style={{ position: "absolute", top: 0, width: WINDOW_WIDTH }}>
          <NavigationBar title={title} onBackPress={this.onBack} />
        </View>
        {content}
        {/*<BottomModal t={this.props.t} type={} />*/}
      </View>
    );
  }
}

export default SignResult;
