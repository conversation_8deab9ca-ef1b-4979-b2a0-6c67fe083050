/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-04 17:53
 * @description
 */

import React, { PureComponent } from "react";
import { <PERSON>, BackH<PERSON><PERSON>, DeviceEventEmitter } from "react-native";
import { WebView, WebViewNavigation } from "react-native-webview";
import { NavigationBar, Loading, Android, NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import RootView from "common/components/Layout/RootView";
import store from "./store/signPage";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import { BottomModal } from "./components";
import { ContractSignStatus, SIGN_PAGE_BACK_EVENT, SignPageNavParams, SignResultPageType } from "./types";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";
import { get, debounce } from "lodash";
import SensorManager, { UserStatus } from "./util/SensorManager";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import SignPageStore from "./store/signPage";
import { Type } from "common/components/BaseContainer";
import ESignatureConstants from "./constants";
import { WebViewSource } from "react-native-webview/lib/WebViewTypes";

import AkConsole from "common/util/AkConsole/AkConsole";

const PATH = "/ec-basic/digisignCallback";

@RootView({
  withI18n: [
    ESignatureConstants.MODULE_NAME,
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json"),
      vi: require("./i18n/vi.json"),
      ms: require("./i18n/vi.json")
    }
  ],
  store
})
@withTranslation(ESignatureConstants.MODULE_NAME)
@inject("store")
@observer
export default class SignPage extends PureComponent<
  Type.Props<SignPageStore, SignPageNavParams>,
  { source?: WebViewSource }
> {
  constructor(props: Type.Props<SignPageStore, SignPageNavParams>) {
    super(props);
    const { store } = this.props;
    const { navParams } = store;
    this.state = {
      source: { uri: navParams.url }
    };
    this.props.configSensorEvent({
      page_id: "1009",
      page_name: "Third login page",
      extra: { Aku_UserStatus: UserStatus.USER_UNSIGNED }
    });
  }

  componentDidMount(): void {
    BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
  }

  componentWillUnmount(): void {
    this.webview = null;
    BackHandler.removeEventListener("hardwareBackPress", this.onBackPress);
  }

  private webview: WebView | null = null;

  private canGoBack?: boolean = false;

  onBackPress = () => {
    const { navigation } = this.props;
    if (this.canGoBack) {
      this.webview && this.webview.goBack();
    } else {
      DeviceEventEmitter.emit(SIGN_PAGE_BACK_EVENT); //
      // this.webview.goBack();
      navigation.goBack();
    }
    return true;
  };

  onClickNext = async () => {
    const {
      store,
      store: { pageStore, navParams }
    } = this.props;
    Loading.show();
    let userStatus = UserStatus.USER_UNSIGNED;
    try {
      const sign: { signStatus: number } | undefined = await pageStore.getSignStatus();
      if (
        sign &&
        sign.signStatus &&
        (sign.signStatus === ContractSignStatus.SIGNED || sign.signStatus === ContractSignStatus.SIGNED_BY_USER)
      ) {
        userStatus = UserStatus.USER_SIGNED;
        pageStore.signStatus = userStatus;
        this.props.navigation.navigate({
          screen: "SignResult",
          params: {
            type: SignResultPageType.SIGNED,
            title: navParams.title,
            source: navParams.source,
            platform: navParams.platform
          }
        });
      } else {
        pageStore.changeModalVisible();
      }
    } catch (e) {
      AkConsole("hetong", e);
      NativeToast.showMessage(e.errMsg);
    } finally {
      SensorManager.sensorSignPageClickNext(userStatus);
      Loading.dismiss();
    }
  };

  hasCallBack = false;

  jumpSignResult = debounce(
    (navParams: SignPageNavParams, event: WebViewNavigation) => {
      let data = "";
      const urls = get(event, "url", "").split(`${PATH}?`);
      if (urls[1] && urls[1].startsWith("msg")) {
        if (this.hasCallBack) return;
        this.hasCallBack = true;
        // this.setState({ source: undefined });
        data = urls[1].split("msg=")[1];
        NativeNavigationModule.navigate({
          screen: "SignResult",
          params: {
            type: SignResultPageType.LOADING,
            source: navParams.source,
            platform: navParams.platform,
            title: navParams.title,
            digsignData: data,
            popTwo: true
          }
        });
      }
    },
    1000,
    { leading: true, trailing: false }
  );

  //  等号问题   多次跳转
  onNavigationStateChange = (navParams: SignPageNavParams) => (event: WebViewNavigation) => {
    AkConsole("hetong", "onNavigationStateChange: ", event);
    if (this.canGoBack !== event.canGoBack) {
      this.canGoBack = event.canGoBack;
    }
    if (!!~get(event, "url", "").indexOf(PATH)) {
      AkConsole("hetong", get(event, "url", ""));
      this.props.store.pageStore.signStatus = UserStatus.USER_SIGNED;
      this.jumpSignResult(navParams, event);
    }
  };

  renderLoading = () => {
    return <Loading />;
  };

  renderError = () => {
    return (
      <NetworkErrorComponent
        message={this.props.t("global:啊，网络故障导致我们的距离好远啊!")}
        onRetryPress={() => {
          this.webview && this.webview.reload();
        }}
        buttonText={this.props.t("global:刷新")}
      />
    );
  };

  render() {
    const { t, store, navigation } = this.props;
    const { pageStore, navParams } = store;
    AkConsole("hetong", navParams.url);
    return (
      <>
        <NavigationBar
          title={navParams.title || ""}
          onBackPress={this.onBackPress}
          renderRight={<Text onPress={this.onClickNext}>{t("下一步")}</Text>}
        />
        <WebView
          ref={webview => {
            this.webview = webview;
          }}
          scalesPageToFit={Android}
          onNavigationStateChange={this.onNavigationStateChange(navParams)}
          originWhitelist={["*"]}
          style={{ flex: 1, backgroundColor: "transparent" }}
          source={this.state.source}
          startInLoadingState={true}
          renderLoading={this.renderLoading}
          renderError={this.renderError}
        />
        <BottomModal
          onConfirm={() => {
            SensorManager.sensorSignPageNextTipDialogClickConfirm();
            // pageStore.changeModalVisible();
          }}
          t={t}
          backPress={() => {
            pageStore.changeModalVisible();
          }}
          visible={pageStore.modalVisible}
          onClose={() => {
            AkConsole("hetong", "SignPage BottomModal onClose");
            SensorManager.sensorSignPageNextTipDialogClickSkip();
            pageStore.changeModalVisible();
            this.props.navigation.navigate({
              screen: "SignResult",
              params: {
                type: SignResultPageType.UN_SIGNED,
                title: navParams.title,
                source: navParams.source,
                platform: navParams.platform
              }
            });
          }}
          type={"IncompleteSignature"}
        />
      </>
    );
  }
}
