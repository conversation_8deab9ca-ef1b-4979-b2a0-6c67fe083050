import * as React from "react";
import { StyleSheet, TouchableOpacity, View, Text, GestureResponderEvent } from "react-native";

const styles = StyleSheet.create({
  btnContainer: {
    height: 56,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
    paddingHorizontal: 12
  },
  btn: {
    height: 40,
    backgroundColor: "#E62117",

    alignSelf: "stretch",
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center"
  },
  btnText: {
    fontSize: 16,
    color: "#fff"
  },
  mask: {
    position: "absolute",
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: "rgba(0, 0, 0, 0.12)",
    opacity: 16
  }
});

interface Props {
  onPress: (event: GestureResponderEvent) => void;
  text: string;
  disabled?: boolean;
}

const Btn: React.FC<Props> = (props: Props) => {
  const { text, disabled } = props;
  if (disabled) {
    return (
      <View style={[styles.btnContainer]}>
        <View style={[styles.btn, { position: "relative", backgroundColor: "rgb(255,176,176)" }]}>
          <Text style={styles.btnText}>{text}</Text>
        </View>
        {/* <View style={styles.mask} /> */}
      </View>
    );
  }

  return (
    <TouchableOpacity
      onPress={event => {
        event.persist();
        props.onPress.call(null, event);
      }}
      style={styles.btnContainer}
      activeOpacity={0.85}
    >
      <View style={styles.btn}>
        <Text style={styles.btnText}>{text}</Text>
      </View>
    </TouchableOpacity>
  );
};

Btn.defaultProps = {
  text: "click me ",
  onPress: () => {},
  disabled: false
};

export default Btn;
