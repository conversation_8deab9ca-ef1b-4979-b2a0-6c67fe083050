import * as React from "react";
import { View, TouchableOpacity, Text, Image } from "react-native";
import styles from "./styles";
import { getCurrentScreen } from "common/constant";
import AkConsole from "common/util/AkConsole/AkConsole";

// eslint-disable-next-line @typescript-eslint/no-var-requires
const tipIcon = require("../../images/bill_tip.webp");
// eslint-disable-next-line @typescript-eslint/no-var-requires
const tipClose = require("../../images/bill_tip_close.webp");

interface Props {
  txt: string;
  onClose: () => void;
}

const Tips: React.FC<Props> = (props: Props) => {
  AkConsole("hetong", "tips render");
  const { txt, onClose } = props;
  return (
    <View style={styles.container}>
      <Image style={styles.tipIcon} source={tipIcon} />
      <Text style={styles.tipText}>{txt}</Text>
      <TouchableOpacity style={styles.closeContainer} onPress={onClose}>
        <Image style={styles.closeIcon} source={tipClose} />
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(Tips, (prevProps: Props, nextProps: Props) => {
  return prevProps.txt === nextProps.txt;
});
