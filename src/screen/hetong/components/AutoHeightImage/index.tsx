import * as React from "react";
import { Image, ImageProps, NativeSyntheticEvent, ImageLoadEventData } from "react-native";

interface Props extends ImageProps {
  width: number;
}

const AutoHeightImage: React.FC<Props> = (props: Props) => {
  const [height, changeHeight] = React.useState(1);
  const { width, style, ...p } = props;
  return (
    <Image
      style={{ width, height }}
      {...p}
      onLoad={(evt: NativeSyntheticEvent<ImageLoadEventData>) => {
        const hh = (evt.nativeEvent.source.height / evt.nativeEvent.source.width) * width;
        changeHeight(hh);
      }}
    />
  );
};

export default AutoHeightImage;
