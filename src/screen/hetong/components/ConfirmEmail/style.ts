import { StyleSheet } from "react-native";
import fontWeightTrans, { FontWeightNameMappings } from "../../util/fontTrans";
import { FontStyles } from "@akulaku-rn/akui-rn";

const styles = StyleSheet.create({
  title: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    fontSize: 16,
    textAlign: "center",
    marginBottom: 39,
    color: "#333"
  },
  block: {
    // height: 95,
    paddingTop: 16,
    paddingHorizontal: 16,
    backgroundColor: "#F5F5F5",
    borderRadius: 8
  },
  list: {
    flexDirection: "row",
    marginBottom: 12
  },
  listLeft: {
    color: "#333",
    fontSize: 12,
    marginRight: 10,
    ...FontStyles["rob-medium"]
  },
  listRight: {
    fontSize: 12,
    color: "#999",
    flex: 1,
    flexWrap: "wrap"
  },
  h2: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    marginTop: 16,
    marginBottom: 12,
    fontSize: 14,
    color: "#333"
  },
  inputBlock: {
    height: 50,
    // paddingHorizontal: 12,
    paddingLeft: 12,
    // paddingVertical: 13,
    backgroundColor: "#F5F5F5",
    borderRadius: 4,
    flexDirection: "row",
    alignItems: "center",
    // marginBottom: 198
    marginBottom: 8
  },
  inputIcon: {
    width: 24,
    height: 24,
    marginRight: 12
  },
  clearIcon: {
    width: 12,
    height: 12
    // marginLeft: 12
  },
  textInput: {
    flex: 1,
    // height: 50,
    // backgroundColor: "#f5f5f5",
    fontSize: 15,
    color: "#333",
    padding: 0,
    margin: 0,
    height: 50
    // fontFamily: Roboto,
    // fontWeight: Regular
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    height: 14
  },
  errorIcon: {
    width: 12,
    height: 12,
    marginRight: 5
  },
  errormsg: {
    fontSize: 12,
    color: "#E62117"
  }
});

export default styles;
