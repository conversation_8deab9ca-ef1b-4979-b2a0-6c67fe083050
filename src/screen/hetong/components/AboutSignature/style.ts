import { FontStyles } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";
import fontWeightTrans, { FontWeightNameMappings } from "../../util/fontTrans";

const styles = StyleSheet.create({
  title: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    fontSize: 16,
    textAlign: "center",
    marginBottom: 34,
    color: "#333"
  },
  h2: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    fontSize: 14,
    color: "#333"
  },
  p: {
    fontSize: 12,
    color: "#999",
    flex: 1,
    flexWrap: "wrap"
  },
  list: {
    flexDirection: "row",
    marginBottom: 12
  },
  listLeft: {
    ...FontStyles["rob-medium"],
    color: "#333",
    fontSize: 12,
    marginRight: 10
  },
  stepRow: {
    flexDirection: "row",
    paddingLeft: 42,
    paddingRight: 52,
    justifyContent: "space-between"
  },
  imagebox: {
    width: 86
  },
  image: {
    width: 64,
    height: 64,
    backgroundColor: "rgb(252, 234, 234)",
    borderRadius: 12,
    alignSelf: "center"
    // marginHorizontal: 7
  },
  imagetext: {
    marginTop: 6,
    fontSize: 10,
    color: "#333",
    textAlign: "center",
    ...fontWeightTrans(FontWeightNameMappings.Medium)
  }
});

export default styles;
