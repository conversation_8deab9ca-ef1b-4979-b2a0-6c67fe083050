import * as React from "react";
import { View, Text, Image } from "react-native";
import styles from "./style";

const Content: React.FC = () => {
  return (
    <View
      style={{
        paddingHorizontal: 16
      }}
    >
      <Text style={styles.title}>About Signature</Text>
      <Text
        style={[
          styles.h2,
          {
            marginBottom: 24
          }
        ]}
      >
        Only Two Steps
      </Text>
      <View style={styles.stepRow}>
        <View style={styles.imagebox}>
          <Image style={styles.image} source={require("../../images/step2.webp")} />
          <Text style={styles.imagetext}>① Registy Email</Text>
        </View>
        <View style={styles.imagebox}>
          <Image style={styles.image} source={require("../../images/step1.webp")} />
          <Text style={styles.imagetext}>② Lakukan tanda tangan</Text>
        </View>
      </View>
      <Text
        style={[
          styles.h2,
          {
            marginTop: 32,
            marginBottom: 12
          }
        ]}
      >
        Apa itu tanda tangan digital?
      </Text>
      <Text style={styles.p}>
        Tanda tangan yang dilekatkan pada informasi eletonik guna sebagai alat perlindungan hukum anda terhadap
        perjanjian kredit di Akulaku. Tanda tangan digital ini bekerjasama dengan Privy.ID
      </Text>
      <Text
        style={[
          styles.h2,
          {
            marginTop: 16,
            marginBottom: 12
          }
        ]}
      >
        Apa keuntungan tanda tangan elektronik
      </Text>
      <View style={styles.list}>
        <Text style={styles.listLeft}>01</Text>
        <Text style={styles.p}>Mudah, bisa dilakukan melalui ponsel dan dimanapun kamu berada</Text>
      </View>
      <View style={styles.list}>
        <Text style={styles.listLeft}>02</Text>
        <Text style={styles.p}>Terjamin, tanda tangan eletronik ini dilingungi oleh hukum di Indonesia</Text>
      </View>
      <View style={styles.list}>
        <Text style={styles.listLeft}>03</Text>
        <Text style={styles.p}>Aman, tanda tangan kamu unik dan tidak bisa ditiru oleh orang lain</Text>
      </View>
    </View>
  );
};

export default Content;
