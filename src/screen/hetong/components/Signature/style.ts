import { StyleSheet } from "react-native";
import fontWeightTrans, { FontWeightNameMappings } from "../../util/fontTrans";

const styles = StyleSheet.create({
  title: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    fontSize: 16,
    textAlign: "center",
    marginBottom: 39,
    color: "#333"
  },
  list: {
    flexDirection: "row"
  },
  listLeft: {
    width: 24,
    height: 24,
    marginRight: 12
  },
  listRight: {
    flex: 1,
    alignContent: "stretch",
    paddingTop: 3
  },
  listRightH2: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    color: "#333",
    fontSize: 14,
    marginBottom: 10
  },
  listRightH3: {
    // backgroundColor: "red",
    // height: 41,
    // alignSelf: "stretch",
    color: "#666",
    fontSize: 12,
    marginBottom: 5,
    flex: 1,
    flexWrap: "wrap"
  },
  listRightDetail: {
    color: "#E62117",
    fontSize: 12
  },
  bold: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    color: "#333",
    fontSize: 12
  }
});

export default styles;
