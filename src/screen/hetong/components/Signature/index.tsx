import * as React from "react";
import { Image, Text, TouchableOpacity, View } from "react-native";
import { inject, observer } from "mobx-react";
import { debounce, get } from "lodash";
import BlockBtn from "../BlockBtn";
import styles from "./style";
import SensorManager, { UserStatus } from "../../util/SensorManager";
import { ElectricSignPlatformType, PageRegisterStatus } from "../../types";
import { NativeNavigationModule } from "common/nativeModules";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import ESignatureStore from "../../store";

const ELECTRIC_SIGNATURE_DETAIL = "https://privy.id/terms-and-conditions";

@inject("store")
@observer
class Signature extends React.Component<{ store?: pageStoreModel<ESignatureStore> }> {
  constructor(props: { store: pageStoreModel<ESignatureStore> }) {
    super(props);
    this.toWebView = debounce(this.toWebView, 300);
    this.toEditEmail = debounce(this.toEditEmail, 300);
    this.toSave = debounce(this.toSave, 300);
  }

  toWebView = () => {
    SensorManager.sensorClickTipDetail(UserStatus.USER_UNSIGNED);
    NativeNavigationModule.navigate({
      url: ELECTRIC_SIGNATURE_DETAIL
    });
  };

  toEditEmail = () => {
    const { store } = this.props;
    if (!store) return;
    const { pageStore } = store;
    pageStore.onEditEmail && pageStore.onEditEmail();
    SensorManager.sensorClickTipModify(UserStatus.USER_UNSIGNED);
  };

  toSave = () => {
    const { store } = this.props;
    if (!store) return;
    const { pageStore } = store;
    pageStore.onSaveEdit &&
      pageStore.onSaveEdit(async (url: string) => {
        pageStore.changeModalVisible(false);
        pageStore.navigateSignPage(url);
      });
    SensorManager.sensorClickTipConfirm(UserStatus.USER_UNSIGNED);
  };

  render() {
    const { store } = this.props;
    if (!store) return;
    const { pageStore } = store;
    const { getT, electricSignPlatform, email = "" } = pageStore;
    const t = getT();

    let tip;
    let title;
    let hasBtn = false;
    if (electricSignPlatform === ElectricSignPlatformType.PRIVY) {
      hasBtn = true;
      title = t("您将被指引至privy页面");
      tip = t("您的数据将提供给privy,用于创建电子签名账户");
    } else {
      title = t("您将被引导至Digisign签名页面");
      tip = t("您的数据将提供给Digisign，用于创建电子签名帐户");
    }

    const showModify =
      get(this.props.store, "pageStore.pageRegisterStatus", PageRegisterStatus.UN_REGISTER) !==
      PageRegisterStatus.REGISTER_FAIL_WAIT_CONFIRM;

    return (
      <>
        <View style={{ paddingHorizontal: 16 }}>
          <Text style={styles.title}>{t("提示")}</Text>
          <View style={[styles.list, { marginBottom: 21 }]}>
            <Image source={require("../../images/send.webp")} style={styles.listLeft} />
            <View style={styles.listRight}>
              <Text style={styles.listRightH2}>{title}</Text>
              <Text style={styles.listRightH3}>{tip}</Text>
              {hasBtn && (
                <TouchableOpacity onPress={this.toWebView}>
                  <Text
                    style={[
                      styles.listRightDetail,
                      {
                        marginTop: 8
                      }
                    ]}
                  >
                    {t("详情")}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
          <View
            style={[
              styles.list,
              {
                marginBottom: 188
              }
            ]}
          >
            <Image source={require("../../images/grayemail.webp")} style={styles.listLeft} />
            <View style={styles.listRight}>
              <Text style={styles.listRightH2}>{t("账户信息")}</Text>
              <Text style={styles.listRightH3}>
                {t("您可以使用邮箱查看账号信息")}
                <Text style={styles.bold}>({email})</Text>
              </Text>
              {!!showModify && (
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    alignItems: "flex-start",
                    marginTop: 8
                  }}
                  onPress={this.toEditEmail}
                >
                  <Image
                    style={{
                      width: 13,
                      height: 12,
                      marginRight: 2,
                      marginTop: 2
                    }}
                    source={require("../../images/edit2.webp")}
                  />
                  <Text style={styles.listRightDetail}>{t("修改")}</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        <BlockBtn onPress={this.toSave} text={t("确认")} />
      </>
    );
  }
}

export default Signature;
