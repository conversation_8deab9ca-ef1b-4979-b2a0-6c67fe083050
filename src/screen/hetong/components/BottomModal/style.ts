import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  modal: {
    justifyContent: "flex-end",
    margin: 0
  },
  container: {
    backgroundColor: "white",
    paddingTop: 16,
    // paddingBottom: isShapedIOS ? 27 : 0,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    alignContent: "stretch",
    position: "relative"
  },
  right: {
    position: "absolute",
    top: 0,
    right: 0,
    paddingTop: 13,
    paddingRight: 12
    // backgroundColor: "skyblue"
  },
  icon: {
    width: 24,
    height: 24
  },
  left: {
    position: "absolute",
    top: 0,
    left: 0,

    paddingTop: 13,
    paddingLeft: 12
  },
  skipright: {
    position: "absolute",
    top: 0,
    right: 0,
    paddingTop: 19,
    paddingRight: 19
  },
  skip: {
    color: "#666",
    fontSize: 14
  }
});

export default styles;
