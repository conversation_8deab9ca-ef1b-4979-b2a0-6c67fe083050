import * as React from "react";
import { Image, KeyboardAvoidingView, ScrollView, Text, TouchableOpacity, View } from "react-native";

import IncompleteSignature from "../IncompleteSignature";
import ConfirmEmail from "../ConfirmEmail";
import SignInProcess from "../SignInProcess";
import Signature from "../Signature";

import styles from "./style";
import SensorManager, { UserStatus } from "../../util/SensorManager";
import { DialogContainer } from "common/components/DialogContainer";
import { TFunction } from "i18next";
import ReConfirmEmail from "../ReComfirnEmail";
import { modalType } from "../../types";
import { isShapedIOS } from "@akulaku-rn/akui-rn";

interface Props {
  t: TFunction;
  visible: boolean;
  onClose(): void;
  type: modalType;
  onConfirm?: () => void;
  backPress?: () => void;
}

class BottomModal extends React.PureComponent<Props> {
  dialogContainerRef: React.ReactNode;

  constructor(props: Props) {
    super(props);
    DialogContainer.addBackListener(this.goBack);
  }

  componentWillUnmount(): void {
    DialogContainer.removeBackListener();
  }

  goBack = () => {
    if (!!this.props.backPress) {
      this.props.backPress();
    } else {
      this.props.onClose();
    }
    return false;
  };

  render() {
    const { visible, onClose, type, t, onConfirm = () => {} } = this.props;
    const showLeft = type === "Signature";
    let tipRightBtn;

    if (type !== "IncompleteSignature") {
      tipRightBtn = (
        <TouchableOpacity style={styles.right} onPress={onClose}>
          <Image style={styles.icon} source={require("../../images/close.webp")} />
        </TouchableOpacity>
      );
    } else {
      tipRightBtn = (
        <TouchableOpacity style={styles.skipright} onPress={onClose}>
          <Text style={styles.skip}>{t("跳过")}</Text>
        </TouchableOpacity>
      );
    }
    let content;
    switch (type) {
      case "IncompleteSignature":
        if (visible) {
          SensorManager.sensorSignPageNextTipDialog();
        }
        content = <IncompleteSignature onConfirm={onConfirm} />;
        break;
      case "ConfirmEmail":
        if (visible) {
          // 邮箱曝光埋点
          SensorManager.sensorExposeEmailDialog(UserStatus.USER_UNSIGNED);
        }
        content = <ConfirmEmail />;
        break;
      case "ReConfirmEmail":
        if (visible) {
          // 邮箱曝光埋点
          SensorManager.sensorExposeReConfirmDialog(UserStatus.USER_UNSIGNED);
        }
        content = <ReConfirmEmail />;
        break;
      case "Signature":
        if (visible) {
          // Tip 曝光埋点
          SensorManager.sensorExposeTipDialog(UserStatus.USER_UNSIGNED);
        }
        content = <Signature />;
        break;
      case "SignInProcess":
        if (visible) {
          // Tip 曝光埋点
          SensorManager.sensorExposeRegisterDialog(UserStatus.USER_UNSIGNED);
        }
        content = <SignInProcess />;
        break;
      default:
        content = null;
        break;
    }
    if (!visible) {
      return null;
    }

    const renderContent = (
      <View
        style={{
          // display: visible ? "flex" : "none",
          justifyContent: "flex-end",
          zIndex: 10,
          position: "absolute",
          top: 0,
          bottom: 0,
          left: 0,
          right: 0
          // backgroundColor: "rgba(0,0,0,.5)"
        }}
      >
        <KeyboardAvoidingView enabled>
          <ScrollView scrollEnabled={false} pointerEvents="auto" keyboardShouldPersistTaps="handled">
            <View style={[{ flex: 1 }]}>
              <View style={[styles.container, { paddingBottom: isShapedIOS ? 34 : 0 }]}>
                {content}
                {tipRightBtn}
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    );

    return (
      <DialogContainer
        ref={ref => {
          this.dialogContainerRef = ref;
        }}
        renderContent={renderContent}
      />
    );
  }
}

export default BottomModal;
