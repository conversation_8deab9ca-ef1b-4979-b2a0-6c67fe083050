import * as React from "react";
import { View, Text, Image, Platform } from "react-native";
import { inject, observer } from "mobx-react";
import BlockBtn from "../BlockBtn";
import styles from "./style";
import * as AndroidNav from "../../util/androidNav";
import { isFunction } from "lodash";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import ESignatureStore from "../../store";

interface Props {
  onConfirm: () => void;
  store?: pageStoreModel<ESignatureStore>;
}

@inject("store")
@observer
class Incompletesignature extends React.Component<Props> {
  render() {
    const { store, onConfirm } = this.props;
    if (!store) return null;
    const { pageStore } = store;
    const t = pageStore.getT();
    return (
      <>
        <View style={{ paddingHorizontal: 16, alignItems: "center" }}>
          <Text style={styles.title}>{t("提示")}</Text>
          <Image style={styles.image} source={require("../../images/incompletesignature.webp")} />
          <Text style={styles.h2}>{t("签名未完成")}</Text>
          <Text style={styles.h3}>{t("若您跳过，您最终可以重新从Bills进入完善签名")}</Text>
        </View>
        <BlockBtn
          onPress={event => {
            isFunction(onConfirm) && onConfirm();
            pageStore.changeModalVisible(false);
          }}
          text={t("现在签名")}
        />
      </>
    );
  }
}

export default Incompletesignature;
