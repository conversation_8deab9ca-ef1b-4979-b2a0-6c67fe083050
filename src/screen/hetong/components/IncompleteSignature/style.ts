import { StyleSheet } from "react-native";
import fontWeightTrans, { FontWeightNameMappings } from "../../util/fontTrans";

const styles = StyleSheet.create({
  title: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    fontSize: 16,
    textAlign: "center",
    marginBottom: 40,
    color: "#333"
  },
  image: {
    width: 150,
    height: 150
  },
  h2: {
    ...fontWeightTrans(FontWeightNameMappings.Medium),
    fontSize: 16,
    color: "#333",
    textAlign: "center",
    marginTop: 16,
    marginBottom: 12
  },
  h3: {
    paddingHorizontal: 53,
    textAlign: "center",
    fontSize: 14,
    color: "#666",
    marginBottom: 68
  }
});

export default styles;
