import * as React from "react";
import { View, Text, Image } from "react-native";
import { inject, observer } from "mobx-react";
import BlockBtn from "../BlockBtn";
import styles from "./style";
import SensorManager from "../../util/SensorManager";
import ESignatureStore from "../../store";

interface Props {
  store?: {
    pageStore: ESignatureStore;
  };
}

@inject("store")
@observer
class SignInProcess extends React.Component<Props> {
  timer: number | undefined;

  constructor(props: Props) {
    super(props);
  }

  componentDidMount() {
    if (this.props.store && this.props.store.pageStore) {
      const { electricSignPlatform } = this.props.store.pageStore;
    }
    // console.log(electricSignPlatform);
  }

  countExpireTime = 0;

  getCountExpireTime() {
    if (this.countExpireTime <= 0) {
      this.countExpireTime = 0; // 获取原生缓存
    }
    if (this.countExpireTime <= 0) {
      this.countExpireTime = Date.now() + 60 * 1000;
    }
    return this.countExpireTime;
  }

  startCountDown() {
    const expireTime = this.getCountExpireTime();
    const leftMills = expireTime - Date.now();
    this.timer = setInterval(() => {}, 1000);
  }

  render() {
    const { store } = this.props;
    if (!store) return null;
    const { pageStore } = store;
    const t = pageStore.getT();
    // const { electricSignPlatform } = pageStore;
    return (
      <>
        <View style={{ paddingHorizontal: 16, alignItems: "center" }}>
          <Text style={styles.title}></Text>
          <Image style={styles.image} source={require("../../images/processnow.webp")} />
          <Text style={styles.h2}>{t("账号注册中")}</Text>
          <Text style={styles.h3}>{t("Akulaku已将您的信息提交注册，正在受理中，您可稍后重试")}</Text>
        </View>
        <BlockBtn
          onPress={event => {
            SensorManager.sensorClickRegisterConfirm(pageStore.userStatus);
            pageStore.changeModalVisible(false);
          }}
          text={t("我知道了")}
        />
      </>
    );
  }
}

export default SignInProcess;
