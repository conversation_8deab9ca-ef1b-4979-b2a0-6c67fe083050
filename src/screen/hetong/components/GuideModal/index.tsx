import * as React from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ImageBackground,
  BackHandler,
  NativeEventSubscription,
  ScrollView,
  NativeSyntheticEvent,
  NativeScrollEvent
} from "react-native";
import { inject, observer } from "mobx-react";
import styles from "./style";
import SensorManager, { UserStatus } from "../../util/SensorManager";
import { get, noop, debounce } from "lodash";
import { ElectricSignPlatformType } from "../../types";
import { setShowGuideFlag } from "../../util/nativeStorge";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import ESignatureStore from "../../store";
import { MutableRefObject, ReactElement, RefObject } from "react";
import { getCurrentScreen } from "common/constant";
import AkConsole from "common/util/AkConsole/AkConsole";

const imageMap: { [key: string]: Array<number> } = {
  [ElectricSignPlatformType.DIGISIGN]: [
    require("../../images/digisign1.webp"),
    require("../../images/digisign2.webp"),
    require("../../images/digisign3.webp"),
    require("../../images/digisign4.webp")
  ],
  [ElectricSignPlatformType.PRIVY]: [
    require("../../images/privy1.png"),
    require("../../images/privy2.webp"),
    require("../../images/privy3.webp"),
    require("../../images/privy4.webp")
  ]
};

const ITEM_IMAGE_WIDTH = 250;

interface Props {
  onClose: () => void;
  store?: pageStoreModel<ESignatureStore>;
}

interface State {
  txt: string;
  currentIndex: number;
}

@inject("store")
@observer
class GuideModal extends React.Component<Props, State> {
  indexChanged: (index: number) => void;

  swiperIndex: number;

  backListener?: NativeEventSubscription;

  scrollViewRef: RefObject<ScrollView>;

  constructor(props: Props) {
    super(props);
    this.indexChanged = this.onIndexChanged.bind(this);
    this.onBtnTouch = this.onBtnTouch.bind(this);
    this.state = {
      txt: "下一张",
      currentIndex: 0
    };
    this.swiperIndex = 0;
    this.scrollViewRef = React.createRef();
  }

  componentDidMount(): void {
    this.backListener = BackHandler.addEventListener("hardwareBackPress", this.onBack);
    setShowGuideFlag();
  }

  componentWillUnmount(): void {
    this.backListener && this.backListener.remove();
  }

  onBack = () => {
    if (!!this.props.store) {
      this.props.store.pageStore.changeGuideModalVisible(false);
      this.props.onClose && this.props.onClose();
      return true;
    }
  };

  onIndexChanged(index: number) {
    const { store } = this.props;
    if (!store) return;
    const { pageStore } = store;
    if (index === 3) {
      this.setState({
        txt: "确认"
      });
    } else {
      this.setState({
        txt: "下一张"
      });
    }
    this.guideSensor(index, "EXPOSE", pageStore.userStatus);
  }

  guideSensor = (index: number, type: "EXPOSE" | "CLICK", userStatus: UserStatus) => {
    let functionName = "";
    const indexFinal = index + 1;
    if (type === "EXPOSE") {
      functionName = `sensorExposeGuideDialog`;
    } else {
      functionName = `sensorClickGuideNextStep`;
    }
    const params: Array<number | UserStatus> = [indexFinal, userStatus];
    get(SensorManager, `${functionName}`, noop)(...params);
  };

  onBtnTouch() {
    const { store } = this.props;
    if (!store) return;
    const { pageStore } = store;
    const index = this.state.currentIndex + 1;
    if (this.state.currentIndex !== 3) {
      this.setCurrentIndex(index);
      if (this.scrollViewRef && this.scrollViewRef.current) {
        this.scrollViewRef.current.scrollTo({ x: ITEM_IMAGE_WIDTH * index, y: 0, animated: true });
      }
    } else {
      this.props.onClose();
    }
    this.guideSensor(this.state.currentIndex, "CLICK", pageStore.userStatus);
  }

  onDraging = false;

  onScrollEndDrag = () => {
    this.onDraging = false;
  };

  onScrollBeginDrag = () => {
    this.onDraging = true;
  };

  onMomentumScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    AkConsole("hetong", `onScrollEndDrag.nativeEvent: `, event.nativeEvent);
    if (this.onDraging) return;
    const currentIndex = Math.round(event.nativeEvent.contentOffset.x / ITEM_IMAGE_WIDTH);
    AkConsole("hetong", `currentIndex: `, currentIndex);
    this.setCurrentIndex(currentIndex);
  };

  setCurrentIndex = debounce((index: number) => {
    this.setState({
      currentIndex: index
    });
    this.onIndexChanged(index);
  }, 100);

  render() {
    const { txt } = this.state;
    const { store } = this.props;
    if (!store) return;
    const { pageStore } = store;
    const t = pageStore.getT();
    const ds = imageMap[`${pageStore.electricSignPlatform}`] || imageMap.digisign;
    return (
      <ImageBackground source={require("../../images/shadow.webp")} style={styles.container}>
        <View
          style={{
            flex: 1
          }}
        >
          <View style={styles.wrapper}>
            <CusSwiper
              onMomentumScrollEnd={this.onMomentumScrollEnd}
              ref={this.scrollViewRef}
              data={ds}
              currentIndex={this.state.currentIndex}
              onScrollBeginDrag={this.onScrollBeginDrag}
              onScrollEndDrag={this.onScrollEndDrag}
              // onScroll={this.onScroll}
            />
          </View>

          <TouchableOpacity
            onPress={this.onBtnTouch}
            style={{
              alignSelf: "stretch",
              height: 40,
              marginBottom: 16,
              backgroundColor: "#E62117",
              borderRadius: 4,
              justifyContent: "center",
              alignItems: "center"
            }}
          >
            <Text style={styles.btn}>{t(txt)}</Text>
          </TouchableOpacity>
        </View>
      </ImageBackground>
    );
  }
}

type CusSwiperType = {
  data: Array<number>;
  onScrollEndDrag: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  onScrollBeginDrag: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  // onScroll: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  onMomentumScrollEnd: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  currentIndex: number;
};

const CusSwiper = React.forwardRef(function CusSwiperComp(
  { data, onScrollEndDrag, onScrollBeginDrag, currentIndex, onMomentumScrollEnd }: CusSwiperType,
  ref: ((instance: ScrollView | null) => void) | MutableRefObject<ScrollView | null> | null
) {
  return (
    <View>
      <ScrollView
        showsHorizontalScrollIndicator={false}
        ref={ref}
        onScrollEndDrag={onScrollEndDrag}
        onScrollBeginDrag={onScrollBeginDrag}
        onMomentumScrollEnd={onMomentumScrollEnd}
        // onScroll={onScroll}
        horizontal={true}
        pagingEnabled={true}
      >
        {data.map((item: number, index: number) => {
          return (
            <Image
              key={`${index}`}
              style={{
                width: ITEM_IMAGE_WIDTH,
                height: 310
              }}
              source={item}
            />
          );
        })}
      </ScrollView>
      <View style={{ position: "absolute", bottom: 4, width: "100%", alignItems: "center", justifyContent: "center" }}>
        <View style={{ flexDirection: "row" }}>
          {data.map((item, index) => {
            const isCurrent = index === currentIndex;
            return (
              <View
                key={`${index}`}
                style={{
                  height: 4,
                  width: isCurrent ? 12 : 4,
                  borderRadius: 2,
                  backgroundColor: isCurrent ? "#E62117" : "#BBBBBB",
                  marginHorizontal: 1
                }}
              />
            );
          })}
        </View>
      </View>
    </View>
  );
});

export default GuideModal;
