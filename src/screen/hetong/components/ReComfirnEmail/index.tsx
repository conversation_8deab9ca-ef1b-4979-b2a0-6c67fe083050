import * as React from "react";
import { Image, Text, TextInput, TouchableOpacity, View } from "react-native";
import { inject, observer } from "mobx-react";
import BlockBtn from "../BlockBtn";
import styles from "./style";
import { debounce, isEqual, get } from "lodash";
import SensorManager, { UserStatus } from "../../util/SensorManager";
import { ElectricSignPlatformType } from "../../types";
import NativeConfigModule from "common/nativeModules/basics/nativeConfigModule";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import ESignatureStore from "../../store";
import { FontStyles } from "@akulaku-rn/akui-rn";

interface Props {
  onClose?: () => void;
  store?: pageStoreModel<ESignatureStore>;
}

function validateEmail(email: string) {
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(email.toLowerCase());
}

@inject("store")
@observer
class ReConfirmEmail extends React.Component<Props, { value: string; validateStatus: boolean; caretHidden: boolean }> {
  constructor(props: Props) {
    super(props);
    this.state = {
      value: get(props, "store.pageStore.email", ""),
      validateStatus: true,
      caretHidden: false
    };
    this.saveEmail = debounce(this.saveEmail, 300);
  }

  async componentDidMount() {
    this.isFirst = false;
    const {
      data: { deviceBrand, osVersionCode }
    } = await NativeConfigModule.getEnvInfoSilent();
    this.setState({
      caretHidden: deviceBrand === "Xiaomi" && osVersionCode === 29
    });
  }

  isFirst = true;

  inputOnChange = (value: string) => {
    this.setState({
      value,
      validateStatus: validateEmail(value)
    });
  };

  onConfirm = async () => {
    const { store } = this.props;
    if (!store) return;
    const { pageStore } = store;
    const { value } = this.state;
    SensorManager.sensorClickEmailConfirm(UserStatus.USER_UNSIGNED);
    await pageStore.saveEmail(value, false);
    pageStore.onSaveEdit &&
      pageStore.onSaveEdit(async (url: string) => {
        pageStore.changeModalVisible(false);
        pageStore.navigateSignPage(url);
      });
  };

  saveEmail = () => {
    const { store } = this.props;
    if (!store) return;
    const { pageStore } = store;
    const { value } = this.state;
    SensorManager.sensorClickEmailConfirm(UserStatus.USER_UNSIGNED);
    pageStore.saveEmail(value);
  };

  onFocus = () => SensorManager.sensorFocusEmail(UserStatus.USER_UNSIGNED);

  render() {
    const { store } = this.props;
    if (!store) return;
    const { pageStore } = store;
    const email = pageStore.hadRegisteredEmail;
    const { value, validateStatus } = this.state;
    const platform = pageStore.electricSignPlatform;
    const t = pageStore.getT();
    return (
      <>
        <View
          style={{
            paddingHorizontal: 16,
            paddingBottom: 184
          }}
        >
          <Text style={styles.title}>{t("重新确认电子邮箱")}</Text>
          <View style={styles.block}>
            {isEqual(platform, ElectricSignPlatformType.PRIVY) && (
              <View style={styles.list}>
                <Text style={styles.listLeft}>01</Text>
                <Text style={styles.listRight}>{t("用于接受您的电子签名账户信息")}</Text>
              </View>
            )}
            <View style={styles.list}>
              {isEqual(platform, ElectricSignPlatformType.PRIVY) && <Text style={styles.listLeft}>02</Text>}
              <Text style={styles.listRight}>
                {t("您已使用xx在digisign平台注册，无法使用其他邮箱再次注册", { email: pageStore.hadRegisteredEmail })}
              </Text>
            </View>
          </View>
          <Text style={[styles.h2]}>{t("确认邮箱以便完成注册")}</Text>
          <Text style={{ marginVertical: 12, color: "#666666", fontSize: 12 }}>
            {t("请检查以下邮箱地址，确认无误后可重新提交进行电子签名服务")}
          </Text>
          <View style={styles.inputBlock}>
            <Image style={styles.inputIcon} source={require("../../images/email.webp")} />
            <TextInput
              caretHidden={this.state.caretHidden}
              onChangeText={this.inputOnChange}
              value={email}
              onFocus={this.onFocus}
              placeholder={t("请输入邮箱")}
              underlineColorAndroid="transparent"
              style={[styles.textInput, FontStyles["rob-medium"]]}
              maxLength={30}
              editable={false}
            />
          </View>

          <View style={styles.row}>
            {!validateStatus && !this.isFirst && (
              <>
                <Image style={styles.errorIcon} source={require("../../images/error.webp")} />
                <Text style={styles.errormsg}>{t("您输入的邮箱有误")}</Text>
              </>
            )}
          </View>
        </View>
        <BlockBtn disabled={!validateStatus} onPress={this.onConfirm} text={t("邮箱确认")} />
      </>
    );
  }
}

export default ReConfirmEmail;
