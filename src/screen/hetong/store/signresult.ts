import { action, observable } from "mobx";
import Basic from "common/store/Basic";
import { get } from "lodash";
import { API, ElectricSign, RegisterStatus, SignResultPageType } from "../types";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { getI18n } from "react-i18next";
import ESignatureConstants from "../constants";
import PdfUrlHelper from "../util/PdfUrlHelper";
import { UserRouteName } from "../../../split-package-config";

export interface BaseResponse<T> {
  data: T;
  success: boolean;
  errCode: string;
  errMsg: string;
}

export default class SignResultStore extends Basic {
  @observable type: SignResultPageType = SignResultPageType.UN_SIGNED;

  source = "";

  platform = "";

  title = "";

  digsignData = "";

  // isLoading = false;

  @action
  setNavParams = ({
    type,
    source,
    platform,
    title,
    digsignData
  }: {
    type: SignResultPageType;
    source: string;
    platform: string;
    title: string;
    digsignData: string;
  }) => {
    this.type = type;
    this.source = source;
    this.title = title;
    this.digsignData = digsignData;
  };

  @action
  async nextBtnClick() {
    await this.getSignRegistStatus();
    // if (this.type === SignResultPageType.SIGNED) {
    //   await this.improveElectricSign();
    // }
  }

  @action
  async getSignRegistStatus() {
    try {
      const res = await this.coustomHttpRequest(API.ELECTRIC_SIGNATURE_REGIST_STATUS);
      if (res.success && !!res.data) {
        const registStatus = get(res, "data.status", 0);
        if (registStatus === RegisterStatus.REGISTED) {
          await this.improveElectricSign(get(res, "data.registerEmail", ""));
        }
      }
    } catch (e) {
      NativeToast.showMessage(e.errMsg);
    }
  }

  @action
  async improveElectricSign(email: string) {
    try {
      const res = await this.coustomHttpRequest(API.ELECTRIC_SIGNATURE_IMPROVE_SIGN, {
        email,
        source: this.source
      });
      if (res.success && !!res.data) {
        NativeNavigationModule.navigate({
          screen: "SignPage",
          params: {
            source: this.source,
            platform: this.platform,
            title: this.title
          }
        });
      }
    } catch (e) {
      NativeToast.showMessage(e.errMsg);
    }
  }

  getT() {
    return getI18n().getFixedT(null, ESignatureConstants.MODULE_NAME);
  }

  @action
  async uploadDigsignData(callback: Function) {
    try {
      const res = await this.io.post(
        API.ELECTRIC_SIGNATURE_UPLOAD_DIGSIGN_MSG,
        { msg: this.digsignData },
        { retryTimes: 3 }
      );
      if (res.success) {
        this.type = SignResultPageType.SIGNED;
        callback && callback();
      } else {
        NativeNavigationModule.popToScreen(UserRouteName.Hetong);
        NativeToast.showMessage(res.errMsg);
      }
    } catch (e) {
      NativeNavigationModule.popToScreen(UserRouteName.Hetong);
      NativeToast.showMessage(e.errMsg || e.message);
    }
  }

  @action
  async viewContract() {
    const { success, data } = await this.coustomHttpRequest<ElectricSign>(API.ELECTRIC_SIGNATURE_POLICY);
    if (success) {
      if (data.docUrl) {
        NativeNavigationModule.navigate({
          screen: "ContractPdf",
          params: {
            url: await PdfUrlHelper.getUrl(data.docUrl)
          }
        });
      }
    } else {
      NativeNavigationModule.popToScreen(UserRouteName.Hetong);
    }
  }

  async coustomHttpRequest<T>(url: string, params = {}, method = "post"): Promise<BaseResponse<T>> {
    let res;
    try {
      res = await this.io[method](url, params);
    } catch (error) {
      res = {
        success: false
      };
    }
    return res;
  }
}
