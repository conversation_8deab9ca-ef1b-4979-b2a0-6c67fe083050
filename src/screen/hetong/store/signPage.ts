import { action, observable } from "mobx";
import Basic from "common/store/Basic";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { UserStatus } from "../util/SensorManager";
import { API, BaseResponse, ElectricSign } from "../types";
import { getI18n } from "react-i18next";
import ESignatureConstants from "../constants";

export default class SignPageStore extends Basic {
  @observable modalVisible = false;

  signStatus = UserStatus.USER_UNSIGNED;

  async coustomHttpRequest<T>(url: string, params = {}, method = "post"): Promise<BaseResponse<T>> {
    let res;
    try {
      res = await this.io[method](url, params);
    } catch (error) {
      res = {
        success: false
      };
    }
    return res;
  }

  getT() {
    return getI18n().getFixedT(null, ESignatureConstants.MODULE_NAME);
  }

  async getSignStatus() {
    try {
      const res = await this.coustomHttpRequest<ElectricSign>(API.ELECTRIC_SIGNATURE_POLICY);
      const { data } = res;
      return { signStatus: data.signStatus };
    } catch (e) {
      NativeToast.showMessage(e.errMsg);
    }
  }

  @action
  changeModalVisible() {
    this.modalVisible = !this.modalVisible;
  }
}
