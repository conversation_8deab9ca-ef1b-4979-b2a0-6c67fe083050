import { action, observable } from "mobx";
import Basic from "common/store/Basic";
import { debounce, get, isEmpty } from "lodash";
import { Loading as WindowsLoading } from "@akulaku-rn/akui-rn";
import Dialog from "@akulaku-rn/akui-rn/src/components/Dialog";
import { getEmail, needShowGuideFlag } from "../util/nativeStorge";
import SensorManager, { UserStatus } from "../util/SensorManager";
import * as Types from "../types";
import {
  API,
  AuthContractProcessData,
  AuthContractProcessStatus,
  BaseResponse,
  ContractSignStatus,
  DialogHadShow,
  ElectricSign,
  ElectricSignPlatformType,
  ElectricSignRegist,
  HeaderIconType,
  modalType,
  PageRegisterStatus,
  RegisterError,
  RegisterStatus,
  SignResultPageType
} from "../types";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";
import { getI18n } from "react-i18next";
import ESignatureConstants from "../constants";
import { NetworkResponse } from "common/nativeModules/network/nativeNetworkModule";
import AsyncStorage from "@react-native-community/async-storage";
import { COUNTRY_CODE } from "common/components/BaseContainer/Type";
import PdfUrlHelper from "../util/PdfUrlHelper";
import { getCurrentScreen } from "common/constant";

export default class ESignatureStore extends Basic {
  electricSignPlatform: ElectricSignPlatformType | undefined;

  @observable loading = true;

  @observable showError = false;

  @observable toastMsg: string | undefined;

  @observable tipVisible = false;

  @observable headerIcon: HeaderIconType = "default";

  @observable headerSignState = "未签名";

  @observable btnText = "完善签名";

  @observable modalVisible = false;

  @observable windowsLoading = false;

  @observable showGuideModal = false;

  @observable email = "";

  source = 0;

  title: string | undefined;

  @observable
  modalType: modalType = "IncompleteSignature";

  electricSign: ElectricSign | undefined;

  signRegist: ElectricSignRegist | undefined;

  userStatus = UserStatus.USER_UNSIGNED;

  registerInfo?: NetworkResponse<ElectricSignRegist>;

  isFromSignPage = false;

  hadRegisteredEmail = "";

  pageRegisterStatus = PageRegisterStatus.UN_REGISTER;

  getT() {
    return getI18n().getFixedT(null, ESignatureConstants.MODULE_NAME);
  }

  @action("进入页面请求用户注册信息和合同信息")
  async fetchRemoteData(fromEnter: boolean) {
    this.loading = fromEnter;
    this.showError = false;
    this.registerInfo = await this.io.post(
      API.ELECTRIC_SIGNATURE_REGIST_STATUS,
      {},
      { throwNetworkExceptionOnly: true, yapiId: 568 }
    );
    if (!this.registerInfo) return;
    this.signRegist = this.registerInfo.data;

    this.email = get(this.registerInfo, "data.registerEmail", "") || (await getEmail()) || "";
    if (!this.registerInfo.success) {
      // 请求注册信息失败
      this.showError = true;
      this.loading = false;
      return;
    }
    this.getRegisterStatus(this.registerInfo);
    if (this.pageRegisterStatus === PageRegisterStatus.REGISTER_SUCCESS) {
      const { data } = await this.coustomHttpRequest<ElectricSign>(API.ELECTRIC_SIGNATURE_POLICY);
      this.electricSign = data;
      if (
        this.isFromSignPage &&
        !(
          this.electricSign.signStatus === ContractSignStatus.SIGNED ||
          this.electricSign.signStatus === ContractSignStatus.SIGNED_BY_USER
        )
      ) {
        this.changeModalVisible(true, "IncompleteSignature");
        this.isFromSignPage = false;
      }
    } else {
      const hadRegisteredEmail = get(this.registerInfo, "data.hadRegisteredEmail", "");
      const errMsg = get(this.registerInfo, "errMsg", "");
      const rejectReason = get(this.registerInfo, "data.rejectReason", "");
      this.defaultHandlePageRegisterStatus(this.pageRegisterStatus, { hadRegisteredEmail, errMsg, rejectReason });
    }
    // 确定电子签名平台
    this.electricSignPlatform = get(this.registerInfo, "data.thirdPartyType") || ElectricSignPlatformType.DIGISIGN; // 电子签名平台
    this.getUserSignStatus(get(this.electricSign, "signStatus", ContractSignStatus.NO_COMPLETE)); // 获得是否已经签名
    // 判断是否显示tips 与 更新注册信息
    SensorManager.sensorPageEnter(this.userStatus);
    this.updateTips(this.signRegist, this.electricSign);
    this.updatePolicy(this.signRegist, this.electricSign);
    this.loading = false;
    // !fromEnter && WindowsLoading.dismiss();
  }

  getRegisterStatus = (res: NetworkResponse<ElectricSignRegist>) => {
    const registerStatus = get(res, "data.status", RegisterStatus.UNREGISET);
    const errCode = get(res, "data.errCode");
    switch (registerStatus) {
      case RegisterStatus.UNREGISET:
        this.pageRegisterStatus = PageRegisterStatus.UN_REGISTER;
        break;
      case RegisterStatus.REGISTER_BY_OTHER_CHANNEL:
      case RegisterStatus.REGISTED:
        this.pageRegisterStatus = PageRegisterStatus.REGISTER_SUCCESS;
        break;
      case RegisterStatus.REGISTER_FAIL_WAIT_CONFIRM:
        if (errCode === RegisterError.REGISTER_PHONE_EXIST) {
          // 电话号码存在
          this.pageRegisterStatus = PageRegisterStatus.REGISTER_FAIL_PHONE_EXIST;
        } else {
          // 默认是 邮箱待确认， 目前状态6 只有10820040012101 10820040012102  两种错误码
          this.pageRegisterStatus = PageRegisterStatus.REGISTER_FAIL_WAIT_CONFIRM;
        }
        break;
      case RegisterStatus.REGIST_FAIL:
        if (errCode === RegisterError.REGISTER_VERIFY_PHOTO_FAIL) {
          this.pageRegisterStatus = PageRegisterStatus.REGISTER_FAIL_VERIFY_KYC;
        } else {
          this.pageRegisterStatus = PageRegisterStatus.REGISTER_FAIL_OTHER_ERROR;
        }
        break;
      case RegisterStatus.REGISTING:
        this.pageRegisterStatus = PageRegisterStatus.REGISTERING;
        break;
    }
  };

  defaultHandlePageRegisterStatus = async (
    status: PageRegisterStatus,
    ext?: { hadRegisteredEmail?: string; errMsg?: string; rejectReason?: string }
  ) => {
    const rejectReason = get(ext, "rejectReason", "");
    switch (status) {
      case PageRegisterStatus.UN_REGISTER:
        this.openGuideModal();
        break;
      case PageRegisterStatus.REGISTER_FAIL_WAIT_CONFIRM:
        this.hadRegisteredEmail = get(ext, "hadRegisteredEmail", "");
        this.changeModalVisible(true, "ReConfirmEmail");
        break;
      case PageRegisterStatus.REGISTER_FAIL_VERIFY_KYC:
        SensorManager.sensorExposeRegisterFailDialog(rejectReason);
        Dialog.show({
          title: this.getT()("注册失败"),
          desc: this.getT()("您的KYC信息校验未通过，请联系客服处理。"),
          negativeText: this.getT()("确认"),
          containerStyle: { borderRadius: 10 }
        });
        break;
      case PageRegisterStatus.REGISTER_FAIL_PHONE_EXIST:
        SensorManager.sensorExposeRegisterFailDialog(rejectReason);
        Dialog.show({
          title: this.getT()("注册失败"),
          desc: this.getT()("您的手机号xxx在digisign平台已注册，请联系digisign平台进行更改"),
          negativeText: this.getT()("确认"),
          containerStyle: { borderRadius: 10 }
        });
        break;
      case PageRegisterStatus.REGISTER_FAIL_OTHER_ERROR:
        const errMsg = get(ext, "errMsg", "");
        !!errMsg && NativeToast.showMessage(errMsg);
        break;
      case PageRegisterStatus.REGISTERING:
      case PageRegisterStatus.REGISTER_SUCCESS:
      default:
        break;
    }
  };

  /**
   * 打开引导框
   */
  @action("打开引导框")
  async openGuideModal() {
    const showGuide = await needShowGuideFlag();
    if (!showGuide) {
      this.showGuideModal = true;
      // SensorManager.sensorExposeGuideDialog1(UserStatus.USER_UNSIGNED);
      SensorManager.sensorExposeGuideDialog(1, UserStatus.USER_UNSIGNED);
    }
  }

  /**
   * 关闭引导框
   * @param flag
   */
  @action("更改引导弹窗的显示")
  changeGuideModalVisible(flag: boolean): void {
    this.showGuideModal = flag;
  }

  timer?: number;

  @action
  async improveElectricSignFetchCore(): Promise<NetworkResponse<ElectricSign | null>> {
    let result;
    // 第一次请求完善合同接口
    try {
      const res: NetworkResponse<ElectricSign> = await this.io.post(
        API.ELECTRIC_SIGNATURE_POLICY,
        {},
        {
          yapiId: 568
        }
      );
      result = res;
      // 如果res 成功， 状态 0 / 10 => 轮询check  90 toast 99 直接跳转
      if (res.success && !!res.data) {
        switch (res.data.signStatus) {
          // 未完善需要请求接口
          case AuthContractProcessStatus.NO_COMPLETE:
          case AuthContractProcessStatus.PROCESSING:
            const resInner: NetworkResponse<ElectricSign | null> = await new Promise(resolve => {
              if (getCurrentScreen() === ESignatureConstants.SCREEN_NAME) {
                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(async () => {
                  const resTimeout = await this.improveElectricSignFetchCore();
                  resolve(resTimeout);
                }, ESignatureConstants.LOOP_TIME);
              }
            });
            result = resInner;
            break;
          case AuthContractProcessStatus.RE_COMPLETE:
            NativeToast.showMessage(this.getT()("网络延迟，请重新尝试。"), { duration: 4 });
            break;
        }
      } else {
        NativeToast.showMessage(get(res, "errMsg", "") || "improveElectricSignFetch error");
      }
    } catch (e) {
      NativeToast.showMessage(get(e, "errMsg", "") || get(e, "message") || "improveElectricSignFetch error");
      result = {
        success: false,
        data: null,
        errCode: "",
        errMsg: "",
        sysTime: new Date()
      };
    }
    return result;
  }

  getAsyncStorageKey = () => `${API.ELECTRIC_SIGNATURE_IMPROVE_SIGN}_retryNextBeginTime`;

  saveAuthContractProcessTime = debounce(
    (time: number) => {
      AsyncStorage.setItem(this.getAsyncStorageKey(), `${time}`, error => {
        if (error) {
          console.log(`error: `, error);
        }
      });
    },
    500,
    { leading: true, trailing: false }
  );

  async getAuthContractProcessTime() {
    return await AsyncStorage.getItem(this.getAsyncStorageKey(), (error, result) => {
      if (error) {
        console.warn(`getAuthContractProcessTime error: `, error);
      }
    });
  }

  // 调用完善合同接口， 判断条件， 是否能够继续
  async processCondition(res: NetworkResponse<AuthContractProcessData | null>, checkTime: boolean) {
    if (!!get(res, "data.overLimitFlag", null)) {
      NativeToast.showMessage(this.getT()("您重试的次数已到上限，请联系在线客服反馈。"), { duration: 4 });
      return false;
    }
    if (!res.data) return false;
    if (!!res.data.retryNextBeginTime && checkTime) {
      const nextRetryTime = await this.getAuthContractProcessTime();
      if (`${nextRetryTime}` === `${res.data.retryNextBeginTime}`) {
        const getNextTime = (curTimeStamps: number, nextTimeStamps: number) => {
          const timeDiff = +nextTimeStamps - +curTimeStamps;
          const timeSeconds = (timeDiff / 1000) | 0;
          const second = timeSeconds % 60;
          const minute = (timeSeconds / 60) | 0;
          return `${minute}:${second}`;
        };
        NativeToast.showMessage(
          this.getT()("您的电子合同正在签署流程中，请于 xxx 后再次尝试。", {
            time: getNextTime(new Date().getTime(), res.data.retryNextBeginTime)
          }),
          { duration: 4 }
        );
        return false;
      }
      !!res.data.retryNextBeginTime && this.saveAuthContractProcessTime(res.data.retryNextBeginTime);
    }
    return true;
  }

  @action("完善签名")
  async improveElectricSign(): Promise<{ success: boolean; url?: string }> {
    console.log("improveElectricSign");
    return await new Promise(async resolve => {
      const res: NetworkResponse<AuthContractProcessData> = await this.io.post(
        API.ELECTRIC_SIGNATURE_IMPROVE_SIGN,
        {
          email: this.email,
          source: this.source
        },
        {},
        {
          yapiId: 568
        }
      );
      // 请求失败
      if (!res.success) {
        !!res.errMsg && NativeToast.showMessage(res.errMsg);
        resolve({ success: false });
        return;
      }
      switch (res.data.status) {
        case AuthContractProcessStatus.SIGNING:
          // 判断是否限制时间、是否超出次数
          if (!(await this.processCondition(res, true))) {
            return resolve({ success: false });
          }
          return resolve({ success: true, url: res.data.redirect });
        case AuthContractProcessStatus.PROCESSING:
        case AuthContractProcessStatus.NO_COMPLETE:
          const fetchCoreRes = await this.improveElectricSignFetchCore();
          if (this.timer) {
            // @ts-ignore
            clearTimeout(this.timer);
          }
          return resolve({ success: fetchCoreRes.success, url: get(fetchCoreRes, "data.redirect", "") });
        case AuthContractProcessStatus.RE_COMPLETE:
          // 可重试失败， Toast提示用户
          if (!(await this.processCondition(res, false))) {
            return resolve({ success: false });
          }
          NativeToast.showMessage(this.getT()("网络延迟，请重新尝试。"), { duration: 4 });
          return resolve({ success: false });
        case AuthContractProcessStatus.FETCH_ERROR:
        default:
          return resolve({ success: false });
      }
    });
  }

  /**
   * 判断签名是否通过，isFromSignPage的作用是，第一次点击下一步已经签名了，回到首页后
   * 再次点击下一步，此刻可能还没有注册成功，这时候打开未完成签名的弹窗，
   * iOS没有这个逻辑
   */
  @action("点击按钮请求用户注册信息和合同信息")
  async checkBeforeImprove(): Promise<{
    success: boolean;
    electricSign?: ElectricSign;
    signRegist?: ElectricSignRegist;
  }> {
    try {
      const registerInfo = await this.io.post(
        API.ELECTRIC_SIGNATURE_REGIST_STATUS,
        {},
        { yapiId: 568, throwNetworkExceptionOnly: true }
      );
      this.signRegist = registerInfo.data;
      // 注册失败会切平台， 如果2次失败会返回allFailed， 代表不能再走注册流程
      if (get(registerInfo, "data.allFailed", false)) {
        NativeToast.showMessage(get(registerInfo, "errMsg", this.getT()("很抱歉，注册失败")));
        return { success: false };
      }
      let contractInfo: BaseResponse<ElectricSign>;
      this.getRegisterStatus(registerInfo);
      // 判断注册是否是有问题的， 注意： 注册失败
      if (
        [
          PageRegisterStatus.REGISTER_FAIL_WAIT_CONFIRM,
          PageRegisterStatus.REGISTER_FAIL_PHONE_EXIST,
          PageRegisterStatus.REGISTER_FAIL_VERIFY_KYC
        ].includes(this.pageRegisterStatus)
      ) {
        // 邮箱待确认、电话号码存在、KYC不通过
        const hadRegisteredEmail = get(registerInfo, "data.hadRegisteredEmail", "");
        const errMsg = get(registerInfo, "errMsg", "");
        const rejectReason = get(registerInfo, "data.rejectReason", "");
        // 有问题进行错误处理
        this.defaultHandlePageRegisterStatus(this.pageRegisterStatus, { hadRegisteredEmail, errMsg, rejectReason });
        return { success: false };
      } else {
        // 未注册、注册中、注册成功走这里
        if (this.pageRegisterStatus === PageRegisterStatus.REGISTER_SUCCESS) {
          contractInfo = await this.io.post(API.ELECTRIC_SIGNATURE_POLICY, {}, { throwNetworkExceptionOnly: true });
          if (!(contractInfo && contractInfo.success)) {
            NativeToast.showMessage(contractInfo && contractInfo.errMsg);
            return { success: false };
          }
          this.electricSign = contractInfo.data;
          this.getUserSignStatus(get(this.electricSign, "signStatus", ContractSignStatus.NO_COMPLETE)); // 获得是否已经签名
        }
        return {
          success: true,
          electricSign: this.electricSign,
          signRegist: this.signRegist
        };
      }
    } catch (e) {
      const message = e.errMsg || e.message;
      !!message && NativeToast.showMessage(message);
      return {
        success: false
      };
    }
  }

  @action
  updateSourceAndTitle(source: number, title?: string) {
    this.source = source;
    this.title = title;
  }

  getTitle = () => {
    if (!this.title) return this.getT()("借款合同");
    return this.title;
  };

  countryCode: string = COUNTRY_CODE.ID;

  setCountryCode(setCountyCode: COUNTRY_CODE) {
    this.countryCode = setCountyCode;
  }

  async coustomHttpRequest<T>(url: string, params = {}, method = "postJson"): Promise<BaseResponse<T>> {
    let res;
    try {
      res = await this.io[method](url, params);
    } catch (error) {
      res = {
        ...error,
        success: false
      };
    }
    return res;
  }

  @action("更改提示引导显示")
  changeTipVisible(flag: boolean): void {
    this.tipVisible = flag;
  }

  @action("更改弹窗显示和弹窗类型")
  changeModalVisible(flag: boolean, type?: modalType): void {
    this.modalVisible = flag;
    if (type) {
      this.modalType = type;
    }
  }

  @action("更新提示")
  updateTips(regist: ElectricSignRegist, policy?: ElectricSign): void {
    this.tipVisible = !(
      !isEmpty(regist) &&
      regist.status === RegisterStatus.REGISTED &&
      !isEmpty(policy) &&
      policy &&
      policy.signStatus
    );
  }

  @action
  updatePolicy(regist: ElectricSignRegist, policy?: ElectricSign): void {
    if (isEmpty(regist)) {
      // 注册信息为空
      this.headerIcon = "notSign";
      this.headerSignState = "未签名";
      this.btnText = "完善签名";
      return;
    }
    if (regist.status === RegisterStatus.REGIST_FAIL) {
      // 注册失败
      this.headerIcon = "notSign";
      this.headerSignState = "注册失败";
      this.btnText = "完善签名";
      return;
    }

    if (regist.status !== RegisterStatus.REGISTED) {
      // 不是签名成功
      this.headerIcon = "notSign";
      this.headerSignState = "未签名";
      this.btnText = "完善签名";
      return;
    }

    if (
      isEmpty(policy) ||
      !(
        (policy && policy.signStatus === ContractSignStatus.SIGNED) ||
        (policy && policy.signStatus === ContractSignStatus.SIGNED_BY_USER)
      )
    ) {
      // 协议信息为空 或者 协议签名信息为空
      this.headerIcon = "notCompleteSign";
      this.headerSignState = "签名未完成";
      this.btnText = "完善签名";
      return;
    }

    this.headerIcon = "electricSigned";
    this.headerSignState = "已签名";
    this.btnText = "查看签名";
  }

  @action
  async saveEmail(email: string, jump2Signature = true) {
    this.email = email;
    this.changeModalVisible(true, "Signature");
  }

  @action
  onEditEmail() {
    this.modalVisible = false;
    this.changeModalVisible(true, "ConfirmEmail");
  }

  @action
  updateEmail(email: string) {
    if (email) {
      this.email = email;
    }
  }

  @action
  async onSaveEdit(cb: Function) {
    this.changeModalVisible(false);
    WindowsLoading.show();
    const errorHandler = (res: Error & NetworkResponse) => {
      if (res.errMsg) NativeToast.showMessage(res.errMsg);
    };
    try {
      const res = await this.io.post(API.ELECTRIC_SIGNATURE_REGIST, {
        email: this.hadRegisteredEmail || this.email,
        source: this.source || 10
      });
      if (!res.success) {
        errorHandler(res);
        WindowsLoading.dismiss();
        return;
      }
      if (res.data.status === RegisterStatus.REGISTING) {
        WindowsLoading.dismiss();
        this.navigateResultPage(SignResultPageType.REEGEISTERING);
        return;
      }

      if (res.data.status === RegisterStatus.REGISTED) {
        const { url, success } = await this.improveElectricSign();
        WindowsLoading.dismiss();
        if (success) {
          cb(url);
          return;
        }
      }
    } catch (error) {
      errorHandler(error);
    } finally {
      WindowsLoading.dismiss();
    }
  }

  getUserSignStatus = (signStatus: ContractSignStatus) => {
    if (signStatus === ContractSignStatus.SIGNED || signStatus === ContractSignStatus.SIGNED_BY_USER) {
      this.userStatus = UserStatus.USER_SIGNED;
    } else {
      this.userStatus = UserStatus.USER_UNSIGNED;
    }
  };

  navigateResultPage(type: Types.SignResultPageType) {
    NativeNavigationModule.navigate({
      screen: "SignResult",
      params: {
        type,
        source: this.source,
        platform: this.electricSignPlatform,
        title: this.getTitle()
      }
    });
  }

  navigateSignPage(url: string) {
    NativeNavigationModule.navigate({
      screen: "SignPage",
      params: {
        source: this.source,
        platform: this.electricSignPlatform,
        title: this.getTitle(),
        url
      }
    });
  }

  async navigateContractPdfPage(url: string) {
    const encodeUrl = await PdfUrlHelper.getUrl(url);
    NativeNavigationModule.navigate({
      screen: "ContractPdf",
      params: {
        url: encodeUrl
      }
    });
  }
}
