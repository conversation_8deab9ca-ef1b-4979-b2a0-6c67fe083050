import { errCode } from "./../../increaseLimit/main/constents/index";
import BasePageStore from "@akulaku-rn/akulaku-ec-common/src/store/BasePageStore";
import { action, observable } from "mobx";
import { FieldErrorsData, ReportFraudTicket, ReportTypeEnum } from "../constants";
import AsyncStorage from "@react-native-community/async-storage";
import { NativeNavigationModule } from "@akulaku-rn/akulaku-ec-common/src/nativeModules";
import NativeToast from "@akulaku-rn/akulaku-ec-common/src/nativeModules/basics/nativeUIModule/Toast";
import { ReportFraudTicketSensor } from "../utils/sensor";

const url = "/capi/personal/user/faq-center/fraud-case/create"; //反馈中心- 欺诈工单反馈

export default class Store extends BasePageStore {
  @observable isSuccess = false;

  @observable fieldErrors?: FieldErrorsData[];

  @action("反馈中心- 欺诈工单反馈")
  createFraudCase = async (
    fraudType: ReportTypeEnum,
    fraudPhoneLists: string[],
    fraudLink: string,
    fraudDetail: string,
    imageUrls: string[],
    identityNo: string,
    phoneNumber: string,
    email: string,
    callback?: (isSuccess: boolean) => void
  ) => {
    this.isLoading = true;
    try {
      const params = {
        fraudType,
        fraudPhoneLists,
        fraudLink,
        fraudDetail,
        imageUrls,
        identityNo,
        phoneNumber,
        email
      };
      const response = await this.io.post(url, params);
      if (response && response.success) {
        this.isSuccess = response.data.isSuccess;
        this.fieldErrors = response.data.fieldErrors;
        this.isLoading = false;
        callback && callback(response.data.isSuccess);
      } else {
        callback && callback(false);
        if (!!response.data) {
          response.data.fieldErrors.length !== 0 && NativeToast.showMessage(response.data.fieldErrors[0].message);
        } else {
          NativeToast.showMessage(response.errMsg);
        }
      }
      ReportFraudTicketSensor.onClickSunmbit(response.errCode ?? "");
    } catch (error) {
      callback && callback(false);
    } finally {
      this.isLoading = false;
    }
  };

  fetchIsTodaySubmitted = async () => {
    // 查看今天是否提交成功过，只能提交一次
    try {
      let newData;
      const data = await AsyncStorage.getItem(ReportFraudTicket);
      if (!!data) newData = JSON.parse(data);
      // 提交时间超过一天就可以重新提交表单，不需要跳转结果页
      if (Math.abs(Date.now() - newData.time) < 86400000) {
        if (newData.isSubmit) {
          setTimeout(() => {
            NativeNavigationModule.navigate({ screen: "ReportFraudTicketResult", params: { gestureEnabled: false } });
          }, 400);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };
}
