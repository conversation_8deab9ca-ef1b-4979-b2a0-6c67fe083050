import { AkuTextComponent, AkuTextComponentType, FigmaStyle, PopUpContainer } from "@akulaku-rn/akui-rn";
import BottomButton, { BottomSuctionBarType } from "@akulaku-rn/akui-rn/src/components/BottomButton";
import { TFunction } from "i18next";
import React, { useState } from "react";
import { ScrollView, TouchableOpacity, View, StyleSheet } from "react-native";
import Image from "@akulaku-rn/akulaku-ec-common/src/components/ScriptUrlImage";

type Props = {
  t: TFunction;
  onSelectType: (index: number) => void;
  initialIndex: number;
};

const SelectTypeItems = React.memo(function SelectTypeItemsComp({ t, onSelectType, initialIndex }: Props) {
  const [selectedIndex, setSelectedIndex] = useState(initialIndex ?? -1);

  const onClickButton = () => {
    onSelectType && onSelectType(selectedIndex + 1);
    PopUpContainer.dismiss();
  };

  const onPressItem = (index: number) => {
    setSelectedIndex(index);
  };

  const typeData = [t("冒充客服"), t("冒充政府人员"), t("返现刷单"), t("账户提额")];

  return (
    <View style={{ flex: 1 }}>
      <ScrollView style={{ flex: 1 }}>
        {typeData.map((item, index) => {
          return (
            <TouchableOpacity
              key={index}
              style={styles.itemContainer}
              onPress={() => {
                onPressItem(index);
              }}
            >
              <AkuTextComponent type={AkuTextComponentType.Aku_font_14_medium} text={item} />
              <Image
                source={selectedIndex === index ? require("../img/select.webp") : require("../img/notSelect.webp")}
                style={styles.select}
              />
            </TouchableOpacity>
          );
        })}
      </ScrollView>
      <BottomButton type={BottomSuctionBarType.H8_1_1} text={t("提交")} onPress={onClickButton} />
    </View>
  );
});

export default SelectTypeItems;

const styles = StyleSheet.create({
  itemContainer: {
    marginLeft: 16,
    paddingRight: 16,
    paddingVertical: 20,
    borderBottomWidth: 0.5,
    borderBottomColor: FigmaStyle.Color.Grey_Divider,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  select: {
    width: 24,
    height: 24
  }
});
