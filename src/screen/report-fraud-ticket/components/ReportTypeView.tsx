import {
  AkuTextComponent,
  AkuTextComponentType,
  FigmaStyle,
  PopUpContainer,
  PopUpContainerType
} from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";
import React, { useState } from "react";
import { View, TouchableOpacity, StyleSheet } from "react-native";
import SelectTypeItems from "./SelectTypeItems";
import { ReportTypeEnum } from "../constants";
import Image from "@akulaku-rn/akulaku-ec-common/src/components/ScriptUrlImage";

type Props = {
  t: TFunction;
  onSelectTypeIndex: (index: number) => void;
  initialIndex: number;
  text: string;
};

const ReportTypeView = React.memo(function UploadPhotoComp({ t, onSelectTypeIndex, initialIndex, text }: Props) {
  const [selectTypeText, setSelectTypeText] = useState(text);

  const onSelectType = (index: number) => {
    onSelectTypeIndex && onSelectTypeIndex(index);

    switch (index) {
      case ReportTypeEnum.Fake_Customer_Sevice:
        setSelectTypeText(t("冒充客服"));
        break;
      case ReportTypeEnum.Impersonate_Government_Officials:
        setSelectTypeText(t("冒充政府人员"));
        break;
      case ReportTypeEnum.Brushing_Scam:
        setSelectTypeText(t("返现刷单"));
        break;
      case ReportTypeEnum.Limit_Upgrade:
        setSelectTypeText(t("账户提额"));
        break;
    }
  };

  const onPress = () => {
    PopUpContainer.show({
      type: PopUpContainerType.D4_1_1,
      headerProps: {
        title: t("举报类型")
      },
      renderContent: (
        <SelectTypeItems
          t={t}
          onSelectType={(index: number) => {
            onSelectType(index);
          }}
          initialIndex={initialIndex}
        />
      )
    });
  };

  const renderSelectedTypeView = () => {
    //选中举报类型原因视图
    return (
      <View>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <Image source={require("../img/star.webp")} style={styles.star} />
          <AkuTextComponent
            type={AkuTextComponentType.Aku_font_12_regular}
            text={t("举报类型")}
            style={{ color: FigmaStyle.Color.Grey_Text3 }}
          />
        </View>
        <AkuTextComponent type={AkuTextComponentType.Aku_font_16_regular} text={selectTypeText} />
      </View>
    );
  };

  const renderNoSelectTypeView = () => {
    //未选中举报类型原因视图
    return (
      <View style={{ flexDirection: "row" }}>
        <Image source={require("../img/star.webp")} style={styles.star} />
        <AkuTextComponent type={AkuTextComponentType.Aku_font_16_regular} text={t("举报类型")} />
      </View>
    );
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      {!!selectTypeText ? renderSelectedTypeView() : renderNoSelectTypeView()}
      <Image source={require("../img/expand.webp")} style={styles.expand} />
    </TouchableOpacity>
  );
});

export default ReportTypeView;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderColor: FigmaStyle.Color.Grey_Divider,
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 16
  },
  star: {
    width: 8,
    height: 8,
    marginRight: 2
  },
  expand: {
    width: 16,
    height: 16
  }
});
