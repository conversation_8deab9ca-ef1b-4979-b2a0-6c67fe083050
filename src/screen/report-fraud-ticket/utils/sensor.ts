import NativeSensorModule, { SensorType } from "@akulaku-rn/akulaku-ec-common/src/nativeModules/sdk/nativeSensroModule";

export class ReportFraudTicketSensor {
  // 欺诈举报页面-完成提交点击
  static onClickSunmbit(errorCode?: string): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1567",
      page_name: "report fraud page",
      element_id: "15670101",
      element_name: "submit",
      module_name: "fraud",
      module_id: "01",
      position_id: "01",
      extra: {
        Aku_errorCode: errorCode ?? ""
      }
    });
  }

  // 提交成功页面-返回个人中心
  static onClickBack(): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1566",
      page_name: "report fraud result page ",
      element_id: "15660101",
      element_name: "back to",
      module_name: "fraud",
      module_id: "01",
      position_id: "01"
    });
  }
}
