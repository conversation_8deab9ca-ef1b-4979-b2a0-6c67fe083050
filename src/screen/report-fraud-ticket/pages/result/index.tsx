import {
  AKButton,
  AkuTextComponent,
  AkuTextComponentType,
  Android,
  FigmaStyle,
  NavigationBar,
  WINDOW_WIDTH,
  iOS
} from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { AkTextInputType } from "@akulaku-rn/akui-rn/src/components/AKTextInput";
import RootView from "@akulaku-rn/akulaku-ec-common/src/components/Layout/RootView";
import { GlobalRuntime } from "@akulaku-rn/akulaku-ec-common/src/constant";
import { NativeNavigationModule } from "@akulaku-rn/akulaku-ec-common/src/nativeModules";
import { withTranslation } from "@akulaku-rn/akulaku-ec-common/src/services/i18n";
import { observer } from "mobx-react";
import React, { Component } from "react";
import { TFunction } from "react-i18next";
import { View, Image, Text, NativeEventSubscription, BackHandler } from "react-native";
import styles from "./style";
import { ReportFraudTicketSensor } from "../../utils/sensor";
import AsyncStorage from "@react-native-community/async-storage";
import { ReportFraudTicket } from "../../constants";

type Props = {
  t: TFunction;
};

type States = {
  isBackInitial: boolean;
};

@RootView({
  withI18n: [
    "ReportFraudTicketResult",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json")
    }
  ],
  keyApi: []
})
@withTranslation("ReportFraudTicketResult")
@observer
export default class ReportFraudTicketResult extends Component<Props, States> {
  backHandlerListener?: NativeEventSubscription;

  constructor(props: Props) {
    super(props);
    this.state = {
      isBackInitial: false
    };
  }

  onBackPress = () => {
    if (this.state.isBackInitial) NativeNavigationModule.popTo(2);
    return true;
  };

  async componentDidMount(): Promise<void> {
    try {
      let newData;
      const data = await AsyncStorage.getItem(ReportFraudTicket);
      if (!!data) newData = JSON.parse(data);
      if (newData.isSubmit) {
        this.setState({ isBackInitial: true });
      }
    } catch (error) {
      console.log(error);
    }
    if (Android) {
      this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
    }
  }

  componentWillUnmount(): void {
    if (Android) {
      this.backHandlerListener && this.backHandlerListener.remove();
    }
  }

  onPress = () => {
    NativeNavigationModule.popToHome(iOS ? 4 : 3);
    ReportFraudTicketSensor.onClickBack();
  };

  renderTopView = () => {
    return (
      <View style={styles.topContainer}>
        <Image source={require("../../img/success.webp")} style={styles.successIcon} />
        <AkuTextComponent type={AkuTextComponentType.Aku_font_16_medium} text={this.props.t("成功")} />
        <AkuTextComponent
          type={AkuTextComponentType.Aku_font_14_regular}
          text={this.props.t("您的举报已提交成功，客服会在7个工作日内处理")}
          style={styles.successText}
        />
        <AKButton type={AKButtonType.B1_1_2} text={this.props.t("返回个人中心")} onPress={this.onPress} />
      </View>
    );
  };

  onClickText = () => {
    if (!GlobalRuntime.isLogin) {
      NativeNavigationModule.navigate({
        url: "ak://m.akulaku.com/36"
      });
      return;
    } else {
      NativeNavigationModule.navigate({
        url: "ak://m.akulaku.com/1203?shopName=CS AKULAKU&shopId=1&orgId=1&orgAccountType=3"
      });
    }
  };

  renderBottomView = () => {
    return (
      <View style={styles.bottomContainer}>
        <Text style={styles.tips}>
          {this.props.t("小提示：如果你没有被联系，可在【个人中心-客服live chat】咨询进度噢")}
          <Text style={{ color: FigmaStyle.Color.Deeporange_5 }} onPress={this.onClickText}>
            {this.props.t("个人中心-客服live chat")}
          </Text>
        </Text>
        <Image source={require("../../img/tips.webp")} style={styles.image} />
      </View>
    );
  };

  renderSuccessView = () => {
    return (
      <View style={{ flex: 1 }}>
        {this.renderTopView()}
        {this.renderBottomView()}
      </View>
    );
  };

  render(): JSX.Element {
    return (
      <View style={styles.container}>
        <NavigationBar title={this.props.t("举报被欺诈")} onBackPress={this.onBackPress} />
        {this.renderSuccessView()}
      </View>
    );
  }
}
