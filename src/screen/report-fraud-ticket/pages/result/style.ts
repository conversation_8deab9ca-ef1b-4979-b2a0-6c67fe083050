import { FigmaStyle, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff"
  },
  topContainer: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    alignItems: "center"
  },
  successIcon: {
    width: 50,
    height: 50,
    marginBottom: 16
  },
  successText: {
    color: FigmaStyle.Color.Grey_Text3,
    marginTop: 8,
    marginBottom: 16
  },
  bottomContainer: {
    marginHorizontal: 16,
    padding: 12,
    backgroundColor: FigmaStyle.Color.Grey_Boxbg,
    borderRadius: 8
  },
  tips: {
    fontSize: 14,
    color: FigmaStyle.Color.Grey_Text1
  },
  image: {
    width: WINDOW_WIDTH - 56,
    height: ((WINDOW_WIDTH - 56) * 86) / 304,
    marginTop: 12
  }
});
