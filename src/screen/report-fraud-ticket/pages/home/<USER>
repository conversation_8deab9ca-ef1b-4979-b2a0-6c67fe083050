import BaseContainer, { Type } from "@akulaku-rn/akulaku-ec-common/src/components/BaseContainer";
import RootView from "@akulaku-rn/akulaku-ec-common/src/components/Layout/RootView";
import { withTranslation } from "@akulaku-rn/akulaku-ec-common/src/services/i18n";
import { inject, observer } from "mobx-react";
import store from "../../store/index";
import { ScrollView, View, EmitterSubscription, DeviceEventEmitter } from "react-native";
import React from "react";
import { AkuTextComponent, AkuTextComponentType, FigmaStyle } from "@akulaku-rn/akui-rn";
import AKTextInput, { AkTextInputType } from "@akulaku-rn/akui-rn/src/components/AKTextInput";
import BottomButton, { BottomSuctionBarType } from "@akulaku-rn/akui-rn/src/components/BottomButton";
import ReportTypeView from "../../components/ReportTypeView";
import styles from "./style";
import { NativeNavigationModule } from "@akulaku-rn/akulaku-ec-common/src/nativeModules";
import { GlobalRuntime } from "@akulaku-rn/akulaku-ec-common/src/constant";
import NativeToast from "@akulaku-rn/akulaku-ec-common/src/nativeModules/basics/nativeUIModule/Toast";
import { ReportFraudTicketSensor } from "../../utils/sensor";
import AsyncStorage from "@react-native-community/async-storage";
import { ReportFraudTicket, ReportTypeEnum } from "../../constants";
import { UpdateImageTypeEnum } from "@akulaku-rn/akulaku-ec-common/src/nativeModules/network/nativeNetworkModule";
import ImageUpload from "@akulaku-rn/akulaku-ec-common/src/screen/ImageUpload";
import Image from "@akulaku-rn/akulaku-ec-common/src/components/ScriptUrlImage";
import {
  PreviewUriListItem,
  SubmitEvaluationImgItem
} from "@akulaku-rn/akulaku-ec-common/src/screen/ImageUpload/types/type";
import { changePreViewUriListItem2SubmitEvaluationImgItem } from "@akulaku-rn/akulaku-ec-common/src/screen/ImageUpload/types";
import { CameraImageVideoType } from "@akulaku-rn/akulaku-ec-common/src/screen/ImageUpload/constants";

@RootView({
  withI18n: [
    "ReportFraudTicketHome",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json")
    }
  ],
  store,
  keyApi: []
})
@withTranslation("ReportFraudTicketHome")
@inject("store")
@observer
export default class ReportFraudTicketHome extends BaseContainer<store> {
  listener?: EmitterSubscription;

  state = {
    selectTypeIndex: -1,
    fraudPhoneLists: "",
    fraudLink: "",
    fraudDetail: "",
    identityNo: "",
    phoneNumber: "",
    email: "",
    uriList: []
  };

  constructor(props: Type.Props<store>) {
    super(props);
    this.props.store.pageStore.isLoading = false;
    this.props.configSensorEvent({
      page_id: "1567",
      page_name: "report fraud page"
    });
  }

  navigationBarProps = () => {
    const {
      store: { pageStore }
    } = this.props;
    return {
      title: this.props.t("举报被欺诈"),
      containerStyle: pageStore.isError ? { backgroundColor: "#fff" } : { backgroundColor: "#D5F1FD" }
    };
  };

  componentDidMount(): void {
    const {
      store: { pageStore }
    } = this.props;
    pageStore.fetchIsTodaySubmitted();
    this.listener = DeviceEventEmitter.addListener(
      "PreviewPageOnConfirm",
      (params: { uriList: Array<PreviewUriListItem> }) => {
        const addUriList = changePreViewUriListItem2SubmitEvaluationImgItem(params.uriList);
        this.setState({ uriList: (this.state.uriList as SubmitEvaluationImgItem[]).concat(addUriList) });
      }
    );
  }

  componentWillUnmount(): void {
    this.listener && this.listener.remove();
  }

  errorComponentOnRetryPress = () => {
    const {
      store: { pageStore }
    } = this.props;
    const { fraudLink, fraudDetail, identityNo, phoneNumber, email, selectTypeIndex, fraudPhoneLists } = this.state;
    pageStore.createFraudCase(
      selectTypeIndex,
      [fraudPhoneLists],
      fraudLink,
      fraudDetail,
      this.returnUrlLists(),
      identityNo,
      phoneNumber,
      email
    );
  };

  _renderTopView = () => {
    const { t } = this.props;
    return (
      <View style={styles.topContainer}>
        <AkuTextComponent
          type={AkuTextComponentType.Aku_font_16_medium}
          text={t("遇到可疑情况？我们在这里帮您！")}
          style={styles.topTitle}
        />
        <AkuTextComponent
          type={AkuTextComponentType.Aku_font_12_regular}
          text={t("如发现任何欺诈行为，请在下方简要描述，我们尽快联系您确认和处理，保障您的账户和财产安全。")}
          style={styles.topText}
        />
      </View>
    );
  };

  _onImageChange = (list: Array<SubmitEvaluationImgItem>) => {
    this.setState({ uriList: list });
  };

  _renderEvidenceScreenShots = () => {
    const { t } = this.props;
    return (
      <View style={styles.evidenceView}>
        <View style={{ flexDirection: "row" }}>
          <Image source={require("../../img/star.webp")} style={styles.star} />
          <AkuTextComponent
            type={AkuTextComponentType.Aku_font_16_bold}
            text={t("证据截图")}
            style={{ marginBottom: 16 }}
          />
        </View>
        <ImageUpload
          t={this.props.t}
          listenerId={""}
          uriList={this.state.uriList}
          limitCount={5}
          countryId={GlobalRuntime.countryId}
          selectType={"multiple"}
          onChange={this._onImageChange}
          updateImageType={UpdateImageTypeEnum.PUBLIC_FEEDBACK}
          cameraImageVideoType={CameraImageVideoType.PHOTO}
        />
        <AkuTextComponent
          type={AkuTextComponentType.Aku_font_12_regular}
          text={t("请提供聊天记录截图、诈骗网站截图等页面")}
          style={{ color: FigmaStyle.Color.Grey_Text3, marginTop: 8 }}
        />
      </View>
    );
  };

  checkPhoneNumber = (v: string) => {
    const pattern = /^\d{12}$/;
    if (!pattern.test(v)) {
      return true;
    } else {
      return false;
    }
  };

  checkEmail = (v: string) => {
    const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!pattern.test(v)) {
      return true;
    } else {
      return false;
    }
  };

  checkKTPNumber = (v: string) => {
    const pattern = /^((1[1-9])|(21)|([37][1-6])|(5[1-3])|(6[1-5])|([8-9][1-2]))[0-9]{2}[0-9]{2}(([0-6][0-9])|(7[0-1]))((0[1-9])|(1[0-2]))([0-9]{2})[0-9]{4}$/;
    if (!pattern.test(v)) {
      return true;
    } else {
      return false;
    }
  };

  _renderUserInfo = () => {
    const { t } = this.props;
    return (
      <View>
        <AkuTextComponent
          type={AkuTextComponentType.Aku_font_16_bold}
          text={t("如何联系您")}
          style={{ marginBottom: 16 }}
        />
        <AKTextInput
          isRequired
          type={AkTextInputType.A2_1_1}
          placeholder={t("电话号码")}
          value={this.state.phoneNumber}
          setValue={(value: string) => {
            this.setState({ phoneNumber: value });
          }}
          style={{ marginBottom: 16 }}
          keyboardType={"numeric"}
          defaultDarkText={t("请填写")}
          errorText={t("手机号码有误，请重新填写")}
          checkTextIsError={this.checkPhoneNumber}
        />
        <AKTextInput
          isRequired
          type={AkTextInputType.A2_1_1}
          placeholder={t("邮箱")}
          value={this.state.email}
          setValue={(value: string) => {
            this.setState({ email: value });
          }}
          errorText={t("邮箱有误，请重新填写")}
          style={{ marginBottom: 16 }}
          checkTextIsError={this.checkEmail}
        />
        <AKTextInput
          isRequired
          defaultDarkText={t("KTP号码方便客服联系您时确认身份")}
          type={AkTextInputType.A2_1_1}
          placeholder={t("KTP号码")}
          errorText={t("KTP号码有误，请重新填写")}
          value={this.state.identityNo}
          setValue={(value: string) => {
            this.setState({ identityNo: value });
          }}
          style={{ marginBottom: 16 }}
          checkTextIsError={this.checkKTPNumber}
        />
      </View>
    );
  };

  onSelectTypeIndex = (index: number) => {
    this.setState({
      selectTypeIndex: index
    });
  };

  showSelectTypeText = () => {
    const { t } = this.props;
    switch (this.state.selectTypeIndex) {
      case ReportTypeEnum.Fake_Customer_Sevice:
        return t("冒充客服");
      case ReportTypeEnum.Impersonate_Government_Officials:
        return t("冒充政府人员");
      case ReportTypeEnum.Brushing_Scam:
        return t("返现刷单");
      case ReportTypeEnum.Limit_Upgrade:
        return t("账户提额");
      default:
        return "";
    }
  };

  _renderMainView = () => {
    const { t } = this.props;
    return (
      <View style={{ flex: 1 }}>
        <Image source={require("../../img/aku.webp")} style={styles.aku} />
        <View style={styles.infoContainer}>
          <AkuTextComponent
            type={AkuTextComponentType.Aku_font_16_bold}
            text={t("举报诈骗说明")}
            style={{ marginBottom: 16 }}
          />
          <ReportTypeView
            t={t}
            onSelectTypeIndex={(index: number) => {
              this.onSelectTypeIndex(index);
            }}
            initialIndex={this.state.selectTypeIndex - 1}
            text={this.showSelectTypeText()}
          />
          <AKTextInput
            isRequired
            keyboardType={"numeric"}
            type={AkTextInputType.A2_1_1}
            placeholder={t("举报手机号")}
            defaultDarkText={t("请填写")}
            value={this.state.fraudPhoneLists}
            errorText={t("手机号码有误，请重新填写")}
            setValue={(value: string) => {
              this.setState({ fraudPhoneLists: value });
            }}
            style={{ marginBottom: 16 }}
            checkTextIsError={this.checkPhoneNumber}
          />
          <AKTextInput
            type={AkTextInputType.A2_1_1}
            placeholder={t("举报链接")}
            value={this.state.fraudLink}
            setValue={(value: string) => {
              this.setState({ fraudLink: value });
            }}
            errorText={t("手机号码有误，请重新填写")}
            style={{ marginBottom: 16 }}
            maxLength={2000}
          />
          <AKTextInput
            isRequired
            type={AkTextInputType.A2_1_1}
            placeholder={t("举报详情")}
            value={this.state.fraudDetail}
            setValue={(value: string) => {
              this.setState({ fraudDetail: value });
            }}
            style={{ marginBottom: 16 }}
            defaultDarkText={t("请详细描述您遇到的事情经过")}
            maxLength={2000}
          />
          {this._renderEvidenceScreenShots()}
          {this._renderUserInfo()}
        </View>
      </View>
    );
  };

  _showUnfilledFields = () => {
    const { t } = this.props;
    const { uriList, fraudDetail, identityNo, phoneNumber, email, selectTypeIndex, fraudPhoneLists } = this.state;
    let text;
    if (selectTypeIndex === -1) {
      text = t("举报类型");
    } else if (!fraudPhoneLists) {
      text = t("举报手机号");
    } else if (!fraudDetail) {
      text = t("举报详情");
    } else if (uriList.length === 0) {
      text = t("证据截图");
    } else if (!phoneNumber) {
      text = t("电话号码");
    } else if (!email) {
      text = t("邮箱");
    } else if (!identityNo) {
      text = t("KTP号码");
    }
    return text;
  };

  returnUrlLists = () => {
    const { uriList } = this.state;
    const data: string[] = [];
    if (uriList.length !== 0) {
      uriList.map((item: SubmitEvaluationImgItem) => {
        if (!!item.src) data.push(item.src);
      });
    }
    return data;
  };

  _showMessageText = () => {
    const { t } = this.props;
    let text = "";
    if (this.checkPhoneNumber(this.state.fraudPhoneLists)) {
      text = t("手机号码有误，请重新填写");
    } else if (this.checkPhoneNumber(this.state.phoneNumber)) {
      text = t("手机号码有误，请重新填写");
    } else if (this.checkEmail(this.state.email)) {
      text = t("邮箱有误，请重新填写");
    } else if (this.checkKTPNumber(this.state.identityNo)) {
      text = t("KTP号码有误，请重新填写");
    }
    return text;
  };

  _onClickButton = () => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    const {
      fraudLink,
      fraudDetail,
      identityNo,
      phoneNumber,
      email,
      uriList,
      selectTypeIndex,
      fraudPhoneLists
    } = this.state;
    // 必填项未填toast提示
    if (
      selectTypeIndex === -1 ||
      !fraudPhoneLists ||
      !fraudDetail ||
      uriList.length === 0 ||
      !identityNo ||
      !phoneNumber ||
      !email
    ) {
      NativeToast.showMessage(t("XX未填写，不能提交", { XX: this._showUnfilledFields() }));
      ReportFraudTicketSensor.onClickSunmbit();
    }
    // 全部填写前端校验是否填对，没填对toast提示具体错误
    else if (
      this.checkPhoneNumber(this.state.fraudPhoneLists) ||
      this.checkPhoneNumber(this.state.phoneNumber) ||
      this.checkEmail(this.state.email) ||
      this.checkKTPNumber(this.state.identityNo)
    ) {
      NativeToast.showMessage(this._showMessageText());
      ReportFraudTicketSensor.onClickSunmbit();
    } else {
      // 最后后端接口校验
      pageStore.createFraudCase(
        selectTypeIndex,
        [fraudPhoneLists],
        fraudLink,
        fraudDetail,
        this.returnUrlLists(),
        identityNo,
        phoneNumber,
        email,
        (isSuccess: boolean) => {
          if (isSuccess) {
            AsyncStorage.setItem(ReportFraudTicket, JSON.stringify({ isSubmit: true, time: Date.now() }));
            NativeNavigationModule.navigate({ screen: "ReportFraudTicketResult", params: { gestureEnabled: false } });
          }
        }
      );
    }
  };

  _renderBottomView = () => {
    const { t } = this.props;
    return <BottomButton type={BottomSuctionBarType.H8_1_1} text={t("提交")} onPress={this._onClickButton} />;
  };

  _render(): JSX.Element {
    return (
      <View style={styles.container}>
        <View style={styles.bottomWhite} />
        <ScrollView keyboardShouldPersistTaps={"handled"} keyboardDismissMode={"on-drag"}>
          {this._renderTopView()}
          {this._renderMainView()}
        </ScrollView>
        {this._renderBottomView()}
      </View>
    );
  }
}
