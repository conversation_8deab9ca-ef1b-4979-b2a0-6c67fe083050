import { FigmaStyle, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  bottomWhite: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 500,
    backgroundColor: "#fff"
  },
  container: {
    flex: 1,
    backgroundColor: "#D5F1FD"
  },
  aku: {
    width: 65,
    height: 106,
    position: "absolute",
    right: 28,
    top: -79
  },
  topContainer: {
    paddingLeft: 16,
    paddingTop: 12,
    paddingBottom: 20,
    backgroundColor: "#D5F1FD"
  },
  topTitle: {
    marginBottom: 4,
    maxWidth: WINDOW_WIDTH - 115
  },
  topText: {
    maxWidth: WINDOW_WIDTH - 115,
    color: FigmaStyle.Color.Grey_Text2
  },
  infoContainer: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingTop: 20
  },
  evidenceView: {
    marginTop: 4,
    marginBottom: 20
  },
  star: {
    width: 8,
    height: 8,
    marginRight: 2
  }
});
