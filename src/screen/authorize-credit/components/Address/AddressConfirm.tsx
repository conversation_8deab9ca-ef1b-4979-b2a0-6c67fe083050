/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/components/Address/AddressConfirm.tsx
 */

import { StyleSheet, Text, View } from "react-native";
import React from "react";
import { AKButton } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { TFunction } from "i18next";
import { SensorClickKTPAddress } from "../../tool/EventTracking";

type Props = {
  isSelectTrue?: boolean;
  onPress?: (isSelectTrue: boolean) => void;
  t: TFunction;
};

const AddressConfirm = ({ isSelectTrue, onPress, t }: Props) => {
  return (
    <View style={styles.content}>
      <View style={styles.container}>
        <Text>{t("您的身份证地址与您的居住地址是否一样？")}</Text>
        <View style={styles.cell}>
          <AKButton
            style={{ flex: 1, backgroundColor: "#fff", marginRight: 12, paddingHorizontal: 16 }}
            type={isSelectTrue ? AKButtonType.B1_2_3 : AKButtonType.B1_3_3}
            text={"Yes"}
            onPress={() => {
              if (isSelectTrue === true) return;
              onPress && onPress(true);
              SensorClickKTPAddress(true);
            }}
          />
          <AKButton
            style={{ flex: 1, backgroundColor: "#fff", paddingHorizontal: 16 }}
            type={isSelectTrue ? AKButtonType.B1_3_3 : AKButtonType.B1_2_3}
            text={"No"}
            onPress={() => {
              if (isSelectTrue === false) return;
              onPress && onPress(false);
              SensorClickKTPAddress(false);
            }}
          />
        </View>
      </View>
      <View style={styles.triangle} />
    </View>
  );
};

export default AddressConfirm;

const styles = StyleSheet.create({
  content: { marginTop: 8 },
  container: {
    backgroundColor: "#EFF2F6",
    marginTop: 8,
    borderRadius: 6,
    padding: 16
  },
  title: {
    color: "#282B2E",
    fontSize: 14
  },
  cell: {
    marginTop: 16,
    flexDirection: "row",
    alignItems: "center"
  },
  containerTriangle: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  },
  triangle: {
    position: "absolute",
    left: 35,
    width: 0,
    height: 0,
    backgroundColor: "transparent",
    borderStyle: "solid",
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderBottomWidth: 8,
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
    borderBottomColor: "#EFF2F6"
  }
});
