/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/components/BottomProtocol.tsx
 */
import { StyleSheet, Text, Image, TouchableOpacity, View, StyleProp, ViewStyle } from "react-native";
import React from "react";
import { FontStyles } from "@akulaku-rn/akui-rn";
import { SensorType } from "@akulaku-rn/rn-v4-sdk/src/ReporterSdk/types";
import NativeSensorModule from "@akulaku-rn/akulaku-ec-common/src/nativeModules/sdk/nativeSensroModule";
import { TFunction } from "i18next";
import { NativeNavigationModule } from "@akulaku-rn/akulaku-ec-common/src/nativeModules";

interface Props {
  isSelected: boolean;
  content: string;
  protocol: string;
  onPressIcon: () => void;
  onPressContent: () => void;
  style?: StyleProp<ViewStyle>;
  isNew?: boolean;
  renderBottomView?: () => React.ReactElement | null;
}
/**
 * 授信社保协议，出现在第一页，后台控制是否显示
 */
const SocialProtocol = ({
  isSelected,
  content,
  protocol,
  style,
  isNew,
  onPressIcon,
  onPressContent,
  renderBottomView
}: Props) => {
  return (
    <View style={[styles.socical, style]}>
      <TouchableOpacity style={styles.btn} onPress={onPressIcon}>
        {isSelected ? (
          <Image
            style={styles.selectIcon}
            source={isNew ? require("../img/credit_tips_select_new.webp") : require("../img/credit_tips_select.png")}
          />
        ) : (
          <View style={styles.unselectIcon} />
        )}
      </TouchableOpacity>
      <View style={{ flex: 1 }}>
        <Text style={styles.agreementTitle}>
          {content}
          <Text onPress={onPressContent} style={isNew ? styles.underlineNew : styles.underline}>
            {protocol}
          </Text>
        </Text>
        {renderBottomView && renderBottomView()}
      </View>
    </View>
  );
};

interface IdPhotoProtocolProps {
  isSelected: boolean;
  onPressIcon: () => void;
  style?: StyleProp<ViewStyle>;
  isNew?: boolean;
  t: TFunction;
  config4030: Record<string, string | boolean>;
}

const IdPhotoProtocol = ({ isSelected, style, isNew, t, onPressIcon, config4030 }: IdPhotoProtocolProps) => {
  const _onPressKreditoPayLater = () => {
    NativeNavigationModule.navigate({
      url: config4030.is_new ? ((config4030?.link ?? "") as string) : "https://www.akulaku.com/artikel/kreditopaylater/"
    });
  };
  const _onPressKreditoPrivacy = () => {
    NativeNavigationModule.navigate({
      url: config4030.is_new
        ? ((config4030?.authCreditPrivacyPolicyLink ?? "") as string)
        : "https://www.akulaku.com/artikel/kebijakanprivasikredito/"
    });
  };
  const _onPressAfiPayLater = () => {
    NativeNavigationModule.navigate({
      url: "https://www.akulaku.com/artikel/akulakupaylater/"
    });
  };
  const _onPressAfiPrivacy = () => {
    NativeNavigationModule.navigate({
      url: "https://www.akulaku.com/artikel/kebijakanprivasi/"
    });
  };
  return (
    <View>
      {!config4030.is_new && (
        <View style={{ marginBottom: 16, marginHorizontal: 16 }}>
          <View style={{ paddingHorizontal: 16, paddingVertical: 12, borderRadius: 8, backgroundColor: "#EFF2F6" }}>
            <Text style={{ fontSize: 12, color: "#282B2E" }}>
              {t(
                "先买后付服务由PT Fintek Digital Indonesia (Kredito) 提供，该公司由 Otoritas Jasa Keuangan许可并接受其监管。"
              )}
            </Text>
          </View>
        </View>
      )}
      <View style={[styles.socical, style]}>
        <TouchableOpacity style={styles.btn} onPress={onPressIcon}>
          {isSelected ? (
            <Image
              style={styles.selectIcon}
              source={isNew ? require("../img/credit_tips_select_new.webp") : require("../img/credit_tips_select.png")}
            />
          ) : (
            <View style={styles.unselectIcon} />
          )}
        </TouchableOpacity>
        <View style={{ flex: 1 }}>
          <Text style={styles.agreementTitle}>
            {t("我承诺提供的所有信息真实准确。并同意Kredito PayLater的", {
              x: config4030.is_new ? "Akulaku" : "Kredito"
            })}
            <Text onPress={_onPressKreditoPayLater} style={isNew ? styles.underlineNew : styles.underline}>
              {t("Term & Conditions")}
            </Text>
            <Text>
              {t("and", {
                x: config4030.is_new ? "Akulaku" : "Kredito"
              })}
            </Text>
            <Text onPress={_onPressKreditoPrivacy} style={isNew ? styles.underlineNew : styles.underline}>
              {t("Privacy Policy")}
            </Text>
            <Text>.</Text>
            <Text>
              {t(
                "我同意将我的个人数据处理为签发电子证书的目的，以及由VIDA和Privy作为印度尼西亚共和国通信和数字部认证的电子认证组织者（PSrE）提供的其他服务"
              )}
            </Text>
          </Text>
          {/* <View style={{ marginTop: 12 }}>
            <Text style={styles.agreementTitle}>
              {t(
                "为了更好地提供服务，你的申请将会提供给PT Akulaku Finance Indonesia(AFI)用于预授信。勾选同意AFI Paylater"
              )}
              <Text onPress={_onPressAfiPayLater} style={isNew ? styles.underlineNew : styles.underline}>
                {t("Term & Conditions")}
              </Text>
              <Text>{t("and-AFI")}</Text>
              <Text onPress={_onPressAfiPrivacy} style={isNew ? styles.underlineNew : styles.underline}>
                {t("Privacy Policy")}
              </Text>
              <Text>{t("AFI-结尾")}</Text>
            </Text>
          </View> */}
        </View>
      </View>
    </View>
  );
};

interface BottomProtocolProps {
  isSelected: boolean;
  content: string;
  onPressIcon: () => void;
  style?: StyleProp<ViewStyle>;
  isNew?: boolean;
}

/**
 * 授信底部协议，一般出现在 合规版本紧急联系人，照片页，后台控制是否显示
 */
const BottomProtocol = ({ isSelected, content, onPressIcon, isNew, style }: BottomProtocolProps) => {
  return (
    <View style={[isNew ? styles.agreementContainerNew : styles.agreementContainer, style]}>
      <TouchableOpacity style={isNew ? styles.agreementViewNew : styles.agreementView} onPress={onPressIcon}>
        {isSelected ? (
          <Image
            style={styles.selectIcon}
            source={isNew ? require("../img/credit_tips_select_new.webp") : require("../img/credit_tips_select.png")}
          />
        ) : (
          <View style={styles.btn}>
            <View style={styles.unselectIcon} />
          </View>
        )}
        <Text style={styles.agreementTitle}>{content}</Text>
      </TouchableOpacity>
    </View>
  );
};

export { SocialProtocol, BottomProtocol, IdPhotoProtocol };

const styles = StyleSheet.create({
  agreementView: {
    flexDirection: "row",
    alignSelf: "flex-start",
    marginRight: 33,
    marginBottom: 50
  },
  agreementViewNew: {
    flexDirection: "row",
    alignSelf: "flex-start",
    marginRight: 33,
    marginBottom: 0
  },
  selectIcon: {
    width: 16,
    height: 16
  },
  unselectIcon: {
    width: 14,
    height: 14,
    borderRadius: 7,
    borderWidth: 1.5,
    borderColor: "#989FA9"
  },
  agreementContainer: {
    paddingHorizontal: 16,
    paddingTop: 8
  },
  agreementContainerNew: {
    paddingHorizontal: 16,
    paddingTop: 2
  },
  agreementTitle: {
    fontSize: 12,
    lineHeight: 14,
    color: "#6E737D",
    marginLeft: 8
  },
  underline: {
    fontSize: 12,
    color: "#282B2E",
    textDecorationLine: "underline",
    ...FontStyles["rob-medium"]
  },
  underlineNew: {
    fontSize: 12,
    color: "#0072D6",
    textDecorationLine: "underline"
  },
  socical: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingTop: 2,
    marginBottom: 24
  },
  socialContent: {
    marginRight: 33,
    marginBottom: 50
  },
  btn: { width: 16, height: 16, justifyContent: "center", alignItems: "center" }
});
