import React, { useEffect, useState } from "react";
import { Platform, View, Image, StyleSheet } from "react-native";
import { AkuTextComponent, AkuTextComponentType, FigmaStyle, PriceComponent, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import MultiSlider, { LabelProps, MarkerProps } from "common/components/MulitiSlider";
import SliderConfig from "./SliderConfig";
import { NewEntityInfos } from "../../tool/DataHandling";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import store from "../../store";

interface SliderProps {
  setValue: (text: string, id: number, type?: any, key?: any) => void;
  data: NewEntityInfos;
  missingId: number | null;
  store: pageStoreModel<store>;
}

const Slider = (props: SliderProps): React.ReactNode => {
  const config = SliderConfig[`${props.data.id}`];
  if (!config) return null;
  const [showError, setShowError] = useState(false);

  const getStyleObj = () => {
    const newUi = props.store.pageStore.newUi;
    return {
      title: {
        defaultColor: newUi ? FigmaStyle.Color.Grey_Text3 : "#B3B3B3",
        errorColor: newUi ? FigmaStyle.Color.Primary_6 : "#E62117",
        fontSize: newUi ? AkuTextComponentType.Aku_font_16_regular : AkuTextComponentType.Aku_font_14_regular
      },
      label: {
        color: newUi ? FigmaStyle.Color.Grey_Text1 : "#333333"
      },
      marker: {
        color: newUi ? FigmaStyle.Color.Primary_6 : "#E62117"
      },
      track: {
        color: newUi ? FigmaStyle.Color.Grey_Divider : "#E6E6E6"
      }
    };
  };

  const style = getStyleObj();

  useEffect(() => {
    const value = props.store.pageStore.useCreditData[props.data.id];
    if (!value || `${value}` === "0") {
      setShowError(props.missingId === props.data.id);
    }
  }, [props.missingId]);

  const CustomLabel = ({ oneMarkerValue }: LabelProps) => {
    return (
      <View style={{ justifyContent: "center", alignItems: "center", marginBottom: 2 }}>
        <AkuTextComponent
          type={AkuTextComponentType.Aku_font_18_medium}
          text={config.transformLabel(Number(oneMarkerValue))}
          style={{
            color: style.label.color
          }}
        />
      </View>
    );
  };

  const CustomMarker = ({ markerStyle }: MarkerProps) => {
    return (
      <Image
        source={require("./img/marker.png")}
        style={{ height: markerStyle.height, width: markerStyle.width, borderRadius: 0 }}
      />
    );
  };

  return (
    <View style={{ marginBottom: 12 }}>
      <AkuTextComponent
        style={{
          color: showError ? style.title.errorColor : style.title.defaultColor,
          marginBottom: 12
        }}
        text={props.data.title}
        type={style.title.fontSize}
      />
      <View style={{ width: WINDOW_WIDTH - 32, justifyContent: "center", alignItems: "center" }}>
        <MultiSlider
          values={[config.initValue(props.data.lastValue)]}
          markerContainerStyle={{ height: 50, backgroundColor: "transparent" }}
          containerStyle={{ height: 32, backgroundColor: "transparent" }}
          enableLabel={true}
          customLabel={CustomLabel}
          customMarker={CustomMarker}
          selectedStyle={{ backgroundColor: FigmaStyle.Color.Primary_6 }}
          markerStyle={{
            height: 32,
            width: 20,
            backgroundColor: style.marker.color,
            // borderWidth: 2,
            // borderColor: "white",
            borderRadius: 10
          }}
          allowOverlap={true}
          trackStyle={{ backgroundColor: style.track.color, height: 4, borderRadius: 2 }}
          onValuesChange={values => {
            if (values[0] === 0) {
              setShowError(true);
            } else if (showError) {
              setShowError(false);
            }
            props.setValue(`${config.preSetValue(values[0])}`, props.data.id);
          }}
          sliderLength={WINDOW_WIDTH - 52}
          step={config.step}
          min={config.min}
          max={config.max}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  markerStyle: {
    ...Platform.select({
      ios: {
        height: 30,
        width: 30,
        borderRadius: 30,
        borderWidth: 1,
        borderColor: "#DDDDDD",
        backgroundColor: "#FFFFFF"
      },
      android: {
        height: 12,
        width: 12,
        borderRadius: 12,
        backgroundColor: "#0D8675"
      },
      web: {
        height: 30,
        width: 30,
        borderRadius: 30,
        borderWidth: 1,
        borderColor: "#DDDDDD",
        backgroundColor: "#FFFFFF",
        shadowColor: "#000000",
        shadowOffset: {
          width: 0,
          height: 3
        },
        shadowRadius: 1,
        shadowOpacity: 0.2
      }
    })
  },
  pressedMarkerStyle: {
    ...Platform.select({
      web: {},
      ios: {},
      android: {
        height: 20,
        width: 20,
        borderRadius: 20
      }
    })
  },
  disabled: {
    backgroundColor: "#d3d3d3"
  }
});

export default Slider;
