import { PriceComponent } from "@akulaku-rn/akui-rn";
import { GlobalRuntime } from "common/constant";

const SliderConfig: Record<
  string,
  {
    min: number;
    max: number;
    step: number;
    stepValue: number;
    transformLabel: (value: number) => string;
    preSetValue: (value: number) => string;
    initValue: (value: string) => number;
  }
> = {
  "1103010": {
    min: 0,
    max: 34,
    step: 1,
    stepValue: 500000,
    transformLabel: (value: number): string => {
      let price = 0;
      if (value > 0) {
        if (value <= 20) {
          price = 500000 * (+value || 0);
        } else if (value > 20 && value <= 30) {
          price += 20 * 500000;
          price += 500000 * 2 * (value - 20);
        } else if (value > 30) {
          price += 20 * 500000;
          price += 10 * 500000 * 2;
          price += 500000 * 10 * (value - 30);
        }
      }
      return PriceComponent.priceFormat(price, GlobalRuntime.countryId);
    },
    preSetValue: (value: number): string => {
      if (value === 0) return "0";
      return `1103010${value + 40}`;
    },
    initValue: (value: string): number => {
      try {
        console.log("initValue", value, typeof value);
        if (!value) return 0;
        if (value.length < 2) return 0;
        return Number(`${value}`.substr(-2)) - 40 || 0;
      } catch (e) {
        console.log("SliderConfig initValue e", e);
        return 0;
      }
    }
  }
};

export default SliderConfig;
