/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-01 11:50
 * @description
 */

import React, { PureComponent } from "react";
import { View, StyleSheet, Image, TouchableOpacity, Text, ScrollView } from "react-native";
import { TFunction } from "i18next";
import { FontStyles, isShapedIOS, UrlImage, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import PopUpPanel from "@akulaku-rn/akui-rn/src/components/PopUpPanel";
import Dialog from "@akulaku-rn/akui-rn/src/components/Dialog";

type Props = {
  data: any;
  t: TFunction;
};

export class AgreementDialog extends PureComponent<Props> {
  onConfirm = () => {
    Dialog.dismiss();
  };

  render() {
    const {
      t,
      data: { data }
    } = this.props;
    return (
      <>
        <View style={{ marginTop: -24 }}>
          <Image source={require("../../img/bg.webp")} style={{ height: 158, width: 280 }} />
          <View
            style={{ flexDirection: "row", position: "absolute", top: 14, width: "100%", justifyContent: "center" }}
          >
            <Text style={{ fontSize: 16, ...StyleSheet.flatten(FontStyles["rob-medium"]), color: "#fff" }}>
              {t("提示")}
            </Text>
            <TouchableOpacity activeOpacity={0.85} onPress={this.onConfirm} style={styles.topBgContainer}>
              <Image source={require("common/images/icon_close_2.webp")} style={styles.topBg} />
            </TouchableOpacity>
          </View>
        </View>
        <ScrollView style={{ maxHeight: 115, paddingHorizontal: 24, marginVertical: 16 }}>
          {data.map((e: string, i: string | number | null | undefined) => {
            return (
              <Text key={i} style={styles.info}>
                {e}
              </Text>
            );
          })}
          {isShapedIOS && <View style={{ height: 40 }} />}
        </ScrollView>
      </>
    );
  }
}
export class AgreementPopView extends PureComponent<Props> {
  onConfirm = () => {
    PopUpPanel.dismiss();
  };

  render() {
    const {
      t,
      data: { linkInfo }
    } = this.props;
    return (
      <>
        <ScrollView>
          <UrlImage
            style={{ width: WINDOW_WIDTH, height: (linkInfo.height / linkInfo.width) * WINDOW_WIDTH, marginBottom: 12 }}
            source={linkInfo.link}
            width={WINDOW_WIDTH}
            height={(linkInfo.height / linkInfo.width) * WINDOW_WIDTH}
            resizeMode={"contain"}
          />
        </ScrollView>
        <TouchableOpacity style={styles.redButton} onPress={this.onConfirm}>
          <Text style={styles.redButtonText}>{t("我同意")}</Text>
        </TouchableOpacity>
        {isShapedIOS && <View style={{ height: 40 }} />}
      </>
    );
  }
}

const styles = StyleSheet.create({
  shadow: {
    width: WINDOW_WIDTH,
    height: 64,
    backgroundColor: "#fff",
    shadowOffset: { width: 0, height: 2 },
    shadowColor: "rgba(0,0,0,0.28)",
    shadowRadius: 4,
    shadowOpacity: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 12
  },
  redButton: {
    margin: 16,
    backgroundColor: "#FCD204",
    height: 44,
    justifyContent: "center",
    borderRadius: 8
  },
  topBg: { height: 24, width: 24 },
  topBgContainer: {
    position: "absolute",
    right: 12,
    top: -2
  },
  info: {
    color: "#666666",
    fontSize: 14,
    lineHeight: 16,
    marginBottom: 10
  },
  btnText: {
    lineHeight: 19,
    color: "#E62117",
    fontSize: 16,
    textAlign: "center",
    paddingVertical: 16
  },
  redButtonText: {
    color: "#2C2C2A",
    fontSize: 16,
    textAlign: "center",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  }
});
