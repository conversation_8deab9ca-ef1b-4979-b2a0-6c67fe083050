/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/components/ResultBottomSheet.tsx
 */

import { FontStyles } from "@akulaku-rn/akui-rn";
import PopUpPanel from "@akulaku-rn/akui-rn/src/components/PopUpPanel";
import { TFunction } from "i18next";
import React from "react";
import { View, Image, Text, StyleSheet, TouchableOpacity } from "react-native";

interface Props {
  t: TFunction;
  onPressIntention: (intentionType: number) => void;
}

const ResultBottomSheet = ({ t, onPressIntention }: Props) => {
  const _onCloseModal = (intentionType: number) => {
    PopUpPanel.dismiss();
    onPressIntention(intentionType);
  };

  return (
    <View>
      <View style={styles.container}>
        <Image source={require("../img/credit_submit_left.webp")} style={styles.rightImg} />
        <View style={{ flex: 1 }}>
          <Text style={styles.title}>{t("请选择您申请信用额度的目的")}</Text>
        </View>
        <Image source={require("../img/credit_submit_right.webp")} style={styles.leftImg} />
      </View>
      <Cell title={t("购物")} image={require("../img/credit_submit_shopping.webp")} onPress={() => _onCloseModal(2)} />
      <Cell title={t("借款")} image={require("../img/credit_submit_loan.webp")} onPress={() => _onCloseModal(1)} />
      <TouchableOpacity style={{ paddingHorizontal: 16, paddingTop: 16 }} onPress={() => _onCloseModal(3)}>
        <Text style={styles.selectTitle}>{`${t("先逛逛")} >`}</Text>
      </TouchableOpacity>
    </View>
  );
};

interface CellProps {
  title: string;
  image: number;
  onPress: () => void;
}

const Cell = ({ title, image, onPress }: CellProps) => {
  return (
    <TouchableOpacity style={styles.cellContainer} onPress={onPress}>
      <Image source={image} style={styles.img} />
      <View style={styles.cellView}>
        <Text style={styles.cell}>{title}</Text>
      </View>
      <Image source={require("../img/credit_submit_arrow.webp")} style={styles.arrow} />
    </TouchableOpacity>
  );
};

export default ResultBottomSheet;

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    marginBottom: 16
  },
  cellContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: "#EFF2F688",
    borderRadius: 8,
    marginHorizontal: 16,
    marginTop: 12
  },
  title: {
    fontSize: 16,
    color: "#282b2e",
    textAlign: "center",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  arrow: { width: 12, height: 12, marginLeft: 12 },
  img: { width: 40, height: 40 },
  cellView: { marginLeft: 12, flex: 1 },
  cell: { fontSize: 16, color: "#282b2e", ...StyleSheet.flatten(FontStyles["rob-medium"]) },
  leftImg: { width: 20, height: 14, marginLeft: 7 },
  rightImg: { width: 20, height: 14, marginRight: 7 },
  selectTitle: { fontSize: 14, color: "#989FA9", textAlign: "center", textDecorationLine: "underline" }
});
