import React, { Component } from "react";
import { View, Text, Image, TouchableOpacity, StyleSheet, DeviceEventEmitter } from "react-native";
import { iOS, Loading, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import { observer, inject } from "mobx-react";
import _, { isObject } from "lodash";
import { EnterLeavePage, SensorTypeClickItem, V3ClickItem, V4ClickItem } from "../../tool/EventTracking";
import { TFunction } from "i18next";
import { NewEntityInfos } from "../../tool/DataHandling";
import store from "../../store";
import api from "../../store/api";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { NativeImageCaptureModule } from "common/nativeModules";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { ItemType } from "../../dict/ComponentType";
import { NetworkResponse } from "common/nativeModules/network/nativeNetworkModule";
import * as queryString from "querystring";
import getDownloadUrl from "./getOssShowImgUrl";
import IdImg from "./IdImg";

type Props = {
  setValue: (text: string, id: number, type: string, key: string) => void;
  data: NewEntityInfos;
  containerStyle?: object;
  t: TFunction;
  missingId: number | null;
  imgOcr?: (imageKey: string) => void;
  isFirstApply: boolean;
  store: pageStoreModel<store>;
  allItems: NewEntityInfos[];
  testType: number;
};

type States = {
  image: string | null;
  imgError: boolean;
  showError: boolean;
};

enum ExampleDialogType {
  IDENTIFICATION, // 证件照
  HOLD_IDENTIFICATION, // 手持证件照
  SIGNATURE // 签名
}
export default class Photo extends Component<Props, States> {
  constructor(props: Props) {
    super(props);
    const {
      data: { id, needModified, kycStatus },
      store: { pageStore }
    } = props;
    this.state = {
      image: this.getImageLocalPath(pageStore.useCreditData[id]),
      imgError: false,
      showError: needModified
    };
  }

  didSelectedItem = _.debounce(
    () => {
      const {
        data: { id, pageType, editable, kycStatus },
        t,
        isFirstApply
      } = this.props;
      if (!editable) {
        if (!editable) {
          if (kycStatus) {
            NativeToast.showMessage(t("信息已验证，不可修改"));
          } else {
            NativeToast.showMessage(t("抱歉，该信息暂不可修改"));
          }
          return;
        }
      }
      SensorTypeClickItem(pageType, id);
      V3ClickItem(id);
      const v4Data = { entryid: id, type: "photo", isFirst: isFirstApply };
      V4ClickItem(v4Data);
      const type = ExampleDialogType.IDENTIFICATION;

      this._finishChoosePhoto(id, type);
    },
    500,
    { leading: true, trailing: false }
  );

  _finishChoosePhoto = async (id: number, type: ExampleDialogType) => {
    const {
      setValue,
      store: { pageStore },
      t,
      data: { pageType, fieldName },
      isFirstApply,
      testType
    } = this.props;
    //如果不加延迟会导致原生闪回RN
    setTimeout(async () => {
      // @ts-ignore
      let image: any = null;
      let response;
      const enterPageData = { pageType, isFirst: isFirstApply, enter: false, testType };
      EnterLeavePage(enterPageData);
      switch (type) {
        case ExampleDialogType.IDENTIFICATION:
          response = await NativeImageCaptureModule.go2Photo(1, "instalment_credit", id);
          break;
        case ExampleDialogType.HOLD_IDENTIFICATION:
          response = await NativeImageCaptureModule.go2Face(2, "instalment_credit", id);
          break;
        case ExampleDialogType.SIGNATURE:
          response = await NativeImageCaptureModule.takePhoto();
          break;
        default:
          break;
      }
      enterPageData.enter = true;
      EnterLeavePage(enterPageData);
      if (response.success) {
        const waitImgs = [
          iOS ? { entryId: id, base64: response.data.base64 } : { entryId: id, filePath: response.data.filePath }
        ];
        const uploadResponse = await pageStore.uploadImages(waitImgs);
        if (uploadResponse.success) {
          image = uploadResponse.data[0];
          const downloadUrlRes = await getDownloadUrl(image.key);
          if (downloadUrlRes.success) {
            image.downloadUrl = downloadUrlRes.url;
          } else {
            console.log("downloadUrlRes.error", downloadUrlRes.error);
          }
        } else {
          Toast.show(t("网络延迟，上传图片失败"), {
            position: 0
          });
        }
      } else {
        !!response.errMsg &&
          Toast.show(response.errMsg, {
            position: 0
          });
      }
      if (image) {
        const finalPath = this.getImageLocalPath(image);
        setValue(image, id, "file", "");
        if (fieldName === "Upload ID photo") {
          pageStore.showHideComponent = true;
        }
        this.setState({ image: finalPath, imgError: false, showError: false }, () => {
          if (type === ExampleDialogType.IDENTIFICATION) {
            this.imgOcr(image.key);
          }
        });
      }
    }, 500);
  };

  imgOcr = (imageKey: string) => {
    Loading.show({ hasBackcolor: true });
    const {
      store: {
        pageStore,
        runtime: { countryId }
      },
      allItems,
      t
    } = this.props;
    pageStore.post(
      api.IMG_OCR,
      { countryId, imageKey },
      (response: any) => {
        if (response.data) {
          const { ocrInfo, textImages = {} } = response.data;
          if (!!ocrInfo && !!ocrInfo.icNo) {
            const isIcNumberItem = allItems.find(i => {
              return i.fieldName === "IdNumber";
            });
            const hideOcrItem = allItems.find(i => {
              return i.type === ItemType.hidden && i.fieldName === "ocrIcNo";
            });
            if (!!hideOcrItem) pageStore.useCreditData[hideOcrItem.id] = ocrInfo.icNo;

            const data = {
              id: (!!isIcNumberItem && isIcNumberItem.id) || 0,
              value: ocrInfo.icNo,
              imgUrl: textImages.icNoTextImage
            };
            DeviceEventEmitter.emit("setValue", data);
          }
          const { birthDate, sex } = ocrInfo;
          if (!!birthDate) {
            const re = birthDate.split("-");
            if (re.length === 3) {
              const year = re[2];
              const month = re[1];
              const date = re[0];
              DeviceEventEmitter.emit("setValue", {
                id: 1101009,
                value: { month, date, year },
                imgUrl: textImages.birthDateTextImage
              });
            }
          } else {
            // 默认1900-01-01
            DeviceEventEmitter.emit("setValue", {
              id: 1101009,
              value: { month: "01", date: "01", year: "1900" }
            });
          }
          if (!!sex) {
            DeviceEventEmitter.emit("setValue", {
              id: 1101010,
              value: sex === "PEREMPUAN" ? 110101002 : 110101001
            });
          }

          if (!!ocrInfo.name) {
            const icNameItem = allItems.find(i => {
              return i.fieldName === "IdName";
            });
            const data = {
              id: (!!icNameItem && icNameItem.id) || 0,
              value: ocrInfo.name,
              imgUrl: textImages.nameTextImage
            };
            DeviceEventEmitter.emit("setValue", data);
          }
          // 出生地需要灰度 1101004 出生地地址
          if (this.props.store.pageStore.ocrTextImageUi) {
            if (!!ocrInfo.birthAddress) {
              const birthAddressItem = allItems.find(i => {
                return i.id === 1101004;
              });
              const data = {
                id: (!!birthAddressItem && birthAddressItem.id) || 0,
                value: ocrInfo.birthAddress
              };
              DeviceEventEmitter.emit("setValue", data);
            }
          }
        }

        Loading.dismiss();
      },
      (error: any) => {
        Loading.dismiss();
        const { errMsg } = error;
        NativeToast.showMessage(errMsg);
      }
    );
  };

  getImageLocalPath = (path: string | { base64: string; filePath: string; downloadUrl: string }) => {
    // console.log("getImageLocalPath path", path);
    if (!path) return null;
    const {
      store: { pageStore },
      data: { fieldName }
    } = this.props;
    if (fieldName === "Upload ID photo") {
      pageStore.showHideComponent = true;
    }
    if (typeof path === "string") {
      return path;
    } else {
      return path.downloadUrl;
    }
  };

  onError = (e: any) => {
    console.log("e", e);
    this.setState({
      imgError: true
    });
  };

  returnUri = () => {
    const { image, imgError } = this.state;
    const {
      data: { desc, title }
    } = this.props;
    if (imgError) {
      return require("common/images/img_failed.webp");
    }
    if (image) {
      return { uri: image };
    }
    if (title && title.length) {
      return { uri: title };
    } else {
      return require("common/images/img_failed.webp");
    }
  };

  returnResizeMode = () => {
    const { imgError, image } = this.state;
    const {
      data: { desc }
    } = this.props;
    if (!image) {
      return "contain";
    }
    if (imgError || !desc.placeholder1) {
      return "contain";
    }

    return "center";
  };

  returnWatermark = () => {
    const { countryId } = this.props.store.runtime;
    switch (countryId) {
      case 1:
        return require("common/images/watermark_in.png");
      case 2:
        return require("common/images/watermark_ms.png");
      case 3:
        return require("common/images/watermark_ph.png");
      case 4:
        return require("common/images/watermark_vn.png");
    }
  };

  render() {
    const {
      containerStyle,
      missingId,
      data: { id, desc, errMsg, needModified },
      store: { pageStore },
      t
    } = this.props;
    const { image, imgError, showError } = this.state;
    const uri = this.returnUri();
    // console.log("uri:", uri);
    const resizeMode = this.returnResizeMode();
    const watermark = this.returnWatermark();
    const storageData = (pageStore.useCreditData[id] as unknown) as {
      key: string;
      downloadUrl?: string;
      expires?: string;
    };
    return (
      <View
        style={[
          styles.container,
          { borderRadius: pageStore.newUi ? 8 : 0, paddingBottom: pageStore.newUi ? 2 : 0, overflow: "hidden" },
          containerStyle
        ]}
      >
        {/* <Text style={styles.desc}>{desc.hintInfo}</Text> */}
        <TouchableOpacity style={styles.touch} onPress={this.didSelectedItem}>
          {/*<Image resizeMode={resizeMode} source={uri} style={styles.image} onError={this.onError} />*/}
          <IdImg
            source={uri}
            expires={isObject(storageData) ? storageData.expires : undefined}
            imgkey={isObject(storageData) ? storageData.key : undefined}
            onUpdateUrl={(url, expires) => {
              if (isObject(storageData)) {
                storageData.downloadUrl = url;
                storageData.expires = expires;
                this.props.setValue(storageData as any, this.props.data.id, "file", "");
              }
            }}
            style={styles.image}
            onError={this.onError}
            resizeMode={resizeMode}
          />
          {!!image && !imgError && <Image style={styles.watermark} source={watermark} />}
          {((missingId === id && !image) || showError) && (
            <View style={styles.errorView}>
              <Image style={styles.errorIcon} source={require("../../img/error_icon.webp")} />
              <Text style={styles.errorText}>{!!needModified ? t("请重新上传") : t(errMsg)}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    // marginHorizontal: 16
  },
  touch: {
    backgroundColor: "#FAFAF9",
    overflow: "hidden"
  },
  desc: {
    fontSize: 14,
    color: "#333",
    marginBottom: 12
  },
  image: {
    width: WINDOW_WIDTH - 32,
    height: (168 / 328) * (WINDOW_WIDTH - 32)
  },
  errorView: {
    width: WINDOW_WIDTH - 32,
    flexDirection: "row",
    alignItems: "flex-end",
    position: "absolute",
    bottom: 0,
    paddingLeft: 8,
    paddingVertical: 7,
    backgroundColor: "#FCEAEA"
  },
  errorText: {
    fontSize: 12,
    lineHeight: 12,
    color: "#E62117",
    textAlignVertical: "bottom"
  },
  errorIcon: {
    height: 12,
    width: 12,
    marginRight: 4
  },
  watermark: {
    width: 63,
    height: 63,
    position: "absolute",
    left: 7,
    bottom: 8
  }
});
