import { TFunction } from "i18next";
import React from "react";
import { ItemType } from "../../dict/ComponentType";
import PAGE_COMPONENTS_MAP, { ComponentInfo } from "../../dict/PageComponents";
import { NewEntityInfos } from "../../tool/DataHandling";
import store from "../../store";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { NativeConfigModule } from "common/nativeModules";
import { ENV } from "common/nativeModules/basics/nativeConfigModule";
type Props = {
  data: NewEntityInfos;
  selectItems: NewEntityInfos[];
  allItems: NewEntityInfos[];
  t: TFunction;
  setValue: (value: string, id: number, type: string, key: string) => void;
  containerStyle?: object;
  missingId: number | null;
  isFirstApply: boolean;
  store: pageStoreModel<store>;
  testType: number;
};

interface State {
  componentConfig: ComponentInfo | null | undefined;
}
class DynamicComponent extends React.PureComponent<Props, State> {
  constructor(props: Props) {
    super(props);
    if (props.data.type in ItemType) {
      this.state = {
        componentConfig: PAGE_COMPONENTS_MAP[props.data.type]
      };
    } else {
      this.state = {
        componentConfig: null
      };
    }
  }

  render() {
    const {
      t,
      data,
      setValue,
      containerStyle,
      missingId,
      isFirstApply,
      selectItems,
      store,
      allItems,
      testType
    } = this.props;

    if (!this.state.componentConfig) {
      if (NativeConfigModule.ENV === ENV.production) {
        return null;
      } else {
        // 还是保留此异常，开发环境不会有问题，测试环境报错，说明数据有问题，需要修改活动数据或者停用活动
        throw new Error(`未定义此类型组件: ${JSON.stringify(data)}`);
      }
    }

    const {
      componentConfig: { component: C, extraPropsKeys }
    } = this.state;
    // 公用的 props
    const props: any = {
      t,
      data,
      setValue,
      containerStyle,
      missingId,
      isFirstApply,
      selectItems,
      store,
      allItems,
      testType
    };
    if (extraPropsKeys) {
      extraPropsKeys.forEach((key: string | number) => {
        // @ts-ignore
        props[key] = this.props[key];
      });
    }

    return <C {...props} />;
  }
}
export default React.memo(DynamicComponent);
