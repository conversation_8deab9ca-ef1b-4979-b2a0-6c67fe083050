import React, { Component } from "react";
import { View, Text, DeviceEventEmitter, Image, TouchableOpacity, StyleSheet } from "react-native";
import { FontStyles, NAV_BAR_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { Shadow } from "@akulaku-rn/akui-rn/src/components/NeomorphShadows";
import { observer } from "mobx-react";
import { NewEntityInfos } from "../../tool/DataHandling";
import store from "../../store";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { ItemType } from "../../dict/ComponentType";

type Props = {
  store: pageStoreModel<store>;
  allItems: NewEntityInfos[];
};

type States = {};

const emailList = [
  {
    mailSuffix: "gmail.com",
    icon: require("common/images/credit_input_email_ico_gmail.webp")
  },
  {
    mailSuffix: "yahoo.com",
    icon: require("common/images/credit_input_email_ico_yahoo.webp")
  }
];

@observer
export default class Email extends Component<Props, States> {
  emitter = (email: string) => {
    DeviceEventEmitter.emit("setEmail", email);
    this.props.store.pageStore.showEmailTips = false;
  };

  returnEmail = () => {
    const { store, allItems } = this.props;
    const emailItem = allItems.find((i: NewEntityInfos) => {
      return i.type === ItemType.email;
    });
    if (!emailItem) return null;
    const email = store.pageStore.useCreditData[emailItem.id];
    if (!email) return null;
    const atIndex = email.indexOf("@", 0);
    return email.substring(0, atIndex + 1);
  };

  render() {
    const {
      store: {
        pageStore: { showEmailTips, emailPageY }
      }
    } = this.props;
    if (!showEmailTips) return null;
    const email = this.returnEmail();
    return (
      <View style={[styles.container, { top: emailPageY }]}>
        <Shadow width={WINDOW_WIDTH - 44} height={(WINDOW_WIDTH - 50) / 3.2} style={styles.containerView}>
          {emailList.map((e, i) => (
            <TouchableOpacity
              key={i}
              style={[
                styles.item,
                i !== 0 && {
                  borderColor: "#E6E6E6",
                  borderTopWidth: 0.5
                }
              ]}
              onPress={() => {
                this.emitter(e.mailSuffix);
              }}
            >
              <Text style={styles.itemTitle}>
                {email}
                <Text style={styles.boldTitle}>{e.mailSuffix}</Text>
              </Text>
              <Image style={styles.icon} source={e.icon} />
            </TouchableOpacity>
          ))}
        </Shadow>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    left: 16,
    zIndex: 1,
    overflow: "visible"
  },
  containerView: {
    width: WINDOW_WIDTH - 32,
    height: (WINDOW_WIDTH - 50) / 3.2,
    borderRadius: 8,
    backgroundColor: "#fff",
    shadowOffset: { width: 0, height: 2 },
    shadowColor: "rgba(0,0,0,0.06)",
    shadowRadius: 2,
    shadowOpacity: 1
  },
  item: {
    width: WINDOW_WIDTH - 44,
    flexDirection: "row",
    alignItems: "center",
    height: (WINDOW_WIDTH - 50) / 3.2 / 2,
    paddingLeft: 16,
    paddingRight: 8,
    justifyContent: "space-between"
  },
  itemTitle: {
    fontSize: 14,
    color: "#B3B3B3"
  },
  icon: {
    width: 20,
    height: 20
  },
  boldTitle: {
    color: "#333",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  shadowImg: {
    width: WINDOW_WIDTH - 28,
    height: (WINDOW_WIDTH - 28) / 3.12,
    position: "absolute",
    zIndex: -1,
    left: -8
  }
});
