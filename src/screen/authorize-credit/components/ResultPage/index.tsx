import React, { PureComponent } from "react";
import { View, Image, Text, ScrollView, Linking, TouchableOpacity } from "react-native";
import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import styles from "./styles";
import ADBanner from "common/components/ADGroup/ADBanner";
import { Creatives, ResAD } from "common/components/ADGroup/helpers/types";
import { NativeNavigationModule } from "common/nativeModules";
import { ResultPageSensor, SensorTypeResultPage } from "../../tool/EventTracking";
import { TFunction } from "i18next";
import { isNil } from "lodash";
import NativeEventModule from "common/nativeModules/bussinse/nativeEventModule";
import RecommendList, { RecommendScene } from "common/components/RecommendList";
import { GlobalRuntime } from "common/constant";
import { reportClick, reporter } from "@akulaku-rn/rn-v4-sdk";
import { RecommendItemModel } from "common/components/RecommendListItem/model";
import { CreditChanelType } from "../../dict/PageType";
import { ResultState } from "../../dict/type";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import store from "../../store";

type Props = {
  t: TFunction;
  store: pageStoreModel<store>;
  isFirstApply: boolean;
  gobackNum: number;
  fromOpenPayToUseAkuPay: CreditChanelType; //op站内标识
  callBackUrl: string; //回调地址
};
export default class ResultPage extends PureComponent<Props, ResultState> {
  adBanner1Ref!: ADBanner;

  adBanner2Ref!: ADBanner;

  adBanner3Ref!: ADBanner;

  constructor(props: Props) {
    super(props);
    this.state = {
      dataServer: {},
      loading: true
    };
  }

  getResultFromServer = async () => {
    const {
      store: { pageStore }
    } = this.props;
    const data = await pageStore.getCreditSubmitStatus();
    if (!data) return;
    this.setState({ dataServer: data, loading: false });
  };

  componentDidMount() {
    this.getResultFromServer();
    const { isFirstApply } = this.props;
    reporter.setPageConfig({
      sn: 300131,
      sp: { status: !!isFirstApply ? "first" : "not_first" }
    });
    reporter.enterScreen({
      sn: 300131,
      sp: { status: !!isFirstApply ? "first" : "not_first" }
    });
  }

  componentWillUnmount() {
    this.leavePage();
  }

  leavePage = () => {
    const { isFirstApply } = this.props;
    reporter.leaveScreen({
      sn: 300131,
      sp: { status: !!isFirstApply ? "first" : "not_first" }
    });
  };

  adBannerClick = (adData: ResAD, creatives: Creatives) => {
    const { isFirstApply } = this.props;
    ResultPageSensor.adBannerClick({
      advertisingID: adData.id,
      image_id: creatives.id,
      adPositionid: adData.spotId
    });
    reportClick({
      sn: 300131,
      cn: 3,
      bct: 8,
      sp: {
        status: !!isFirstApply ? "first" : "not_first",
        result: this.state.dataServer.subStatus === 4 ? "failed" : "success"
      },
      bi: { spot_id: adData.spotId, ad_id: adData.id, image_id: creatives.id } as any
    });
  };

  adBannerExpose = (adData: ResAD, creatives: Creatives) => {
    ResultPageSensor.adBannerExpose({
      advertisingID: adData.id,
      image_id: creatives.id,
      adPositionid: adData.spotId
    });
  };

  adBanner1OnLoad = (isLoad: boolean) => {
    !!isLoad && this.adBanner1Ref && this.adBanner1Ref.onExposeHandler();
  };

  adBanner2OnLoad = (isLoad: boolean) => {
    !!isLoad && this.adBanner2Ref && this.adBanner2Ref.onExposeHandler();
  };

  adBanner3OnLoad = (isLoad: boolean) => {
    !!isLoad && this.adBanner3Ref && this.adBanner3Ref.onExposeHandler();
  };

  returnImgAndtitle = () => {
    const { t } = this.props;
    const { subStatus, tipTitle, tipMsg } = this.state.dataServer; //1：白天（30分钟内） 2：白天（超过30分钟） 3：夜间 4：申请超过每天次数上限
    let statusImg = null;
    const title = tipTitle;
    const msg = tipMsg;

    switch (subStatus) {
      case 1:
        statusImg = require("../../img/result1.webp");

        break;
      case 2:
        statusImg = require("../../img/result2.webp");

        break;
      case 3:
        statusImg = require("../../img/result3.webp");

        break;
      case 4:
        statusImg = require("../../img/result4.webp");

        break;
    }
    return { statusImg, title, msg };
  };

  done = () => {
    const { isFirstApply, gobackNum, callBackUrl, fromOpenPayToUseAkuPay } = this.props;
    NativeEventModule.postWeb({
      eventName: "applyCreditEnd",
      extraData: { applySuccess: true }
    });
    SensorTypeResultPage(isFirstApply, this.state.dataServer.subStatus);
    if (fromOpenPayToUseAkuPay === CreditChanelType.OP) {
      //OP渠道过来的返回回调地址进行解码
      NativeNavigationModule.navigate({ url: decodeURIComponent(callBackUrl) });
    } else if (!isNil(gobackNum)) {
      NativeNavigationModule.popTo(parseInt(gobackNum + "") + 1);
    } else {
      NativeNavigationModule.popToHome(1);
    }
  };

  onItemClick = (item: RecommendItemModel, index: number) => {
    if (item.spuId) {
      NativeNavigationModule.navigate({
        url: `ak://m.akulaku.com/1301?id=${item.spuId}&countryCode=${GlobalRuntime.countryCode}&skuId=${item.skuId}`
      });
    }
  };

  _getExtPitInfo = () => {
    return {
      cn: 4,
      sn: 300131
    };
  };

  renderADView = () => {
    const { fromOpenPayToUseAkuPay } = this.props;
    const { adStatus } = this.state.dataServer;
    if (!(fromOpenPayToUseAkuPay === CreditChanelType.OP)) {
      return (
        <>
          <ADBanner
            wrapperStyle={styles.wrapperStyle}
            adGroup={138}
            relatedBizCodes={[{ pageStatus: adStatus || "" }]}
            imageWidth={WINDOW_WIDTH - 32}
            imageHeight={(WINDOW_WIDTH - 32) * (143 / 328)}
            creativesExposeHandler={this.adBannerExpose}
            creativesClickHandler={this.adBannerClick}
            onLoad={this.adBanner1OnLoad}
            ref={(ref: ADBanner) => {
              this.adBanner1Ref = ref;
            }}
          />
          <ADBanner
            wrapperStyle={styles.wrapperStyle}
            adGroup={169}
            relatedBizCodes={[{ pageStatus: adStatus || "" }]}
            imageWidth={WINDOW_WIDTH - 32}
            imageHeight={(WINDOW_WIDTH - 32) * (80 / 328)}
            creativesExposeHandler={this.adBannerExpose}
            creativesClickHandler={this.adBannerClick}
            onLoad={this.adBanner2OnLoad}
            ref={(ref: ADBanner) => {
              this.adBanner2Ref = ref;
            }}
          />
          <ADBanner
            wrapperStyle={styles.wrapperStyle}
            adGroup={170}
            relatedBizCodes={[{ pageStatus: adStatus || "" }]}
            imageWidth={WINDOW_WIDTH - 32}
            imageHeight={(WINDOW_WIDTH - 32) * (80 / 328)}
            creativesExposeHandler={this.adBannerExpose}
            creativesClickHandler={this.adBannerClick}
            onLoad={this.adBanner3OnLoad}
            ref={(ref: ADBanner) => {
              this.adBanner3Ref = ref;
            }}
          />
        </>
      );
    } else {
      return null;
    }
  };

  renderHeader = () => {
    const { t } = this.props;
    const { statusImg, title, msg } = this.returnImgAndtitle();
    return (
      <View style={styles.container}>
        <View style={styles.topView}>
          <Image source={statusImg} style={styles.img} />
        </View>
        <TouchableOpacity
          onPress={this.done}
          style={styles.btnStyle}
          hitSlop={{ top: 20, bottom: 20, left: 50, right: 50 }}
        >
          <Text style={styles.buttonTitle}>{t("完成1")}</Text>
        </TouchableOpacity>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.reason}>{msg}</Text>
        <View style={styles.adView}>{this.renderADView()}</View>
      </View>
    );
  };

  render() {
    const { t } = this.props;
    if (this.state.loading) {
      return null;
    }
    return (
      <RecommendList
        RecommendScene={RecommendScene.AUTHORIZE_CREDIT_RESULT}
        renderHeader={this.renderHeader}
        uniqKey={"authorizeCreditResult"}
        onItemClick={this.onItemClick}
        getExtPitInfo={this._getExtPitInfo}
        title={t("新人低价购")}
      />
    );
  }
}
