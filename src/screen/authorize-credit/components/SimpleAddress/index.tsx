import React, { Component } from "react";
import { Image, View, Text, TouchableOpacity, DeviceEventEmitter } from "react-native";
import styles from "./styles";
import _ from "lodash";
import { EnterLeavePage, SensorTypeClickItem, V4ClickItem } from "../../tool/EventTracking";
import { NewEntityInfos } from "../../tool/DataHandling";
import { TFunction } from "i18next";
import store from "../../store";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import { NativeAkuAddressModule, NativeNavigationModule } from "common/nativeModules";
import { EVENT_SELECT_ADDRESS } from "../../../../constant";
import { ItemType } from "../../dict/ComponentType";
import { observer } from "mobx-react";
import { handleCityAddress } from "../../tool/addressUtils";

type Props = {
  setValue: (text: string, id: number, type: string, key: string) => void;
  data: NewEntityInfos;
  missingId: number | null;
  containerStyle?: object;
  t: TFunction;
  store: pageStoreModel<store>;
  isFirstApply: boolean;
  testType: number;
};

type State = {
  address: string | null;
  showError: boolean;
};
@observer
export default class Address extends Component<Props, State> {
  listener: any;

  constructor(props: Props) {
    super(props);
    const {
      store: { pageStore },
      data: { id, needModified }
    } = props;
    this.state = {
      address: pageStore.useCreditData[id],
      showError: needModified
    };
  }

  setValue = _.debounce(
    async () => {
      const {
        data: { id, type, pageType, editable, title },
        isFirstApply,
        setValue,
        testType
      } = this.props;

      if (!editable) return;
      const { address } = this.state;
      SensorTypeClickItem(pageType, id);
      const v4Data = { entryid: id, type: "address", isFirst: isFirstApply };
      V4ClickItem(v4Data);
      const enterPageData = { pageType, isFirst: isFirstApply, enter: false, testType };
      EnterLeavePage(enterPageData);
      let addressObj = null;
      if (!!address) {
        try {
          addressObj = JSON.parse(address);
        } catch (e) {
          console.log("setValue SimpleAddress  e", e);
          addressObj = null;
        }
      }
      const result = await handleCityAddress(addressObj);
      enterPageData.enter = true;
      EnterLeavePage(enterPageData);
      if (result.success) {
        const newData = this.hungary(result.data);
        setValue(newData, id, "map", "");
        this.setState({ address: newData, showError: false });
      }
    },
    500,
    { leading: true, trailing: false }
  );

  hungary = (data: any) => {
    const newData = {
      ...data
    };
    return JSON.stringify(newData);
  };

  returnAddressText = () => {
    const { address } = this.state;
    let addressObj = null;
    if (!!address) {
      try {
        addressObj = JSON.parse(address);
      } catch (e) {
        console.log("returnAddressText SimpleAddress e", e);
        addressObj = null;
      }
    }
    if (!addressObj) {
      return addressObj;
      // return this.props.data.title;
    } else {
      const { province, city } = addressObj;
      return `${city ? city : ""} ${province ? province : ""}`;
    }
  };

  renderOldAddress = () => {
    const {
      data: { id, title },
      missingId
    } = this.props;
    const { address } = this.state;
    const addressText = this.returnAddressText();
    return (
      <TouchableOpacity
        style={[styles.container, missingId === id && !address && { borderBottomColor: "#e62117" }]}
        onPress={this.setValue}
      >
        <View style={styles.fdView}>
          <Image style={styles.addressIcon} source={require("common/images/credit_input_ico_address.webp")} />

          <Text style={[styles.title, !!address && styles.hasTitle]}>{addressText}</Text>
        </View>
        <Image style={styles.arrowIcon} source={require("../../img/credit_input_ico_arrow.webp")} />
        {!!address && <Text style={styles.placeholderText}>{title}</Text>}
      </TouchableOpacity>
    );
  };

  renderNewAddress = () => {
    const {
      data: { id, title },
      missingId
    } = this.props;
    const { address, showError } = this.state;
    const addressText = this.returnAddressText();
    return (
      <TouchableOpacity
        style={[styles.containerNew, missingId === id && !address && { borderBottomColor: "#e62117" }]}
        onPress={this.setValue}
      >
        <View style={styles.fdView}>
          {!!address && !!addressText ? (
            <Text style={[styles.newTitle, !!address && styles.hasTitle]}>{addressText}</Text>
          ) : null}
        </View>
        <Image style={styles.arrowIconNew} source={require("../../img/credit_arrow_new.webp")} />
        <Text style={!!address && !!addressText ? styles.placeholderTextFouces : styles.placeholderText2}>{title}</Text>
      </TouchableOpacity>
    );
  };

  render() {
    const {
      data: { id, title, needModified, pageType },
      missingId,
      containerStyle,
      t,
      store: { pageStore }
    } = this.props;
    const { address, showError } = this.state;
    if (pageType === 5 && !pageStore.showHideComponent) return null;
    const addressText = this.returnAddressText();
    return (
      <View style={containerStyle}>
        {pageStore.newUi ? this.renderNewAddress() : this.renderOldAddress()}
        {((missingId === id && !address) || showError) && (
          <View style={styles.errorView}>
            <Text style={styles.errorText}>{!!needModified ? t("请修改") : t("此项未填写")}</Text>
          </View>
        )}
      </View>
    );
  }
}
