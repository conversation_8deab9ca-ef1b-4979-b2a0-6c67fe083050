import React, { Component, ComponentClass } from "react";
import {
  View,
  Text,
  StyleSheet,
  Animated,
  DeviceEventEmitter,
  Keyboard,
  TouchableOpacity,
  Platform,
  Image,
  TextInput,
  TextInputProps,
  LayoutChangeEvent
} from "react-native";
import { observer } from "mobx-react";
import { V3V4ClickItemDic } from "../../dict/EventTrackingDictionary";
import { SensorTypeClickItem, V3ClickItem } from "../../tool/EventTracking";
import { NewEntityInfos } from "../../tool/DataHandling";
import { TFunction } from "i18next";
import store from "../../store";
import { NativeConfigModule } from "common/nativeModules";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { HocActionLogInput, HocActionLogInputCompProps } from "@akulaku-rn/rn-v4-sdk";
import { ItemType } from "../../dict/ComponentType";
import { iOS, STATUS_BAR_HEIGHT, UrlImage } from "@akulaku-rn/akui-rn";
import { TitleInfo, getTitleInfo } from "../../dict/GrayRelease";

type Props = {
  store: pageStoreModel<store>;
  setValue: (text: string, id: number, type: any, key: any) => void;
  data: NewEntityInfos;
  isFirstApply: boolean;
  containerStyle?: object;
  missingId: number | null;
  t: TFunction;
  textInputStyle?: object;
  hideCleanButton?: boolean;
};

type States = {
  value: string;
  color: string;
  isfocus: boolean;
  error: boolean;
  caretHidden: boolean;
  isSelfPhone: boolean;
  imgUrl: { url: string; width: number; height: number } | null;
  withAnimated: boolean;
};

interface TextInputRef {
  blur: () => void;
  focus: () => void;
}

type CompProps = TextInputProps & { getRef?: (ref: any) => void; sensitive: boolean };

interface HocInput extends React.ComponentClass<CompProps> {
  new (props: TextInputProps & { sensitive: boolean }): Component<TextInputProps & { sensitive: boolean }> &
    TextInputRef;
}

@observer
export default class AnimatedTextInput extends Component<Props, States> {
  textInputRef: any;

  top: Animated.Value;

  fontSize: Animated.Value;

  keyboardDidHideListener: any;

  listener: any;

  timer: any;

  showEmailTips: boolean;

  isChanged: boolean;

  ActionTextInput: React.ComponentType<HocActionLogInputCompProps>;

  titleInfo: TitleInfo;

  constructor(props: Props) {
    super(props);
    const {
      data: { type, id, fieldName, needModified },
      store: { pageStore }
    } = props;
    const lastValue = pageStore.useCreditData[id];
    this.state = {
      value:
        (type === "iphone" || type === ItemType.id_number) && lastValue
          ? lastValue.replace(/(\d{4})(?=\d)/g, "$1 ")
          : lastValue,
      color: lastValue ? "#999" : "#B3B3B3",
      isfocus: false,
      error: needModified,
      caretHidden: true,
      isSelfPhone: false,
      imgUrl: null,
      withAnimated: true
    };
    this.titleInfo = getTitleInfo(pageStore.newUi);
    this.top = new Animated.Value(lastValue ? this.titleInfo.focus.top : this.titleInfo.blur.top);
    this.fontSize = new Animated.Value(lastValue ? this.titleInfo.focus.fontSize : this.titleInfo.blur.fontSize);
    this.timer = null;
    this.listener = null;
    this.showEmailTips = false;
    this.isChanged = false;
    this.ActionTextInput = this.getActionTextInput();
  }

  async componentDidMount() {
    const {
      setValue,
      data: { id, type, editable }
    } = this.props;
    this.returnCaretHidden();
    if (type === ItemType.email) {
      this.listener = DeviceEventEmitter.addListener("setEmail", email => {
        const newText = this.state.value + email;
        this.setState({ value: newText }, () => {
          this.onEndEditing();
          this.onBlur();
        });
        setValue(newText, id, type, null);
      });
    } else {
      this.listener = DeviceEventEmitter.addListener("setValue", data => {
        if (id === data.id && editable) {
          const newState = { value: data.value };
          // imgUrl 目前主要是OCR 那里的输入框可能传过来的
          if (!!data.imgUrl && this.props.store.pageStore.ocrTextImageUi) {
            Object.assign(newState, { imgUrl: data.imgUrl, withAnimated: false });
            this.closePlaceholderAnimated();
          }
          this.setState(newState, () => {
            this.onEndEditing();
            this.onBlur();
          });
          setValue(data.value, id, type, null);
        }
      });
    }
    this.keyboardDidHideListener = Keyboard.addListener("keyboardDidHide", this.keyboardDidHide);
    this.onEndEditing();
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    this.listener && this.listener.remove();
    this.keyboardDidHideListener && this.keyboardDidHideListener.remove();
  }

  actionLogKey = "";

  keyboardDidHide = () => {
    this.textInputRef && this.textInputRef.blur();
  };

  onFocus = () => {
    const {
      data: { id, pageType }
    } = this.props;
    SensorTypeClickItem(pageType, id);
    V3ClickItem(id);
    this.onFocusAnimation();
  };

  onFocusAnimation = () => {
    if (!this.state.withAnimated) return;
    Animated.parallel([
      Animated.timing(this.top, {
        toValue: this.titleInfo.focus.top,
        duration: 200,
        useNativeDriver: false
      }),
      Animated.timing(this.fontSize, {
        toValue: this.titleInfo.focus.fontSize,
        duration: 200,
        useNativeDriver: false
      })
    ]).start();
    this.setState({
      color: "#999",
      isfocus: true
    });
  };

  closePlaceholderAnimated = () => {
    this.top.setValue(this.titleInfo.focus.top);
    this.fontSize.setValue(this.titleInfo.focus.fontSize);
    this.setState({
      color: "#999"
    });
  };

  onBlur = () => {
    this.setState({ isfocus: false });
    if (!this.state.withAnimated) return;
    if (this.state.value && !!this.state.value.length) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: this.titleInfo.focus.top,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: this.titleInfo.focus.fontSize,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#999"
      });
    } else {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: this.titleInfo.blur.top,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: this.titleInfo.blur.fontSize,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#B3B3B3"
      });
    }
  };

  onChangeText = (text: string) => {
    const {
      setValue,
      data: { id, type }
    } = this.props;
    let nowText = text;
    if (type === ItemType.iphone || type === ItemType.id_number) {
      nowText = nowText.replace(/(\d{4})(?=\d)/g, "$1 ");
    }
    this.isChanged = true;
    this.setState({ value: nowText });
    setValue(nowText, id, type, null);
    if (type === ItemType.email) {
      const spstr = nowText.split("");
      this.showEmailTips = spstr[spstr.length - 1] === "@";
      if (this.showEmailTips) {
        Keyboard.dismiss();
        setTimeout(() => {
          this.props.store.pageStore.showEmailTips = true;
        }, 500);
      } else {
        this.props.store.pageStore.showEmailTips = false;
      }
    }
  };

  onEndEditing = () => {
    const {
      data: { type, regex, needModified, fieldName },
      store: { pageStore }
    } = this.props;
    let text = this.state.value;
    if (type === ItemType.iphone || type === ItemType.id_number) {
      while (text && text.indexOf(" ") >= 0) {
        // 电话号码 去掉空格
        text = text.replace(" ", "");
      }
    }
    let error = false;
    if (!!regex && !!text) {
      const reg = new RegExp(regex);
      error = !reg.test(text);
    }
    if (needModified && !this.isChanged) {
      error = true;
    }
    if (type === ItemType.email && this.showEmailTips) return;
    // 对比手机号后4位 即可
    if (
      !!text &&
      text.length === pageStore.phoneNumber.length &&
      text.substring(text.length - 4) === pageStore.phoneNumber.substring(pageStore.phoneNumber.length - 4) &&
      (fieldName === "phoneNumber" || fieldName === "emergencyContactNumber")
    ) {
      this.setState({ isSelfPhone: true });
    } else {
      this.setState({ isSelfPhone: false });
    }
    this.setState({ error });
  };

  clickText = () => {
    if (!this.props.data.editable) return;
    this.textInputRef.focus();
  };

  onLayout = () => {
    if (this.props.data.type !== ItemType.email) return;
    this.timer = setInterval(() => {
      !!this.textInputRef &&
        this.textInputRef.measure((x: any, y: any, width: any, height: any, pageX: number, pageY: any) => {
          const {
            store: { pageStore }
          } = this.props;
          // 这里是ui库NavigationBar的高度
          const navHeight = iOS ? STATUS_BAR_HEIGHT + 50 : STATUS_BAR_HEIGHT + 56;
          // 18是TextInput的高度
          pageStore.emailPageY = pageY - navHeight + height + 10;
        });
    }, 500);
  };

  returnV4Dic = () => {
    const {
      data: { id },
      isFirstApply
    } = this.props;
    const dic = V3V4ClickItemDic[id];
    if (!!dic && !!dic.v4Dic) {
      delete dic.v4Dic.controlAction;
      dic.v4Dic.sp = { status: isFirstApply ? "first" : "not_first" };
      return dic.v4Dic;
    } else {
      return { sn: 123456789, cn: 123456789 };
    }
  };

  editableToast = () => {
    const {
      data: { kycStatus, editable },
      t
    } = this.props;
    if (!editable) {
      if (kycStatus) {
        NativeToast.showMessage(t("信息已验证，不可修改"));
      } else {
        NativeToast.showMessage(t("抱歉，该信息暂不可修改"));
      }
    }
  };

  returnCaretHidden = async () => {
    const {
      data: { deviceBrand, osVersionCode }
    } = await NativeConfigModule.getEnvInfoSilent();
    if (deviceBrand === "Xiaomi" && osVersionCode === 29) {
      this.setState({ caretHidden: true });
    } else {
      this.setState({ caretHidden: false });
    }
  };

  returnErrorText = () => {
    const {
      t,
      data: { needModified, errMsg }
    } = this.props;
    const { error } = this.state;
    if (this.state.isSelfPhone) return t("联系人手机号不能与本人相同");

    let errorText = "";
    if (error) {
      if (!!errMsg) {
        errorText = t(errMsg);
      } else {
        errorText = t("格式不正确");
      }
      if (needModified) {
        errorText = t("请修改");
      }
    } else {
      errorText = t("此项未填写");
    }
    return errorText;
  };

  getActionTextInput = () => {
    if (!this.ActionTextInput) {
      this.ActionTextInput = HocActionLogInput(this.returnV4Dic())(TextInput);
    }
    return this.ActionTextInput;
  };

  _onPressClean = () => {
    this.onChangeText("");
    this.onBlur();
    this.setState({ error: false });
    // this.onChangeText("");
    // this.textInputRef && this.textInputRef.clear();
  };

  getUrlImageShowWidth = (showHeight: number, oriHeight: number, oriWidth: number) => {
    const showWidth = oriWidth * (showHeight / oriHeight);
    if (!!this.inputWidth && showWidth >= this.inputWidth) return this.inputWidth;
    return showWidth;
  };

  inputWidth = 0;

  onInputLayout = (event: LayoutChangeEvent) => {
    if (event.nativeEvent.layout.width !== this.inputWidth) {
      this.inputWidth = event.nativeEvent.layout.width;
    }
  };

  render() {
    const {
      containerStyle,
      textInputStyle,
      data: { id, title, type, editable, sensitive, pageType },
      missingId,
      store: { pageStore, navParams },
      hideCleanButton
    } = this.props;
    const { value, isfocus, error, caretHidden, isSelfPhone } = this.state;
    //这里是因为在证件照页面，需要等图片选择完毕再显示 这些类型
    if (pageType === 5 && !pageStore.showHideComponent) return null;
    const ActionTextInput = this.ActionTextInput;
    if (!editable) {
      return (
        <TouchableOpacity onPress={this.editableToast} style={containerStyle} onLayout={this.onLayout}>
          <View style={[pageStore.newUi ? styles.newContainerDisable : styles.container]}>
            <Text
              style={[
                pageStore.newUi ? styles.newTextInput : styles.textInput,
                textInputStyle,
                { color: pageStore.newUi ? "#989FA9" : "#999" }
              ]}
            >
              {value}
            </Text>
            <Animated.View
              style={[
                styles.placeholderView,
                {
                  top: this.top,
                  left: pageStore.newUi ? 15 : 0
                }
              ]}
            >
              <Animated.Text
                style={[
                  pageStore.newUi ? styles.newPlaceholderText : styles.placeholderText,
                  { fontSize: this.fontSize, color: pageStore.newUi ? "#989FA9" : this.state.color }
                ]}
              >
                {title}
              </Animated.Text>
            </Animated.View>
          </View>
        </TouchableOpacity>
      );
    }
    const newContainerStyle = [
      styles.newContainer,
      isfocus && styles.newFocus,
      ((missingId === id && !value) || error) && { borderColor: "#F32823" }
    ];
    const oldContainerStyle = [
      styles.container,
      isfocus && { borderBottomColor: "#666", borderBottomWidth: 1 },
      ((missingId === id && !value) || error) && { borderBottomColor: "#e62117" }
    ];
    return (
      <View style={containerStyle} onLayout={this.onLayout}>
        <View style={pageStore.newUi ? newContainerStyle : oldContainerStyle}>
          {!!this.state.imgUrl && (
            <UrlImage
              resizeMode={"contain"}
              source={this.state.imgUrl.url}
              style={{
                height: 16,
                width: this.getUrlImageShowWidth(16, this.state.imgUrl.height, this.state.imgUrl.width),
                alignSelf: "flex-start",
                marginTop: 3,
                marginBottom: 6
              }}
            />
          )}
          <View onLayout={this.onInputLayout}>
            <ActionTextInput
              screen={navParams.screen}
              getRef={(textInputRef: any) => {
                this.textInputRef = textInputRef;
              }}
              caretHidden={caretHidden}
              multiline={true}
              onEndEditing={this.onEndEditing}
              selectionColor={"#e62117"}
              style={[pageStore.newUi ? styles.newTextInput : styles.textInput, textInputStyle]}
              maxLength={100}
              underlineColorAndroid="transparent"
              onChangeText={this.onChangeText}
              onFocus={this.onFocus}
              onBlur={this.onBlur}
              value={value}
              sensitive={sensitive}
              hitSlop={{ top: 10, bottom: 10 }}
              keyboardType={type && (type === "number" || type === "iphone") ? "numeric" : "default"}
            />
          </View>
          <Animated.View
            style={[
              styles.placeholderView,
              {
                top: this.top,
                left: pageStore.newUi ? 15 : 0
              }
            ]}
          >
            <Animated.Text
              onPress={this.clickText}
              style={[
                styles.placeholderText,
                { fontSize: this.fontSize, color: pageStore.newUi ? "#989FA9" : this.state.color }
              ]}
            >
              {title}
            </Animated.Text>
          </Animated.View>
          {pageStore.newUi && !hideCleanButton && !!value && isfocus ? (
            <TouchableOpacity onPress={this._onPressClean} style={styles.deleteContainer}>
              <Image style={styles.deleteIcon} source={require("../../img/credit_company_delete.webp")} />
            </TouchableOpacity>
          ) : null}
        </View>
        {((missingId === id && !value) || error || isSelfPhone) && (
          <View style={styles.errorView}>
            {pageStore.newUi ? (
              <Image
                source={require("../../img/credit_error_new.png")}
                style={{ width: 16, height: 16, marginLeft: 15, marginRight: 6 }}
              />
            ) : null}
            <Text style={pageStore.newUi ? styles.newErrorText : styles.errorText}>{this.returnErrorText()}</Text>
          </View>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingBottom: 10,
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    paddingTop: 24,
    paddingRight: 12
  },
  newContainerDisable: {
    ...Platform.select({
      ios: { paddingTop: 28 },
      android: { paddingTop: 28 }
    }),
    paddingRight: 12,
    borderWidth: 1,
    borderColor: "#E2E5E9",
    paddingHorizontal: 15,
    borderRadius: 6,
    minHeight: 60
  },
  newContainer: {
    ...Platform.select({
      ios: { paddingTop: 24 },
      android: { paddingTop: 28 }
    }),
    paddingRight: 12,
    borderWidth: 1,
    borderColor: "#E2E5E9",
    paddingHorizontal: 15,
    borderRadius: 6,
    minHeight: 60
  },
  newFocus: {
    borderWidth: 1,
    borderColor: "#282B2E"
  },
  placeholderView: { position: "absolute", zIndex: 2 },
  placeholderText: {
    lineHeight: 18,
    height: 18,
    textAlignVertical: "center"
  },
  newPlaceholderText: {
    textAlignVertical: "center"
  },
  textInput: {
    padding: 0,
    fontSize: 14,
    textAlign: "left",
    textAlignVertical: "top",
    color: "#333",
    ...Platform.select({
      ios: { lineHeight: 13 }
    }),

    height: 18,
    paddingRight: 30
  },
  newTextInput: {
    padding: 0,
    fontSize: 16,
    textAlign: "left",
    textAlignVertical: "top",
    color: "#282B2E",
    ...Platform.select({
      ios: { lineHeight: 20 },
      android: { lineHeight: 20 }
    }),
    paddingRight: 30
  },
  errorView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8
  },
  errorText: {
    fontSize: 14,
    color: "#e62117"
  },
  newErrorText: {
    fontSize: 12,
    color: "#F32823"
  },
  errorIcon: {
    height: 12,
    width: 12,
    marginRight: 4
  },
  deleteContainer: {
    position: "absolute",
    right: 12,
    bottom: 10,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "flex-end"
  },
  deleteIcon: {
    width: 20,
    height: 20
  }
});
