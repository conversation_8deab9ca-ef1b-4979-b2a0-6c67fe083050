/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-01 21:05
 * @description
 */

import React, { useEffect, useRef, useState } from "react";
import { View, StyleSheet } from "react-native";
import { AKButton, Loading, PopUpContainer } from "@akulaku-rn/akui-rn";
import { scale } from "common/util/P2D";
import { AKButtonType } from "common/components/AKButton";
import { HOCLoadTranslation } from "common/services/i18n";
import { TFunction } from "i18next";
import WebView, { WebViewMessageEvent } from "react-native-webview";

type Props = {
  forceRunOver?: boolean; // 强制浏览完成
  // onClickCancel: () => void;
  onClickScrollBottom?: () => void;
  onConfirm?: () => void;
  agreementUrl: string;
  t: TFunction;
  onMounted?: () => void;
};

enum BtnStatus {
  ScrollToBottom = "滑动到底部",
  ConfirmAgreement = "我已阅读并同意"
}

const CreditAgreementRunOverDialog = HOCLoadTranslation([
  "InstallmentAgreementRunOverDialog",
  {
    en: require("./i18n/en.json"),
    in: require("./i18n/in.json")
  }
])(
  React.memo(function CreditAgreementRunOverDialogComp({
    t,
    forceRunOver,
    onClickScrollBottom,
    agreementUrl,
    onConfirm,
    onMounted
  }: Props) {
    const [buttonText, setButtonText] = useState(forceRunOver ? BtnStatus.ScrollToBottom : BtnStatus.ConfirmAgreement);
    const webViewRef = useRef<WebView>(null);
    const [loading, setLoading] = useState(true); // 加载状态
    const [webError, setWebError] = useState(false); // 加载状态

    useEffect(() => {
      onMounted && onMounted();
    }, []);

    const onClickBtn = () => {
      if (buttonText === BtnStatus.ScrollToBottom) {
        onClickScrollBottom && onClickScrollBottom();
        scrollToBottom();
      } else {
        !!onConfirm && onConfirm();
        PopUpContainer.dismiss();
      }
    };

    const scrollToBottom = () => {
      // 调用 WebView 中的滚动到底部方法
      !!webViewRef.current &&
        webViewRef.current.injectJavaScript(`
      window.scrollTo(0, document.body.scrollHeight);
      true; // 必须返回 true 以防止 WebView 中断
    `);
      setButtonText(BtnStatus.ConfirmAgreement);
    };

    const handleMessage = (event: WebViewMessageEvent) => {
      // WebView 发送的消息，更新按钮文本
      const { data } = event.nativeEvent;
      if (data === "scrolledToBottom") {
        setButtonText(BtnStatus.ConfirmAgreement);
      }
    };

    const initInjectJavaScript = `
          // 监听滚动事件
          document.addEventListener('scroll', () => {
            const bottomReached =
              document.body.scrollHeight - window.innerHeight - window.scrollY < 10;
            if (bottomReached) {
              window.ReactNativeWebView.postMessage('scrolledToBottom');
            }
          });
          true; // 必须返回 true
        `;

    return (
      <View style={style.container}>
        <View style={{ flex: 1 }}>
          <WebView
            onLoadStart={() => {
              console.log("onLoadStart");
            }}
            onLoadEnd={() => {
              setLoading(false);
              console.log("onLoadEnd");
            }} // 加载完成后隐藏 Loading
            onError={() => {
              setWebError(true);
              console.log("onError");
            }}
            onHttpError={() => {
              setWebError(true);
              console.log("onError");
            }}
            ref={webViewRef}
            source={{ uri: agreementUrl }}
            onMessage={handleMessage} // 接收来自 WebView 的消息
            injectedJavaScript={initInjectJavaScript}
            originWhitelist={["*"]}
          />
          {loading && (
            <Loading
              containerStyle={{
                height: "100%",
                width: "100%",
                backgroundColor: "transparent",
                justifyContent: "center"
              }}
            />
          )}
        </View>
        <View style={{ padding: 12, paddingTop: 8, backgroundColor: "white" }}>
          <AKButton
            disabled={loading || webError}
            type={AKButtonType.B1_1_2}
            text={t(buttonText)}
            onPress={onClickBtn}
          />
        </View>
      </View>
    );
  })
);

export default CreditAgreementRunOverDialog;

const style = StyleSheet.create({
  text: {
    color: "#282B2E",
    fontSize: scale(14)
  },
  container: {
    height: PopUpContainer.MAX_CONTENT_HEIGHT
  }
});
