/**
 * Create by z<PERSON><PERSON><PERSON> on 2023-12 11:24
 * @description
 */

import React, { PropsWithChildren, useEffect } from "react";
import { View, StyleSheet, BackHandler } from "react-native";

type Props = {
  backFunc: () => boolean;
};

const BackListenerComp = (props: PropsWithChildren<Props>) => {
  useEffect(() => {
    const backHandlerListener = BackHandler.addEventListener("hardwareBackPress", props.backFunc);
    return () => {
      !!backHandlerListener && backHandlerListener.remove();
    };
  }, []);
  return <>{props.children}</>;
};

export default BackListenerComp;

const styles = StyleSheet.create({});
