import { action, observable } from "mobx";
import api from "./api";
import { Loading } from "@akulaku-rn/akui-rn";
import _ from "lodash";
import { NativeNetworkModuleV2, NativeUserInfoModule } from "common/nativeModules";
import Basic from "common/store/Basic";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { getConfig } from "common/commonConfig";
import ProgressInfosNodeItem from "../dict/ProgressInfosNodeItem";
import DataProcess from "../tool/DataProcess";

export default class Store extends Basic {
  // 请求状态

  @observable useCreditData: Record<number, string> = {};

  @observable sumbitLoading = false;

  @observable emailPageY = 0;

  @observable email = null;

  @observable showEmailTips = false;

  @observable nowStep = null;

  @observable incomplete: Array<any> = [];

  @observable asiApplicationId = "";

  @observable providentTipShow = false;

  @observable showHideComponent = false;

  //是否勾选社保协议
  @observable isSelectSocialProtocol = false;

  //是否显示返回按钮
  @observable showBackBtn = true;

  //居住地址是否 复用 ID地址信息
  @observable isReuseAddress = true;

  hadShowFirstAgreementDialog = false;

  //1：正常流程 2：合规流程 3: 合规2.0
  version = 1;

  //是否是新UI
  newUi = false;

  //是否展示授信申请钱的协议弹窗
  //https://www.tapd.cn/tapd_fe/62937117/story/detail/1162937117001281476
  contract = false;

  progressInfosNodeObj?: ProgressInfosNodeItem;

  //是否走新地址组件
  newAddressSubassembly = false;

  // OCR接口是否返回图片信息
  ocrTextImageUi = false;

  phoneNumber = "";

  //是否已经设置Pin码
  havePaymentPsw = true;

  //是否通过kyc状态
  kycPassed = false;

  //是否启动PIN码判断逻辑
  paymentPwdSwitch = true;

  //特殊资料项，可选项被其他资料项控制
  displayOptions: { [index: number]: number[] } = {};

  complianceConfig: { [key: string]: string | boolean } = {
    link: "",
    authCreditPrivacyPolicyLink: "",
    IN: "",
    EN: "",
    is_new: false
  };

  // 授信想要区分配置, 使用新配置4031
  @action("合规演示需要修改部分UI和协议，通过配置中心来配置是否开启")
  getConfigCompliance = async () => {
    const res = await getConfig(4031);
    if (!!res) {
      this.complianceConfig = res;
    }
  };

  @action
  post = async (url: string, params: object, requestSuccess: any, requestFailure?: any) => {
    this.io.POST(
      url,
      params,
      (response: any) => {
        requestSuccess(response);
      },
      (error: any) => {
        requestFailure(error);
        Loading.dismiss();
      }
    );
  };

  @action
  uploadImages = async (waitImgs: Array<any>) => {
    const entryIds: any[] = [];
    waitImgs.map(i => {
      entryIds.push(i.entryId);
    });
    let data: any = null;
    Loading.show({ hasBackcolor: true });
    try {
      const response = await this.io.post(api.UPLOAD_URL, { busiType: 10, cnt: entryIds.length });
      response.data.map((i: { url: any; key: any }, k: any) => {
        waitImgs[k].url = i.url;
        waitImgs[k].key = i.key;
      });
      const uploadSuccess = await this.uploadFilesToUrls(waitImgs);
      if (uploadSuccess) {
        data = {
          success: true,
          data: waitImgs
        };
      } else {
        data = {
          success: false
        };
      }
    } catch (e) {
      data = {
        success: false
      };
    } finally {
      Loading.dismiss();
      return data;
    }
  };

  uploadFilesToUrls = async (waitImgs: any) => {
    const newWaitImgs = JSON.parse(JSON.stringify(waitImgs));
    newWaitImgs.map((i: any) => {
      delete i.entryId;
      delete i.key;
    });
    const response = await NativeNetworkModuleV2.uploadFilesToUrls(newWaitImgs);
    return response.success;
  };

  @action
  dataSubmit = (data: { pageNo: number; entries: any[]; asiApplicationId: string }, callback: () => void) => {
    Loading.show({ hasBackcolor: true });
    DataProcess.preSubmitProcess(data.entries);
    this.sumbitLoading = true;
    this.post(
      api.CREDIT_SUBMIT,
      data,
      (response: any) => {
        const { success, errMsg } = response;
        if (success) {
          Loading.dismiss();
          callback();
        } else {
          Loading.dismiss();
          NativeToast.showMessage(errMsg);
        }
        this.sumbitLoading = false;
      },
      (error: any) => {
        Loading.dismiss();
        const { errMsg } = error;
        NativeToast.showMessage(errMsg);
        this.sumbitLoading = false;
      }
    );
  };

  //获取PIN码信息
  @action
  getErrorInfo = async (data: object) => {
    const result = [];
    try {
      const response = await this.io.post(api.CREDIT_SUBMIT_PRE_VERIFY, data);
      if (response.success) {
        for (const key in response.data) {
          if (Object.prototype.hasOwnProperty.call(response.data, key)) {
            const element = response.data[key];
            if (element === false) {
              result.push(key);
            }
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
    return result;
  };

  //获取PIN码信息
  @action
  getPinCodeStatus = () => {
    this.post(
      api.GET_PIN_CODE_STATUS,
      {},
      (response: any) => {
        const { success, errMsg, data } = response;
        if (success && data) {
          const { kycPassed, havePaymentPsw, paymentPwdSwitch } = data;
          this.havePaymentPsw = havePaymentPsw;
          this.kycPassed = kycPassed;
          this.paymentPwdSwitch = paymentPwdSwitch;
        }
      },
      (error: any) => {}
    );
  };

  getPhoneNumber = async () => {
    const { data } = await NativeUserInfoModule.getUserInfo();
    if (!data) return;
    this.phoneNumber = data.phoneNumber;
  };

  /**
   * 授信意图收集
   */
  postCreditIntention = (intentionType: number) => {
    this.post(
      api.CREDIT_INTENTION_COLLECT,
      { intentionType },
      (response: any) => {},
      (error: any) => {}
    );
  };

  /**
   * 获取授信提交状态
   */
  getCreditSubmitStatus = async () => {
    Loading.show();
    try {
      const response = await this.io.post(api.CREDIT_SUBMIT_STATUS, {});
      Loading.dismiss();
      return response.data;
    } catch (error) {
      Loading.dismiss();
    }
  };

  getConfig1025 = async () => {
    const bncConfig = await getConfig(1025);
    // showSkipBtn 为true则 显示 返回按钮，showSkipBtn为false则不显示
    if (bncConfig) {
      this.showBackBtn = bncConfig.showSkipBtn;
    }
  };
}
