/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/select-address/select-province/index.tsx
 */

import React, { Component } from "react";
import { inject, observer } from "mobx-react";
import { FlatList, Text, TouchableOpacity, View, StyleSheet, Image } from "react-native";
import _, { isNil } from "lodash";

import { NavigationBar } from "@akulaku-rn/akui-rn";

import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import store, { Province } from "../store";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { TFunction } from "i18next";
import { configSensorEvent } from "common/components/BaseContainer/Type";
import SelectAreaList, { AreaType } from "../components/SelectAreaList";

type Props = {
  navigation: navigationModel;
  store: pageStoreModel<store>;
  t: TFunction;
  configSensorEvent: configSensorEvent;
};

type States = {
  index: number;
};

@RootView({
  withI18n: [
    "SelectProvince",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json")
    }
  ],
  store,
  keyApi: ["/capi/credit/account/process"]
})
@withTranslation("SelectProvince")
@inject("store")
@observer
export default class SelectProvince extends Component<Props, States> {
  render() {
    const {
      t,
      store: { pageStore }
    } = this.props;
    return (
      <View style={{ flex: 1 }}>
        <NavigationBar title={t("选择你的省")} />
        <SelectAreaList type={AreaType.PROVINCE} />
      </View>
    );
  }
}
