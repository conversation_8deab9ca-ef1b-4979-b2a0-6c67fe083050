/*
 * @LastEditors: zou yu
 * @Description: 省市选择器 （原生的地址选择器不满足要求，但是这个需求【【授信模块】授信合规改造-个人信息&职业信息】如果不想依赖原生版本）
 * @FilePath: /akulaku-ec-user-in/src/screen/select-address/index.tsx
 */

import React, { Component } from "react";
import { inject, observer } from "mobx-react";
import {
  DeviceEventEmitter,
  EmitterSubscription,
  FlatList,
  ScrollView,
  StyleSheet,
  View,
  ViewabilityConfigCallbackPairs
} from "react-native";
import _, { isNil } from "lodash";

import { AKButton, iOS, Loading, NavigationBar, NetworkErrorComponent, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import store from "./store";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { TFunction } from "i18next";
import { configSensorEvent } from "common/components/BaseContainer/Type";
import AddressCell from "./components/AddressCell";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { EVENT_SELECT_PROVINCE, EVENT_SELECT_CITY, EVENT_SELECT_ADDRESS } from "../../constant";

interface AddressInfo {
  province: string;
  provinceId: string;
  city: string;
  cityId: string;
}

type Props = {
  navigation: navigationModel;
  store: {
    pageStore: store;
    navParams: {
      addressInfo?: AddressInfo;
    };
  };
  t: TFunction;
  configSensorEvent: configSensorEvent;
};

type States = {
  index: number;
};

@RootView({
  withI18n: [
    "SelectAddress",
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json"),
      vi: require("./i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SelectAddress")
@inject("store")
@observer
export default class SelectAddress extends Component<Props, States> {
  provinceListener?: EmitterSubscription;

  cityListener?: EmitterSubscription;

  constructor(props: Props) {
    super(props);
    const {
      store: { pageStore, navParams }
    } = props;
    if (navParams && navParams.addressInfo) {
      const { province, provinceId, city, cityId } = navParams.addressInfo;
      if (province && provinceId) {
        pageStore.toSelectProvince({ province, provinceId });
      }
      if (city && cityId) {
        pageStore.toSelectCity({ city, cityId });
      }
    }
    this.provinceListener = DeviceEventEmitter.addListener(
      EVENT_SELECT_PROVINCE,
      ({ province, provinceId }: { province: string; provinceId: string }) => {
        this.props.store.pageStore.toSelectProvince({ province, provinceId });
      }
    );

    this.cityListener = DeviceEventEmitter.addListener(
      EVENT_SELECT_CITY,
      ({ city, cityId }: { city: string; cityId: string }) => {
        this.props.store.pageStore.toSelectCity({ city, cityId });
      }
    );
  }

  componentWillUnmount(): void {
    this.provinceListener && this.provinceListener.remove();
    this.cityListener && this.cityListener.remove();
  }

  _renderItem = ({ item, index }: { item: string; index: number }) => {
    return <View></View>;
  };

  _onPressProvince = () => {
    this.props.navigation.navigate({ screen: "SelectProvince" });
  };

  _onPressCity = () => {
    const {
      t,
      store: { navParams, pageStore }
    } = this.props;
    if (!pageStore.selectProvince) {
      this._onPressProvince();
      return;
    }
    this.props.navigation.navigate({ screen: "SelectCity", params: pageStore.selectProvince });
  };

  _onBackPress = () => {
    this.props.navigation.goBack();
    DeviceEventEmitter.emit(EVENT_SELECT_ADDRESS, { success: false, data: null });
  };

  render() {
    const {
      t,
      store: { navParams, pageStore }
    } = this.props;

    return (
      <View style={{ flex: 1, backgroundColor: "#fff" }}>
        <NavigationBar title={t("选择你的地址")} onBackPress={this._onBackPress} />
        <ScrollView>
          <AddressCell
            placeholder={"Province"}
            value={pageStore.selectProvince?.province}
            onPress={this._onPressProvince}
          />
          <AddressCell placeholder={"City"} value={pageStore.selectCity?.city} onPress={this._onPressCity} />
        </ScrollView>

        <View style={styles.container}>
          <AKButton
            disabled={!pageStore.canSave}
            style={{ width: "100%" }}
            type={AKButtonType.B1_1_2}
            text={"Save"}
            onPress={() => {
              if (!pageStore.selectCity || !pageStore.selectProvince) return;
              DeviceEventEmitter.emit(EVENT_SELECT_ADDRESS, {
                success: true,
                data: { ...pageStore.selectCity, ...pageStore.selectProvince }
              });
              this.props.navigation.goBack();
            }}
          />
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
    backgroundColor: "#fff"
  }
});
