/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/select-address/select-city/index.tsx
 */

import React, { Component } from "react";
import { inject, observer } from "mobx-react";
import { FlatList, StatusBar, View, ViewabilityConfigCallbackPairs } from "react-native";
import _, { isNil } from "lodash";

import { iOS, Loading, NavigationBar, NetworkErrorComponent, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import store from "../store";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";

import { getAdList } from "common/components/ADGroup/helpers";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { TFunction } from "i18next";
import { configSensorEvent } from "common/components/BaseContainer/Type";
import NativeEventModule from "common/nativeModules/bussinse/nativeEventModule";
import { PopupExposerHelper } from "@akulaku-rn/rn-v4-sdk";
import PayPasswordModule, { PinSceneEnum } from "common/nativeModules/bussinse/PayPasswordModule";
import SelectAreaList, { AreaType } from "../components/SelectAreaList";

type Props = {
  navigation: navigationModel;
  store: {
    pageStore: store;
    navParams: {
      provinceId: string;
    };
  };
  t: TFunction;
  configSensorEvent: configSensorEvent;
};

type States = {
  index: number;
};

@RootView({
  withI18n: [
    "SelectCity",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json")
    }
  ],
  store,
  keyApi: ["/capi/credit/account/process"]
})
@withTranslation("SelectCity")
@inject("store")
@observer
export default class SelectCity extends Component<Props, States> {
  render() {
    const {
      t,
      store: { navParams }
    } = this.props;

    return (
      <View style={{ flex: 1 }}>
        <NavigationBar title={t("选择你的市")} />
        <SelectAreaList type={AreaType.CITY} provinceId={navParams.provinceId} />
      </View>
    );
  }
}
