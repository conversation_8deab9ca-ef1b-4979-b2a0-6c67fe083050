/*
 * @LastEditors: zou yu
 * @Description: 社保测试页store
 * @FilePath: /akulaku-ec-user-in/src/screen/select-address/store/index.ts
 */

import { action, computed, observable } from "mobx";
import Basic from "common/store/Basic";

export interface Province {
  province: string;
  provinceId: string;
}

export interface City {
  city: string;
  cityId: string;
}
export default class Store extends Basic {
  @observable isLoading = true;

  @observable isError = false;

  @observable selectProvince?: Province;

  @observable selectCity?: City;

  @computed
  get canSave() {
    return this.selectProvince && this.selectCity ? true : false;
  }

  @action
  toSelectProvince = (province: Province) => {
    this.selectProvince = province;
  };

  @action
  toSelectCity = (city: City) => {
    this.selectCity = city;
  };

  @action
  updateIsError = (isError: boolean) => {
    if (this.isError === isError) return;
    this.isError = isError;
  };
}
