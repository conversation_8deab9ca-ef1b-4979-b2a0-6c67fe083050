/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/select-address/components/SelectAreaList.tsx
 */

import { FlatList, StyleSheet, Text, TouchableOpacity, Image, DeviceEventEmitter, View } from "react-native";
import React, { useEffect, useState } from "react";
import { NativeNavigationModule, NativeNetworkModuleV2 } from "@akulaku-rn/akulaku-ec-common/src/nativeModules";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { CountryID } from "@akulaku-rn/akulaku-ec-common/src/components/BaseContainer/Type";
import { EVENT_SELECT_CITY, EVENT_SELECT_PROVINCE } from "../../../constant";

export enum AreaType {
  PROVINCE = "PROVINCE",
  CITY = "CITY"
}

interface AreaI {
  name: string;
  id: string;
}

const CONFIG = {
  PROVINCE: "/capi/personal/public/common/address/provinces",
  CITY: "/capi/personal/public/common/address/cities"
};
interface Props {
  type: AreaType;
  provinceId?: string;
}

const SelectAreaList = ({ type, provinceId }: Props) => {
  const [data, setData] = useState<AreaI[]>();

  const [selectItem, setSelectItem] = useState<AreaI>();

  useEffect(() => {
    getAreaData();
  }, []);

  const getParmas = () => {
    if (type === AreaType.PROVINCE) {
      return { countryId: CountryID.ID };
    } else if (type === AreaType.CITY) {
      return { provinceId: provinceId };
    }
    return {};
  };

  const getAreaData = async () => {
    try {
      const response = await NativeNetworkModuleV2.post(CONFIG[type], getParmas());
      const { success, data, errMsg } = response;
      if (success) {
        setData(data);
      } else {
        NativeToast.showMessage(errMsg);
      }
    } catch (e) {
      console.log(e);
      // NativeToast.showMessage(t("邮件发送失败请重试"));
    } finally {
    }
  };

  const _renderItem = ({ item, index }: { item: AreaI; index: number }) => {
    const isSelected = selectItem ? item.id === selectItem.id : false;
    return (
      <TouchableOpacity
        onPress={() => {
          setSelectItem(item);
          if (type === AreaType.PROVINCE) {
            NativeNavigationModule.navigate({
              screen: "SelectCity",
              params: { province: item.name, provinceId: item.id }
            });
            DeviceEventEmitter.emit(EVENT_SELECT_PROVINCE, { province: item.name, provinceId: item.id });
          } else if (type === AreaType.CITY) {
            NativeNavigationModule.popTo(2);
            DeviceEventEmitter.emit(EVENT_SELECT_CITY, { city: item.name, cityId: item.id });
          }
        }}
        style={styles.button}
      >
        <Text style={[styles.title, isSelected && { color: "#E62117" }]}>{item.name}</Text>
        {isSelected && <Image style={styles.choose} source={require("../img/choose_icon.png")} />}
        <View style={styles.sep} />
      </TouchableOpacity>
    );
  };

  return <FlatList data={data} renderItem={_renderItem} extraData={selectItem} />;
};

export default SelectAreaList;

const styles = StyleSheet.create({
  button: {
    flexDirection: "row",
    paddingHorizontal: 16,
    justifyContent: "space-between",
    height: 50,
    alignItems: "center",
    width: "100%",

    backgroundColor: "#fff"
  },
  title: {
    fontSize: 14,
    color: "#333"
  },
  sep: {
    height: 0.5,
    backgroundColor: "#D9D9D9",
    position: "absolute",
    left: 16,
    right: 0,
    bottom: 0
  },
  choose: { width: 24, height: 24, marginRight: 12 }
});
