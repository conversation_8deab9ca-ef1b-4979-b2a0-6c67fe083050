/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/select-address/components/AddressCell.tsx
 */
import { StyleSheet, Text, View, Image, TextStyle, StyleProp, TouchableOpacity } from "react-native";
import React from "react";

interface Props {
  placeholder: string;
  value?: string;
  onPress?: () => void;
}

const useTitle = (placeholder: string, value?: string) => {
  if (value && value.length) {
    return { fontSize: 10, top: 20, left: 16 };
  }
  return { fontSize: 14, left: 16, bottom: 8.25 };
};

const AddressCell = ({ placeholder, value, onPress }: Props) => {
  const titleStyle = useTitle(placeholder, value);
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Text style={[styles.placeholder, titleStyle]}>{placeholder}</Text>
      {value ? <Text style={styles.value}>{value}</Text> : null}
      <Image source={require("../img/credit_input_ico_arrow.png")} style={styles.arrow} />
      <View style={styles.sep} />
    </TouchableOpacity>
  );
};

export default AddressCell;

const styles = StyleSheet.create({
  container: {
    height: 61,
    flexDirection: "row",
    alignItems: "flex-end",
    paddingHorizontal: 16,
    justifyContent: "space-between"
  },
  image: {
    width: 16,
    height: 16
  },
  sep: {
    height: 0.5,
    backgroundColor: "#D9D9D9",
    position: "absolute",
    left: 16,
    right: 0,
    bottom: 0
  },
  placeholder: { color: "#A5A5A5", position: "absolute" },
  value: { fontSize: 14, color: "#505050", position: "absolute", bottom: 8.25, left: 16 },
  arrow: { width: 16, height: 16, position: "absolute", bottom: 8.25, right: 16 }
});
