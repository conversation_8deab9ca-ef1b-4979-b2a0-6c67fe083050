/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022-01 17:28
 * @description RN信息页
 */

import React, { Component } from "react";
import { View, StyleSheet, Text, NativeModules } from "react-native";
import codePush, { LocalPackage } from "@akulaku-rn/react-native-code-push";
const styles = StyleSheet.create({});

type State = {
  trade: string;
  pay: string;
  bill: string;
  activity: string;
  user: string;
  loader: string;
};

type Props = {};

export default class RnInfomation extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      trade: "",
      pay: "",
      bill: "",
      activity: "",
      user: "",
      loader: ""
    };
  }

  componentDidMount() {
    const CodePush = NativeModules.CodePush;
    CodePush.getCurrentMetadata("loader", "loader", codePush.UpdateState.RUNNING).then(
      (update: LocalPackage | null) => {
        if (update) {
          this.setState({
            loader: update.label
          });
        }
      }
    );
    CodePush.getCurrentMetadata("user", "user", codePush.UpdateState.RUNNING).then((update: LocalPackage | null) => {
      if (update) {
        this.setState({
          user: update.label
        });
      }
    });
    CodePush.getCurrentMetadata("activity", "activity", codePush.UpdateState.RUNNING).then(
      (update: LocalPackage | null) => {
        if (update) {
          this.setState({
            activity: update.label
          });
        }
      }
    );
    CodePush.getCurrentMetadata("bill", "bill", codePush.UpdateState.RUNNING).then((update: LocalPackage | null) => {
      if (update) {
        this.setState({
          bill: update.label
        });
      }
    });
    CodePush.getCurrentMetadata("pay", "pay", codePush.UpdateState.RUNNING).then((update: LocalPackage | null) => {
      if (update) {
        this.setState({
          pay: update.label
        });
      }
    });
    CodePush.getCurrentMetadata("trade", "trade", codePush.UpdateState.RUNNING).then((update: LocalPackage | null) => {
      if (update) {
        this.setState({
          trade: update.label
        });
      }
    });
  }

  render() {
    const { trade, pay, bill, activity, user, loader } = this.state;
    return (
      <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
        <Text style={{ fontSize: 20 }}>{"trade:" + trade}</Text>
        <Text style={{ fontSize: 20 }}>{"pay:" + pay}</Text>
        <Text style={{ fontSize: 20 }}>{"bill:" + bill}</Text>
        <Text style={{ fontSize: 20 }}>{"activity:" + activity}</Text>
        <Text style={{ fontSize: 20 }}>{"user:" + user}</Text>
        <Text style={{ fontSize: 20 }}>{"loader:" + loader}</Text>
      </View>
    );
  }
}
