import { AKDialog, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { Shadow } from "@akulaku-rn/akui-rn/src/components/NeomorphShadows";
import { TFunction } from "i18next";
import React, { useEffect, useState, forwardRef, useImperativeHandle } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Animated,
  InteractionManager,
  DeviceEventEmitter,
  Platform
} from "react-native";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { NativeConfigModule, NativeNavigationModule } from "common/nativeModules";
import { StoreData } from "common/util/cache";
import { UserInfo } from "../../dict/userInfo";
import store from "../../store";
import { SensorCallPhone, SensorCallPhoneType, SensorTypeCLICKItem } from "../../tool/sensorLogger";
import styles from "./styles";
import { ButtonInfos, Data as educationInfoData } from "../../dict/educationInfo";
import { DialogType, LayoutDirection } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import NativePhoneModule from "@akulaku-rn/akulaku-ec-common/src/nativeModules/basics/nativePhoneModule";

const SetPayPassWordType = 5;
const SafeOperation = (
  props: {
    t: TFunction;
    status: string;
    navigation: navigationModel;
    data: {} | educationInfoData;
    store: pageStoreModel<store>;
    userInfo: UserInfo;
  },
  ref: React.RefObject<unknown>
) => {
  const { t, navigation, data, store, userInfo } = props;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [height, setHeight] = useState<any>(null);
  const [aniTop, setAniTop] = useState<Animated.Value>(new Animated.Value(-89));
  const animatedView: View | null = null;
  useEffect(() => {
    switch (props.status) {
      case "error":
      case "optimizable":
        setTimeout(() => {
          InteractionManager.runAfterInteractions(() => {
            reduceView();
          });
        }, 1100);
        break;
      case "scanning":
      case "success":
        InteractionManager.runAfterInteractions(() => {
          increaseView();
        });
        break;
    }
  }, [props.status]);

  const reduceView = () => {
    Animated.timing(aniTop, {
      toValue: -39,
      duration: 290,
      useNativeDriver: false
    }).start(() => {
      setAniTop(new Animated.Value(-39));
    });
  };

  const increaseView = () => {
    Animated.timing(aniTop, {
      toValue: -89,
      duration: 290,
      useNativeDriver: false
    }).start(() => {
      setAniTop(new Animated.Value(-89));
    });
  };

  useImperativeHandle(ref, () => ({
    reduceView: () => {
      reduceView();
    },
    increaseView: () => {
      increaseView();
    }
  }));
  const contactCustomer = async () => {
    SensorTypeCLICKItem({ type: 101 });
    if (!userInfo.data) {
      SensorCallPhone(SensorCallPhoneType.ExposeCallPhone);
      AKDialog.show({
        type: DialogType.C3_1_1,
        title: t("拨打客服服务"),
        positiveText: t("确认"),
        negativeText: t("取消"),
        onPositivePress: () => {
          NativePhoneModule.callCustomerService("1500920");
          SensorCallPhone(SensorCallPhoneType.onClickPhone);
        },
        onNegativePress: () => {
          AKDialog.dismiss();
          SensorCallPhone(SensorCallPhoneType.onClickCancel);
        },
        layoutDirection: LayoutDirection.LayoutVertical
      });
    } else {
      NativeNavigationModule.navigate({ screen: "helpCenterHomeScreen" });
    }
  };

  const goOperation = async (data: ButtonInfos) => {
    SensorTypeCLICKItem(data);
    if (!userInfo.data) {
      const response = await NativeConfigModule.goLogin();
      if (response.success) {
        DeviceEventEmitter.emit("refreshSecurityCenter");
      }
      return;
    }
    const { runtime, pageStore } = store;
    navigation.navigate({ url: data.link });
    if (data.type === SetPayPassWordType) {
      pageStore.hasClickDot = true;

      StoreData(`${runtime.uid}hasClickDot`, JSON.stringify(pageStore.hasClickDot));
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.operations}>
        {!!data &&
          // @ts-ignore
          data.buttonInfos.map((item: ButtonInfos, index: number) => {
            if (item.type !== 7) {
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    goOperation(item);
                  }}
                  style={styles.item}
                >
                  <Image source={{ uri: item.imageUrl }} style={styles.icon} />
                  <Text style={styles.text}>{item.title}</Text>
                </TouchableOpacity>
              );
            } else {
              if (Platform.OS === "android") {
                return (
                  <TouchableOpacity
                    key={index}
                    onPress={() => {
                      goOperation(item);
                    }}
                    style={styles.item}
                  >
                    <Image source={{ uri: item.imageUrl }} style={styles.icon} />
                    <Text style={styles.text}>{item.title}</Text>
                  </TouchableOpacity>
                );
              } else {
                return null;
              }
            }
          })}
      </View>
      <TouchableOpacity style={styles.button} onPress={contactCustomer}>
        <Image style={styles.leftIcon} source={require("../../img/tips.webp")} />
        <Text style={styles.buttonText}>{t("联系官方客服")}</Text>
        <Image style={styles.rightIcon} source={require("../../img/arrow_16.webp")} />
      </TouchableOpacity>
    </View>
  );
};
// @ts-ignore
export default forwardRef(SafeOperation);
