import { FigmaStyle, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";
export default StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingTop: 20,
    marginBottom: 8
  },
  containerView: {
    backgroundColor: "#fff",
    borderRadius: 12
  },
  operations: {
    marginBottom: 4,
    flexDirection: "row",
    flexWrap: "wrap"
  },
  item: {
    width: (WINDOW_WIDTH - 32) / 3,
    alignItems: "center",
    marginBottom: 16
  },
  icon: {
    width: 44,
    height: 44,
    backgroundColor: "#eff2f6"
  },
  text: {
    textAlign: "center",
    color: FigmaStyle.Color.Grey_Text1,
    fontSize: 12
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderTopColor: FigmaStyle.Color.Grey_Divider,
    borderTopWidth: 0.5
  },
  leftIcon: {
    width: 20,
    height: 20
  },
  buttonText: {
    marginHorizontal: 10,
    fontSize: 14,
    color: FigmaStyle.Color.Grey_Text1,
    flex: 1
  },
  rightIcon: {
    width: 16,
    height: 16
  },
  redDot: {
    width: 6,
    height: 6,
    borderRadius: 6,
    backgroundColor: "#ff5353",
    position: "absolute",
    right: 2,
    top: 2
  }
});
