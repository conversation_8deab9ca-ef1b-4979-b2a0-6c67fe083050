import React, { PureComponent } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Animated,
  DeviceEventEmitter,
  Image,
  Keyboard,
  findNodeHandle,
  UIManager,
  Platform,
  EmitterSubscription,
  TouchableOpacity
} from "react-native";

type Props = {
  setValue: (value: string, id?: number) => void;
  savePageY?: (pageY: number) => void;
  textFormat?: (value: string) => string;
  onFocus?: (id?: number) => void;
  value: string;
  placeholder: string;
  pattern?: any;
  id?: number;
  containerStyle?: object;
  nullId?: number | null;
  keyboardType?: any;
  editable?: boolean;
  listenerName?: string;
  mergeListenerValue?: boolean;
  errorText?: string;
  nullText?: string;
  secureTextEntry?: boolean;
  textInputComponent?: any;
  showErrorText?: boolean;
  isHidePassword?: (isHidePassword: boolean) => void;
  onBlur?: (id?: number) => void;
  marginBottom?: number;
};

type States = {
  value: string;
  color: string;
  isfocus: boolean;
  error: boolean;
  isHidePassword: boolean;
};

export default class InputPassword extends PureComponent<Props, States> {
  textInputRef: any;

  top: Animated.Value;

  fontSize: Animated.Value;

  keyboardDidHideListener?: EmitterSubscription;

  listener?: EmitterSubscription;

  timer?: any;

  static defaultProps = {
    editable: true,
    nullId: null,
    keyboardType: "default"
  };

  constructor(props: Props) {
    super(props);
    this.state = {
      value: props.value,
      color: props.value ? "#999" : "#B3B3B3",
      isfocus: false,
      error: false,
      isHidePassword: true
    };
    this.top = new Animated.Value(props.value ? 0 : 32);

    this.fontSize = new Animated.Value(props.value ? 12 : 14);
  }

  componentDidMount() {
    const { setValue, id, listenerName, mergeListenerValue } = this.props;
    if (listenerName) {
      this.listener = DeviceEventEmitter.addListener(listenerName, value => {
        const newText = mergeListenerValue ? this.state.value + value : value;
        this.setState({ value: newText });
        !!id ? setValue(newText, id) : setValue(newText);
      });
    }
    this.keyboardDidHideListener = Keyboard.addListener("keyboardDidHide", this.keyboardDidHide);
  }

  UNSAFE_componentWillReceiveProps(nextProps: any) {
    this.setState(
      {
        value: nextProps.value
      },
      () => {
        this.onFocusAnimation();
        this.onEndEditing();
      }
    );
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    this.listener && this.listener.remove();
    this.keyboardDidHideListener && this.keyboardDidHideListener.remove();
  }

  keyboardDidHide = () => {
    this.textInputRef && this.textInputRef.blur();
  };

  onFocus = () => {
    const { onFocus, id } = this.props;
    !!onFocus && onFocus(id);
    this.setState({ isfocus: true }, () => {
      this.onFocusAnimation();
    });
  };

  onFocusAnimation = () => {
    const { value, isfocus } = this.state;
    if (isfocus || (!!value && !!value.length)) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 10,
          duration: 200,
          useNativeDriver: false
        }),

        Animated.timing(this.fontSize, {
          toValue: 12,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#999"
      });
    }
  };

  onBlur = () => {
    const { onBlur, id } = this.props;
    !!onBlur && onBlur(id);
    this.setState({ isfocus: false }, () => {
      this.onEndEditing();
    });
    if (this.state.value && !!this.state.value.length) {
      return;
    } else {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 32,
          duration: 200,
          useNativeDriver: false
        }),

        Animated.timing(this.fontSize, {
          toValue: 14,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#B3B3B3"
      });
    }
  };

  onChangeText = (value: string) => {
    const { setValue, id } = this.props;
    this.setState({ value, error: false });
    !!id ? setValue(value, id) : setValue(value);
  };

  onEndEditing = () => {
    if (this.state.isfocus) {
      return;
    }
    const { pattern, textFormat } = this.props;
    let text = this.state.value;
    if (!!textFormat) {
      text = textFormat(text);
    }
    let error = false;
    if (!!pattern && !!text) {
      const reg = new RegExp(pattern);
      error = !reg.test(text);
    }
    if (this.props.showErrorText !== undefined) error = this.props.showErrorText;
    this.setState({ error });
  };

  clickText = () => {
    if (!this.props.editable) return;
    this.textInputRef.focus();
  };

  onLayout = () => {
    const { savePageY } = this.props;
    if (!savePageY) return;
    const handle = findNodeHandle(this.textInputRef);
    if (handle) {
      this.timer = setInterval(() => {
        UIManager.measure(handle, (_x, _y, _width, _height, _pageX, pageY) => {
          savePageY && savePageY(pageY);
        });
      }, 500);
    }
  };

  showError = () => {
    const { error, value } = this.state;
    const { id, nullId, errorText, nullText } = this.props;
    if (!errorText && !nullText) {
      return false;
    }
    if (error) {
      return true;
    }
    if (nullId === id && !value) {
      return true;
    }
    return false;
  };

  renderTextInput = () => {
    const { textInputComponent: TextInputComponent, editable, keyboardType } = this.props;
    const { value } = this.state;
    const refFunc = (textInputRef: TextInput | null) => {
      this.textInputRef = textInputRef;
    };
    const props = {
      ref: refFunc,
      editable: editable,
      selectionColor: "#e62117",
      numberOfLines: 1,
      style: [styles.textInput],
      maxLength: 16,
      underlineColorAndroid: "transparent",
      onChangeText: this.onChangeText,
      onFocus: this.onFocus,
      onBlur: this.onBlur,
      value: value,
      hitSlop: {
        top: 10,
        bottom: 10
      },
      keyboardType: keyboardType,
      secureTextEntry: this.props.secureTextEntry
    };

    if (!!TextInputComponent) {
      return <TextInputComponent {...props} />;
    }
    return <TextInput {...props} style={{ padding: 0, height: 16, marginTop: 6 }} />;
  };

  render() {
    const { placeholder, containerStyle, errorText, nullText, isHidePassword, marginBottom } = this.props;
    const { isfocus, error } = this.state;
    const showError = this.showError();
    return (
      <View style={containerStyle} onLayout={this.onLayout}>
        <View
          style={[
            styles.container,
            isfocus && { borderBottomColor: "#E2E5E9" },
            showError && { borderBottomColor: "#e62117" }
          ]}
        >
          {this.renderTextInput()}
          <TouchableOpacity
            style={{ position: "absolute", right: 16, top: 30 }}
            onPress={() => {
              this.setState({ isHidePassword: !this.state.isHidePassword });
              isHidePassword && isHidePassword(this.state.isHidePassword);
            }}
          >
            {this.state.isHidePassword === true ? (
              <Image source={require("../img/closeEay.webp")} style={{ width: 20, height: 20 }} />
            ) : (
              <Image source={require("../img/openEay.webp")} style={{ width: 20, height: 20 }} />
            )}
          </TouchableOpacity>
          <Animated.View
            style={{
              position: "absolute",
              zIndex: 1,
              top: this.top
            }}
          >
            <Animated.Text
              onPress={this.clickText}
              style={[{ fontSize: this.fontSize, lineHeight: 16, color: this.state.color }]}
            >
              {placeholder}
            </Animated.Text>
          </Animated.View>
        </View>
        {showError && (
          <View style={[styles.errorView, { marginBottom: marginBottom }]}>
            <Image style={styles.errorIcon} source={require("../img/error_icon.webp")} />
            <Text style={styles.errorText}>{error ? errorText : nullText}</Text>
          </View>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 0.5,
    paddingTop: 30,
    paddingBottom: 10
  },
  textInput: {
    height: 16,
    padding: 0,
    fontSize: 14,
    textAlign: "left",
    color: "#333",
    // marginTop: 6,
    ...Platform.select({
      ios: { lineHeight: 16 }
    })
  },
  errorView: {
    flexDirection: "row",
    marginTop: 3,
    marginRight: 16
  },
  errorText: {
    fontSize: 12,
    color: "#E62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginTop: 3,
    marginRight: 4
  }
});
