import { FontStyles } from "@akulaku-rn/akui-rn";
import React, { PureComponent } from "react";
import { View, Text, StyleSheet, ViewStyle, TextStyle } from "react-native";

type Props = {
  title: string;
  data: Array<string>;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  listStyle?: ViewStyle;
  textStyle?: TextStyle;
};

export default class ListComponent extends PureComponent<Props> {
  render() {
    const { title, data, style, titleStyle, listStyle, textStyle } = this.props;
    return (
      <View style={style}>
        <Text style={[styles.title, titleStyle]}>{title}</Text>
        <View style={listStyle}>
          {data?.map((item: string, index: number) => {
            return (
              <View key={index} style={styles.listContainer}>
                <View style={[styles.dot, textStyle]} />
                <Text style={[styles.text, textStyle]}>{item}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  title: {
    fontSize: 14,
    color: "#282B2E",
    ...FontStyles["rob-medium"],
    marginBottom: 12
  },
  listContainer: {
    flexDirection: "row",
    marginBottom: 8
  },
  dot: {
    width: 3,
    height: 3,
    borderRadius: 3,
    backgroundColor: "#989FA9",
    marginRight: 8,
    position: "absolute",
    top: 5
  },
  text: {
    fontSize: 12,
    color: "#989FA9",
    marginLeft: 11
  }
});
