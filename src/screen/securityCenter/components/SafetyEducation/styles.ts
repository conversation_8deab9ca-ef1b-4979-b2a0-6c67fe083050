import { FigmaStyle, FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingTop: 20,
    marginBottom: 8
  },
  headerText: {
    marginBottom: 16,
    fontSize: 16,
    color: FigmaStyle.Color.Grey_Text1,
    ...FontStyles["roboto-bold"]
  },
  swiperShadow: {
    width: WINDOW_WIDTH - 24,
    height: (WINDOW_WIDTH - 24) * (104 / 336),
    backgroundColor: "#fff",
    borderRadius: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowColor: "rgba(0,0,0,0.06)",
    shadowRadius: 2,
    shadowOpacity: 1
  },
  swiper: {
    width: WINDOW_WIDTH - 24,
    height: (WINDOW_WIDTH - 24) * (104 / 336),
    borderRadius: 12,
    overflow: "hidden"
  },
  pagination: {
    position: "absolute",
    bottom: 6
  },
  dot: {
    width: 10,
    height: 3,
    backgroundColor: "#BDC1CA",
    borderRadius: 3,
    marginHorizontal: 1.5
  },
  activeDot: {
    width: 10,
    height: 3,
    backgroundColor: "#FF5353",
    borderRadius: 3,
    marginHorizontal: 1.5
  },
  chalkboardView: {
    paddingVertical: 6,
    backgroundColor: "#fff",
    borderRadius: 12
  },
  chalkboardItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20
  },
  chalkboardItemNumber: {
    height: 56,
    width: 56,

    marginRight: 16
  },
  chalkboardItemTextView: {
    paddingBottom: 8,
    borderBottomColor: "#EFF2F6",
    borderBottomWidth: 0.5,
    width: WINDOW_WIDTH * (289 / 360)
  },
  chalkboardItemText: {
    fontSize: 14,
    color: FigmaStyle.Color.Grey_Text2,
    maxWidth: WINDOW_WIDTH - 28 - 16 - 16 - 56
  },

  faqItem: {
    height: 60,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottomColor: FigmaStyle.Color.Grey_Divider,
    borderBottomWidth: 0.5
  },
  faqItemText: {
    fontSize: 14,
    color: FigmaStyle.Color.Grey_Text1,
    width: WINDOW_WIDTH * (285 / 360)
  },
  rightIcon: {
    height: 16,
    width: 16
  },
  faqContainer: {
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingTop: 20,
    marginBottom: 80
  },
  adDot: {
    width: 5,
    height: 3,
    backgroundColor: "#c0c3c7",
    marginRight: 3,
    borderRadius: 1
  },
  adActiveDot: {
    width: 14,
    height: 3,
    backgroundColor: "#e5e7e9",
    marginRight: 3,
    borderRadius: 1
  }
});
