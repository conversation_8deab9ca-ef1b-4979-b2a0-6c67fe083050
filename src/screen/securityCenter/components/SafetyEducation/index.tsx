import React from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import styles from "./styles";
import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { SensorTypeCLICKItem } from "../../tool/sensorLogger";
import { Data as educationInfoData, FaqList } from "../../dict/educationInfo";
import { TFunction } from "i18next";
import { NativeNavigationModule } from "@akulaku-rn/akulaku-ec-common/src/nativeModules";
import ADBanner from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/ADBanner";
import ADCarousel from "@akulaku-rn/akulaku-ec-common/src/components/ADGroup/ADCarousel";

const SafetyEducation = ({ t, data }: { t: TFunction; data: {} | educationInfoData }) => {
  const goFaqDetail = (data: FaqList, index: number) => {
    SensorTypeCLICKItem({ type: 103, index: index + 1 });
    NativeNavigationModule.navigate({ screen: "SecurityFaqDetail", params: { data } });
  };

  const renderTipsView = () => {
    return (
      <View style={styles.container}>
        <Text style={styles.headerText}>{t("防骗秘诀")}</Text>
        <View>
          {!!data &&
            // @ts-ignore
            data.antiFraudTips.map((item: { imageUrl: string; title: string }, index: number) => {
              const url =
                index === 0
                  ? require("../../img/tipsIcon_1.webp")
                  : index === 1
                  ? require("../../img/tipsIcon_2.webp")
                  : index === 2
                  ? require("../../img/tipsIcon_3.webp")
                  : require("../../img/tipsIcon_4.webp");
              return (
                <View key={index} style={styles.chalkboardItem}>
                  <Image source={url} style={styles.chalkboardItemNumber} />
                  <Text style={styles.chalkboardItemText}>{item.title || ""}</Text>
                </View>
              );
            })}
        </View>
      </View>
    );
  };

  const renderFaqView = () => {
    return (
      <View style={styles.faqContainer}>
        <Text style={[styles.headerText, { marginBottom: 8 }]}>{t("常见问题")}</Text>
        <View>
          {!!data &&
            // @ts-ignore
            data.faqList.map((item: FaqList, index: number) => {
              return (
                <TouchableOpacity
                  onPress={() => {
                    goFaqDetail(item, index);
                  }}
                  key={index}
                  // @ts-ignore
                  style={[styles.faqItem, index === data.faqList.length - 1 && { borderBottomWidth: 0 }]}
                >
                  <Text numberOfLines={1} style={styles.faqItemText}>
                    {item.title || ""}
                  </Text>
                  <Image style={styles.rightIcon} source={require("../../img/arrow_16.webp")} />
                </TouchableOpacity>
              );
            })}
        </View>
      </View>
    );
  };
  const dot = <View style={styles.adDot} />;
  const activeDot = <View style={styles.adActiveDot} />;

  return (
    <View>
      {renderTipsView()}
      <ADCarousel
        adGroup={109}
        wrapperStyle={{ marginBottom: 8 }}
        imageHeight={(WINDOW_WIDTH * 354) / 1080}
        imageWidth={WINDOW_WIDTH}
        dot={dot}
        activeDot={activeDot}
        removeClippedSubviews={false}
      />
      {renderFaqView()}
    </View>
  );
};

export default SafetyEducation;
