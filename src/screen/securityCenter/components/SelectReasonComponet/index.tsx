import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import React, { useState } from "react";
import { TFunction } from "react-i18next";
import { View, Text, TouchableOpacity, Image, TextInput, StyleSheet, ViewStyle, ScrollView } from "react-native";

type Props = {
  t: TFunction;
  data: Array<{ title: string; desc: string }>;
  selectReason: (index: number) => void;
  onClickOther: (text: string) => void;
  backgroundStyle?: ViewStyle;
};

export default function SelectReasonComponet({ t, data, selectReason, onClickOther }: Props) {
  const [reason, setReason] = useState("");
  const [reasonLength, setReasonLength] = useState(0);
  const [clickIndex, setClickIndex] = useState(-1);

  const onChangeText = (text: string) => {
    setReason(text);
    setReasonLength(text.length);
    onClickOther && onClickOther(text);
  };

  return (
    <View style={styles.container}>
      <View style={{ paddingHorizontal: 16 }}>
        <Text style={styles.text}>{t("请提交您的冻结原因")}</Text>
      </View>
      <ScrollView style={{ paddingBottom: 20, paddingHorizontal: 16 }}>
        {data.map((item: { title: string; desc: string }, index: number) => {
          return (
            <TouchableOpacity
              key={index}
              activeOpacity={0.8}
              style={[
                styles.listContainer,
                index !== data.length - 1 && styles.line,
                index === data.length - 1 && { paddingTop: 16, paddingBottom: 8 }
              ]}
              onPress={() => {
                setClickIndex(index);
                selectReason && selectReason(index);
              }}
            >
              <View>
                <Text style={styles.title}>{item.title}</Text>
                <Text style={styles.desc}>{item.desc}</Text>
              </View>
              <Image
                source={clickIndex === index ? require("../../img/select.webp") : require("../../img/notSelect.webp")}
                style={styles.image}
              />
            </TouchableOpacity>
          );
        })}
        {clickIndex === data.length - 1 && (
          <View style={styles.reason}>
            <TextInput
              style={styles.textInput}
              value={reason}
              onChangeText={(text: string) => {
                onChangeText(text);
              }}
              multiline
              maxLength={500}
              placeholder={t("具体原因")}
              placeholderTextColor={"#989FA9"}
            />
            <View style={{ height: 12 }} />
            <Text style={styles.length}>{`${reasonLength}/500`}</Text>
          </View>
        )}
        <View style={{ height: 20 }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    paddingTop: 16,
    flex: 1,
    backgroundColor: "#fff"
  },
  text: {
    fontSize: 16,
    color: "#000000",
    ...FontStyles["rob-medium"],
    marginBottom: 12
  },
  listContainer: {
    paddingVertical: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  line: {
    borderBottomColor: "rgba(226,229,233,0.8)",
    borderBottomWidth: 0.5
  },
  title: {
    fontSize: 14,
    color: "#282B2E",
    marginBottom: 4
  },
  desc: {
    fontSize: 12,
    color: "#989FA9",
    maxWidth: WINDOW_WIDTH - 60
  },
  image: {
    width: 16,
    height: 16
  },
  textInput: {
    paddingTop: 0,
    paddingLeft: 0
  },
  length: {
    fontSize: 12,
    color: "#989FA9",
    textAlign: "right"
  },
  reason: {
    borderRadius: 6,
    backgroundColor: "#EFF2F6",
    padding: 12
  }
});
