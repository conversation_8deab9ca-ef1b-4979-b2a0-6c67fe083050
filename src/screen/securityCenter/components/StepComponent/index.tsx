import React from "react";
import { TFunction } from "react-i18next";
import { View, Text, Image, StyleSheet } from "react-native";

type Props = {
  step: number;
  t: TFunction;
};

export enum StepNumber {
  first = 1,
  second
}

export default function StepComponent({ step, t }: Props) {
  const data = [t("确认原因"), t("身份验证"), t("冻结成功")];
  return (
    <View style={styles.container}>
      <View style={styles.stepContainer}>
        <Image source={require("../../img/step_1.webp")} style={[styles.image, { marginLeft: 50 }]} />
        <View style={[styles.line, { backgroundColor: "#F32823" }]} />
        <View style={[styles.line, , step === 2 && { backgroundColor: "#F32823" }]} />
        <Image
          source={step === 1 ? require("../../img/step_2_normal.webp") : require("../../img/step_2_red.webp")}
          style={styles.image}
        />
        <View style={styles.line} />
        <View style={styles.line} />
        <Image source={require("../../img/step_3.webp")} style={[styles.image, { marginRight: 50 }]} />
      </View>
      <View style={styles.textContainer}>
        {data.map((item: string, index: number) => {
          return (
            <Text key={index} style={styles.text}>
              {item}
            </Text>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
    backgroundColor: "#EFF2F6",
    justifyContent: "center"
  },
  textContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12
  },
  text: {
    fontSize: 12,
    color: "#282B2E",
    flex: 1,
    textAlign: "center"
  },
  stepContainer: {
    flexDirection: "row",
    alignItems: "center"
  },
  image: {
    width: 20,
    height: 20
  },
  line: {
    height: 1,
    backgroundColor: "#989FA9",
    flex: 1
  }
});
