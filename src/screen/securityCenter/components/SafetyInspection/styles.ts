import { WINDOW_WIDTH, FontStyles, FigmaStyle, STATUS_BAR_HEIGHT, iOS } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    width: WINDOW_WIDTH,
    alignItems: "center",
    backgroundColor: "#D2F0FA"
  },
  background: {
    top: 0,
    position: "absolute",
    width: WINDOW_WIDTH,
    height: (WINDOW_WIDTH * (300 + STATUS_BAR_HEIGHT)) / 360
  },
  json: {
    width: WINDOW_WIDTH,
    height: (WINDOW_WIDTH * 300) / 360
  },
  linearGradient: {
    top: 0,
    position: "absolute",
    width: WINDOW_WIDTH,
    height: 320
  },
  startScan: {
    height: WINDOW_WIDTH,
    width: WINDOW_WIDTH,
    marginTop: -34
  },
  lottieView: {
    height: 100,
    width: 84
  },
  main: {
    position: "absolute",
    bottom: 24
  },
  scanText: {
    fontSize: 14,
    position: "absolute",
    textAlign: "center",
    bottom: iOS ? 35 : 30,
    color: FigmaStyle.Color.Grey_Text1
  },
  tips: {
    textAlign: "center",
    fontSize: 16,
    color: FigmaStyle.Color.Grey_Text1,
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    maxWidth: WINDOW_WIDTH - 32
  },
  button: {
    height: 32,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 16,
    marginTop: 12,
    borderRadius: 16,
    borderColor: FigmaStyle.Color.Primary_6,
    borderWidth: 1
  },
  buttonText: {
    textAlign: "center",
    fontSize: 14,
    color: FigmaStyle.Color.Primary_6,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  }
});
