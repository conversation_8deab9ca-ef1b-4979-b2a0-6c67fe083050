import React, { Component, PureComponent, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  Text,
  Animated,
  Easing,
  TouchableHighlight,
  DeviceEventEmitter,
  ImageBackground
} from "react-native";
import <PERSON><PERSON>View from "lottie-react-native";
import LinearGradient from "react-native-linear-gradient";
import styles from "./styles";
import { NativeConfigModule, NativeNavigationModule, NativeUserInfoModule } from "common/nativeModules";
import { SensorTypeCLICKItem } from "../../tool/sensorLogger";
import { FigmaStyle, STATUS_BAR_HEIGHT, UrlImage, WINDOW_WIDTH, iOS } from "@akulaku-rn/akui-rn";
import { Shadow } from "@akulaku-rn/akui-rn/src/components/NeomorphShadows";
import { UserInfo } from "../../dict/userInfo";
import { TFunction } from "i18next";
import { Optimizations } from "../../dict/optimization";
import { debounce } from "lodash";
import { dynamicT } from "@akulaku-rn/akulaku-ec-common/src/services/i18n";
type Props = {
  t: TFunction;
  status: string;
  optimizations: Optimizations[];
  againScanAnimated: () => void;
  againScanNetwork: () => void;
  userInfo: UserInfo;
};
type State = {
  url: string | null;
  isError: boolean;
};

const AnimatedUrlImg = Animated.createAnimatedComponent(UrlImage);
export default class SafetyInspection extends Component<Props, State> {
  startScanOpacity: Animated.Value;

  againOpacity: Animated.Value;

  successOpacity: Animated.Value;

  errorOpacity: Animated.Value;

  tipOpacity: Animated.Value;

  buttonOpacity: Animated.Value;

  successLottieView: LottieView | null;

  errorLottieView: LottieView | null;

  angainLottieView: LottieView | null;

  buttonView!: TouchableHighlight;

  constructor(props: Props) {
    super(props);
    this.state = {
      url: null,
      isError: false
    };
    this.startScanOpacity = new Animated.Value(1);
    this.successOpacity = new Animated.Value(1);
    this.errorOpacity = new Animated.Value(1);
    this.againOpacity = new Animated.Value(1);
    this.tipOpacity = new Animated.Value(0);
    this.buttonOpacity = new Animated.Value(0);
    this.successLottieView = null;
    this.errorLottieView = null;
    this.angainLottieView = null;
  }

  componentDidUpdate(prevProps: Props, prevState: State) {
    const { status } = this.props;
    if (status !== prevProps.status) {
      switch (status) {
        case "error":
          this.setState({ isError: true });
        case "optimizable":
          Animated.timing(this.startScanOpacity, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true
          }).start(() => {
            this.setState(
              {
                url: "error"
              },
              () => {
                this.errorLottieView && this.errorLottieView.play();
                Animated.parallel([
                  Animated.timing(this.tipOpacity, {
                    toValue: 1,
                    duration: 600,
                    useNativeDriver: true
                  }),
                  Animated.timing(this.buttonOpacity, {
                    toValue: 1,
                    duration: 600,
                    useNativeDriver: true
                  })
                ]).start();
              }
            );
          });
          break;
        case "success":
          this.setState({ url: "success" }, () => {
            this.successLottieView && this.successLottieView.play();
            Animated.parallel([
              Animated.timing(this.tipOpacity, {
                toValue: 1,
                duration: 600,
                useNativeDriver: true
              })
            ]).start();
          });
          Animated.timing(this.startScanOpacity, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true
          }).start();
          break;
      }
    }
  }

  onAnimationFinish = () => {
    this.setState({ url: null }, () => {
      this.props.againScanNetwork();
    });
  };

  buttonOnPress = debounce(
    async () => {
      const { isError } = this.state;
      const { optimizations, userInfo } = this.props;
      if (!userInfo.data) {
        const response = await NativeConfigModule.goLogin();
        if (response.success) {
          DeviceEventEmitter.emit("refreshSecurityCenter");
        }
        return;
      }
      if (!isError) {
        NativeNavigationModule.navigate({
          screen: "SecurityOptimizableDetail",
          params: { data: optimizations }
        });
        SensorTypeCLICKItem({ type: 100 });
        return;
      }
      this.angainLottie();
    },
    500,
    { leading: true, trailing: false }
  );

  angainLottie = () => {
    this.setState({ url: "again" }, () => {
      this.angainLottieView && this.angainLottieView.play();
    });
    this.props.againScanAnimated();
    Animated.parallel([
      Animated.timing(this.tipOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true
      }),
      Animated.timing(this.buttonOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true
      })
    ]).start(() => {
      this.setState({ isError: false });
    });
  };

  returnTipsAndButtonText = () => {
    const { isError, url } = this.state;
    const { t, optimizations, userInfo } = this.props;
    let tips = "",
      buttonText = "";
    if (!userInfo.data) {
      buttonText = t("注册/登录");
    } else {
      if (isError) {
        tips = t("检测异常，请重试");
        buttonText = t("再试一次");
      } else {
        if (url === "success") {
          tips = t("暂未发现可优化项");
        } else if (url === "error") {
          (tips = (dynamicT(t, "有待加强，发现x个优化项", {
            ["x"]: <Text style={{ color: FigmaStyle.Color.Primary_6 }}>{optimizations.length}</Text>
          }) as unknown) as string),
            (buttonText = t("立即优化"));
        }
      }
    }
    return { tips, buttonText };
  };

  render() {
    const { url } = this.state;
    const { tips, buttonText } = this.returnTipsAndButtonText();

    return (
      <View style={styles.container}>
        <ImageBackground
          source={
            !this.state.url
              ? require("../../img/scaning.webp")
              : url === "success"
              ? require("../../img/noOptime.webp")
              : require("../../img/hasOptime.webp")
          }
          style={styles.background}
        />
        {!url ? (
          <Animated.View style={{ opacity: this.startScanOpacity }}>
            <LottieView
              ref={animation => {
                this.angainLottieView = animation;
              }}
              style={[styles.json, { marginTop: 4 }]}
              autoPlay
              loop={false}
              onAnimationFinish={this.onAnimationFinish}
              source={require("../../img/start_scan.json")}
            />
          </Animated.View>
        ) : null}

        {url === "success" && (
          <Animated.View style={{ opacity: this.successOpacity }}>
            <LottieView
              autoPlay
              ref={animation => {
                this.successLottieView = animation;
              }}
              style={[styles.json, { marginTop: 4 }]}
              loop={false}
              source={require("../../img/no_optimization.json")}
            />
          </Animated.View>
        )}
        {url === "error" && (
          <Animated.View style={{ opacity: this.errorOpacity }}>
            <LottieView
              autoPlay
              ref={animation => {
                this.errorLottieView = animation;
              }}
              style={[styles.json, { marginTop: 4 }]}
              loop={false}
              source={require("../../img/has_optimization.json")}
            />
          </Animated.View>
        )}
        {url === "again" && (
          <Animated.View style={{ opacity: this.againOpacity }}>
            <LottieView
              autoPlay
              ref={animation => {
                this.angainLottieView = animation;
              }}
              style={styles.json}
              loop={false}
              onAnimationFinish={this.onAnimationFinish}
              source={require("../../img/start_scan.json")}
            />
          </Animated.View>
        )}
        <Animated.Text style={[styles.scanText, { opacity: this.startScanOpacity }]}>
          {this.props.t("进行中")}
        </Animated.Text>
        <View style={[styles.main, url === "success" && { bottom: iOS ? 70 : 60 }]}>
          <Animated.Text style={[styles.tips, { opacity: this.tipOpacity }]}>{tips}</Animated.Text>
          {!!buttonText && (
            <Animated.View style={{ opacity: this.buttonOpacity, alignSelf: "center" }}>
              <TouchableOpacity style={styles.button} onPress={this.buttonOnPress}>
                <Text style={styles.buttonText}>{buttonText}</Text>
              </TouchableOpacity>
            </Animated.View>
          )}
        </View>
      </View>
    );
  }
}

class StartScan extends PureComponent<{}> {
  roatateValue: Animated.Value;

  rotateAnimation: Animated.CompositeAnimation;

  constructor(props: {}) {
    super(props);
    this.roatateValue = new Animated.Value(0);
    this.rotateAnimation = Animated.timing(this.roatateValue, {
      toValue: 1,
      duration: 1000,
      easing: Easing.linear,
      useNativeDriver: true
    });
  }

  componentDidMount() {
    this.rotate();
  }

  componentWillUnmount() {
    this.rotateAnimation.stop();
  }

  rotate = () => {
    this.roatateValue.setValue(0);
    this.rotateAnimation.start(() => this.rotate());
  };

  render() {
    const spin = this.roatateValue.interpolate({
      inputRange: [0, 1],
      outputRange: ["360deg", "0deg"]
    });
    return (
      <AnimatedUrlImg
        isBackground={false}
        clip={"nocenter"}
        isScale={false}
        source={
          "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ec_country_In_screen_securityCenter_a402ca7f71cacee9bc51f0e443a0d533"
        }
        style={[
          styles.startScan,
          {
            transform: [
              {
                rotate: spin
              }
            ]
          }
        ]}
      />
    );
  }
}
