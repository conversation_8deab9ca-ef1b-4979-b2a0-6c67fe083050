/**
 * @Author: wenhui.dong
 * @Date: 2021-12-29 11:20:32
 * @description:埋点
 */

import NativeSensorModule, { SensorEvent, SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { ClickData, reportClick } from "@akulaku-rn/rn-v4-sdk";

type sensorType = Omit<SensorEvent, "page_id" | "page_name">;

class BaseEventTrace {
  page_id: string;

  page_name: string;

  sn: number;

  constructor({ page_id, page_name, sn }: { page_id: string; page_name: string; sn: number }) {
    this.page_id = page_id;
    this.page_name = page_name;
    this.sn = sn;
  }

  sensorClick = (event: sensorType) => {
    const sensorEvent = {
      page_id: this.page_id,
      page_name: this.page_name,
      module_name: "pin",
      module_id: "01",
      ...event
    };
    NativeSensorModule.sensorLogger(SensorType.CLICK, sensorEvent);
  };

  sensorExpose = (event: sensorType) => {
    const sensorEvent = {
      page_id: this.page_id,
      page_name: this.page_name,
      module_name: "pin",
      module_id: "01",
      ...event
    };
    NativeSensorModule.sensorLoggerExpose([sensorEvent]);
  };

  v4Click = (event: ClickData) => {
    const sensorEvent = {
      page_id: this.page_id,
      page_name: this.page_name,
      ...event
    };
    reportClick({ sn: this.sn, ...event });
  };
}

export class PINCodeManagementSensorEventTrace extends BaseEventTrace {
  /**
   * 点击小额免密
   * @param status 01已开通，02未开通
   */
  pinVerificationClick = (status: string) => {
    const event: sensorType = {
      element_id: "11770101",
      element_name: "PIN waiver",
      position_id: "01",
      extra: {
        buttonStatus: status
      }
    };
    this.sensorClick(event);
    this.pinVerificationV4Click();
  };

  /**
   * 更改PIn
   */
  pinChangeClick = () => {
    const event: sensorType = {
      element_id: "11770202",
      position_id: "02",
      element_name: "change pin code"
    };
    this.sensorClick(event);
    this.pinChangeV4Click();
  };

  /**
   * 点击小额免密V4
   * @param status 01未开通，02已开通
   */
  pinVerificationV4Click = () => {
    const event: ClickData = {
      cn: 1,
      bct: 0
    };
    this.v4Click(event);
  };

  /**
   * 更改PInV4
   */
  pinChangeV4Click = () => {
    const event: ClickData = {
      cn: 2,
      bct: 0
    };
    this.v4Click(event);
  };
}

export class PINVerificationSettingsSensorEventTrace extends BaseEventTrace {
  /**
   * 立即开通
   */
  activateNowClick = () => {
    const event: sensorType = {
      element_id: "11780101",
      position_id: "01",
      element_name: "activate"
    };
    this.sensorClick(event);
    this.activateNowV4Click();
  };

  /**
   * 问号
   */
  tipClick = () => {
    const event: sensorType = {
      element_id: "11780102",
      position_id: "02",
      element_name: "instructions"
    };
    this.sensorClick(event);
    this.tipV4Click();
  };

  /**
   * 小额免密说明
   */
  // tipPopUpPanelExpose = () => {
  //   const event: sensorType = {
  //     element_id: "11780303",
  //     module_id: "01",
  //     position_id: "03",
  //     element_name:"agreement",
  //   };
  //   this.sensorExpose(event);
  // };

  /**
   * 查看协议
   */
  protocolClick = () => {
    const event: sensorType = {
      element_id: "11780103",
      position_id: "03",
      element_name: "agreement"
    };
    this.sensorClick(event);
    this.protocolV4Click();
  };

  /**
   * 关闭小额免密功能
   */
  closeActivatedSecretFreePayment = () => {
    const event: sensorType = {
      element_id: "11780104",
      position_id: "04",
      element_name: "turn off"
    };
    this.sensorClick(event);
    this.closeActivatedSecretFreePaymentV4();
  };

  /**
   * 确认关闭
   */
  confirmClose = () => {
    const event: sensorType = {
      element_id: "11780105",
      position_id: "05",
      element_name: "close"
    };
    this.sensorClick(event);
    this.confirmCloseV4();
  };

  /**
   * 继续使用
   */
  continueToUseClick = () => {
    const event: sensorType = {
      element_id: "11780106",
      position_id: "06",
      element_name: "continue to use"
    };
    this.sensorClick(event);
    this.continueToUseClickV4();
  };

  /**
   * 立即开通V4
   */
  activateNowV4Click = () => {
    const event: ClickData = {
      cn: 1,
      bct: 0
    };
    this.v4Click(event);
  };

  /**
   * 问号V4
   */
  tipV4Click = () => {
    const event: ClickData = {
      cn: 2,
      bct: 0
    };
    this.v4Click(event);
  };

  /**
   * 查看协议V4
   */
  protocolV4Click = () => {
    const event: ClickData = {
      cn: 4,
      bct: 0
    };
    this.v4Click(event);
  };

  /**
   * 关闭小额免密功能V4
   */
  closeActivatedSecretFreePaymentV4 = () => {
    const event: ClickData = {
      cn: 5,
      bct: 0
    };
    this.v4Click(event);
  };

  /**
   * 确认关闭V4
   */
  confirmCloseV4 = () => {
    const event: ClickData = {
      cn: 6,
      bct: 0
    };
    this.v4Click(event);
  };

  /**
   * 继续使用V4
   */
  continueToUseClickV4 = () => {
    const event: ClickData = {
      cn: 7,
      bct: 0
    };
    this.v4Click(event);
  };
}

export class DeleteAccountSensor extends BaseEventTrace {
  clickDeleteAccountPage1049(extra: Record<string, string | number | boolean>) {
    const event: sensorType = {
      element_id: "********",
      element_name: "account cancellation",
      module_id: "01",
      module_name: "unfreeze ",
      position_id: "02",
      extra
    };
    this.sensorClick(event);
  }

  clickDeleteAccountPage1091(extra: Record<string, string | number | boolean>) {
    const event: sensorType = {
      element_id: "********",
      element_name: "account cancellation",
      module_id: "01",
      module_name: "freeze ",
      position_id: "03",
      extra
    };
    this.sensorClick(event);
  }
}
