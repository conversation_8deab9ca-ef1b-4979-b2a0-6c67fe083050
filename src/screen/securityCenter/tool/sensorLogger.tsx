import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { reportClick, reporter } from "@akulaku-rn/rn-v4-sdk";

export enum sensorType {
  EnUnfreeze = 1,
  LeUnfreeze = 2,
  EnSuccess = 3,
  LeSuccess = 4,
  EnFailed = 5,
  LeFailed = 6,
  EnResetPassword = 7,
  LeResetPassword = 8,
  unfreezeNow = 9,
  useNow = 10,
  gotIt = 11,
  contactCustomer = 12,
  complete = 13,
  EnReviewing = 14,
  LeReviewing = 15,
  ClickOK = 16,
  ExposeCallPhone = 17,
  onClickPhone = 18,
  onClickCancel = 19
}

export enum SensorCallPhoneType {
  ExposeCallPhone = 1,
  onClickPhone = 2,
  onClickCancel = 3
}
const SensorTypeCLICKItem = (data: any) => {
  //按钮类型：1修改密码，2修改手机，3设备管理，4账户绑定，5设置支付密码，6冻结解冻 7指纹icon
  //个人添加 100点击立即优化 101联系客服，102点击安全守护banner，103点击常见问题列表 104点击具体的优化项
  switch (data.type) {
    case 1:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160202",
        element_name: "modify login password",
        module_id: "02",
        module_name: "",
        position_id: "02",
        page_id: "916"
      });
      reportClick({ sn: 103026, cn: 3 });
      break;
    case 2:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160303",
        element_name: "modify phone num",
        module_id: "03",
        module_name: "",
        position_id: "03",
        page_id: "916"
      });
      reportClick({ sn: 103026, cn: 4 });
      break;
    case 3:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160404",
        element_name: "equipment management",
        module_id: "04",
        module_name: "",
        position_id: "04",
        page_id: "916"
      });
      reportClick({ sn: 103026, cn: 5 });
      break;
    case 4:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160707",
        element_name: "account binding",
        module_id: "07",
        module_name: "",
        position_id: "07",
        page_id: "916"
      });
      reportClick({ sn: 103026, cn: 8 });
      break;
    case 5:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160505",
        element_name: "payment password",
        module_id: "05",
        module_name: "",
        position_id: "05",
        page_id: "916"
      });
      reportClick({ sn: 103026, cn: 6 });
      break;
    case 6:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160606",
        element_name: "freeze-thaw",
        module_id: "06",
        module_name: "",
        position_id: "06",
        page_id: "916"
      });
      reportClick({ sn: 103026, cn: 7 });
      break;
    case 7:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160809",
        element_name: "Login Fingerprint Manage",
        module_id: "08",
        position_id: "09",
        page_id: "916"
      });
      break;
    case 100:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160101",
        element_name: "optimize now",
        module_id: "01",
        module_name: "",
        position_id: "01",
        page_id: "916"
      });
      reportClick({ sn: 103026, cn: 1 });
      break;
    case 101:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160808",
        element_name: "contact service",
        module_id: "08",
        module_name: "",
        position_id: "08",
        page_id: "916"
      });
      reportClick({ sn: 103026, cn: 9 });
      break;
    case 102:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9160909",
        element_name: "security guard",
        module_id: "09",
        module_name: "",
        position_id: "09",
        page_id: "916"
      });
      reportClick({ sn: 103026, cn: 10 });
      break;
    case 103:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center page",
        element_id: "9161010",
        element_name: "FAQ list",
        module_id: "10",
        module_name: "",
        position_id: "10",
        page_id: "916",
        extra: {
          positionidlist: data.index
        }
      });
      reportClick({ sn: 103026, cn: 11, ext: { list_index: data.index } });
      break;
    case 104:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Security center subpage",
        element_id: "9170101",
        element_name: "optimization",
        module_id: "01",
        module_name: "",
        position_id: "01",
        page_id: "917",
        extra: {
          buttonId: data.item.type
        }
      });
      reportClick({ sn: 103027, cn: 1, ext: { optimization_ty: data.item.type } });
      break;
    default:
      console.warn("安全中心上报参数错误");
      break;
  }
};

const SensorCallPhone = (type: SensorCallPhoneType): void => {
  switch (type) {
    case SensorCallPhoneType.ExposeCallPhone:
      NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
        page_id: "1344",
        page_name: "authorization detail",
        pop_id: "pop30286",
        pop_name: "service"
      });
      break;
    case SensorCallPhoneType.onClickPhone:
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        page_id: "1344",
        page_name: "authorization detail",
        pop_id: "pop30286",
        pop_name: "service",
        element_id: "pop3028601",
        element_name: "phone"
      });
      break;
    case SensorCallPhoneType.onClickCancel:
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        page_id: "1344",
        page_name: "authorization detail",
        pop_id: "pop30286",
        pop_name: "service",
        element_id: "pop3028603",
        element_name: "close"
      });
      break;
    default:
      break;
  }
};

const ExposeV4 = (data: any) => {
  const hasPaymentPassword = data.findIndex((i: { type: number }) => i.type === 5);
  const hasFreezeThaw = data.findIndex((i: { type: number }) => i.type === 6);
  if (hasPaymentPassword > 0) {
    NativeSensorModule.sensorLoggerExpose([
      {
        page_name: "Security center page",
        element_id: "9160505",
        element_name: "payment password",
        module_id: "05",
        module_name: "",
        position_id: "05",
        page_id: "916"
      }
    ]);
    reporter.setPageConfig({ sn: 103026 });
    const data = reporter.generateExposeEvent([{ cn: 6 }]);
    reporter.expose(data);
  }
  if (hasFreezeThaw > 0) {
    NativeSensorModule.sensorLoggerExpose([
      {
        page_name: "Security center page",
        element_id: "9160606",
        element_name: "freeze-thaw",
        module_id: "06",
        module_name: "",
        position_id: "06",
        page_id: "916"
      }
    ]);
    reporter.setPageConfig({ sn: 103026 });
    const data = reporter.generateExposeEvent([{ cn: 7 }]);
    reporter.expose(data);
  }
};

const EnterLeaveHomePage = (num: number, isEnter?: boolean) => {
  if (isEnter) {
    NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
      page_id: "916",
      page_name: "Security center page",
      extra: {
        Aku_PageStatus: num
      }
    });
    reporter.enterScreen({ sn: 103026, sp: { optimization_num: num } });
  } else {
    NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
      page_id: "916",
      page_name: "Security center page",
      extra: {
        Aku_PageStatus: num
      }
    });
    reporter.leaveScreen({ sn: 103026, sp: { optimization_num: num } });
  }
};

const equipmentManagementClick = () => {
  NativeSensorModule.sensorLogger(SensorType.CLICK, {
    element_id: "10240101",
    element_name: "delete",
    page_id: "1024",
    page_name: "Equipment management page",
    module_id: "01",
    module_name: "equipment",
    position_id: "01"
  });
};

const selectReasonClick = (removeReason: number | null) => {
  NativeSensorModule.sensorLogger(SensorType.CLICK, {
    element_id: removeReason === 1 ? "10240201" : removeReason === 2 ? "10240202" : "10240203",
    element_name: removeReason === 1 ? "not my phone" : removeReason === 2 ? "discard the phone" : "phone lost",
    position_id: removeReason === 1 ? "01" : removeReason === 2 ? "02" : "03",
    page_id: "1024",
    page_name: "Equipment management page",
    module_id: "02",
    module_name: "delete reason"
  });
};

const EnterLeaveUnfreeze = (name: number) => {
  switch (name) {
    case sensorType.EnUnfreeze:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
        page_id: "1049",
        page_name: "Unfreeze account"
      });
      reporter.enterScreen({ sn: 201086 });
      break;
    case sensorType.LeUnfreeze:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1049",
        page_name: "Unfreeze account"
      });
      reporter.leaveScreen({ sn: 201086 });
      break;
    case sensorType.EnReviewing:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
        page_id: "1053",
        page_name: "Unfreezing account",
        element_name: "Unfreezing account"
      });
      reporter.enterScreen({ sn: 201090 });
      break;
    case sensorType.LeReviewing:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1053",
        page_name: "Unfreezing account",
        element_name: "Unfreezing account"
      });
      reporter.leaveScreen({ sn: 201090 });
      break;
    case sensorType.EnSuccess:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
        page_id: "1050",
        page_name: "Unfreeze account success"
      });
      reporter.enterScreen({ sn: 201087 });
      break;
    case sensorType.LeSuccess:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1050",
        page_name: "Unfreeze account success"
      });
      reporter.leaveScreen({ sn: 201087 });
      break;
    case sensorType.EnFailed:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
        page_id: "1051",
        page_name: "Unfreeze account fail"
      });
      reporter.enterScreen({ sn: 201088 });
      break;
    case sensorType.LeFailed:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1051",
        page_name: "Unfreeze account fail"
      });
      reporter.leaveScreen({ sn: 201088 });
      break;
    case sensorType.EnResetPassword:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1052",
        page_name: "Unfreeze account sucess",
        element_name: "Unfreeze account sucess"
      });
      reporter.enterScreen({ sn: 201089 });
      break;
    case sensorType.LeResetPassword:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1052",
        page_name: "Unfreeze account sucess",
        element_name: "Unfreeze account sucess"
      });
      reporter.leaveScreen({ sn: 201089 });
      break;
  }
};

const UnfreezePageClick = (name: number, status?: number) => {
  switch (name) {
    case sensorType.unfreezeNow:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Unfreeze account",
        element_id: "********",
        element_name: "Unfreeze account",
        module_id: "01",
        position_id: "01",
        page_id: "1049",
        extra: { Aku_PageStatus: status }
      });
      reportClick({ sn: 201086, cn: 1 });
      break;
    case sensorType.useNow:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Unfreeze account success",
        element_id: "********",
        element_name: "Use Now",
        module_id: "01",
        position_id: "01",
        page_id: "1050"
      });
      reportClick({ sn: 201087, cn: 1 });
      break;
    case sensorType.gotIt:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Unfreeze account fail",
        element_id: "********",
        element_name: "got it",
        module_id: "01",
        position_id: "01",
        page_id: "1051"
      });
      reportClick({ sn: 201088, cn: 1 });
      break;
    case sensorType.contactCustomer:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_name: "Unfreeze account fail",
        element_id: "********",
        element_name: "Contact Customer Service",
        module_id: "02",
        position_id: "02",
        page_id: "1051"
      });
      reportClick({ sn: 201088, cn: 2 });
      break;
    case sensorType.complete:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "********",
        element_name: "complete",
        module_id: "01",
        position_id: "01",
        page_id: "1052"
      });
      reportClick({ sn: 201089, cn: 1 });
      break;
    case sensorType.ClickOK:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "********",
        element_name: "ok",
        module_id: "01",
        position_id: "01",
        page_id: "1053"
      });
      reportClick({ sn: 201090, cn: 1 });
      break;
    default:
      break;
  }
};

export {
  SensorTypeCLICKItem,
  ExposeV4,
  EnterLeaveHomePage,
  equipmentManagementClick,
  selectReasonClick,
  EnterLeaveUnfreeze,
  UnfreezePageClick,
  SensorCallPhone
};
