const SERVICE_TYPES = {
  EducationInfo: `/capi/risk-security/public/center/educationInfo`,
  Optimization: "/capi/risk-security/public/center/optimization",
  DeleteLog: "/capi/risk-security/center/deleteLog",
  adGroupMget: "/capi/ad/public/adGroup/mget",
  GetLoginDeviceInfo: "/capi/risk-security/center/getLoginDeviceInfo",
  FreezeCancel: "/capi/personal/security/freeze/cancel", //登录态解冻账户
  NoLogin_Freeze: "/capi/personal/public/freeze/cancel", //无登录态解冻账户
  Get_Security_Payment_Status: "/capi/personal/security/payment/status/get",
  Update_Security_Verification: "/capi/personal/security/verification-waiver/update"
};

const FREEZE_TYPES = {
  Login_FreezeStatus: "/capi/personal/freeze/freeze-info/get", //有登录态查询用户冻结页状态
  Nologin_FreezeStatus: "/capi/personal/public/freeze/freeze-info/get", //无登录态查询用户冻结页状态
  NoLogin_EmerencyFreeze: "/capi/personal/public/freeze/user/urgent-freeze", //无登录态紧急冻结账户
  Login_EmerencyFreeze: "/capi/personal/freeze/user/urgent-freeze", //有登录态紧急冻结账户
  Login_ForeverFreeze: "/capi/personal/freeze/user/permanent-freeze", //有登录态永久冻结账户
  Permanent_Freeze_Condition: "/capi/personal/freeze/permanent-freeze-condition/get" //符合永久冻结条件
};

const LISTENER = {
  PIN_CODE_MANAGEMENT: "PINCodeManagement"
};

export { SERVICE_TYPES, LISTENER, FREEZE_TYPES };
