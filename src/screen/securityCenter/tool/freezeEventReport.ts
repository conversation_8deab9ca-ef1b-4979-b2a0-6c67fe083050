import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { reporter } from "@akulaku-rn/rn-v4-sdk";

export enum FreezeEventType {
  ClickEmerencyFreeze = 0,
  ClickForeverFreeze,
  SelectReason,
  ClickSMS,
  ClickFace,
  ClickOtherMethod,
  ClickReturn,
  ClickFreezeButton,
  ClickReason,
  ClickDone,
  EnterEmerencyFreezeFirst,
  LeaveEmerencyFreezeFirst,
  EnterEmerencyFreezeSecond,
  LeaveEmerencyFreezeSecond,
  EnterForeverFreezeFirst,
  LeaveForeverFreezeFirst,
  EnterForeverFreezeSecond,
  LeaveForeverFreezeSecond
}

export enum PageStatus {
  Error = -1,
  EmerencyFreeze = 1, //紧急冻结
  ForeverFreeze = 2, //永久冻结
  NoFreeze = 3, //不可解冻
  Other = 4 //其他
}

export enum LoginStatusReport {
  Login = 1, //登录态
  NoLogin = 2 //非登录态
}

export const FreezeEventReport = (type: FreezeEventType, status?: LoginStatusReport, reason?: number) => {
  switch (type) {
    case FreezeEventType.ClickEmerencyFreeze:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1091",
        page_name: "freeze account page",
        element_name: "emergency freeze",
        element_id: "********",
        module_id: "01",
        module_name: "freeze",
        position_id: "01",
        extra: { Aku_PageStatus: status }
      });
      reporter.click({ sn: 202155, cn: 1 });
      break;
    case FreezeEventType.ClickForeverFreeze:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1091",
        page_name: "freeze account page",
        element_name: "freeze forever",
        element_id: "********",
        module_id: "01",
        module_name: "freeze",
        position_id: "02",
        extra: { Aku_PageStatus: status }
      });
      reporter.click({ sn: 202155, cn: 2 });
      break;
    case FreezeEventType.SelectReason:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1092",
        page_name: "select freeze reason1",
        element_name: "submit",
        element_id: "********",
        module_id: "01",
        module_name: "freeze",
        position_id: "01",
        extra: { Aku_buttonName: reason }
      });
      reporter.click({
        sn: 202156,
        cn: 1,
        sp: { status: 1 },
        ext: {
          reason: reason === 3 ? 5 : reason === 1 ? 1 : 2
        }
      });
      break;
    case FreezeEventType.ClickSMS:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1093",
        page_name: "select verification page",
        element_name: "sms",
        element_id: "10930101",
        module_id: "01",
        module_name: "freeze",
        position_id: "01"
      });
      reporter.click({ sn: 202157, cn: 1 });
      break;
    case FreezeEventType.ClickFace:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1093",
        page_name: "select verification page",
        element_name: "face",
        element_id: "10930102",
        module_id: "01",
        module_name: "freeze",
        position_id: "02"
      });
      reporter.click({ sn: 202157, cn: 2 });
      break;
    case FreezeEventType.ClickOtherMethod:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1093",
        page_name: "select verification page",
        element_name: "none",
        element_id: "10930103",
        module_id: "01",
        module_name: "freeze",
        position_id: "03"
      });
      reporter.click({ sn: 202157, cn: 3 });
      break;
    case FreezeEventType.ClickReturn:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1095",
        page_name: "freeze success1",
        element_name: "Return",
        element_id: "10950101",
        module_id: "01",
        module_name: "freeze",
        position_id: "01"
      });
      reporter.click({ sn: 202159, cn: 2, ext: { status: 1 } });
      break;
    case FreezeEventType.ClickFreezeButton:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1096",
        page_name: "freeze forever",
        element_name: "freeze",
        element_id: "10960101",
        module_id: "01",
        module_name: "freeze",
        position_id: "01"
      });
      reporter.click({ sn: 202160, cn: 1 });
      break;
    case FreezeEventType.ClickReason:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1097",
        page_name: "select freeze reason2",
        element_name: "submit",
        element_id: "10970101",
        module_id: "01",
        module_name: "freeze",
        position_id: "01",
        extra: { Aku_buttonName: reason }
      });
      break;
    case FreezeEventType.ClickDone:
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        page_id: "1098",
        page_name: "freeze success2",
        element_name: "done",
        element_id: "10980101",
        module_id: "01",
        module_name: "freeze",
        position_id: "01"
      });
      reporter.click({ sn: 202159, cn: 1, ext: { status: 2 } });
      break;
    case FreezeEventType.EnterEmerencyFreezeFirst:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
        page_id: "1092",
        page_name: "select freeze reason1"
      });
      reporter.enterScreen({ sn: 202156, sp: { status: 1 } });
      break;
    case FreezeEventType.LeaveEmerencyFreezeFirst:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1092",
        page_name: "select freeze reason1"
      });
      reporter.leaveScreen({ sn: 202156, sp: { status: 1 } });
      break;
    case FreezeEventType.EnterEmerencyFreezeSecond:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
        page_id: "1093",
        page_name: "select verification page"
      });
      reporter.enterScreen({ sn: 202157 });
      break;
    case FreezeEventType.LeaveEmerencyFreezeSecond:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1093",
        page_name: "select verification page"
      });
      reporter.leaveScreen({ sn: 202157 });
      break;
    case FreezeEventType.EnterForeverFreezeFirst:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
        page_id: "1096",
        page_name: "freeze forever"
      });
      reporter.enterScreen({ sn: 202160 });
      break;
    case FreezeEventType.LeaveForeverFreezeFirst:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1096",
        page_name: "freeze forever"
      });
      reporter.leaveScreen({ sn: 202160 });
      break;
    case FreezeEventType.EnterForeverFreezeSecond:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_ENTER, {
        page_id: "1097",
        page_name: "select freeze reason2"
      });
      reporter.enterScreen({ sn: 202156, sp: { status: 2 } });
      break;
    case FreezeEventType.LeaveForeverFreezeSecond:
      NativeSensorModule.sensorLogger(SensorType.VIEW_SCREEN_LEAVE, {
        page_id: "1097",
        page_name: "select freeze reason2"
      });
      reporter.leaveScreen({ sn: 202156, sp: { status: 2 } });
      break;
    default:
      break;
  }
};
