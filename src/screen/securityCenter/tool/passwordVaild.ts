/*
 * @LastEditors: z<PERSON> yu
 * @Description: 密码校验
 * @FilePath: /react-native/src/screen/securityCenter/tool/passwordVaild.ts
 */

import { NativeNetworkModuleV2 } from "common/nativeModules";

type AKPasswordVaildType = {
  ruleId: number; //规则id
  ruleRe: string; //正则匹配
  type: number; //默认为0，不需要去重不需要区别大小写，1：需要去重，2：需要区分大小写，3：需要去重，需要区分大小写
};

export default class AKPasswordVaildHelper {
  ruleList?: AKPasswordVaildType[];

  //异常时上报999
  levelVersion = "999";

  passwordLevel = 0;

  constructor() {
    this.requestRuleList();
  }

  /**请求规则列表 */
  async requestRuleList() {
    try {
      const result = await NativeNetworkModuleV2.post("/capi/personal/public/risk-rules/get");
      const { data } = result;
      this.ruleList = data;
      this.levelVersion = "0";
    } catch (error) {
      console.log(error);
    }
  }

  /**
   * 去重的前置条件。 重复的字符大于2个
   * @param password 密码
   * @returns 是否需要去重
   */
  needDelDuplicationChar = (password: string) => {
    const dict: { [index: string]: number } = {};
    for (let i = 0; i < password.length; i++) {
      const char = password.charAt(i);
      if (dict[char]) {
        dict[char] = dict[char] + 1;
      } else {
        dict[char] = 1;
      }
    }
    return Object.values(dict).filter(item => item > 1).length > 2;
  };

  /**
   * 相邻字符去重
   * @param password 密码
   * @returns 去重之后的字符串
   */
  execDelDuplicationChar = (password: string) => {
    let newStr = "";
    let prevChar = "";
    for (let i = 0; i < password.length; i++) {
      const char = password.charAt(i);
      if (char !== prevChar) {
        newStr += char;
        prevChar = char;
      }
    }
    return newStr;
  };

  /**
   * 判断是否为简单密码
   * @param password 密码
   * @param regExp 正则
   * @param type //默认为0，不需要去重不需要区别大小写，1：需要去重，2：需要区分大小写，3：需要去重，需要区分大小写
   */
  isEasyPassword = (password: string, regExp: string, type: number) => {
    const numType = Number(type);
    if (numType === 1) {
      password = password.toLowerCase();
    }
    if (numType === 1 || numType === 3) {
      if (this.needDelDuplicationChar(password)) {
        password = this.execDelDuplicationChar(password);
      }
    }
    const reg = new RegExp(regExp);
    const result = password.match(reg);
    return result && result[0];
  };

  /**
   * 开始校验密码
   * @param password 密码
   */
  vaildPassword = async (password: string) => {
    console.log("vaildPassword==>", new Date().getTime());
    if (this.ruleList) {
      const levelVersionArr: number[] = [];
      this.ruleList.forEach(rule => {
        const isEasy = this.isEasyPassword(password, rule.ruleRe, rule.type);
        if (isEasy) {
          levelVersionArr.push(rule.ruleId);
        }
      });
      this.passwordLevel = levelVersionArr.length > 0 ? 1 : 2;
      this.levelVersion = levelVersionArr.length > 0 ? levelVersionArr.join(",") : "1000";
    }
  };
}
