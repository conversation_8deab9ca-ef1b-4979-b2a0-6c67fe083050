import { getCurrentScreen } from "common/constant";
import { ReportScene } from "common/nativeModules/basics/nativeEventReportModule";
import DeviceInfoHelper from "common/util/DeviceInfoHelper";
import { action, observable } from "mobx";
import { FREEZE_TYPES } from "../tool/client";
import * as Sentry from "@sentry/react-native";
import { FreezeSource, LoginStatus } from "../dict/userInfo";
import Basic from "common/store/Basic";

export interface ConditionData {
  allOrdersFinished: boolean; //订单都已完成
  allPaymentFinished: boolean; //账单都已支付
}

export default class Store extends Basic {
  @observable freezeSuccess = false;

  @observable isLoading = true;

  @observable conditionData?: ConditionData;

  @action("是否符合永久冻结条件")
  async checkIsMeetFreeze() {
    try {
      const response = await this.io.post(FREEZE_TYPES.Permanent_Freeze_Condition);
      if (response && response.success) {
        this.conditionData = response.data;
        this.isLoading = false;
      }
    } catch (error) {}
  }

  @action("调用冻结的接口")
  async getFreezeResult(source: number, loginStatus: number, riskFlowId: string, phone?: string) {
    let url;
    if (source === FreezeSource.EmerencyFreeze && loginStatus === LoginStatus.LOGIN) {
      url = FREEZE_TYPES.Login_EmerencyFreeze;
    } else if (source === FreezeSource.EmerencyFreeze && loginStatus === LoginStatus.NO_LOGIN) {
      url = FREEZE_TYPES.NoLogin_EmerencyFreeze;
    } else {
      url = FREEZE_TYPES.Login_ForeverFreeze;
    }
    const data = await DeviceInfoHelper.getDefaultDeviceInfo(ReportScene.WITHDRAW);
    const deviceInfo = data.data.deviceInfo;
    const params = {
      riskFlowId: riskFlowId,
      phone: phone,
      appSystemInfoVO: deviceInfo
    };
    try {
      const response = await this.io.post(url, params);
      if (response && response.success) {
        this.freezeSuccess = true;
        this.isLoading = false;
      } else {
        this.freezeSuccess = false;
        this.isLoading = false;
      }
    } catch (error) {
      Sentry.customReport(error, getCurrentScreen());
    }
  }
}
