import { TimeFormat } from "common/util";
import { ReportScene } from "common/nativeModules/basics/nativeEventReportModule";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import DeviceInfoHelper from "common/util/DeviceInfoHelper";
import { action, computed, observable } from "mobx";
import Basic from "common/store/Basic";
import { SettingPINListData } from "../dict/SettingPINType";
import { FREEZE_TYPES, SERVICE_TYPES } from "../tool/client";
import * as Sentry from "@sentry/react-native";
import { getCurrentScreen } from "common/constant";
import ADBanner from "common/components/ADGroup/ADBanner";
import { LoginStatus } from "../dict/userInfo";
import { getConfig } from "common/commonConfig";

export interface UnfreezeResult {
  riskFlowType: number; //安全风控流程类型 1 同步流程 2 异步流程
  riskFlowStatus: UnfreezeResultType; //流程状态  100 通过 90 拒绝 20 等待审核
}

export enum UnfreezeResultType {
  SUCCESS = 100, //通过
  FAUILE = 90, //拒绝
  REVIEWING = 20 //等待审核
}

export interface FreezeStatusData {
  freezeStatus: FreezeStatus; //冻结状态，1-正常，0-不可解冻，2-紧急冻结，3-永久冻结，4-其他
  reason: FreezeReason; //冻结原因 5-疑似盗号,8-用户自主注销账号,11-dukcapil 不一致,-1-其他,
  unfreezeTime: number; //解冻时间戳
  registeredDays: number; //已注册天数
}

export enum FreezeStatus {
  Normal = 1, //正常
  NotUnFreeze = 0, //不可解冻
  EmerencyFreeze = 2, //紧急冻结
  ForeverFreeze = 3, //永久冻结
  Other = 4 //其他
}

export enum FreezeReason {
  Hacking = 5,
  Logout = 8,
  Other = -1,
  dukcapil = 11
}

export enum EventCode {
  NoLogin_UnFreeze = 50, //非登录态解冻
  UserUnFreeze = 39 //用户解冻
}

export default class Store extends Basic {
  @observable isLoading = true;

  @observable adBannerRef?: ADBanner;

  @observable hasClickDot = false;

  @observable SettingPINData: SettingPINListData[] = [];

  @observable unfreezeResult?: UnfreezeResult;

  @observable freezeStatusData?: FreezeStatusData;

  @observable freezeStatus?: FreezeStatus;

  @observable exist: boolean = true;

  @observable unfreezeResult_err = false;

  // 请求总和
  @action
  fetch = async (url: string, params: object, requestSuccess: any, requestFailure?: any) => {
    this.io.POST(
      url,
      params,
      (response: any) => {
        requestSuccess(response);
      },
      (error: any) => {
        requestFailure(error);
      }
    );
  };

  @observable logoutConfig = { showLogout: false };

  @action("获取账号注销的配置中心配置")
  getAccountLogoutConfig = async () => {
    const res = await getConfig(12124);
    if (!!res) {
      this.logoutConfig = res;
    }
  };

  @computed get showLogout(): boolean {
    return this.logoutConfig.showLogout;
  }

  @action("查询用户冻结状态")
  async checkUserFreezeStatus() {
    try {
      const response = await this.io.post(FREEZE_TYPES.Login_FreezeStatus);
      if (response && response.success) {
        this.freezeStatusData = response.data;
        this.freezeStatus = this.freezeStatusData?.freezeStatus;
        this.isLoading = false;
      }
    } catch (error) {
      Sentry.customReport(error, getCurrentScreen());
    }
  }

  @action("获取解冻结果")
  async getUnfreezeResult(
    riskFlowId: string,
    pwdLevel: number,
    levelVersion: string,
    newPwd: string,
    loginStatus: number,
    phone: string,
    openId: string,
    platformType: string
  ) {
    try {
      const data = await DeviceInfoHelper.getDefaultDeviceInfo(ReportScene.WITHDRAW);
      const deviceInfo = data.data.deviceInfo;
      const params = {
        riskFlowId,
        pwdLevel,
        levelVersion,
        newPwd,
        appSystemInfoVO: deviceInfo
      };
      const addParams = loginStatus === LoginStatus.NO_LOGIN ? { phone, openId, platformType } : {};
      const response = await this.io.post(
        loginStatus === LoginStatus.LOGIN ? SERVICE_TYPES.FreezeCancel : SERVICE_TYPES.NoLogin_Freeze,
        { ...params, ...addParams }
      );
      if (response && response.success) {
        this.unfreezeResult = response.data;
        this.isLoading = false;
        this.unfreezeResult_err = false;
      } else {
        this.unfreezeResult_err = true;
        NativeToast.showMessage(response.errMsg);
      }
    } catch (error) {
      Sentry.customReport(error, getCurrentScreen());
    }
  }
}
