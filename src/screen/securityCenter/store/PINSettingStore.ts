/**
 * @Author: wenhui.dong
 * @Date: 2021-12-28 10:28:43
 * @description:PIN设置
 */
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import BasePageStore, { ResponseType } from "common/store/BasePageStore";
import { get } from "lodash";
import { action, observable, computed } from "mobx";
import { SecurityPaymentStatusType, SettingPINListData, VerificationWaiverStatusEnum } from "../dict/SettingPINType";
import { SERVICE_TYPES } from "../tool/client";
import * as Sentry from "@sentry/react-native";
import { getCurrentScreen } from "common/constant";

export default class Store extends BasePageStore {
  settingPIN: SettingPINListData[] = [];

  @observable securityPaymentStatusData: SecurityPaymentStatusType = {};

  @action
  getSecurityPaymentStatusData = async () => {
    await this.post_request<SecurityPaymentStatusType>(
      SERVICE_TYPES.Get_Security_Payment_Status,
      {},
      (response: SecurityPaymentStatusType) => {
        this.securityPaymentStatusData = response;
        this.isLoading = false;
      },
      (error: ResponseType) => {
        NativeToast.showMessage(error.errMsg || "");
        this.isLoading = false;
        Sentry.customReport(new Error(error.errMsg), getCurrentScreen());
      }
    );
  };

  @action
  updateSecurityVerification = (verificationWaiverStatus: VerificationWaiverStatusEnum, callback?: () => void) => {
    this.post_request(
      SERVICE_TYPES.Update_Security_Verification,
      {
        verificationWaiverStatus,
        scene: 1
      },
      () => {
        this.isLoading = false;
        callback && callback();
      },
      (error: ResponseType) => {
        NativeToast.showMessage(error.errMsg || "");
        this.isLoading = false;
        Sentry.customReport(new Error(error.errMsg), getCurrentScreen());
      }
    );
  };

  @computed
  get securityPaymentStatus() {
    return (
      get(this.securityPaymentStatusData, "verificationWaiverStatus", VerificationWaiverStatusEnum.CLOSE) ||
      VerificationWaiverStatusEnum.CLOSE
    );
  }

  @computed
  get settingPINData() {
    this.settingPIN = [
      {
        rightTitle: "小额免密设置",
        isLeftTitle: true,
        subtitle: "开通后,单笔支付100K以内无需验PIN码"
      },
      { rightTitle: "更改PIN码" }
    ];
    return this.settingPIN;
  }
}
