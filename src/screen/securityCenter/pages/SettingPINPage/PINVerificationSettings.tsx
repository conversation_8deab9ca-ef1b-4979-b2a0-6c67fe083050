/**
 * @Author: wenhui.dong
 * @Date: 2021-12-27 15:29:51
 * @description:小额免密设置
 */
import React from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  ImageSourcePropType,
  Platform,
  DeviceEventEmitter,
  EmitterSubscription
} from "react-native";
import BaseContainer, { Type } from "common/components/BaseContainer";
import RootView from "common/components/Layout/RootView";
import { observer, inject } from "mobx-react";
import { withTranslation } from "common/services/i18n";
import Store from "../../store/PINSettingStore";
import { FontStyles, FontUtil, WINDOW_WIDTH, AKDialog, PopUpContainer } from "@akulaku-rn/akui-rn";
import { PINVerificationSettingsSensorEventTrace } from "../../tool/EventTrace";
import { VerificationWaiverStatusEnum } from "../../dict/SettingPINType";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { LISTENER } from "../../tool/client";
import { NativeRiskCheckModule } from "common/nativeModules";
import { CheckRiskType } from "common/nativeModules/bussinse/nativeRiskCheckModule";
import { scale } from "common/util/P2D";
import { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import { PopUpContainerType } from "@akulaku-rn/akui-rn/src/components/PopUpContainer";

type ImageObjType = {
  title: string;
  image: ImageSourcePropType;
};

type State = {
  imageArray: Array<ImageObjType>;
};
type Props = {
  securityPaymentStatus: VerificationWaiverStatusEnum;
};

@RootView({
  withI18n: [
    "PINVerificationSettings",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json")
    }
  ],
  store: Store,
  keyApi: []
})
@withTranslation("PINVerificationSettings")
@inject("store")
@observer
export default class PINVerificationSettings extends BaseContainer<Store, Props, {}, State> {
  securityCenterSensorEventTrace: PINVerificationSettingsSensorEventTrace;

  pinListener?: EmitterSubscription;

  constructor(props: Type.Props<Store, Props>) {
    super(props);
    this.state = {
      imageArray: [
        { title: "抢购更快捷", image: require("../../img/icon_panic_buying.webp") },
        { title: "安全有保障", image: require("../../img/icon_safety.webp") },
        { title: "使用体验好", image: require("../../img/icon_good_experience.webp") }
      ]
    };
    const {
      store: { pageStore, navParams }
    } = props;
    pageStore.securityPaymentStatusData = { verificationWaiverStatus: navParams.securityPaymentStatus };
    const sensor = {
      page_id: "1178",
      page_name: "PIN Verification Waiver",
      sn: 202128
    };
    this.props.configSensorEvent({
      ...sensor,
      extra: {
        buttonStatus: `0${navParams.securityPaymentStatus + 1}`
      }
    });
    this.props.configPageInfo({ sn: 202128, ext: { status: `0${navParams.securityPaymentStatus + 1}` } }, false);
    this.securityCenterSensorEventTrace = new PINVerificationSettingsSensorEventTrace(sensor);
  }

  componentDidMount() {
    const {
      store: { pageStore }
    } = this.props;
    pageStore.isLoading = false;
  }

  componentWillUnmount() {
    this.pinListener && this.pinListener.remove();
  }

  navigationBarProps = () => {
    return {
      title: this.props.t("小额免密设置"),
      renderRight: this._renderRightView()
      // renderTitle: this._renderTitle()
    };
  };

  _renderTitle = () => {
    return <Text style={{ fontSize: scale(12) }}>{this.props.t("小额免密设置")}</Text>;
  };

  _renderRightView = () => {
    const {
      store: { pageStore },
      t
    } = this.props;
    if (pageStore.securityPaymentStatus === VerificationWaiverStatusEnum.CLOSE) {
      return (
        <TouchableOpacity onPress={this._handleTipClick}>
          <Image source={require("../../img/icon_faq.webp")} style={styles.rightViewStyle} />
        </TouchableOpacity>
      );
    }
    return <View></View>;
  };

  _imageItemView = (item: ImageObjType, index: number) => {
    return (
      <View style={styles.itemViewStyle} key={`item_${index}`}>
        <Image source={item.image} style={styles.itemImageStyle} />
        <Text style={styles.itemTextStyle}>{this.props.t(item.title)}</Text>
      </View>
    );
  };

  _handleProtocolClick = () => {
    this.props.navigation.navigate({
      url: "https://www.akulaku.com/artikel/syarat-ketentuan-pembayaran-tanpa-verifikasi-pin/"
    });
    this.securityCenterSensorEventTrace.protocolClick();
  };

  /**
   * 开通
   */
  _handleActivateNowClick = () => {
    const {
      store: { pageStore },
      t,
      navigation
    } = this.props;
    pageStore.updateSecurityVerification(VerificationWaiverStatusEnum.OPEN, () => {
      NativeToast.showMessage(t("开通成功"));
      pageStore.getSecurityPaymentStatusData();
      DeviceEventEmitter.emit(LISTENER.PIN_CODE_MANAGEMENT);
    });

    this.securityCenterSensorEventTrace.activateNowClick();
  };

  _handleTipClick = () => {
    const { t } = this.props;
    PopUpContainer.show({
      type: PopUpContainerType.D4_1_1,
      headerProps: {
        // 小额免密说明
        title: t("小额免密说明"),
        hasBackButton: false
      },
      renderContent: (
        <View style={styles.tipViewStyle}>
          <Text style={styles.tipDesTextStyle}>{t("开通小额免密后,单笔支付100K以内无需验PIN码")}</Text>
          <Text style={styles.tipDesTextStyle}>{t("每日免密次数限制为3次,超过次数限制后需验证PIN码")}</Text>
          <Text style={styles.tipDesTextStyle}>{t("当前仅支持在AKULAKU APP站内下单可使用小额免密功能")}</Text>
        </View>
      )
    });
    this.securityCenterSensorEventTrace.tipClick();
  };

  /**
   * 关闭
   */
  _handleDialogCloseClick = async () => {
    const {
      store: { pageStore },
      t,
      navigation
    } = this.props;
    const checkRisk = await NativeRiskCheckModule.checkRisk({ type: CheckRiskType.CLOSE_PASSWORD });
    if (!checkRisk.success) {
      NativeToast.showMessage(t("安全校验失败"));
    } else {
      pageStore.updateSecurityVerification(VerificationWaiverStatusEnum.CLOSE, () => {
        DeviceEventEmitter.emit(LISTENER.PIN_CODE_MANAGEMENT);
      });
      navigation.goBack();
    }
    this.securityCenterSensorEventTrace.closeActivatedSecretFreePayment();
  };

  _handleDialogContinueToUseClick = () => {
    this.securityCenterSensorEventTrace.continueToUseClick();
  };

  _handleCloseClick = () => {
    const { t } = this.props;
    AKDialog.show({
      type: DialogType.C3_1_1,
      desc: `${t("小额免密功能安全有保障,抢购更快捷")}${t("确认要关闭小额免密吗")}`,
      onPositivePress: this._handleDialogContinueToUseClick,
      positiveText: t("继续使用"),
      onNegativePress: this._handleDialogCloseClick,
      negativeText: t("确认关闭")
    });
    this.securityCenterSensorEventTrace.confirmClose();
  };

  activatedSecretFreePaymentView = () => {
    const {
      store: { pageStore },
      t
    } = this.props;
    return (
      <View style={styles.activatedContent}>
        <View style={styles.topViewStyle}>
          <Image source={require("../../img/icon_set_up.webp")} style={styles.activatedImageViewStyle} />
          <Text style={styles.topTextStyle}>{t("已开启小额免密")}</Text>
        </View>
        <Text style={styles.bottomTextStyle} onPress={this._handleCloseClick}>
          {t("关闭小额免密功能")}
        </Text>
      </View>
    );
  };

  unactivatedSecretFreePaymentView = () => {
    const {
      store: { pageStore },
      t
    } = this.props;
    return (
      <View style={styles.content}>
        <Text style={styles.titleTextStyle}>{t("开通小额免密")}</Text>
        <Text style={styles.subtitleTextStyle}>{t("开通后,单笔支付100K以内无需验PIN码")}</Text>
        <View style={styles.imageViewStyle}>
          {this.state.imageArray.map((value, index) => {
            return this._imageItemView(value, index);
          })}
        </View>
        <View style={styles.protocolViewStyle}>
          <Text style={styles.protocolLeftTextStyle}>
            {t("开启即同意")}
            <Text style={styles.protocolRightTextStyle} onPress={this._handleProtocolClick}>
              {t("小额免密功能使用条款")}
            </Text>
          </Text>
        </View>
        <TouchableOpacity style={styles.touchViewStyle} activeOpacity={0.8} onPress={this._handleActivateNowClick}>
          <Text style={styles.touchTextStyle}>{t("立即开通")}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  _render() {
    const {
      store: { pageStore }
    } = this.props;
    return pageStore.securityPaymentStatus === VerificationWaiverStatusEnum.CLOSE
      ? this.unactivatedSecretFreePaymentView()
      : this.activatedSecretFreePaymentView();
  }
}
const ITEM_WIDTH = (WINDOW_WIDTH - 32) / 3;
const styles = StyleSheet.create({
  content: {
    // alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 20
  },
  imageViewStyle: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
    marginTop: 28
    // paddingHorizontal: 12
  },
  itemImageStyle: {
    width: 44,
    height: 44
  },
  itemViewStyle: {
    alignItems: "center",
    width: ITEM_WIDTH
  },
  itemTextStyle: {
    width: ITEM_WIDTH,
    textAlign: "center",
    marginTop: 8,
    color: "#282B2E",
    fontSize: 12
  },
  titleTextStyle: {
    fontSize: 16,
    color: "#282B2E",
    ...FontStyles["roboto-bold"],
    textAlign: "center"
  },
  subtitleTextStyle: {
    marginTop: 8,
    color: "#989FA9",
    fontSize: 12,
    textAlign: "center"
  },
  protocolLeftTextStyle: {
    fontSize: 11,
    color: "#989FA9"
  },
  protocolRightTextStyle: {
    fontSize: 11,
    color: "#189BF9"
  },
  protocolViewStyle: {
    marginTop: 40
  },
  touchViewStyle: {
    marginTop: 8,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#F32823",
    alignItems: "center",
    justifyContent: "center"
  },
  touchTextStyle: {
    ...FontStyles["rob-medium"],
    fontSize: 16,
    color: "#fff"
  },
  tipTextStyle: {
    height: 12,
    width: 12,
    textAlign: "center",
    color: "#989FA9",
    fontSize: 12,
    borderRadius: 6,
    borderColor: "#989FA9",
    borderWidth: 2 //StyleSheet.hairlineWidth
  },
  tipDesTextStyle: {
    fontSize: 12,
    color: "#989FA9",
    marginBottom: 20
  },
  tipViewStyle: {
    paddingHorizontal: 16
  },
  rightViewStyle: {
    height: 32,
    width: 32
  },
  activatedContent: {
    alignItems: "center",
    flex: 1,
    ...Platform.select({
      ios: {
        paddingTop: 36
      },
      android: {
        paddingVertical: 36
      }
    })
  },
  activatedImageViewStyle: {
    height: 100,
    width: 100
  },
  topViewStyle: {
    flex: 1,
    alignItems: "center"
  },
  topTextStyle: {
    ...FontStyles["rob-medium"],
    fontSize: 14,
    color: "#282B2E",
    textAlign: "center",
    marginTop: 16
  },
  bottomTextStyle: {
    fontSize: 16,
    color: "#189BF9"
  },
  descStyle: {
    fontSize: 14,
    color: "#6E737D",
    textAlign: "center"
  },
  positiveStyle: {
    color: "#6E737D"
  },
  negativeStyle: {
    color: "#FF2E2E"
  }
});
