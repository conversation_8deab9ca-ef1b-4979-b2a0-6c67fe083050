/**
 * @Author: wenhui.dong
 * @Date: 2021-12-27 15:29:51
 * @description:小额免密
 */
import React from "react";
import {
  View,
  FlatList,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  DeviceEventEmitter,
  EmitterSubscription,
  NativeModules,
  Platform
} from "react-native";
import BaseContainer, { Type } from "common/components/BaseContainer";
import RootView from "common/components/Layout/RootView";
import { observer, inject, Observer } from "mobx-react";
import { withTranslation } from "common/services/i18n";
import { get, isEmpty } from "lodash";
import Store from "../../store/PINSettingStore";
import { SettingPINListData, VerificationWaiverStatusEnum } from "../../dict/SettingPINType";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { NativeRiskCheckModule } from "common/nativeModules";
import { PINCodeManagementSensorEventTrace } from "../../tool/EventTrace";
import { LISTENER } from "../../tool/client";
import { CheckRiskType } from "common/nativeModules/bussinse/nativeRiskCheckModule";
import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
const { GetEnvInfoModule } = NativeModules;

@RootView({
  withI18n: [
    "SettingPINPage",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json")
    }
  ],
  store: Store,
  keyApi: []
})
@withTranslation("SettingPINPage")
@inject("store")
@observer
export default class PINCodeManagement extends BaseContainer<Store> {
  securityCenterSensorEventTrace: PINCodeManagementSensorEventTrace;

  listener?: EmitterSubscription;

  constructor(props: Type.Props<Store>) {
    super(props);
    const sensor = { page_id: "1177", page_name: "PIN setting", sn: 202127 };
    this.props.configSensorEvent(sensor);
    this.securityCenterSensorEventTrace = new PINCodeManagementSensorEventTrace(sensor);
  }

  async componentDidMount() {
    const {
      store: { pageStore }
    } = this.props;
    await pageStore.getSecurityPaymentStatusData();
    this.props.configPageInfo({ sn: 202127, ext: { status: `0${pageStore.securityPaymentStatus + 1}` } }, false);
    this.listener = DeviceEventEmitter.addListener(LISTENER.PIN_CODE_MANAGEMENT, () => {
      pageStore.getSecurityPaymentStatusData();
    });
  }

  componentWillUnmount() {
    this.listener && this.listener.remove();
  }

  navigationBarProps = () => {
    return {
      title: this.props.t("PIN码设置管理")
    };
  };

  _handleItemClick = async (index: number) => {
    const {
      navigation,
      t,
      store: { pageStore }
    } = this.props;
    switch (index) {
      case 0:
        const securityPaymentStatus = pageStore.securityPaymentStatus;
        if (securityPaymentStatus === VerificationWaiverStatusEnum.CLOSE) {
          const checkRisk = await NativeRiskCheckModule.checkRisk({ type: CheckRiskType.OPEN_PASSWORD });
          if (!checkRisk.success) {
            NativeToast.showMessage(t("安全校验失败"));
          } else {
            navigation.navigate({
              screen: "PINVerificationSettings",
              params: { securityPaymentStatus: securityPaymentStatus }
            });
          }
        } else {
          navigation.navigate({
            screen: "PINVerificationSettings",
            params: { securityPaymentStatus: securityPaymentStatus }
          });
        }

        this.securityCenterSensorEventTrace.pinVerificationClick(`0${securityPaymentStatus + 1}`);
        break;
      case 1:
        navigation.navigate({ url: "ak://m.akulaku.com/2110" });
        this.securityCenterSensorEventTrace.pinChangeClick();
        break;
      default:
        break;
    }
  };

  itemComponent = (item: SettingPINListData) => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    const { rightTitle = "", isLeftTitle = false, subtitle = "" } = item;
    let leftText = "";
    if (isLeftTitle) {
      leftText = pageStore.securityPaymentStatus === VerificationWaiverStatusEnum.CLOSE ? "未开通" : "已开通";
    }
    return (
      <View style={styles.itemComponentStyle}>
        <View style={styles.leftViewStyle}>
          <View style={styles.topViewStyle}>
            <Text numberOfLines={2} style={styles.rightTitleTextStyle}>
              {t(rightTitle)}
            </Text>
            {isLeftTitle ? (
              <Text
                style={
                  pageStore.securityPaymentStatus === VerificationWaiverStatusEnum.CLOSE
                    ? styles.leftTitleUnactivatedTextStyle
                    : styles.leftTitleActivatedTextStyle
                }
              >
                {t(leftText)}
              </Text>
            ) : null}
            <Image source={require("../../img/icon_right_arrow.webp")} style={styles.rightImageStyle} />
          </View>
          {!isEmpty(subtitle) ? <Text style={styles.subtitleTextStyle}>{t(subtitle)}</Text> : null}
        </View>
      </View>
    );
  };

  _renderItem = ({ item, index }: { item: SettingPINListData; index: number }) => {
    if (index === 0) {
      if (
        (Platform.OS === "ios" && GetEnvInfoModule.versionCode < 437) ||
        (Platform.OS === "android" && GetEnvInfoModule.versionCode < 1190)
      ) {
        return null;
      }
    }

    return (
      <View>
        <TouchableOpacity activeOpacity={0.8} style={styles.itemViewStyle} onPress={() => this._handleItemClick(index)}>
          <Observer>
            {() => {
              return this.itemComponent(item);
            }}
          </Observer>
        </TouchableOpacity>
        {this._itemSeparatorComponent()}
      </View>
    );
  };

  _keyExtractor = (item: SettingPINListData, index: number) => `${index}`;

  _itemSeparatorComponent = () => {
    return <View style={styles.lineStyle} />;
  };

  _render() {
    const {
      store: { pageStore }
    } = this.props;
    return (
      <FlatList
        scrollEnabled={false}
        data={pageStore.settingPINData}
        keyExtractor={this._keyExtractor}
        renderItem={this._renderItem}
        // ItemSeparatorComponent={this._itemSeparatorComponent}
      />
    );
  }
}

const styles = StyleSheet.create({
  lineStyle: {
    backgroundColor: "#E2E5E9",
    height: StyleSheet.hairlineWidth,
    marginLeft: 16
  },
  itemViewStyle: {
    paddingHorizontal: 16,
    paddingVertical: 17
  },
  rightImageStyle: {
    marginTop: 2,
    height: 12,
    width: 12
  },
  leftViewStyle: {
    flex: 1
  },
  itemComponentStyle: {
    flexDirection: "row",
    alignItems: "center"
  },
  topViewStyle: {
    flexDirection: "row",
    // alignItems: "center",
    justifyContent: "space-between"
  },
  subtitleTextStyle: {
    marginTop: 8,
    color: "#989FA9",
    fontSize: 12
  },
  rightTitleTextStyle: {
    color: "#282B2E",
    fontSize: 14,
    width: WINDOW_WIDTH / 1.5
  },
  leftTitleUnactivatedTextStyle: {
    color: "#FF2E2E",
    fontSize: 12,
    marginTop: 2
  },
  leftTitleActivatedTextStyle: {
    marginTop: 2,
    color: "#282B2E",
    fontSize: 12
  }
});
