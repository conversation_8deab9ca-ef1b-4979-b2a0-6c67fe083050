import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    alignItems: "center",
    paddingTop: 12
  },
  image: {
    width: 56,
    height: 56,
    marginBottom: 20
  },
  status: {
    fontSize: 16,
    color: "#333333",
    ...FontStyles["rob-medium"],
    marginBottom: 16
  },
  firstText: {
    fontSize: 12,
    color: "#6E737D",
    marginBottom: 12,
    alignSelf: "flex-start"
  },
  time: {
    fontSize: 14,
    color: "#FF8D1A",
    ...FontStyles["rob-medium"]
  },
  list: {
    marginTop: 12,
    alignSelf: "flex-start"
  },
  lastText: {
    fontSize: 12,
    color: "#6E737D",
    alignSelf: "flex-start"
  },
  serviceNumber: {
    fontSize: 12,
    color: "#282B2E",
    ...FontStyles["rob-medium"]
  },
  firstButton: {
    width: WINDOW_WIDTH - 32,
    marginTop: 16
  },
  secondButton: {
    width: WINDOW_WIDTH - 32,
    marginTop: 12
  }
});
