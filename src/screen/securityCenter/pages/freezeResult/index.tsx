import BaseContainer from "common/components/BaseContainer";
import RootView from "common/components/Layout/RootView";
import { NativeNavigationModule } from "common/nativeModules";
import { withTranslation } from "common/services/i18n";
import { TimeFormat } from "common/util";
import { AKButton, Android } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { inject, observer } from "mobx-react";
import React from "react";
import { View, Image, ImageSourcePropType, Text, BackHandler, NativeEventSubscription } from "react-native";
import ListComponent from "../../components/ListComponent";
import { FreezeSource, LoginStatus } from "../../dict/userInfo";
import store from "../../store/FreezeNewStore";
import { FreezeEventReport, FreezeEventType } from "../../tool/freezeEventReport";
import styles from "./style";

type NavParams = {
  source: FreezeSource;
  loginStatus: LoginStatus; //登录态
  riskFlowId: string; //风控流程id
  phone: string; //电话号码
  registeredDays: number; //已注册天数
};

@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class FreezeResult extends BaseContainer<store, NavParams> {
  backHandlerListener: NativeEventSubscription | undefined;

  navigationBarProps = () => {
    const {
      store: {
        navParams: { source }
      }
    } = this.props;
    return {
      title: source === FreezeSource.EmerencyFreeze ? this.props.t("紧急冻结") : this.props.t("永久冻结"),
      onBackPress: () => this.onBackPress()
    };
  };

  onBackPress = () => {
    NativeNavigationModule.popToScreen("/securityCenter/home");
    return true;
  };

  async componentDidMount() {
    const {
      store: {
        pageStore,
        navParams: { source, loginStatus, riskFlowId, phone }
      }
    } = this.props;
    await pageStore.getFreezeResult(source, loginStatus, riskFlowId, phone);
    if (source === FreezeSource.EmerencyFreeze && pageStore.freezeSuccess) {
      this.props.configSensorEvent({ page_id: "1095", page_name: "freeze success1" });
      this.props.configPageInfo({ sn: 202159, sp: { status: 1 } }, true);
    } else if (source === FreezeSource.ForeverFreeze && pageStore.freezeSuccess) {
      this.props.configSensorEvent({ page_id: "1098", page_name: "freeze success2" });
      this.props.configPageInfo({ sn: 202159, sp: { status: 2 } }, true);
    }
    if (Android) {
      this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
    }
  }

  getCurrentTime = () => {
    const {
      store: {
        runtime: { countryCode }
      }
    } = this.props;
    //加上七天的毫秒值
    return TimeFormat(new Date().getTime() + 604800000, "YYYY-MM-DD", countryCode);
  };

  showContainer = () => {
    const {
      t,
      store: {
        pageStore,
        navParams: { source, registeredDays }
      }
    } = this.props;
    let path: ImageSourcePropType;
    let status;
    let firstText;
    let lastText;
    let showTime;
    let serviceNumber;
    let showList;
    let showForeverFreezeText;
    if (source === FreezeSource.EmerencyFreeze && pageStore.freezeSuccess) {
      //紧急冻结成功
      path = require("../../img/withdrawal_success.webp");
      status = t("紧急冻结成功");
      firstText = t("您的账号已“紧急冻结“成功，将于xxx日将自动解冻。");
      showTime = true;
      showList = true;
    } else if (source === FreezeSource.ForeverFreeze && pageStore.freezeSuccess) {
      //永久冻结成功
      path = require("../../img/withdrawal_success.webp");
      status = t("永久冻结成功");
      firstText = t("AKULAKU已经陪伴您{xxx}天了，感谢相遇，希望我们还能重逢。", { xxx: registeredDays });
      lastText = t("您也可以进行”登录“操作，我们将引导您解冻，期待您的回归。");
      showForeverFreezeText = true;
    } else {
      //冻结失败
      path = require("../../img/withdrawal_fail.webp");
      status = t("冻结失败");
      firstText = t("抱歉，我们当前无法帮您自主冻结账号");
      lastText = t("如有必要,可联系客服电话：");
      serviceNumber = true;
    }
    return (
      <>
        <Image source={path} style={styles.image} />
        <Text style={styles.status}>{status}</Text>
        <Text style={styles.firstText}>
          {firstText}
          {showTime && <Text style={styles.time}>{this.getCurrentTime()}</Text>}
        </Text>
        {showList && (
          <ListComponent
            title={t("在此期间，请尽快进行下列操作：")}
            data={[t("1、修改您的PIN码"), t("2、在安全中心-设备管理中删除非您本人设备")]}
            style={styles.list}
          />
        )}
        <Text style={styles.lastText}>
          {lastText}
          {serviceNumber && <Text style={styles.serviceNumber}>1500920</Text>}
        </Text>
        {showForeverFreezeText && (
          <Text style={[styles.lastText, { marginTop: 12 }]}>
            {t(
              '30天内未操作"解冻账号"，我们将依据隐私政策的要求，删除或匿名化处理您的账号数据，同时您的账号将被永久删除。'
            )}
          </Text>
        )}
      </>
    );
  };

  onPress = () => {
    const {
      store: {
        pageStore,
        navParams: { source }
      }
    } = this.props;
    if (source === FreezeSource.EmerencyFreeze && pageStore.freezeSuccess) {
      NativeNavigationModule.popToScreen("/securityCenter/home");
      FreezeEventReport(FreezeEventType.ClickReturn);
    } else if (source === FreezeSource.ForeverFreeze && pageStore.freezeSuccess) {
      NativeNavigationModule.popToScreen("/securityCenter/home");
      FreezeEventReport(FreezeEventType.ClickDone);
    } else {
      NativeNavigationModule.navigate({ screen: "helpCenterHomeScreen", params: { source: "FreezeResult" } });
    }
  };

  showBottomButton = () => {
    const {
      t,
      store: {
        pageStore,
        navParams: { source, loginStatus }
      }
    } = this.props;
    let text;
    if (source === FreezeSource.EmerencyFreeze && pageStore.freezeSuccess && loginStatus === LoginStatus.LOGIN) {
      text = t("返回安全中心");
    } else if (
      (source === FreezeSource.ForeverFreeze && pageStore.freezeSuccess) ||
      loginStatus === LoginStatus.NO_LOGIN
    ) {
      text = t("完成");
    } else {
      text = t("官方客服");
    }
    return (
      <>
        <AKButton text={text} type={AKButtonType.B1_1_2} style={styles.firstButton} onPress={this.onPress} />
        {!pageStore.freezeSuccess && (
          <AKButton
            text={t("返回")}
            type={AKButtonType.B1_2_2}
            style={styles.secondButton}
            onPress={() => {
              NativeNavigationModule.popToScreen("/securityCenter/home");
            }}
          />
        )}
      </>
    );
  };

  _render() {
    return (
      <View style={styles.container}>
        {this.showContainer()}
        {this.showBottomButton()}
      </View>
    );
  }
}
