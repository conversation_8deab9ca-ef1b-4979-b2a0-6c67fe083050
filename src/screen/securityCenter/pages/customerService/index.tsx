import { configSensorEvent } from "common/components/BaseContainer/Type";
import RootView from "common/components/Layout/RootView";
import { NativeNavigationModule } from "common/nativeModules";
import { withTranslation } from "common/services/i18n";
import { AKButton, FontStyles, NavigationBar } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { PageConfig } from "@akulaku-rn/rn-v4-sdk";
import { TFunction } from "i18next";
import { observer } from "mobx-react";
import React, { PureComponent } from "react";
import { View, Image, Text, StyleSheet } from "react-native";

type Props = {
  t: TFunction;
  configSensorEvent: configSensorEvent;
  configPageInfo: (config: PageConfig, isSupportV3: boolean, isInit?: boolean) => void;
};

@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ]
})
@withTranslation("SecurityCenter")
@observer
export default class CustomerService extends PureComponent<Props> {
  constructor(props: Props) {
    super(props);
    this.props.configSensorEvent({ page_id: "1094", page_name: "guide to sevice" });
    this.props.configPageInfo({ sn: 202158 }, true);
  }

  renderContainer = () => {
    const { t } = this.props;
    return (
      <View style={styles.container}>
        <Image source={require("../../img/noFreeze.webp")} style={styles.image} />
        <Text style={styles.tip}>{t("可联系Akulaku客服提供冻结服务")}</Text>
        <Text style={styles.phone}>{t("Akulaku官方客服电话")}</Text>
        <View style={styles.numberContainer}>
          <Text style={styles.text}>1500920</Text>
        </View>
        <AKButton
          type={AKButtonType.B1_1_2}
          text={t("完成")}
          onPress={() => {
            NativeNavigationModule.goBack();
          }}
        />
      </View>
    );
  };

  render() {
    const { t } = this.props;
    return (
      <>
        <NavigationBar title={t("冻结账户")} />
        {this.renderContainer()}
      </>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingTop: 40,
    backgroundColor: "#fff"
  },
  image: {
    width: 120,
    height: 120,
    alignSelf: "center",
    marginBottom: 12
  },
  tip: {
    fontSize: 14,
    color: "#6E737D",
    marginBottom: 20
  },
  phone: {
    fontSize: 12,
    color: "#989FA9",
    marginBottom: 4
  },
  numberContainer: {
    height: 40,
    backgroundColor: "rgba(239,242,246,0.5)",
    borderRadius: 4,
    marginBottom: 24
  },
  text: {
    fontSize: 14,
    color: "#282B2E",
    ...FontStyles["rob-medium"],
    lineHeight: 40,
    paddingLeft: 12
  }
});
