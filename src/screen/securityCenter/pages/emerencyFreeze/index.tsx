import BaseContainer, { Type } from "common/components/BaseContainer";
import RootView from "common/components/Layout/RootView";
import { NativeNavigationModule, NativeRiskCheckModule } from "common/nativeModules";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { CheckRiskType } from "common/nativeModules/bussinse/nativeRiskCheckModule";
import { withTranslation } from "common/services/i18n";
import { AKButton, Android, FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import Dialog from "@akulaku-rn/akui-rn/src/components/Dialog";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { LayoutDirection } from "@akulaku-rn/akui-rn/src/components/Dialog";
import { debounce } from "lodash";
import { inject, observer } from "mobx-react";
import React from "react";
import {
  View,
  StyleSheet,
  Image,
  Text,
  TouchableOpacity,
  ImageSourcePropType,
  BackHandler,
  NativeEventSubscription
} from "react-native";
import SelectReasonComponet from "../../components/SelectReasonComponet";
import StepComponent, { StepNumber } from "../../components/StepComponent";
import { ForeverProccess, LoginStatus, ReasonCode, SecurityMethod } from "../../dict/userInfo";
import store from "../../store/FreezeNewStore";
import { FreezeEventReport, FreezeEventType } from "../../tool/freezeEventReport";

type NavParams = {
  loginStatus: LoginStatus; //登录状态
  phone: string; //电话号码
};

enum FreezeCode {
  NOFACE = "132132500035" //未授信账户无法刷脸验证
}

@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class EmerencyFreeze extends BaseContainer<store, NavParams> {
  backHandlerListener: NativeEventSubscription | undefined;

  state = {
    process: 1, //流程页面
    selectReason: -1, //选择原因index
    otherReason: "" //用户输入其他原因
  };

  constructor(props: Type.Props<store, NavParams>) {
    super(props);
    const {
      store: { pageStore }
    } = this.props;
    pageStore.isLoading = false;
    FreezeEventReport(FreezeEventType.EnterEmerencyFreezeFirst);
  }

  navigationBarProps = () => {
    return {
      title: this.props.t("紧急冻结"),
      onBackPress: () => this.onBackPress()
    };
  };

  componentDidMount() {
    if (Android) {
      this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
    }
  }

  componentWillUnmount() {
    if (Android) {
      this.backHandlerListener && this.backHandlerListener.remove();
    }
  }

  onBackPress = () => {
    if (this.state.process === ForeverProccess.First) {
      NativeNavigationModule.goBack();
      FreezeEventReport(FreezeEventType.LeaveEmerencyFreezeFirst);
    } else {
      this.setState({ process: ForeverProccess.First });
      FreezeEventReport(FreezeEventType.EnterEmerencyFreezeFirst);
      FreezeEventReport(FreezeEventType.LeaveEmerencyFreezeSecond);
    }
    return true;
  };

  onPress = () => {
    this.setState({ process: ForeverProccess.Second });
    FreezeEventReport(FreezeEventType.SelectReason, 0, this.state.selectReason + 1);
    FreezeEventReport(FreezeEventType.LeaveEmerencyFreezeFirst);
    FreezeEventReport(FreezeEventType.EnterEmerencyFreezeSecond);
  };

  clickCheck = debounce(
    async (index: number) => {
      const {
        t,
        store: {
          navParams: { loginStatus, phone }
        }
      } = this.props;
      let freezeCode;
      let freezeNote;
      let securityMethod;
      switch (this.state.selectReason) {
        case 0:
          freezeCode = ReasonCode.PHONE_LOST;
          break;
        case 1:
          freezeCode = ReasonCode.ACCOUNT_HACKED;
          break;
        case 2:
          freezeCode = ReasonCode.OTRHER;
          freezeNote = this.state.otherReason;
          break;
        default:
          break;
      }
      switch (index) {
        case 0:
          securityMethod = SecurityMethod.SMS_VERFICATION;
          FreezeEventReport(FreezeEventType.ClickSMS);
          break;
        case 1:
          securityMethod = SecurityMethod.FACE_RECOGNITION;
          FreezeEventReport(FreezeEventType.ClickFace);
          break;
        default:
          break;
      }
      const checkRisk = await NativeRiskCheckModule.checkRisk({
        type:
          loginStatus === LoginStatus.NO_LOGIN
            ? CheckRiskType.NOLOGIN_EMERENCY_FREEZE
            : CheckRiskType.LOGIN_EMERENCY_FREEZE,
        phone: phone,
        securityMethod: securityMethod,
        freezeCode: freezeCode,
        freezeNote: freezeNote
      });
      if (checkRisk.success) {
        NativeNavigationModule.navigate({
          screen: "FreezeResult",
          params: { source: 1, loginStatus: loginStatus, riskFlowId: checkRisk?.data.operationId, phone: phone }
        });
        FreezeEventReport(FreezeEventType.LeaveEmerencyFreezeSecond);
      } else if (!checkRisk.success && checkRisk.errCode === FreezeCode.NOFACE) {
        Dialog.show({
          layoutDirection: LayoutDirection.LayoutVertical,
          negativeText: t("我知道了"),
          desc: t("无法使用该方式冻结，请尝试其他验证方式"),
          descStyle: styles.descStyle,
          negativeStyle: styles.negativeText,
          containerStyle: { borderRadius: 12 }
        });
      } else {
        NativeToast.showMessage(checkRisk.errMsg);
      }
    },
    500,
    {
      leading: true,
      trailing: false
    }
  );

  selectVerifyMethod = () => {
    const { t } = this.props;
    const methodData = [
      { image: require("../../img/SMS.webp"), text: t("短信验证码") },
      { image: require("../../img/face.webp"), text: t("人脸识别") }
    ];
    return (
      <View style={styles.container}>
        <Text style={styles.title}>{t("请选择您的身份验证方式")}</Text>
        {methodData.map((item: { image: ImageSourcePropType; text: string }, index: number) => {
          return (
            <TouchableOpacity
              key={index}
              style={styles.methodContainer}
              activeOpacity={0.8}
              onPress={() => {
                this.clickCheck(index);
              }}
            >
              <View style={styles.content}>
                <Image source={item.image} style={styles.image} />
                <Text style={styles.text}>{item.text}</Text>
              </View>
              <Image source={require("../../img/arrow.webp")} style={styles.arrow} />
            </TouchableOpacity>
          );
        })}
        <Text
          style={styles.other}
          onPress={() => {
            FreezeEventReport(FreezeEventType.ClickOtherMethod);
            NativeNavigationModule.navigate({ screen: "CustomerService" });
          }}
        >
          {t("无法通过以上方式冻结")}
        </Text>
      </View>
    );
  };

  _render() {
    const { t } = this.props;
    const { process } = this.state;
    const listContent = [
      { title: t("手机丢失"), desc: t("请紧急冻结后，尽快补办手机卡。") },
      { title: t("怀疑账号被盗"), desc: t("请紧急冻结后，尽快修改手机密码和PIN码") },
      { title: t("其他"), desc: t("请填写具体原因") }
    ];
    return (
      <>
        <View style={{ flex: 1, backgroundColor: "#EFF2F6" }}>
          <StepComponent t={t} step={process === ForeverProccess.First ? StepNumber.first : StepNumber.second} />
          {process === ForeverProccess.First ? (
            <SelectReasonComponet
              t={t}
              data={listContent}
              selectReason={(index: number) => {
                this.setState({ selectReason: index });
              }}
              onClickOther={(text: string) => {
                this.setState({ otherReason: text });
              }}
            />
          ) : (
            this.selectVerifyMethod()
          )}
        </View>
        {process === ForeverProccess.First && (
          <AKButton
            type={AKButtonType.B1_1_2}
            text={t("提交")}
            style={styles.button}
            onPress={this.onPress}
            disabled={
              this.state.selectReason === -1 ||
              (this.state.selectReason === listContent.length - 1 && !this.state.otherReason)
            }
          />
        )}
      </>
    );
  }
}

const styles = StyleSheet.create({
  button: {
    width: WINDOW_WIDTH - 32,
    alignSelf: "center",
    marginBottom: 16
  },
  container: {
    flex: 1,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    paddingTop: 16,
    backgroundColor: "#fff"
  },
  methodContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderBottomColor: "#E2E5E9",
    borderBottomWidth: 0.5,
    paddingVertical: 16,
    paddingHorizontal: 16
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1
  },
  image: {
    width: 32,
    height: 32,
    marginRight: 13
  },
  text: {
    fontSize: 15,
    color: "#43474C",
    ...FontStyles["rob-medium"]
  },
  arrow: {
    width: 12,
    height: 12
  },
  other: {
    fontSize: 14,
    color: "#189BF9",
    marginTop: 20,
    alignSelf: "center"
  },
  descStyle: {
    fontSize: 14,
    color: "#6E737D"
  },
  negativeText: {
    fontSize: 16,
    color: "#F32823",
    ...FontStyles["rob-medium"]
  },
  title: {
    fontSize: 14,
    color: "#000000",
    ...FontStyles["rob-medium"],
    marginLeft: 16,
    marginBottom: 12
  }
});
