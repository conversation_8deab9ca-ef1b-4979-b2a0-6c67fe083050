import React, { PureComponent, Component } from "react";
import { View, Text, ScrollView } from "react-native";
import { NavigationBar, UrlImage, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import styles from "./styles";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import RootView from "common/components/Layout/RootView";
import store from "../../store";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { TFunction } from "i18next";

type Props = {
  navigation: navigationModel;
  store: pageStoreModel<store>;
  t: TFunction;
};
type State = {};
@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class SecurityFaqDetail extends Component<Props, State> {
  render() {
    const {
      store: {
        navParams: { data }
      },
      t
    } = this.props;
    return (
      <View style={styles.container}>
        <NavigationBar title={t("常见问题")} />
        <ScrollView bounces={false}>
          {data.childFaqs.map(
            (item: {
              imgPlace: { imageUrl: string; height: number; width: number };
              answers: { answer: string; imgPlace: { width: number; height: number; imageUrl: string } }[];
              title: string;
            }) => {
              return (
                <>
                  <Text style={styles.headerText}>{item.title}</Text>
                  {!!item.imgPlace && (
                    <UrlImage
                      source={item.imgPlace.imageUrl}
                      width={WINDOW_WIDTH - 64}
                      height={(item.imgPlace.height / item.imgPlace.width) * (WINDOW_WIDTH - 64)}
                      resizeMode="cover"
                      style={{
                        height: (item.imgPlace.height / item.imgPlace.width) * (WINDOW_WIDTH - 64),
                        width: WINDOW_WIDTH - 64,
                        marginLeft: 16
                      }}
                    />
                  )}
                  <View style={styles.faqView}>
                    {item.answers.map(i => {
                      return (
                        <>
                          <Text style={styles.faqText}>{i.answer}</Text>
                          {!!i.imgPlace && (
                            <UrlImage
                              source={i.imgPlace.imageUrl}
                              width={WINDOW_WIDTH - 32}
                              height={(i.imgPlace.height / i.imgPlace.width) * (WINDOW_WIDTH - 32)}
                              resizeMode="cover"
                              style={{
                                height: (i.imgPlace.height / i.imgPlace.width) * (WINDOW_WIDTH - 32),
                                width: WINDOW_WIDTH - 32
                                // marginLeft: 16
                              }}
                            />
                          )}
                        </>
                      );
                    })}
                  </View>
                </>
              );
            }
          )}
        </ScrollView>
      </View>
    );
  }
}
