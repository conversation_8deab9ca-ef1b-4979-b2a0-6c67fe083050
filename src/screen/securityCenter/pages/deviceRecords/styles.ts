import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff"
  },
  headerText: {
    paddingVertical: 6,
    paddingHorizontal: 16,
    fontSize: 12,
    color: "#FE9334",
    backgroundColor: "#FFF5EC"
  },
  deviceItem: {
    paddingVertical: 16,
    marginLeft: 16,
    borderBottomColor: "#E2E5E9",
    borderBottomWidth: 0.5,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  rowView: {
    flexDirection: "row",
    alignItems: "flex-start"
  },
  name: {
    fontSize: 15,
    lineHeight: 18,
    color: "#43474C",
    maxWidth: WINDOW_WIDTH * 0.7
  },
  iconView: {
    marginLeft: 3,
    paddingHorizontal: 4,
    backgroundColor: "#FF5353",
    borderRadius: 4,
    marginRight: 16
  },
  iconText: {
    fontSize: 11,
    lineHeight: 16,
    color: "#fff"
  },
  time: {
    fontSize: 12,
    color: "#989FA9"
  },
  button: {
    paddingVertical: 9,
    paddingHorizontal: 24,
    borderWidth: 1,
    borderColor: "#FF5353",
    borderRadius: 6,
    marginHorizontal: 16
  },
  buttonText: {
    fontSize: 12,
    color: "#FF2E2E",
    ...FontStyles["rob-medium"]
  }
});
