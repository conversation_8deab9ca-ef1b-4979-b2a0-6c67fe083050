import React, { PureComponent } from "react";
import { View, Text, ScrollView, TouchableOpacity, EmitterSubscription } from "react-native";
import { Loading, NavigationBar, NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import VerticalList from "@akulaku-rn/akui-rn/src/components/PopUpPanel/components/VerticalList";
import store from "../../store";
import styles from "./styles";
import { withTranslation } from "common/services/i18n";
import { SERVICE_TYPES } from "../../tool/client";
import { inject } from "mobx-react";
import RootView from "common/components/Layout/RootView";
import { TimeFormat } from "common/util/Timer";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { AkuNativeEventEmitter, NativeRiskCheckModule } from "common/nativeModules";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { TFunction } from "i18next";
import { GetLoginDeviceInfo, LoginDevices, RemoveReason } from "../../dict/getLoginDeviceInfo";
import { equipmentManagementClick, selectReasonClick } from "../../tool/sensorLogger";
import { ScreenSensorEvent } from "common/nativeModules/sdk/nativeSensroModule";
import { AKButton, AKButtonType } from "common/components/AKButton";
import PopUpContainer, { PopUpContainerType } from "@akulaku-rn/akui-rn/src/components/PopUpContainer";
type Props = {
  navigation: navigationModel;
  store: pageStoreModel<store>;
  t: TFunction;
  configSensorEvent: (event: ScreenSensorEvent) => void;
};
type State = {
  data: { loginDevices: LoginDevices[]; removeReason: RemoveReason[] };
  loading: boolean;
  loadFailed: boolean;
};

@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
export default class SecurityDeviceRecords extends PureComponent<Props, State> {
  removeReason: null | number;

  screenEventListener!: EmitterSubscription;

  constructor(props: Props) {
    super(props);
    this.state = {
      data: { loginDevices: [], removeReason: [] },
      loading: true,
      loadFailed: false
    };
    this.removeReason = null;
  }

  componentDidMount() {
    this.getData();
    this.addListener();
  }

  componentWillUnmount() {
    this.screenEventListener && this.screenEventListener.remove();
  }

  getData = () => {
    this.setState({ loading: true, loadFailed: false });
    const {
      store: { pageStore }
    } = this.props;
    pageStore.fetch(
      SERVICE_TYPES.GetLoginDeviceInfo,
      {},
      (response: GetLoginDeviceInfo) => {
        const { success, data } = response;
        if (success) {
          this.setState({ loadFailed: false, loading: false, data });
        } else {
          this.setState({ loadFailed: true, loading: false });
        }
      },
      () => {
        this.setState({ loadFailed: true, loading: false });
      }
    );
  };

  addListener = () => {
    const {
      navParams: { screen }
    } = this.props.store;
    this.screenEventListener = AkuNativeEventEmitter.addListener(screen, async (event: any) => {
      if (event.eventName === "onEnter" || event.eventName === "onLeave") {
        this.props.configSensorEvent({
          page_id: "1024",
          page_name: "Equipment management page"
        });
      }
    });
  };

  setRemoveReason = (removeReason: { reasonType: number; id: number }) => {
    this.removeReason = removeReason.reasonType;
    PopUpContainer.dismiss();
    this.untrustAction(removeReason.id);
  };

  untrustReasons = (id: number) => {
    const { t } = this.props;
    const data: { value: string; reasonType: number; id: number }[] = [];
    this.state.data.removeReason.map(i => {
      data.push({ value: i.reason, reasonType: i.reasonType, id });
    });
    equipmentManagementClick();
    PopUpContainer.show({
      type: PopUpContainerType.D4_1_1,
      headerProps: { title: t("取消信任原因"), hasBackButton: false },
      renderContent: (
        <VerticalList data={data} didSelectedItem={this.setRemoveReason} selectedItem={this.removeReason} />
      )
    });
  };

  untrustAction = async (id: number) => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    const checkRisk = await NativeRiskCheckModule.checkRisk({ type: 31 });
    if (!checkRisk.success) {
      NativeToast.showMessage(t("安全校验失败"));
      return;
    }
    selectReasonClick(this.removeReason);
    pageStore.fetch(
      SERVICE_TYPES.DeleteLog,
      { logId: id, reasonType: this.removeReason, operationId: checkRisk.data.operationId },
      (response: { success: boolean }) => {
        const { success } = response;
        if (success) {
          this.getData();
          NativeToast.showMessage(t("成功取消信任设备"));
        } else {
          NativeToast.showMessage(t("取消信任设备失败"));
        }
      },
      () => {
        NativeToast.showMessage(t("取消信任设备失败"));
      }
    );
  };

  render() {
    const {
      t,
      store: {
        runtime: { countryCode }
      }
    } = this.props;
    const { data, loadFailed, loading } = this.state;
    if (loading) {
      return <Loading />;
    }
    if (loadFailed) {
      return (
        <View style={{ flex: 1 }}>
          <NavigationBar title={t("登录设备记录")} />
          <NetworkErrorComponent
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            onRetryPress={this.getData}
            buttonText={t("global:刷新")}
          />
        </View>
      );
    }
    return (
      <View style={styles.container}>
        <NavigationBar title={t("登录设备记录")} />
        <Text style={styles.headerText}>
          {t("显示您账户最近30天登录的设备情况，发现有非本人操作的设备，请及时修改密码，保障账户安全！")}
        </Text>
        <ScrollView>
          {data.loginDevices.map((item, index) => {
            return (
              <View style={styles.deviceItem} key={index}>
                <View>
                  <View style={styles.rowView}>
                    <Text style={styles.name}>
                      {item.deviceBrand}
                      {item.deviceModel}
                    </Text>
                    {!!item.localUse && (
                      <View style={styles.iconView}>
                        <Text style={styles.iconText}>{t("本机")}</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.time}>
                    {TimeFormat(new Date(item.loginTime), "DD/MM/YYYY hh:mm", countryCode)}
                  </Text>
                </View>
                {!item.localUse && (
                  <AKButton
                    onPress={() => {
                      this.untrustReasons(item.logId);
                    }}
                    text={t("删除")}
                    type={AKButtonType.B1_2_3}
                    style={{ paddingHorizontal: 24, marginHorizontal: 16 }}
                  />
                )}
              </View>
            );
          })}
        </ScrollView>
      </View>
    );
  }
}
