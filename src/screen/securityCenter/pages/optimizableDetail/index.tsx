import React, { Component } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  DeviceEventEmitter,
  BackHandler,
  NativeEventSubscription
} from "react-native";
import { NavigationBar } from "@akulaku-rn/akui-rn";
import PageTipsComponent from "@akulaku-rn/akui-rn/src/components/PageTipsComponent";
import styles from "./styles";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import RootView from "common/components/Layout/RootView";
import store from "../../store";
import { SensorTypeCLICKItem } from "../../tool/sensorLogger";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { ScreenSensorEvent } from "common/nativeModules/sdk/nativeSensroModule";
import { TFunction } from "i18next";
import { PageConfig } from "@akulaku-rn/rn-v4-sdk";
import { PageTipsType } from "@akulaku-rn/akui-rn/src/components/PageTipsComponent";
type Props = {
  navigation: navigationModel;
  store: pageStoreModel<store>;
  t: TFunction;
  configSensorEvent: (event: ScreenSensorEvent) => void;
  configPageInfo: (config: PageConfig, isSupportV3: boolean) => void;
};
type State = {};
@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class SecurityOptimizableDetail extends Component<Props, State> {
  backHandlerListener!: NativeEventSubscription;

  constructor(props: Props) {
    super(props);
    props.configSensorEvent({
      page_name: "Security center subpage",
      page_id: "917"
    });
    props.configPageInfo({ sn: 103027 }, false);
  }

  componentDidMount() {
    this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
  }

  componentWillUnmount() {
    this.backHandlerListener.remove();
  }

  onBackPress = () => {
    this.goback();
    return true;
  };

  buttonOnPress = (data: { link: string }) => {
    this.props.navigation.navigate({ url: data.link });
    SensorTypeCLICKItem({ type: 104, item: data });
  };

  goback = () => {
    DeviceEventEmitter.emit("refreshSecurityCenter");
    this.props.navigation.goBack();
  };

  render() {
    const {
      store: {
        navParams: { data }
      },
      t
    } = this.props;

    return (
      <View style={styles.container}>
        <NavigationBar title={t("安全中心")} onBackPress={this.goback} />
        <PageTipsComponent
          type={PageTipsType.F6_1_1}
          text={t("发现下列异常，若非本人操作，建议立即处理")}
          backgroundColor={"#FFF5EC"}
          textColor={"#FE9334"}
        />
        {data.map(
          (
            item: { link: string; title: React.ReactNode; details: React.ReactNode },
            index: string | number | null | undefined
          ) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  this.buttonOnPress(item);
                }}
                style={styles.button}
              >
                <View>
                  <Text style={styles.title}>{item.title}</Text>
                  <Text style={styles.details}>{item.details}</Text>
                </View>
                <Image style={styles.icon} source={require("../../img/icon_more.webp")} />
              </TouchableOpacity>
            );
          }
        )}
      </View>
    );
  }
}
