import BaseContainer, { Type } from "common/components/BaseContainer";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import React from "react";
import { AKButton, UrlImage } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import styles from "../unfreeze/styles";
import { NativeNavigationModule } from "common/nativeModules";
import { EnterLeaveUnfreeze, sensorType, UnfreezePageClick } from "../../tool/sensorLogger";
import store, { UnfreezeResultType } from "../../store/index";

type NavParams = {
  riskFlowStatus: UnfreezeResultType; //解冻状态
};
@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class UnfreezeResult extends BaseContainer<store, NavParams> {
  constructor(props: Type.Props<store, NavParams>) {
    super(props);
    const {
      store: { pageStore }
    } = this.props;
    pageStore.isLoading = false;
  }

  navigationBarProps = () => {
    return {
      title: this.props.t("解除账户冻结"),
      onBackPress: () => NativeNavigationModule.popToScreen("/securityCenter/home")
    };
  };

  async componentDidMount() {
    const {
      store: {
        navParams: { riskFlowStatus }
      }
    } = this.props;
    if (riskFlowStatus === UnfreezeResultType.REVIEWING) {
      EnterLeaveUnfreeze(sensorType.EnReviewing);
    } else if (riskFlowStatus === UnfreezeResultType.SUCCESS) {
      EnterLeaveUnfreeze(sensorType.EnSuccess);
    } else {
      EnterLeaveUnfreeze(sensorType.EnFailed);
    }
  }

  clickOK = () => {
    UnfreezePageClick(sensorType.ClickOK);
    EnterLeaveUnfreeze(sensorType.LeReviewing);
    NativeNavigationModule.popToScreen("/securityCenter/home");
  };

  clickNowUse = () => {
    const {
      store: {
        user: { isLogin }
      }
    } = this.props;
    UnfreezePageClick(sensorType.useNow);
    EnterLeaveUnfreeze(sensorType.LeSuccess);
    if (isLogin) {
      NativeNavigationModule.popToHome(0);
    } else {
      NativeNavigationModule.navigate({
        url: "ak://m.akulaku.com/36"
      });
    }
  };

  _render() {
    const {
      t,
      store: {
        navParams: { riskFlowStatus }
      }
    } = this.props;
    let content;
    if (riskFlowStatus === UnfreezeResultType.REVIEWING) {
      content = (
        <View style={styles.container}>
          <UrlImage
            source="https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ec_conuntry_in_screen_secutiryCenter_aabd597eaef788fab999dfff5ec7a910_reviewing.png"
            style={styles.image}
          />
          <Text style={styles.reviewText}>{t("资料提交成功，正在审核中，审核结果出具后将短信通知您")}</Text>
          <TouchableOpacity style={styles.reviewButton} onPress={this.clickOK}>
            <Text style={styles.reviewButtonText}>{"OK"}</Text>
          </TouchableOpacity>
        </View>
      );
    } else if (riskFlowStatus === UnfreezeResultType.SUCCESS) {
      content = (
        <View style={styles.container}>
          <Image style={styles.icon} source={require("../../img/withdrawal_success.webp")} />
          <Text style={styles.successTitle}>{t("恭喜，您的账号成功解冻!")}</Text>
          <Text style={[styles.contentTitle, { textAlign: "center" }]}>
            {t("我们会极力保证您的账号安全，请放心使用!")}
          </Text>
          <AKButton
            onPress={this.clickNowUse}
            text={t("立即使用")}
            type={AKButtonType.B1_1_2}
            style={{ width: "100%" }}
          />
        </View>
      );
    } else {
      content = (
        <View style={styles.container}>
          <Image style={styles.icon} source={require("../../img/withdrawal_fail.webp")} />
          <Text style={styles.successTitle}>{t("解冻失败，")}</Text>
          <Text style={[styles.contentTitle, { textAlign: "center" }]}>{t("您的账户仍存在风险，请咨询客服。")}</Text>
          <View style={{ alignItems: "center", width: "100%", flex: 1 }}>
            <AKButton
              onPress={() => {
                UnfreezePageClick(sensorType.contactCustomer);
                EnterLeaveUnfreeze(sensorType.LeFailed);
                NativeNavigationModule.navigate({
                  screen: "helpCenterHomeScreen",
                  params: { source: "UnfreezeResult" }
                });
              }}
              text={t("咨询客服")}
              type={AKButtonType.B1_1_2}
              style={{ width: "100%" }}
            />
            <AKButton
              onPress={() => {
                UnfreezePageClick(sensorType.gotIt);
                EnterLeaveUnfreeze(sensorType.LeFailed);
                NativeNavigationModule.navigate({
                  url:
                    "ak://m.akulaku.com/1602?screen=/securityCenter/home&isNeedLogin=1&isFreezeResult=true&gestureEnabled=false"
                });
              }}
              text={t("我知道了")}
              type={AKButtonType.B1_2_2}
              style={{
                width: "100%",
                marginTop: 12
              }}
            />
          </View>
        </View>
      );
    }
    return <>{content}</>;
  }
}
