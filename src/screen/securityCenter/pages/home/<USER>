import React, { Component, RefObject } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  DeviceEventEmitter,
  EmitterSubscription,
  NativeEventSubscription,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  View
} from "react-native";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import store from "../../store";
import { Android, Loading, NavigationBar, NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import styles from "./styles";
import RootView from "common/components/Layout/RootView";
import SafetyInspection from "../../components/SafetyInspection";
import SafeOperation from "../../components/SafeOperation";
import SafetyEducation from "../../components/SafetyEducation";
import { SERVICE_TYPES } from "../../tool/client";
import { EnterLeaveHomePage, ExposeV4 } from "../../tool/sensorLogger";
import AkuNativeEventEmitter from "common/nativeModules/emitter/nativeEventEmitter";
import { ON_LOGIN_STATE_CHANGE } from "common/constant";
import { RetrieveData } from "common/util/cache";
import { NativeNavigationModule, NativeUserInfoModule } from "common/nativeModules";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { ScreenSensorEvent } from "common/nativeModules/sdk/nativeSensroModule";
import { TFunction } from "i18next";
import { UserInfo } from "../../dict/userInfo";
import { Optimization, Optimizations } from "../../dict/optimization";
import { Data as educationInfoData, EducationInfo } from "../../dict/educationInfo";
import { PageConfig } from "@akulaku-rn/rn-v4-sdk";

type Props = {
  navigation: navigationModel;
  store: pageStoreModel<store>;
  t: TFunction;
  configSensorEvent: (event: ScreenSensorEvent) => void;
  configPageInfo: (config: PageConfig) => void;
};

type State = {
  data: educationInfoData | {};
  optimizations: Optimizations[];
  loading: boolean;
  loadFailed: boolean;
  status: string;
  showNav: boolean;
  scrollEventThrottle: number;
};

@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class SecurityCenter extends Component<Props, State> {
  optimizationNumber: number;

  firstEnter: boolean;

  safetyInspection: RefObject<SafetyInspection>;

  isLogin: boolean;

  userInfo!: UserInfo;

  safeOperation: React.RefObject<unknown>;

  timer!: number;

  screenEventListener!: EmitterSubscription;

  loginListener!: EmitterSubscription;

  refreshListener!: EmitterSubscription;

  backHandlerListener?: NativeEventSubscription;

  constructor(props: Props) {
    super(props);
    this.state = {
      data: {},
      optimizations: [],
      loading: true,
      loadFailed: false,
      status: "scanning",
      showNav: false,
      scrollEventThrottle: 16
    };
    this.optimizationNumber = 0;
    this.safeOperation = React.createRef();
    this.safetyInspection = React.createRef();
    this.firstEnter = true;
    this.isLogin = true;
  }

  async componentDidMount() {
    const {
      store: {
        runtime,
        pageStore,
        navParams: { screen }
      }
    } = this.props;
    this.userInfo = await NativeUserInfoModule.getUserInfo();
    pageStore.hasClickDot = await RetrieveData(`${runtime.uid}hasClickDot`);
    this.getData();
    this.screenEventListener = AkuNativeEventEmitter.addListener(screen, async (event: { eventName: string }) => {
      if (event.eventName === "onEnter") {
        if (!this.isLogin) {
          setTimeout(() => {
            this.props.navigation.popToHome(3);
          }, 1000);
          return;
        }
        if (!!this.optimizationNumber && !this.firstEnter) {
          EnterLeaveHomePage(this.optimizationNumber, true);
        }
        this.firstEnter = false;
      } else if (event.eventName === "onLeave") {
        EnterLeaveHomePage(this.optimizationNumber);
      }
    });
    this.loginListener = AkuNativeEventEmitter.addListener(
      ON_LOGIN_STATE_CHANGE,
      async (loginState: { isLogin: boolean }) => {
        this.isLogin = loginState.isLogin;
      }
    );
    this.refreshListener = DeviceEventEmitter.addListener("refreshSecurityCenter", async () => {
      this.userInfo = await NativeUserInfoModule.getUserInfo();
      this.againScanAnimated();
      this.getData();
      this.safetyInspection && this.safetyInspection.current && this.safetyInspection.current.angainLottie();
    });
    if (Android) {
      this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
    }
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    this.screenEventListener.remove();
    this.loginListener.remove();
    this.refreshListener.remove();
    this.backHandlerListener?.remove;
  }

  onBackPress = () => {
    const {
      store: {
        navParams: { isFreezeResult }
      }
    } = this.props;
    if (isFreezeResult) {
      NativeNavigationModule.popToHome(3);
    } else {
      this.props.navigation.goBack();
    }
    return true;
  };

  getData = () => {
    this.setState({ loading: true, loadFailed: false });
    const {
      store: { pageStore }
    } = this.props;
    pageStore.fetch(
      SERVICE_TYPES.EducationInfo,
      {},
      (response: EducationInfo) => {
        const { success, data } = response;
        if (success) {
          this.setState({ data, loading: false, loadFailed: false }, () => {
            this.getOptimization();
            ExposeV4(data.buttonInfos);
          });
        } else {
          this.setState({ loadFailed: true, loading: false });
        }
      },
      () => {
        this.setState({ loadFailed: true, loading: false });
      }
    );
  };

  getOptimization = () => {
    const {
      store: { pageStore }
    } = this.props;
    pageStore.fetch(
      SERVICE_TYPES.Optimization,
      {},
      (response: Optimization) => {
        const { success, data } = response;
        if (success) {
          this.timer = setTimeout(() => {
            if (!data.optimizationNumber) {
              this.setState({ status: "success" });
            } else {
              this.setState({ status: "optimizable", optimizations: data.optimizations });
            }
          }, 3000);
          this.optimizationNumber = data.optimizationNumber;
          EnterLeaveHomePage(this.optimizationNumber, true);
        } else {
          this.timer = setTimeout(
            () => {
              this.setState({ status: "error" });
            },
            !!this.userInfo.data ? 3000 : 0
          );
        }
      },
      () => {
        this.timer = setTimeout(() => {
          this.setState({ status: "error" });
        }, 3000);
      }
    );
  };

  onScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { pageStore } = this.props.store;
    if (e.nativeEvent.contentOffset.y >= 185) {
      this.setState({ showNav: true, scrollEventThrottle: 0 });
    } else {
      this.setState({ showNav: false, scrollEventThrottle: 16 });
    }
    pageStore.adBannerRef && pageStore.adBannerRef.onScroll();
  };

  againScanAnimated = () => {
    this.setState({
      status: "scanning"
    });
  };

  onScrollEndDrag = () => {
    const { status } = this.state;
    switch (status) {
      case "error":
      case "optimizable":
        // @ts-ignore
        this.safeOperation && this.safeOperation.current && this.safeOperation.current.reduceView();
        break;
      case "scanning":
      case "success":
        // @ts-ignore
        this.safeOperation && this.safeOperation.current && this.safeOperation.current.increaseView();
        break;
    }
  };

  render() {
    const { t, navigation, store } = this.props;
    const { showNav, scrollEventThrottle, data, loadFailed, loading, optimizations, status } = this.state;
    const navStyle = showNav
      ? { position: "absolute", zIndex: 1, width: "100%" }
      : { backgroundColor: "transparent", position: "absolute", zIndex: 1, width: "100%" };
    if (loading) {
      return (
        <View>
          <Loading />
        </View>
      );
    }
    if (loadFailed) {
      return (
        <View style={{ flex: 1 }}>
          <NetworkErrorComponent
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            onRetryPress={this.getData}
            buttonText={t("global:刷新")}
          />
        </View>
      );
    }
    return (
      <View style={{ flex: 1 }}>
        <NavigationBar title={t("安全中心")} containerStyle={navStyle} />
        <ScrollView
          onScroll={this.onScroll}
          scrollEventThrottle={scrollEventThrottle}
          style={styles.scrollView}
          contentContainerStyle={{ marginBottom: 80 }}
          showsVerticalScrollIndicator={false}
          bounces={false}
          onScrollEndDrag={this.onScrollEndDrag}
          onMomentumScrollEnd={this.onScrollEndDrag}
        >
          <SafetyInspection
            t={t}
            ref={this.safetyInspection}
            status={status}
            optimizations={optimizations}
            againScanAnimated={this.againScanAnimated}
            againScanNetwork={this.getOptimization}
            userInfo={this.userInfo}
          />
          <SafeOperation
            ref={this.safeOperation}
            store={store}
            t={t}
            status={status}
            navigation={navigation}
            data={data}
            userInfo={this.userInfo}
          />
          <SafetyEducation t={t} data={data} />
        </ScrollView>
      </View>
    );
  }
}
