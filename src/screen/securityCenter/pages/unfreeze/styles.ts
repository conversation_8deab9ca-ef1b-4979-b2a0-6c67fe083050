import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    alignItems: "center",
    paddingHorizontal: 16
  },
  img: {
    width: 150,
    height: 150,
    marginTop: 40
  },
  icon: {
    width: 56,
    height: 56,
    marginTop: 32
  },
  title: {
    fontSize: 17,
    color: "#43474C",
    ...FontStyles["rob-medium"],
    marginTop: 8,
    marginBottom: 20
  },
  successTitle: {
    fontSize: 17,
    color: "#43474C",
    ...FontStyles["rob-medium"],
    marginTop: 32,
    marginBottom: 12,
    textAlign: "center"
  },
  contentTitle: {
    fontSize: 14,
    color: "#6E737D",
    marginBottom: 24
  },
  button: {
    backgroundColor: "#FF5353",
    width: "100%",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 6
  },
  buttonTitle: {
    fontSize: 16,
    color: "#fff",
    textAlign: "center"
  },
  okButton: {
    borderColor: "#FF5353",
    borderWidth: 1,
    width: "100%",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 6,
    marginTop: 12
  },
  okButtonTitle: {
    fontSize: 16,
    color: "#FF5353"
  },
  resetText: {
    fontSize: 12,
    color: "#282B2E",
    backgroundColor: "#FFF9E6",
    paddingHorizontal: 16,
    paddingVertical: 12
  },
  inputContainer: {
    marginLeft: 16
  },
  buttonContainer: {
    backgroundColor: "#FF5353",
    borderRadius: 6,
    marginHorizontal: 16,
    height: 40,
    marginTop: 40
  },
  buttonText: {
    fontSize: 16,
    color: "#FFFFFF",
    lineHeight: 40,
    textAlign: "center"
  },
  image: {
    width: 150,
    height: 150,
    marginTop: 40,
    marginBottom: 12
  },
  reviewText: {
    fontSize: 14,
    color: "#6E737D",
    textAlign: "center",
    marginBottom: 24
  },
  reviewButton: {
    width: WINDOW_WIDTH - 32,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#F32823"
  },
  reviewButtonText: {
    color: "#FFF",
    fontSize: 16,
    lineHeight: 40,
    textAlign: "center"
  },
  bigImage: {
    width: 120,
    height: 120,
    marginBottom: 12,
    marginTop: 40
  },
  currentText: {
    fontSize: 14,
    color: "#6E737D",
    alignSelf: "flex-start"
  },
  statusText: {
    fontSize: 16,
    color: "#F32823",
    ...FontStyles["rob-medium"]
  },
  notFreezeText: {
    fontSize: 12,
    color: "#989FA9",
    marginTop: 12,
    alignSelf: "flex-start"
  },
  clickButton: {
    marginTop: 28,
    width: WINDOW_WIDTH - 32
  },
  foreverText: {
    fontSize: 16,
    color: "#2092E5",
    marginTop: 16,
    ...FontStyles["rob-medium"]
  },
  service: {
    fontSize: 12,
    color: "#282B2E",
    ...FontStyles["rob-medium"]
  },
  descStyle: {
    fontSize: 14,
    color: "#6E737D"
  },
  dialogText: {
    fontSize: 16,
    ...FontStyles["rob-medium"]
  }
});
