import React from "react";
import { DeviceEventEmitter, Image, Text, TouchableOpacity, View } from "react-native";
import styles from "./styles";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import RootView from "common/components/Layout/RootView";
import store, { FreezeReason, FreezeStatus } from "../../store";
import { NativeNavigationModule, NativeRiskCheckModule } from "common/nativeModules";
import { sensorType, UnfreezePageClick } from "../../tool/sensorLogger";
import { debounce } from "lodash";
import BaseContainer, { Type } from "common/components/BaseContainer";
import { AKButton, AKDialog, AkuTextComponent, AkuTextComponentType } from "@akulaku-rn/akui-rn";
import ListComponent from "../../components/ListComponent";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { LayoutDirection } from "@akulaku-rn/akui-rn/src/components/Dialog";
import { CheckRiskType } from "common/nativeModules/bussinse/nativeRiskCheckModule";
import { LoginStatus } from "../../dict/userInfo";
import { FreezeEventReport, FreezeEventType, LoginStatusReport, PageStatus } from "../../tool/freezeEventReport";
import { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import { DeleteAccountSensor } from "../../tool/EventTrace";

type NavParams = {
  loginStatus: LoginStatus;
  phone: string;
  platform: string;
  openId: string;
  source: string;
};

@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class Unfreeze extends BaseContainer<store, NavParams> {
  loginStatus = LoginStatus.LOGIN;

  constructor(props: Type.Props<store, NavParams>) {
    super(props);
    const {
      store: {
        navParams: { loginStatus }
      }
    } = this.props;
    if (Number(loginStatus) === LoginStatus.NO_LOGIN) {
      this.loginStatus = Number(LoginStatus.NO_LOGIN);
    }
  }

  navigationBarProps = () => {
    const {
      store: { pageStore }
    } = this.props;
    if (pageStore.freezeStatus === FreezeStatus.Normal) {
      return {
        title: this.props.t("冻结账户")
      };
    } else {
      return {
        title: this.props.t("解除账户冻结")
      };
    }
  };

  deleteAccountSensor?: DeleteAccountSensor;

  async componentDidMount() {
    const {
      store: {
        pageStore,
        navParams: { loginStatus }
      }
    } = this.props;
    if (Number(loginStatus) === LoginStatus.NO_LOGIN) {
      //非登录态进来直接显示永久冻结状态
      pageStore.isLoading = false;
      pageStore.freezeStatus = FreezeStatus.ForeverFreeze;
    } else {
      pageStore.getAccountLogoutConfig();
      await pageStore.checkUserFreezeStatus();
    }
    if (pageStore.freezeStatus === FreezeStatus.Normal) {
      //冻结主页面登录态
      this.props.configSensorEvent(
        {
          page_id: "1091",
          page_name: "freeze account page",
          extra: { Aku_PageStatus: LoginStatusReport.Login }
        },
        true
      );
      this.deleteAccountSensor = new DeleteAccountSensor({ page_id: "1091", page_name: "freeze account page", sn: 0 });
      this.props.configPageInfo({ sn: 202155, sp: { status: LoginStatus.LOGIN } }, true);
    } else {
      //解冻其他页面
      this.props.configSensorEvent({
        page_id: "1049",
        page_name: "Unfreeze account",
        extra: { Aku_PageStatus: this.getStatus(pageStore.freezeStatus) }
      });
      this.deleteAccountSensor = new DeleteAccountSensor({ page_id: "1049", page_name: "Unfreeze account", sn: 0 });
      this.props.configPageInfo({ sn: 201086, sp: { status: this.getStatus(pageStore.freezeStatus) } }, true);
    }
  }

  componentWillUnmount() {
    DeviceEventEmitter.emit("refreshSecurityCenter");
  }

  getStatus = (status?: number) => {
    let pageStatus;
    switch (status) {
      case 0:
        pageStatus = PageStatus.NoFreeze;
        break;
      case 2:
        pageStatus = PageStatus.EmerencyFreeze;
        break;
      case 3:
        pageStatus = PageStatus.ForeverFreeze;
        break;
      case 4:
        pageStatus = PageStatus.Other;
        break;
      default:
        pageStatus = PageStatus.Error;
        break;
    }
    return pageStatus;
  };

  unfreezeNow = debounce(
    async () => {
      const {
        store: {
          navParams: { phone, platform, openId, source },
          pageStore
        }
      } = this.props;
      UnfreezePageClick(sensorType.unfreezeNow, this.getStatus(pageStore.freezeStatus));
      const checkRisk = await NativeRiskCheckModule.checkRisk({
        type: this.loginStatus === LoginStatus.LOGIN ? CheckRiskType.LOGIN_UNFREEZE : CheckRiskType.NOLOGIN_UNFREEZE,
        page_id: "1049",
        page_name: "Unfreeze account",
        phone: this.loginStatus === LoginStatus.LOGIN ? "" : phone,
        platform: this.loginStatus === LoginStatus.LOGIN ? "" : platform,
        openId: this.loginStatus === LoginStatus.LOGIN ? "" : openId,
        source: source
      });
      if (!checkRisk.success) {
        NativeNavigationModule.goBack();
      }
      if (checkRisk.success) {
        NativeNavigationModule.navigate({
          screen: "ResetPassword",
          params: {
            operationId: checkRisk.data?.operationId,
            loginStatus: this.loginStatus,
            openId: openId,
            platformType: platform,
            phone
          }
        });
      }
    },
    500,
    { leading: true, trailing: false }
  );

  showDukcapilView = () => {};

  showOtherStatusView = () => {
    const { t } = this.props;
    return (
      <View style={styles.container}>
        <Image style={styles.img} source={require("../../img/icon_frozen.webp")} />
        <Text style={styles.title}>{t("您的账号为冻结状态!")}</Text>
        <Text style={styles.contentTitle}>
          {t("该账号目前无法享受我们的优惠活动和其他服务。建议进行解冻，解冻后将恢复正常")}
        </Text>
        <AKButton
          onPress={() => {
            this.unfreezeNow();
          }}
          text={t("立即解冻")}
          type={AKButtonType.B1_1_2}
          style={{ width: "100%" }}
        />
      </View>
    );
  };

  showImage = () => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    let path;
    let currentText;
    let statusText;
    switch (pageStore.freezeStatus) {
      case FreezeStatus.Normal:
        path = require("../../img/noFreeze.webp");
        statusText = t("正常状态！");
        currentText = t("当前账户为(紧急状态)");
        break;
      case FreezeStatus.EmerencyFreeze:
        path = require("../../img/freeze.webp");
        currentText = t("当前账户为(紧急状态)");
        statusText = t("紧急冻结状态！");
        break;
      case FreezeStatus.ForeverFreeze:
        path = require("../../img/freeze.webp");
        currentText = t("当前账户为(紧急状态)");
        statusText = t("永久冻结状态！");
        break;
      case FreezeStatus.NotUnFreeze:
        path = require("../../img/noCanFreeze.webp");
        currentText = t("当前账户为(不可解冻状态)");
        statusText = t("冻结锁定状态！");
        break;
      case FreezeStatus.Other:
        if (pageStore.freezeStatusData?.reason === 11) {
          path = require("../../img/temporary_state.webp");
          currentText = t("当前账户为(dukcapil不一致)");
          statusText = t("临时冻结");
        }

        break;
      default:
        break;
    }
    return (
      <>
        <Image source={path} style={styles.bigImage} />
        <Text style={styles.currentText}>
          {currentText}
          <Text style={styles.statusText}>{statusText}</Text>
        </Text>
      </>
    );
  };

  showListContent = () => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    let title = "";
    let listContent: any = [];
    switch (pageStore.freezeStatus) {
      case FreezeStatus.Normal:
        title = t("开启【紧急冻结】后");
        listContent = [t("您将无法在Akulaku购物＆借款"), t("7天后将自动解冻")];
        break;
      case FreezeStatus.EmerencyFreeze:
        title = t("您可以解冻账户，解冻后");
        listContent = [t("可以使用账户进行分期支付"), t("可以正常购物和借款"), t("可以正常享受我们的活动和其他服务")];
        break;
      case FreezeStatus.ForeverFreeze:
        title = t("您可以解冻账户，解冻后");
        listContent = [
          t("可以正常登录"),
          t("可以使用账户进行分期支付"),
          t("可以正常购物和借款"),
          t("可以正常享受我们的活动和其他服务")
        ];
        break;
      case FreezeStatus.Other:
        if (pageStore.freezeStatusData?.reason === 11) {
          title = t("更新后你可以：");
          listContent = [t("正常购物和借贷"), t("照常享受我们的活动和其他服务")];
        }

        break;
      default:
        break;
    }
    if (pageStore.freezeStatus === FreezeStatus.NotUnFreeze) {
      return (
        <Text style={styles.notFreezeText}>
          {t("非常抱歉，您的账户暂时不能进行自主解冻，详情请联系客服，客服电话：")}
          <Text style={styles.service}>1500920</Text>
        </Text>
      );
    } else {
      return (
        <>
          {pageStore.freezeStatus === FreezeStatus.Normal && (
            <Text style={styles.notFreezeText}>
              {t("若账号被盗，或手机丢失，可以紧急冻结账号。防止资金损失和信息泄露")}
            </Text>
          )}
          {pageStore.freezeStatus === FreezeStatus.Other && pageStore.freezeStatusData?.reason === 11 && (
            <Text style={styles.notFreezeText}>
              {t("您的授信资料存在安全风险。 请更新您的KTP以验证您的身份并解冻您的账户")}
            </Text>
          )}

          <ListComponent title={title} data={listContent} style={{ marginTop: 24, alignSelf: "flex-start" }} />
        </>
      );
    }
  };

  onPress = () => {
    const {
      store: { pageStore }
    } = this.props;
    switch (pageStore.freezeStatus) {
      case FreezeStatus.Normal: //走紧急解冻流程
        NativeNavigationModule.navigate({ screen: "EmerencyFreeze", params: { loginStatus: this.loginStatus } });
        FreezeEventReport(FreezeEventType.ClickEmerencyFreeze, LoginStatusReport.Login);
        break;
      case FreezeStatus.EmerencyFreeze: //有登录态去解冻
      case FreezeStatus.ForeverFreeze:
        this.unfreezeNow();
        break;
      case FreezeStatus.NotUnFreeze: //返回安全中心
        NativeNavigationModule.popToScreen("/securityCenter/home");
        break;
      case FreezeStatus.Other:
        if (pageStore.freezeStatusData?.reason === 11) {
          NativeNavigationModule.navigate({
            url: "ak://m.akulaku.com/1602?screen=/user/authorize-credit/authorize-credit-update&source=unfreeze"
          });
        }
        break;

      default:
        break;
    }
  };

  onClickForever = () => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    FreezeEventReport(FreezeEventType.ClickForeverFreeze, LoginStatusReport.Login);
    AKDialog.show({
      type: DialogType.C3_1_1,
      layoutDirection: LayoutDirection.LayoutVertical,
      positiveText: t("永久冻结"),
      negativeText: t("关闭"),
      desc: t("我们对您的账号有完善的保护措施，您可以放心使用，建议不要轻易冻结账号！"),
      onPositivePress: () => {
        NativeNavigationModule.navigate({
          screen: "ForeverFreeze",
          params: { registeredDays: pageStore.freezeStatusData?.registeredDays }
        });
      }
    });
  };

  showButton = () => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    let text = "";
    switch (pageStore.freezeStatus) {
      case FreezeStatus.Normal:
        text = t("紧急冻结");
        break;
      case FreezeStatus.EmerencyFreeze:
      case FreezeStatus.ForeverFreeze:
        text = t("解冻账户");
        break;
      case FreezeStatus.NotUnFreeze:
        text = t("返回安全中心");
        break;
      case FreezeStatus.Other:
        if (pageStore.freezeStatusData?.reason === 11) {
          text = t("解冻账户-dukcapil");
        }

        break;
    }
    return (
      <>
        <AKButton type={AKButtonType.B1_1_2} text={text} onPress={this.onPress} style={styles.clickButton} />
        {pageStore.freezeStatus === FreezeStatus.Normal && (
          <TouchableOpacity onPress={this.onClickForever}>
            <Text style={styles.foreverText}>{t("永久冻结")}</Text>
          </TouchableOpacity>
        )}
        {pageStore.showLogout && (
          <TouchableOpacity
            onPress={() => {
              if (pageStore.freezeStatus === FreezeStatus.Normal) {
                this.deleteAccountSensor?.clickDeleteAccountPage1091({ Aku_PageStatus: LoginStatusReport.Login });
              } else {
                this.deleteAccountSensor?.clickDeleteAccountPage1049({
                  Aku_PageStatus: this.getStatus(pageStore.freezeStatus)
                });
              }
              this.props.navigation.navigate({
                screen: "AccountLogoutReminder"
              });
            }}
            style={{ flexDirection: "row", alignItems: "center", marginTop: 50 }}
          >
            <AkuTextComponent type={AkuTextComponentType.Aku_font_14_regular} text={t("注销账户")} />
            <Image source={require("../../img/arrow_16.webp")} style={{ width: 12, height: 12 }} />
          </TouchableOpacity>
        )}
      </>
    );
  };

  _render() {
    const {
      store: { pageStore }
    } = this.props;
    // 其他状态中， 如果是dukcapil不一致 也不走到 其他状态页
    if (pageStore.freezeStatus === FreezeStatus.Other && pageStore.freezeStatusData?.reason !== FreezeReason.dukcapil) {
      return this.showOtherStatusView();
    } else {
      return (
        <View style={styles.container}>
          {this.showImage()}
          {this.showListContent()}
          {this.showButton()}
        </View>
      );
    }
  }
}
