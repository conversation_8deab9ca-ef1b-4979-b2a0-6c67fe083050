import BaseContainer, { Type } from "common/components/BaseContainer";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import { AKButton } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { inject, observer } from "mobx-react";
import React from "react";
import { View, Text } from "react-native";
import InputPassword from "../../components/InputPassword";
import styles from "../unfreeze/styles";
import store from "../../store/index";
import { debounce } from "lodash";
// @ts-ignore
import md5 from "blueimp-md5";
import AKPasswordVaildHelper from "../../tool/passwordVaild";
import { EnterLeaveUnfreeze, sensorType, UnfreezePageClick } from "../../tool/sensorLogger";
import { NativeNavigationModule } from "common/nativeModules";
import { LoginStatus } from "../../dict/userInfo";

type NavParams = {
  operationId: string;
  loginStatus: LoginStatus;
  phone: string;
  openId: string;
  platformType: string;
};
@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class ResetPassword extends BaseContainer<store, NavParams> {
  vaildHelpers: AKPasswordVaildHelper;

  state = {
    firstPassword: "",
    lastPassword: "",
    firstPasswordIsHide: false,
    lastPasswordIsHide: false,
    errorText: ""
  };

  constructor(props: Type.Props<store, NavParams>) {
    super(props);
    const {
      store: { pageStore }
    } = this.props;
    this.vaildHelpers = new AKPasswordVaildHelper();
    EnterLeaveUnfreeze(sensorType.EnResetPassword);
    pageStore.isLoading = false;
  }

  navigationBarProps = () => {
    return {
      title: this.props.t("重置账户密码")
    };
  };

  onChangePassword = (value: string, id: number) => {
    switch (id) {
      case 1:
        this.setState({ firstPassword: value });
        break;
      case 2:
        this.setState({ lastPassword: value });
    }
  };

  setErrorText = () => {
    const { t } = this.props;
    if (this.state.firstPassword !== this.state.lastPassword && this.state.lastPassword.length >= 8) {
      this.setState({ errorText: t("您输入的两个账号密码不一致") });
    } else if (this.state.lastPassword.length < 8) {
      this.setState({ errorText: t("需填写8-16位的数字和字母的组合密码") });
    } else {
      this.setState({ errorText: "" });
    }
  };

  unfreezeResult = debounce(
    async () => {
      const {
        store: {
          pageStore,
          navParams: { operationId, loginStatus, phone, openId, platformType }
        }
      } = this.props;
      const lastPassword = md5(this.state.lastPassword);
      this.vaildHelpers.vaildPassword(lastPassword);
      EnterLeaveUnfreeze(sensorType.LeResetPassword);
      UnfreezePageClick(sensorType.complete);
      await pageStore.getUnfreezeResult(
        operationId,
        this.vaildHelpers.passwordLevel,
        this.vaildHelpers.levelVersion,
        lastPassword,
        loginStatus,
        phone,
        openId,
        platformType
      );
      if (!pageStore.unfreezeResult_err) {
        NativeNavigationModule.navigate({
          screen: "UnfreezeResult",
          params: {
            riskFlowStatus: pageStore.unfreezeResult?.riskFlowStatus
          }
        });
      }
    },
    500,
    { leading: true, trailing: false }
  );

  _render() {
    const { t } = this.props;
    const pattern = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$/;
    let error = null;
    if (!!pattern && !!this.state.lastPassword) {
      const reg = new RegExp(pattern);
      error = !reg.test(this.state.lastPassword);
    }
    return (
      <View style={{ backgroundColor: "#FFF", flex: 1 }}>
        <Text style={styles.resetText}>{t("为了您的账户安全，请重新设置您的账号密码")}</Text>
        <InputPassword
          placeholder={t("输入您的账号密码")}
          pattern={/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$/}
          errorText={t("需填写8-16位的数字和字母的组合密码")}
          value={this.state.firstPassword}
          setValue={(value: string) => {
            this.onChangePassword(value, 1);
          }}
          containerStyle={[styles.inputContainer]}
          isHidePassword={(isHidePassword: boolean) => {
            this.setState({ firstPasswordIsHide: isHidePassword });
          }}
          secureTextEntry={this.state.firstPasswordIsHide !== true ? true : false}
        />
        <InputPassword
          placeholder={t("再次输入您的账号密码")}
          value={this.state.lastPassword}
          setValue={(value: string) => {
            this.onChangePassword(value, 2);
          }}
          containerStyle={styles.inputContainer}
          isHidePassword={(isHidePassword: boolean) => {
            this.setState({ lastPasswordIsHide: isHidePassword });
          }}
          onBlur={() => {
            this.setErrorText();
          }}
          secureTextEntry={this.state.lastPasswordIsHide !== true ? true : false}
          errorText={this.state.errorText}
          showErrorText={!!this.state.errorText}
          marginBottom={-20}
        />
        <AKButton
          onPress={this.unfreezeResult}
          disabled={
            !(!!this.state.firstPassword && !!this.state.lastPassword) ||
            this.state.firstPassword !== this.state.lastPassword ||
            this.state.lastPassword.length < 8 ||
            error
          }
          text={t("提交")}
          type={AKButtonType.B1_1_2}
          style={{
            marginHorizontal: 16,
            marginTop: 40
          }}
        />
      </View>
    );
  }
}
