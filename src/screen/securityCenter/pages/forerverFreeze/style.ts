import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  bg: {
    width: WINDOW_WIDTH,
    height: (WINDOW_WIDTH * 65) / 360,
    position: "absolute",
    top: 16,
    left: 16
  },
  bgText: {
    fontSize: 12,
    color: "#282B2E",
    maxWidth: WINDOW_WIDTH - 32
  },
  container: {
    marginTop: 23,
    marginBottom: 20,
    paddingHorizontal: 16
  },
  titleStyle: {
    fontSize: 16,
    color: "#000000"
  },
  listStyle: {
    backgroundColor: "rgba(239,242,246,0.5)",
    paddingHorizontal: 12,
    paddingVertical: 16,
    borderRadius: 6
  },
  textStyle: {
    color: "#6E737D"
  },
  condition: {
    fontSize: 16,
    color: "#000000",
    ...FontStyles["rob-medium"]
  },
  conditionContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  image: {
    width: 20,
    height: 20
  },
  buttonImage: {
    width: 12,
    height: 12,
    marginRight: 7
  },
  agree: {
    fontSize: 11,
    color: "#989FA9",
    maxWidth: WINDOW_WIDTH - 51
  },
  agreeText: {
    fontSize: 11,
    color: "#189BF9"
  },
  button: {
    width: WINDOW_WIDTH - 32,
    marginTop: 12,
    marginBottom: 16
  },
  background: {
    width: WINDOW_WIDTH,
    height: (WINDOW_WIDTH * 110) / 360
  },
  daysContainer: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    position: "absolute",
    top: 30,
    left: 16,
    backgroundColor: "rgba(255,255,255,0.7)",
    borderRadius: 10,
    width: WINDOW_WIDTH - 32 - 85 - 16
  },
  days: {
    marginLeft: 16,
    fontSize: 14,
    color: "#282B2E",
    ...FontStyles["rob-medium"]
  },
  trangle: {
    width: 0,
    height: 0,
    borderRightWidth: 10,
    borderRightColor: "transparent",
    borderBottomWidth: 10,
    position: "absolute",
    bottom: 14,
    right: -10,
    borderBottomColor: "rgba(255,255,255,0.7)"
  },
  akulaku: {
    width: 48,
    height: 57,
    position: "absolute",
    top: 10,
    right: 16
  },
  secondStep: {
    flex: 1
  },
  line: {
    paddingVertical: 16,
    paddingLeft: 16,
    borderBottomColor: "#EFF2F6",
    borderBottomWidth: 1
  }
});
