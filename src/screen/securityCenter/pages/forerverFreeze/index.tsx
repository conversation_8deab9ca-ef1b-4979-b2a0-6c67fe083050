import BaseContainer, { Type } from "common/components/BaseContainer";
import RootView from "common/components/Layout/RootView";
import { NativeNavigationModule, NativeRiskCheckModule } from "common/nativeModules";
import { CheckRiskType } from "common/nativeModules/bussinse/nativeRiskCheckModule";
import { dynamicT, withTranslation } from "common/services/i18n";
import { AKButton, Android, FontStyles, NavigationBar } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { inject, observer } from "mobx-react";
import React from "react";
import { View, Image, Text, TouchableOpacity, BackHandler, NativeEventSubscription } from "react-native";
import ListComponent from "../../components/ListComponent";
import SelectReasonComponet from "../../components/SelectReasonComponet";
import { ForeverProccess, LoginStatus, ReasonCode } from "../../dict/userInfo";
import store from "../../store/FreezeNewStore";
import { FreezeEventReport, FreezeEventType } from "../../tool/freezeEventReport";
import styles from "./style";
import LinearGradient from "react-native-linear-gradient";

@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class ForeverFreeze extends BaseContainer<store> {
  backHandlerListener: NativeEventSubscription | undefined;

  state = {
    process: 1, //流程页面
    tick: true, //是否勾选协议
    selectReason: -1, //选择原因index
    otherReason: "" //用户输入其他原因
  };

  constructor(props: Type.Props<store>) {
    super(props);
    FreezeEventReport(FreezeEventType.EnterForeverFreezeFirst);
  }

  navigationBarProps = () => {
    return {
      title: this.props.t("永久冻结"),
      onBackPress: () => this.onBackPress()
    };
  };

  renderNavigationBar = () => {
    return null;
  };

  onBackPress = () => {
    if (this.state.process === ForeverProccess.First) {
      NativeNavigationModule.goBack();
      FreezeEventReport(FreezeEventType.LeaveForeverFreezeFirst);
    } else {
      this.setState({ process: ForeverProccess.First });
      FreezeEventReport(FreezeEventType.EnterForeverFreezeSecond);
      FreezeEventReport(FreezeEventType.LeaveEmerencyFreezeFirst);
    }
    return true;
  };

  componentDidMount() {
    const {
      store: { pageStore }
    } = this.props;
    pageStore.checkIsMeetFreeze();
    if (Android) {
      this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
    }
  }

  componentWillUnmount() {
    this.backHandlerListener?.remove();
  }

  showTips = () => {
    const { t } = this.props;
    return (
      <View style={styles.line}>
        <Text style={styles.bgText}>{t("我们对您的账号有完善的保护措施，您可以放心使用，建议不要轻易冻结账号！")}</Text>
      </View>
    );
  };

  showCondition = () => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    const allOrdersFinished = pageStore.conditionData?.allOrdersFinished;
    const allPaymentFinished = pageStore.conditionData?.allPaymentFinished;
    return (
      <View style={{ paddingHorizontal: 16 }}>
        <Text style={styles.condition}>{t("需满足条件")}</Text>
        <View
          style={{
            backgroundColor: "rgba(239,242,246,0.5)",
            marginTop: 12,
            paddingHorizontal: 12,
            paddingVertical: 16,
            borderRadius: 6,
            overflow: "hidden"
          }}
        >
          <View style={[styles.conditionContainer, { marginBottom: 16 }]}>
            <Text style={{ fontSize: 12, color: allOrdersFinished ? "#282B2E" : "#989FA9" }}>
              {t("交易订单已收货/已退款")}
            </Text>
            <Image
              source={allOrdersFinished ? require("../../img/yes.webp") : require("../../img/no.webp")}
              style={styles.image}
            />
          </View>
          <View style={styles.conditionContainer}>
            <Text style={{ fontSize: 12, color: allOrdersFinished ? "#282B2E" : "#989FA9" }}>{t("无未还款账单")}</Text>
            <Image
              source={allPaymentFinished ? require("../../img/yes.webp") : require("../../img/no.webp")}
              style={styles.image}
            />
          </View>
        </View>
      </View>
    );
  };

  onPress = async () => {
    const {
      store: {
        navParams: { registeredDays }
      }
    } = this.props;
    if (this.state.process === ForeverProccess.First) {
      this.setState({ process: ForeverProccess.Second });
      FreezeEventReport(FreezeEventType.ClickFreezeButton);
      FreezeEventReport(FreezeEventType.LeaveEmerencyFreezeFirst);
      FreezeEventReport(FreezeEventType.EnterForeverFreezeSecond);
    } else {
      let freezeCode;
      let freezeNote;
      switch (this.state.selectReason) {
        case 0:
          freezeCode = ReasonCode.NOPERSON_APPLY;
          FreezeEventReport(FreezeEventType.ClickReason, 0, 4);
          break;
        case 1:
          freezeCode = ReasonCode.NO_USE;
          FreezeEventReport(FreezeEventType.ClickReason, 0, 5);
          break;
        case 2:
          freezeCode = ReasonCode.FREE_HIGHER;
          FreezeEventReport(FreezeEventType.ClickReason, 0, 6);
          break;
        case 3:
          freezeCode = ReasonCode.CREDIT_LOW;
          FreezeEventReport(FreezeEventType.ClickReason, 0, 7);
          break;
        case 4:
          freezeCode = ReasonCode.SLOW_LOGISTICS;
          FreezeEventReport(FreezeEventType.ClickReason, 0, 8);
          break;
        case 5:
          freezeCode = ReasonCode.CUSTOM_REASON;
          FreezeEventReport(FreezeEventType.ClickReason, 0, 9);
          break;
        case 6:
          freezeCode = ReasonCode.HARD_TOUSE;
          FreezeEventReport(FreezeEventType.ClickReason, 0, 1);
          break;
        case 7:
          freezeCode = ReasonCode.OTRHER;
          freezeNote = this.state.otherReason;
          FreezeEventReport(FreezeEventType.ClickReason, 0, 3);
          break;
        default:
          break;
      }
      const checkRisk = await NativeRiskCheckModule.checkRisk({
        type: CheckRiskType.FOREVER_FREEZE,
        freezeCode: freezeCode,
        freezeNote: freezeNote
      });
      if (checkRisk.success) {
        NativeNavigationModule.navigate({
          screen: "FreezeResult",
          params: {
            source: 2,
            loginStatus: LoginStatus.LOGIN,
            riskFlowId: checkRisk?.data.operationId,
            registeredDays
          }
        });
        FreezeEventReport(FreezeEventType.LeaveForeverFreezeSecond);
      }
    }
  };

  bottomButton = () => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    const allOrdersFinished = pageStore.conditionData?.allOrdersFinished;
    const allPaymentFinished = pageStore.conditionData?.allPaymentFinished;
    return (
      <View style={{ paddingHorizontal: 16 }}>
        {this.state.process === ForeverProccess.First ? (
          <View style={{ flexDirection: "row" }}>
            <TouchableOpacity
              onPress={() => {
                this.setState({ tick: !this.state.tick });
              }}
            >
              <Image
                source={this.state.tick ? require("../../img/tick.webp") : require("../../img/noTick.webp")}
                style={styles.buttonImage}
              />
            </TouchableOpacity>
            <Text style={styles.agree}>
              {t("我了解并同意")}
              <Text
                style={styles.agreeText}
                onPress={() => {
                  NativeNavigationModule.navigate({
                    url: "https://www.akulaku.com/artikel/accountfreeze-agreement/"
                  });
                }}
              >
                {t("永久冻结协议内容")}
              </Text>
            </Text>
          </View>
        ) : null}
        <AKButton
          type={AKButtonType.B1_1_2}
          text={this.state.process === ForeverProccess.First ? t("永久冻结") : t("完成")}
          style={styles.button}
          onPress={this.onPress}
          disabled={
            this.state.process === ForeverProccess.First
              ? !allOrdersFinished || !allPaymentFinished || !this.state.tick
              : this.state.selectReason === -1 || (this.state.selectReason === 7 && !this.state.otherReason)
          }
        />
      </View>
    );
  };

  renderProcessFirst = () => {
    const { t } = this.props;
    return (
      <>
        {this.showTips()}
        <ListComponent
          title={t("开启永久冻结后")}
          data={[
            t("您将无法登录Akulaku，并且将认为您自愿放弃账户余额。"),
            t("您当前手机号将无法注册新的Akulaku账号，并可能会影响您的授信额度"),
            t(
              '30天内未操作"解冻账号"，我们将依据隐私政策的要求，删除或匿名化处理您的账号数据，同时您的账号将被永久删除。'
            )
          ]}
          style={styles.container}
          titleStyle={styles.titleStyle}
          listStyle={styles.listStyle}
          textStyle={styles.textStyle}
        />
        {this.showCondition()}
      </>
    );
  };

  renderProcessSecond = () => {
    const {
      t,
      store: {
        navParams: { registeredDays }
      }
    } = this.props;
    const listContent = [
      { title: t("非本人申请"), desc: t("请冻结后，尽快与客服反馈") },
      { title: t("无法使用"), desc: t("可以联系客服，我们将努力解决您的问题") },
      { title: t("利率/费用高"), desc: t("感谢您的反馈") },
      { title: t("额度低"), desc: t("感谢您的反馈") },
      { title: t("物流慢"), desc: t("感谢您的建议，我司会不断优化物流服务") },
      { title: t("客服或催收原因"), desc: t("感谢您的建议，我司会提高客服＆催收的服务水平") },
      { title: t("不好用，不想用了"), desc: t("我们将会不断优化产品体验") },
      { title: t("其他"), desc: t("请填写具体原因") }
    ];
    return (
      <View style={styles.secondStep}>
        <View style={{ height: 58, justifyContent: "center" }}>
          <Text style={styles.days}>
            {dynamicT(t, "Akulaku已经陪伴您xxx天了", {
              ["xxx"]: (
                <Text style={{ fontSize: 14, color: "#F5640A", ...FontStyles["rob-medium"] }}>{registeredDays}</Text>
              )
            })}
          </Text>

          <View style={styles.trangle} />
        </View>
        <Image source={require("../../img/akulaku_cry.webp")} style={styles.akulaku} />
        <SelectReasonComponet
          t={t}
          data={listContent}
          selectReason={(selectReason: number) => {
            this.setState({ selectReason });
          }}
          onClickOther={(text: string) => {
            this.setState({ otherReason: text });
          }}
        />
      </View>
    );
  };

  renderBg = () => {
    if (this.state.process === ForeverProccess.First) {
      return (
        <LinearGradient
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          colors={["#ECF4FFFF", "#ECF4FF00"]}
          style={{ height: 156, position: "absolute", left: 0, right: 0 }}
        >
          <Image
            source={require("../../img/freeze_bg.webp")}
            style={{ position: "absolute", top: 0, right: 0, width: 165, height: 143 }}
          />
        </LinearGradient>
      );
    }

    return (
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={["#ECF4FF00", "#ECF4FFFF"]}
        style={{ height: 193, position: "absolute", left: 0, right: 0 }}
      ></LinearGradient>
    );
  };

  _render() {
    return (
      <>
        {this.renderBg()}
        <NavigationBar {...this.navigationBarProps()} containerStyle={{ backgroundColor: "transparent" }} />
        <View style={{ flex: 1 }}>
          {this.state.process === ForeverProccess.First ? this.renderProcessFirst() : this.renderProcessSecond()}
        </View>
        {this.bottomButton()}
      </>
    );
  }
}
