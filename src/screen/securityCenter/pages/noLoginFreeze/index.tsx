import BaseContainer, { Type } from "common/components/BaseContainer";
import RootView from "common/components/Layout/RootView";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";
import { withTranslation } from "common/services/i18n";
import { AKButton, FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import { inject, observer } from "mobx-react";
import React from "react";
import { View, StyleSheet, Image, Text, TextInput, ScrollView } from "react-native";
import ListComponent from "../../components/ListComponent";
import { LoginStatus } from "../../dict/userInfo";
import store from "../../store";
import { FreezeEventReport, FreezeEventType, LoginStatusReport } from "../../tool/freezeEventReport";

@RootView({
  withI18n: [
    "SecurityCenter",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SecurityCenter")
@inject("store")
@observer
export default class NoLoginFreeze extends BaseContainer<store> {
  state = {
    phoneNumber: "",
    showError: false
  };

  constructor(props: Type.Props<store>) {
    super(props);
    const {
      store: { pageStore }
    } = this.props;
    pageStore.isLoading = false;
    this.props.configPageInfo({ sn: 202155, sp: { Aku_PageStatus: LoginStatus.NO_LOGIN } }, true);
    this.props.configSensorEvent(
      {
        page_id: "1091",
        page_name: "freeze account page",
        extra: { Aku_PageStatus: LoginStatus.NO_LOGIN }
      },
      true
    );
  }

  navigationBarProps = () => {
    return {
      title: this.props.t("冻结账户")
    };
  };

  onBlur = () => {
    const pattern = /^[0-9]{9,13}$/;
    this.setState({ showError: !pattern.test(this.state.phoneNumber) });
  };

  showInputAccopunt = () => {
    const { t } = this.props;
    return (
      <View style={{ marginBottom: this.state.showError ? 18 : 36 }}>
        <View style={styles.inputContainer}>
          <View style={styles.country}>
            <Image source={require("../../img/country.webp")} style={styles.image} />
            <Text style={styles.countryCode}>+62</Text>
          </View>
          <TextInput
            placeholder={t("电话号码提示")}
            style={styles.text}
            value={this.state.phoneNumber}
            onChangeText={(value: string) => this.setState({ phoneNumber: value })}
            onBlur={this.onBlur}
            keyboardType={"numeric"}
            maxLength={13}
          />
        </View>
        {this.state.showError && <Text style={styles.error}>{t("电话号码校验")}</Text>}
      </View>
    );
  };

  onPress = () => {
    FreezeEventReport(FreezeEventType.ClickEmerencyFreeze, LoginStatusReport.NoLogin);
    NativeNavigationModule.navigate({
      screen: "EmerencyFreeze",
      params: { phone: this.state.phoneNumber, loginStatus: LoginStatus.NO_LOGIN }
    });
  };

  _render() {
    const { t } = this.props;
    return (
      <ScrollView style={styles.container}>
        <Image source={require("../../img/noFreeze.webp")} style={styles.bigImage} />
        <Text style={styles.account}>{t("请输入要冻结的账号")}</Text>
        {this.showInputAccopunt()}
        <Text style={styles.currentText}>{t("若账号被盗，或手机丢失，可以紧急冻结账号。防止资金损失和信息泄露")}</Text>
        <ListComponent
          title={t("开启【紧急冻结】后")}
          data={[t("您将无法在Akulaku购物＆借款"), t("7天后将自动解冻")]}
          style={{ alignSelf: "flex-start" }}
        />
        <AKButton
          type={AKButtonType.B1_1_2}
          text={t("紧急冻结")}
          style={styles.clickButton}
          onPress={this.onPress}
          disabled={this.state.showError || !this.state.phoneNumber}
        />
      </ScrollView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    paddingHorizontal: 16
  },
  bigImage: {
    width: 120,
    height: 120,
    marginBottom: 12,
    marginTop: 40,
    alignSelf: "center"
  },
  clickButton: {
    marginTop: 36,
    width: WINDOW_WIDTH - 32
  },
  currentText: {
    fontSize: 14,
    color: "#6E737D",
    alignSelf: "flex-start",
    marginBottom: 24
  },
  account: {
    fontSize: 14,
    color: "#000000",
    marginBottom: 12,
    alignSelf: "flex-start"
  },
  inputContainer: {
    flexDirection: "row",
    width: WINDOW_WIDTH - 32,
    height: 40,
    paddingVertical: 10,
    paddingLeft: 12,
    backgroundColor: "#EFF2F6",
    borderRadius: 6
  },
  country: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingRight: 6,
    borderRightWidth: 1,
    borderRightColor: "rgba(203,208,215,0.8)"
  },
  countryCode: {
    fontSize: 14,
    color: "#282B2E",
    ...FontStyles["rob-medium"]
  },
  image: {
    width: 20,
    height: 20,
    marginRight: 4
  },
  text: {
    flex: 1,
    marginLeft: 6,
    padding: 0,
    fontSize: 16,
    ...FontStyles["rob-medium"]
  },
  error: {
    fontSize: 10,
    color: "#F32823",
    alignSelf: "flex-start",
    marginTop: 6
  }
});
