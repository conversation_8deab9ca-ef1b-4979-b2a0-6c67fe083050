/**
 * @Author: wenhui.dong
 * @Date: 2021-12-27 19:02:31
 * @description:PIN页面类型
 */

export type SettingPINListData = {
  rightTitle?: string;
  isLeftTitle?: boolean;
  subtitle?: string;
};

export enum PayPasswordStatusEnum {
  /**
   * 未设置
   */
  NOT_SET = 0,
  /**
   * 已设置
   */
  ALREADY_SET
}

export enum VerificationWaiverStatusEnum {
  /**
   * 关闭
   */
  CLOSE = 0,
  /**
   * 开通
   */
  OPEN
}

export type SecurityPaymentStatusType = {
  status?: PayPasswordStatusEnum;
  verificationWaiverStatus?: VerificationWaiverStatusEnum;
};
