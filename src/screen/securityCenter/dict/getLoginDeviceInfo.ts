export interface GetLoginDeviceInfo {
  success: boolean;
  errMsg: object;
  data: Data;
  errCode: object;
}

export interface Data {
  removeReason: RemoveReason[];
  loginDevices: LoginDevices[];
}

export interface LoginDevices {
  loginTime: number;
  localUse: boolean;
  deviceBrand: object;
  deviceModel: string;
  logId: number;
}

export interface RemoveReason {
  reasonType: number;
  reason: string;
}
