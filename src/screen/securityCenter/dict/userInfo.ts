export interface UserInfo {
  errMsg: string;
  systime: number;
  success: boolean;
  data: Data;
  errCode: string;
}

export interface Data {
  countryId: number;
  token: string;
  phoneNumber: string;
  avatar: string;
  uid: string;
  status: number;
}

export enum LoginStatus {
  LOGIN = 1, //有登录态
  NO_LOGIN = 0 //无登录态
}

export enum FreezeSource { //冻结类型来源
  EmerencyFreeze = 1, //紧急冻结
  ForeverFreeze //永久冻结
}

export enum ReasonCode {
  HARD_TOUSE = "4001", //不好用，不想用
  NOT_INSTALLMENT = "4002", //不能使用分期、贷款
  PHONE_LOST = "4003", //手机丢失
  ACCOUNT_HACKED = "4004", //怀疑账号被盗
  OTRHER = "4005", //其他
  NOPERSON_APPLY = "4006", //非本人申请
  NO_USE = "4007", //无法使用
  FREE_HIGHER = "4008", //利率、费用高
  CREDIT_LOW = "4009", //额度低
  SLOW_LOGISTICS = "4010", //物流慢
  CUSTOM_REASON = "4011" //客服或催收原因
}

export enum SecurityMethod {
  SMS_VERFICATION = "sms", //短信验证
  FACE_RECOGNITION = "face" //人脸识别
}

export enum ForeverProccess {
  First = 1, //第一步
  Second
}
