export interface EducationInfo {
  success: boolean;
  errMsg: object;
  data: Data;
  errCode: object;
}

export interface Data {
  interceptAmount: number;
  interceptNumber: number;
  phoneScrolls: string[];
  faqList: FaqList[];
  buttonInfos: ButtonInfos[];
  antiFraudTips: AntiFraudTips[];
}

export interface AntiFraudTips {
  title: string;
  imageUrl: string;
}

export interface ButtonInfos {
  title: string;
  imageUrl: string;
  type: number;
  link: string;
}

export interface FaqList {
  title: string;
  childFaqs: ChildFaqs[];
  imgPlace: object;
}

export interface ChildFaqs {
  title: string;
  imgPlace: object;
  answers: Answers[];
}

export interface Answers {
  answer: string;
  imgPlace: object | ImgPlace;
}

export interface ImgPlace {
  imageUrl: string;
  width: number;
  height: number;
}
