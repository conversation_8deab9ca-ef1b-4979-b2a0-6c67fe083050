/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2021-06 11:18
 * @description 计数loading
 */

import React, { Component } from "react";
import { View, StyleSheet, Image, Text } from "react-native";
import RootSiblings from "react-native-root-siblings";
import { FontStyles, WINDOW_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

const styles = StyleSheet.create({
  page: {
    flex: 1,
    width: WINDOW_WIDTH,
    height: WINDOW_HEIGHT,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 9999,
    position: "absolute"
  },
  container: {
    width: 104,
    height: 104,
    // backgroundColor: "#000000",
    // opacity:0.6,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 4
  },
  count: {
    ...FontStyles["DIN-Bold"],
    color: "#666666",
    fontSize: 16,
    position: "absolute"
  },
  count1: {
    width: 6,
    height: 6,
    backgroundColor: "#FFFFFF",
    borderRadius: 30,
    marginTop: 12
  },
  count2: {
    width: 6,
    height: 6,
    backgroundColor: "#FFFFFF",
    borderRadius: 30,
    marginTop: 12,
    marginLeft: 6
  },
  count3: {
    width: 6,
    height: 6,
    backgroundColor: "#FFFFFF",
    borderRadius: 30,
    marginTop: 12,
    marginLeft: 6
  }
});

type State = {
  count: number;
};

type Props = {};

export default class CountLoading extends Component<Props, State> {
  timer: number | undefined;

  static show(option?: any) {
    CountLoading.loadingArr.push(new RootSiblings((<CountLoading />)));
  }

  static dismiss() {
    const loading = CountLoading.loadingArr.pop();
    if (loading) loading.destroy();
  }

  constructor(props: Props) {
    super(props);
    this.state = {
      count: 1
    };
  }

  componentDidMount() {
    this.count();
  }

  componentWillUnmount() {
    this.stopCount = true;
    this.timer && clearTimeout(this.timer);
  }

  static loadingArr: Array<RootSiblings> = [];

  listenerArr: Array<any> = [];

  stopCount: boolean | undefined = false;

  count = () => {
    if (this.stopCount) return;
    this.timer = setTimeout(() => {
      this.setState({
        count: this.state.count + 1
      });
      this.count();
    }, 1000);
  };

  render() {
    const { count } = this.state;
    return (
      <View style={styles.page}>
        <View style={styles.container}>
          <View
            style={{
              alignItems: "center",
              justifyContent: "center"
            }}
          >
            <Image source={require("./img/img_loading.webp")} style={{ width: 28, height: 32 }} />
            <Text style={styles.count}>{count}</Text>
          </View>
          <View style={{ flexDirection: "row" }}>
            <View style={[styles.count1, { opacity: 0.2 + ((count + 2) % 3) * 0.4 }]} />
            <View style={[styles.count2, { opacity: 0.2 + ((count + 1) % 3) * 0.4 }]} />
            <View style={[styles.count3, { opacity: 0.2 + (count % 3) * 0.4 }]} />
          </View>
        </View>
      </View>
    );
  }
}
