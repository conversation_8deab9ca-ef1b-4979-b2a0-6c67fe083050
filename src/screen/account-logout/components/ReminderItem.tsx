/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-02 16:03
 * @description
 */

import React from "react";
import { StyleProp, StyleSheet, View, ViewStyle } from "react-native";
import { AkuTextComponent, AkuTextComponentType, FigmaStyle } from "@akulaku-rn/akui-rn";

type Props = {
  style?: StyleProp<ViewStyle>;
  content: string;
  title: string;
};

const ReminderItem = React.memo(function ReminderItemComp({ style, content, title }: Props) {
  return (
    <View style={style}>
      <View style={styles.titleRow}>
        <View style={styles.point} />
        <AkuTextComponent type={AkuTextComponentType.Aku_font_16_medium} text={title} />
      </View>
      <AkuTextComponent
        style={{ color: FigmaStyle.Color.Grey_Text2 }}
        type={AkuTextComponentType.Aku_font_14_regular}
        text={content}
      />
    </View>
  );
});

export default ReminderItem;

const styles = StyleSheet.create({
  titleRow: {
    flexDirection: "row",
    // alignItems: "center",
    marginBottom: 12
  },
  point: {
    height: 6,
    width: 6,
    borderRadius: 3,
    backgroundColor: FigmaStyle.Color.Grey_Text1,
    marginRight: 8,
    marginVertical: 7
  }
});
