/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 16:26
 * @description
 */

import React from "react";
import { StyleSheet, View } from "react-native";
import { AkuTextComponent, AkuTextComponentType, FigmaStyle } from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";

type Props = {
  t: TFunction;
};

const InstructionInfo = React.memo(function InstructionInfoComp({ t }: Props) {
  return (
    <View style={{ padding: 16 }}>
      <AkuTextComponent
        type={AkuTextComponentType.Aku_font_14_medium}
        text={t("为保护账户和资金安全，同时确保不产生资金和售后纠纷，注销账户需要满足以下条件")}
      />
      <AkuTextComponent
        style={{ marginTop: 16, color: FigmaStyle.Color.Grey_Text2 }}
        type={AkuTextComponentType.Aku_font_14_regular}
        text={t("账号内所有订单均已完成，不存在流程中的交易单和授信单。")}
      />
      <AkuTextComponent
        style={{ marginTop: 16, color: FigmaStyle.Color.Grey_Text2 }}
        type={AkuTextComponentType.Aku_font_14_regular}
        text={t("所有账单均已结清。")}
      />
      <AkuTextComponent
        style={{ marginTop: 16, color: FigmaStyle.Color.Grey_Text2 }}
        type={AkuTextComponentType.Aku_font_14_regular}
        text={t("同一手机号/身份证号关联的账户30天内只能注销1次，1年内只能注销2次。")}
      />
    </View>
  );
});

export default InstructionInfo;

const styles = StyleSheet.create({});
