/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 15:23
 * @description
 */

import React, { useEffect, useRef, useState } from "react";
import { View, StyleSheet, ViewStyle, StyleProp } from "react-native";
import AkButton from "@akulaku-rn/akui-rn/src/components/AKButton";
import { AKButtonType } from "common/components/AKButton";

type Props = {
  text: string;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
  startCountDown: boolean;
};

const CountDownBtn = React.memo(function CountDownBtnComp({ onPress, text, style, startCountDown }: Props) {
  const [countDownTime, setTime] = useState<number>(__DEV__ ? 5 : 30);
  const timer = useRef<number | null>(null);

  useEffect(() => {
    if (startCountDown) {
      if (!!timer.current) clearInterval(timer.current);
      timer.current = setInterval(() => {
        setTime(oldState => {
          if (oldState - 1 === 0) {
            if (!!timer.current) clearInterval(timer.current);
          }
          return oldState - 1;
        });
      }, 1000);
    }
  }, [startCountDown]);

  return (
    <AkButton
      disabled={countDownTime !== 0}
      style={style}
      onPress={onPress}
      text={`${text}${countDownTime === 0 ? "" : `(${countDownTime})`}`}
      type={AKButtonType.B1_1_2}
    />
  );
});

export default CountDownBtn;

const styles = StyleSheet.create({});
