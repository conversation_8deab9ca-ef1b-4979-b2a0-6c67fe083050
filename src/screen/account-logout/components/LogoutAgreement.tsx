/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 18:38
 * @description
 */

import React from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import AkuTextComponent, { AkuTextComponentType } from "@akulaku-rn/akui-rn/src/components/AkuTextComponent";
import { FigmaStyle } from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";

type Props = {
  t: TFunction;
  isCheck: boolean;
  onChangeStatus: (status: boolean) => void;
};

const LogoutAgreement = React.memo(function LogoutAgreementComp({ isCheck, t, onChangeStatus }: Props) {
  const source = isCheck
    ? require("common/images/icon_checkbox_checked.webp")
    : require("common/images/icon_checkbox_unchecked.webp");

  const onPress = () => {
    NativeNavigationModule.navigate({ url: "https://www.akulaku.com/artikel/account-cancellationnoticeandagreement/" });
  };

  return (
    <TouchableOpacity
      onPress={() => {
        onChangeStatus(!isCheck);
      }}
      activeOpacity={0.85}
      style={{ padding: 16, flexDirection: "row" }}
    >
      <Image source={source} style={{ width: 16, height: 16, marginRight: 6 }} />
      <Text>
        <AkuTextComponent
          style={{ color: FigmaStyle.Color.Grey_Text2 }}
          type={AkuTextComponentType.Aku_font_12_regular}
          text={t("本人已阅读并同意")}
        />
        <TouchableOpacity hitSlop={{ top: 4, bottom: 4 }} activeOpacity={0.85} onPress={onPress}>
          <AkuTextComponent
            style={{ color: FigmaStyle.Color.Blue_6, textDecorationLine: "underline" }}
            type={AkuTextComponentType.Aku_font_12_regular}
            text={t("《注销须知及注销协议》")}
          />
        </TouchableOpacity>
      </Text>
    </TouchableOpacity>
  );
});

export default LogoutAgreement;

const styles = StyleSheet.create({});
