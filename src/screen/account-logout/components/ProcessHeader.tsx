/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 10:26
 * @description
 */

import React from "react";
import { StyleProp, StyleSheet, View, ViewStyle } from "react-native";
import { AkuTextComponent, AkuTextComponentType, FigmaStyle } from "@akulaku-rn/akui-rn";

type Props = {
  tabs: string[];
  currentIndex: number;
  style?: StyleProp<Omit<ViewStyle, "flexDirection">>;
};

const ProcessHeader = React.memo(function ProcessHeaderComp({ tabs, currentIndex, style }: Props) {
  return (
    <View style={[{ flexDirection: "row" }, style]}>
      {tabs.map((item, index) => {
        return (
          <ProcessHeaderItem
            key={`${item}_${index}`}
            text={item}
            isActive={index <= currentIndex}
            isFirst={index === 0}
            isLast={index === tabs.length - 1}
          />
        );
      })}
    </View>
  );
});

const ProcessHeaderItem = ({
  isActive,
  text,
  isFirst,
  isLast
}: {
  isActive: boolean;
  text: string;
  isFirst?: boolean;
  isLast?: boolean;
}) => {
  const circleBg = isActive ? FigmaStyle.Color.Success_7 : FigmaStyle.Color.Grey_Divider;
  const textColor = isActive ? FigmaStyle.Color.Grey_Text1 : FigmaStyle.Color.Grey_Text3;

  return (
    <View style={{ flex: 1, alignItems: "center" }}>
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        <View style={[styles.greyLine, isFirst && { backgroundColor: "transparent" }]} />
        <View style={{ marginHorizontal: 14, height: 8, width: 8, borderRadius: 4, backgroundColor: circleBg }} />
        <View style={[styles.greyLine, isLast && { backgroundColor: "transparent" }]} />
      </View>
      <AkuTextComponent
        style={{ color: textColor, marginTop: 10, textAlign: "center" }}
        type={AkuTextComponentType.Aku_font_11_regular}
        text={text}
      />
    </View>
  );
};

export default ProcessHeader;

const styles = StyleSheet.create({
  greyLine: { flex: 1, height: 2, flexDirection: "row", backgroundColor: FigmaStyle.Color.Grey_Divider }
});
