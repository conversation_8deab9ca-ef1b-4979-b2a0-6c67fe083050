/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 16:27
 * @description
 */

import AkuTextComponent, { AkuTextComponentType } from "@akulaku-rn/akui-rn/src/components/AkuTextComponent";
import React from "react";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import { TFunction } from "i18next";
import { FigmaStyle } from "@akulaku-rn/akui-rn";

type Props = {
  t: TFunction;
  onPress: () => void;
};

const InstructionVerify = React.memo(function InstructionVerifyComp({ t, onPress }: Props) {
  return (
    <View>
      <AkuTextComponent
        style={{ paddingHorizontal: 16, paddingTop: 16, paddingBottom: 8 }}
        type={AkuTextComponentType.Aku_font_14_medium}
        text={t("为了你的账户安全，请选择一种方式进行身份验证")}
      />
      <TouchableOpacity activeOpacity={0.85} onPress={onPress}>
        <View style={styles.instructionReasonItemView}>
          <AkuTextComponent type={AkuTextComponentType.Aku_font_14_regular} text={t("身份验证")} />
          <View style={{ flex: 1 }} />
          <Image source={require("common/images/arrow_right2.webp")} style={{ height: 16, width: 16 }} />
        </View>
      </TouchableOpacity>
      <View style={styles.instructionReasonItemLine} />
    </View>
  );
});

export default InstructionVerify;

const styles = StyleSheet.create({
  instructionReasonItemView: { paddingHorizontal: 16, paddingVertical: 18, flexDirection: "row", alignItems: "center" },
  instructionReasonItemLine: {
    marginLeft: 16,
    height: StyleSheet.hairlineWidth,
    backgroundColor: FigmaStyle.Color.Grey_Divider
  }
});
