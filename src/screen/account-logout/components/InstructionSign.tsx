/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2024-03 16:27
 * @description
 */

import React from "react";
import { StyleSheet, View } from "react-native";
import AkuTextComponent, { AkuTextComponentType } from "@akulaku-rn/akui-rn/src/components/AkuTextComponent";
import { TFunction } from "i18next";
import { FigmaStyle } from "@akulaku-rn/akui-rn";

type Props = {
  t: TFunction;
};

const InstructionSign = React.memo(function InstructionSignComp({ t }: Props) {
  return (
    <View style={{ padding: 16 }}>
      <AkuTextComponent type={AkuTextComponentType.Aku_font_14_medium} text={t("签署协议")} />
      <AkuTextComponent
        style={{ marginTop: 16, color: FigmaStyle.Color.Grey_Text2 }}
        type={AkuTextComponentType.Aku_font_14_regular}
        text={t("点击确认即代表你本人同意注销本账户，同意放弃本账户相关的一切相关服务。")}
      />
      <AkuTextComponent
        style={{ marginTop: 16, color: FigmaStyle.Color.Grey_Text2 }}
        type={AkuTextComponentType.Aku_font_14_medium}
        text={t("账户注销后将无法找回。")}
      />
    </View>
  );
});

export default InstructionSign;

const styles = StyleSheet.create({});
