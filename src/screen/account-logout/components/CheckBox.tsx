/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 19:54
 * @description
 */

import React from "react";
import { Image, StyleSheet } from "react-native";

type Props = {
  isCheck: boolean;
};

const CheckBox = React.memo(function CheckBoxComp({ isCheck }: Props) {
  const source = isCheck ? require("../images/Check_Selected.png") : require("../images/Check_Off.png");
  return <Image style={styles.image} source={source} />;
});

export default CheckBox;

const styles = StyleSheet.create({
  image: {
    height: 24,
    width: 24
  }
});
