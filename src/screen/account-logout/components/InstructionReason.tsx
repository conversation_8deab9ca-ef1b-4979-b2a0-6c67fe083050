/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 16:26
 * @description
 */

import React, { useEffect, useRef, useState } from "react";
import { Image, StyleSheet, TextInput, TouchableOpacity, View } from "react-native";
import { AkuTextComponent, AkuTextComponentType, FigmaStyle } from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";
import { InstructionReasonItemData, InstructionReasonType } from "../types";
import CheckBox from "./CheckBox";
import isBoolean from "lodash/isBoolean";
import { Observer } from "mobx-react";

type Props = {
  t: TFunction;
  onChangeSelect: (reasons: InstructionReasonItemData) => void;
  reasonArr: InstructionReasonItemData[];
  showOtherReasonError?: boolean;
  onInputChangeText?: (text: string) => void;
  otherReasonText?: string;
};

const InstructionReason = React.memo(function InstructionInfoComp({
  t,
  onChangeSelect,
  reasonArr,
  showOtherReasonError,
  onInputChangeText,
  otherReasonText
}: Props) {
  const onChooseItem = (item: InstructionReasonItemData) => {
    onChangeSelect(item);
  };

  const onChooseOtherItem = (item: InstructionReasonItemData) => {
    onChangeSelect(item);
  };

  const onOtherInputChangeText = (text: string) => {
    !!onInputChangeText && onInputChangeText(text);
  };

  return (
    <View>
      <AkuTextComponent
        style={{ marginBottom: 8, marginHorizontal: 16, marginTop: 16 }}
        type={AkuTextComponentType.Aku_font_14_medium}
        text={t("请选择注销原因（可多选)")}
      />
      {reasonArr.map(item => {
        if (item.value === InstructionReasonType.Other) {
          return (
            <InstructionReasonOtherItem
              t={t}
              value={otherReasonText || ""}
              onChangeText={onOtherInputChangeText}
              item={item}
              onPress={onChooseOtherItem}
              showError={showOtherReasonError}
            />
          );
        }
        return <InstructionReasonItem key={`${item.value}`} item={item} onPress={onChooseItem} />;
      })}
    </View>
  );
});

const InstructionReasonItem = ({
  item,
  onPress
}: {
  item: InstructionReasonItemData;
  onPress: (item: InstructionReasonItemData) => void;
}) => {
  return (
    <Observer>
      {() => (
        <TouchableOpacity activeOpacity={0.85} onPress={() => onPress(item)}>
          <View style={styles.instructionReasonItemView}>
            <AkuTextComponent style={{ flex: 1 }} type={AkuTextComponentType.Aku_font_14_regular} text={item.text} />
            <CheckBox isCheck={item.isSelect} />
          </View>
          <View style={styles.instructionReasonItemLine} />
        </TouchableOpacity>
      )}
    </Observer>
  );
};

const InstructionReasonOtherItem = ({
  item,
  onPress,
  showError,
  value,
  onChangeText,
  t
}: {
  item: InstructionReasonItemData;
  onPress: (item: InstructionReasonItemData) => void;
  showError?: boolean;
  onChangeText: (text: string) => void;
  value: string;
  t: TFunction;
}) => {
  const inputRef = useRef<TextInput>(null);

  const onPressInputArea = () => {
    !!inputRef.current && inputRef.current.focus();
  };

  return (
    <Observer>
      {() => (
        <View>
          <TouchableOpacity
            activeOpacity={0.85}
            onPress={() => onPress(item)}
            style={styles.instructionReasonOtherItemView}
          >
            <AkuTextComponent style={{ flex: 1 }} type={AkuTextComponentType.Aku_font_14_regular} text={item.text} />
            <CheckBox isCheck={item.isSelect} />
          </TouchableOpacity>
          {item.isSelect && (
            <TouchableOpacity
              activeOpacity={1}
              onPress={onPressInputArea}
              style={[styles.otherItemInputAreaContainer, showError && { borderColor: FigmaStyle.Color.Primary_6 }]}
            >
              <TextInput
                ref={inputRef}
                placeholder={t("请输入原因")}
                value={value}
                onChangeText={onChangeText}
                style={{ margin: 0, padding: 0 }}
              />
            </TouchableOpacity>
          )}
        </View>
      )}
    </Observer>
  );
};

export default InstructionReason;

const styles = StyleSheet.create({
  instructionReasonItemView: { paddingHorizontal: 16, paddingVertical: 18, flexDirection: "row", alignItems: "center" },
  instructionReasonOtherItemView: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8
  },
  instructionReasonItemLine: {
    marginLeft: 16,
    height: StyleSheet.hairlineWidth,
    backgroundColor: FigmaStyle.Color.Grey_Divider
  },
  otherItemInputAreaContainer: {
    marginHorizontal: 16,
    padding: 12,
    borderWidth: 1,
    borderColor: FigmaStyle.Color.Grey_Divider,
    borderRadius: 8,
    paddingHorizontal: 16,
    minHeight: 120
  }
});
