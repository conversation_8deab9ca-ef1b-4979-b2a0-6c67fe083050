/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 16:43
 * @description
 */

import React, { PropsWithChildren, useEffect } from "react";
import { InstructionPage } from "../types";
import AccountLogoutSensorManager from "../AccountLogoutSensorManager";

type Props = {
  viewType: InstructionPage;
};

const InstructionSensorExpose = React.memo<PropsWithChildren<Props>>(function InstructionSensorExposeComp({
  viewType,
  children
}: PropsWithChildren<Props>) {
  useEffect(() => {
    AccountLogoutSensorManager.expose16660101({
      Aku_buttonStatus: `${viewType}`
    });
  }, []);
  return <>{children}</>;
});

export default InstructionSensorExpose;
