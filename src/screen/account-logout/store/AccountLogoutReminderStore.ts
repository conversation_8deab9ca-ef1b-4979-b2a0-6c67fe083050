/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-02 15:25
 * @description
 */
import BasePageStore from "common/store/BasePageStore";
import { Api } from "../constants";
import { Loading } from "@akulaku-rn/akui-rn";
import { CusCheckRes, CusCheckResType, InstructionReasonItemData, PreCheckData } from "../types";
import { NetworkResponse } from "common/nativeModules/network/nativeNetworkModule";
import AKDialog, { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";
import i18next, { TFunction } from "i18next";
import { getI18n } from "react-i18next";
import { action, observable } from "mobx";
import { logoutAccountBack } from "../utils";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import AccountLogoutSensorManager from "../AccountLogoutSensorManager";

export default class AccountLogoutReminderStore extends BasePageStore {
  tFunction?: TFunction;

  @observable startCountDown = false;

  @action
  startCountDownAction = () => {
    this.startCountDown = true;
  };

  get t(): TFunction {
    if (!this.tFunction) {
      this.tFunction = getI18n().getFixedT(null, "AccountLogoutReminder");
    }
    return this.tFunction;
  }

  closeFlowId?: number;

  // 获取是否可以注销
  fetchEnableLogoutPreCheck = async (): Promise<CusCheckRes> => {
    return new Promise<CusCheckRes>(async resolve => {
      try {
        Loading.show();
        const res: NetworkResponse<PreCheckData> = await this.io.post(Api.preCheck, {}, { yapiId: 568 });
        if (res.success) {
          if (res.data.isPassed) {
            AccountLogoutSensorManager.popViewCancellationConditions2Pop30504();
            this.closeFlowId = res.data.closeFlowId;
            AKDialog.show({
              type: DialogType.C3_1_1,
              title: this.t("温馨提示"),
              desc: this.t("一旦账号删除成功，将无法再复原"),
              positiveText: this.t("确定"),
              onPositivePress: () => {
                AccountLogoutSensorManager.popClickConfirmPop3050401();
                AKDialog.dismiss();
                this.startCountDownAction();
                resolve({ status: CusCheckResType.Pass });
              },
              negativeText: this.t("取消"),
              onNegativePress: () => {
                AccountLogoutSensorManager.popClickCancelPop3050402();
                AKDialog.dismiss();
                logoutAccountBack();
                resolve({ status: CusCheckResType.UserCancel });
              }
            });
          } else {
            AccountLogoutSensorManager.popViewCancellationConditions1Pop30503();
            // 不满足注销条件
            AKDialog.show({
              type: DialogType.C3_1_1,
              desc: this.t("抱歉，您不符合注销账户的条件，如有需要请联系客服咨询。"),
              positiveText: this.t("确定"),
              onPositivePress: () => {
                AccountLogoutSensorManager.popClickConfirmPop3050301();
                AKDialog.dismiss();
                logoutAccountBack();
                resolve({ status: CusCheckResType.InterfaceSuccessFalse });
              }
            });
          }
        } else {
          resolve({ status: CusCheckResType.Error });
          NativeToast.showMessage(res.errMsg);
          this.isError = true;
        }
      } catch (e) {
        console.log(`e: `, e);
        this.isError = true;
      } finally {
        Loading.dismiss();
      }
    });
  };
}
