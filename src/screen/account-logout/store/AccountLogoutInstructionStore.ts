/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2024-02 15:56
 * @description
 */
import BasePageStore from "common/store/BasePageStore";
import { action, computed, observable } from "mobx";
import { InstructionPage, InstructionReasonItemData, InstructionReasonType } from "../types";
import { TFunction } from "i18next";
import { getI18n } from "react-i18next";
import { NetworkResponse } from "common/nativeModules/network/nativeNetworkModule";
import { Api } from "../constants";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";
import AccountLogoutSensorManager from "../AccountLogoutSensorManager";
import NativeUserInfoModule from "common/nativeModules/bussinse/nativeUserInfoModule";

export default class AccountLogoutInstructionStore extends BasePageStore {
  @observable currentIndex = InstructionPage.Info;

  @action changeIndex = (index: InstructionPage) => {
    this.currentIndex = index;
  };

  @observable operationId = "";

  @action saveOperationId = (id: string) => {
    console.log(`saveOperationId: `, this.operationId);
    this.operationId = id;
  };

  tFunction?: TFunction;

  get t(): TFunction {
    if (!this.tFunction) {
      this.tFunction = getI18n().getFixedT(null, "AccountLogoutReminder");
    }
    return this.tFunction;
  }

  @observable reasons: InstructionReasonItemData[] = [
    {
      text: this.t("怀疑我的账号已经被盗"),
      value: InstructionReasonType.Stolen,
      isSelect: false
    },
    {
      text: this.t("我是帮朋友（他人） 注册的"),
      value: InstructionReasonType.OtherPerson,
      isSelect: false
    },
    {
      text: this.t("我觉得我在akulaku的信息可能会被泄漏"),
      value: InstructionReasonType.InfoLeaked,
      isSelect: false
    },
    {
      text: this.t("其他"),
      value: InstructionReasonType.Other,
      isSelect: false
    }
  ];

  @computed get checkReasonsPass(): boolean {
    const checkReasons = this.reasons.filter(item => item.isSelect);
    if (!checkReasons.length) {
      return false;
    }
    const otherItem = checkReasons.find(item => item.value === InstructionReasonType.Other);
    if (!!otherItem) {
      console.log("checkReasonsPass 1", !!otherItem.otherReason && !!this.inputText);
      return !!this.inputText;
    }
    return true;
  }

  @action
  onChangeSelect = (reason: InstructionReasonItemData) => {
    AccountLogoutSensorManager.clickSelectReason16660105({
      Aku_buttonName: `${reason.value}`,
      Aku_buttonStatus: `${InstructionPage.Reason}`
    });
    this.reasons.forEach(item => {
      if (reason.value === InstructionReasonType.Other) {
        if (item.value === InstructionReasonType.Other) {
          item.isSelect = !item.isSelect;
        } else {
          item.isSelect = false;
        }
      } else {
        if (item.value === InstructionReasonType.Other) {
          item.isSelect = false;
        } else {
          if (item.value === reason.value) {
            item.isSelect = !item.isSelect;
          }
        }
      }
    });
  };

  @observable inputText = "";

  @observable showInputError = false;

  @action
  onInputChangeText = (text: string) => {
    this.inputText = text;
    this.reasons.forEach(item => {
      if (item.value === InstructionReasonType.Other) {
        item.otherReason = text;
      }
    });
  };

  @observable agreeAgreement = false;

  @action
  changeAgreeAgreement = (status: boolean) => {
    AccountLogoutSensorManager.clickCheck16660107({ Aku_buttonStatus: `${InstructionPage.Sign}` });
    this.agreeAgreement = status;
  };

  @computed get buttonEnable(): boolean {
    console.log(`buttonEnable this.operationId: `, this.operationId);
    switch (this.currentIndex) {
      case InstructionPage.Info:
        return true;
      case InstructionPage.Reason:
        return this.checkReasonsPass;
      case InstructionPage.Verify:
        return !!this.operationId;
      case InstructionPage.Sign:
        return this.agreeAgreement;
    }
    return false;
  }

  setCloseFlowId = (id: number) => {
    this.closeFlowId = id;
  };

  closeFlowId = 0;

  doLogoutAccount = async () => {
    try {
      const params: Record<string, any> = {
        closeFlowId: this.closeFlowId,
        riskFlowId: this.operationId,
        closeReasonType: this.reasons.filter(item => item.isSelect).map(item => item.value)
      };
      if (params.closeReasonType.includes(InstructionReasonType.Other)) {
        params.reason = this.inputText;
      }
      Loading.show();
      const res: NetworkResponse = await this.io.post(Api.confirm, params, { yapiId: 568 });
      if (res.success) {
        NativeNavigationModule.popToHome(0);
        NativeUserInfoModule.deleteAccount();
      } else {
        NativeToast.showMessage(res.errMsg);
      }
    } catch (e) {
    } finally {
      Loading.dismiss();
    }
  };
}
