/**
 * Create by z<PERSON><PERSON>z on 2024-03 16:49
 * @description
 */

export type PreCheckData = {
  isPassed: boolean;
  closeFlowId: number;
};

export type CusCheckRes = {
  status: CusCheckResType;
};

export enum CusCheckResType {
  Pass,
  UnPass,
  UserCancel,
  InterfaceSuccessFalse,
  Error
}

export enum InstructionPage {
  Info = 1,
  Reason,
  Verify,
  Sign
}

export enum InstructionReasonType {
  Stolen = 1,
  OtherPerson,
  InfoLeaked,
  Other
}

export type InstructionReasonItemData = {
  text: string;
  value: InstructionReasonType;
  isSelect: boolean;
  otherReason?: string;
};
