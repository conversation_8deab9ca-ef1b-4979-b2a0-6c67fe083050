import NativeSensorModule, { SensorEvent, SensorType } from "common/nativeModules/sdk/nativeSensroModule";

export default class AccountLogoutSensorManager {
  static popViewCancellationConditions1Pop30503(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.POPVIEW, {
      page_id: "1665",
      page_name: "Important reminder page",
      pop_id: "pop30503",
      pop_name: "cancellation conditions1",
      extra: extra
    });
  }

  static popClickConfirmPop3050301(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.POPCLICK, {
      page_id: "1665",
      page_name: "Important reminder page",
      element_id: "pop3050301",
      element_name: "confirm",
      pop_id: "pop30503",
      pop_name: "cancellation conditions1",
      extra: extra
    });
  }

  static popViewCancellationConditions2Pop30504(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.POPVIEW, {
      page_id: "1665",
      page_name: "Important reminder page",
      pop_id: "pop30504",
      pop_name: "cancellation conditions2",
      extra: extra
    });
  }

  static popClickConfirmPop3050401(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.POPCLICK, {
      page_id: "1665",
      page_name: "Important reminder page",
      element_id: "pop3050401",
      element_name: "confirm",
      pop_id: "pop30504",
      pop_name: "cancellation conditions2",
      extra: extra
    });
  }

  static popClickCancelPop3050402(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.POPCLICK, {
      page_id: "1665",
      page_name: "Important reminder page",
      element_id: "pop3050402",
      element_name: "cancel",
      pop_id: "pop30504",
      pop_name: "cancellation conditions2",
      extra: extra
    });
  }

  static pageImportantReminderPage1665(extra: Record<string, boolean | string | number> = {}): SensorEvent {
    return {
      page_id: "1665",
      page_name: "Important reminder page",
      extra: extra
    };
  }

  static clickCancel16650101(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1665",
      page_name: "Important reminder page",
      element_id: "16650101",
      element_name: "cancel",
      module_name: "reminder",
      module_id: "01",
      position_id: "01",
      extra: extra
    });
  }

  static clickContinue16650102(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1665",
      page_name: "Important reminder page",
      element_id: "16650102",
      element_name: "continue",
      module_name: "reminder",
      module_id: "01",
      position_id: "02",
      extra: extra
    });
  }

  static clickReturn16650103(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1665",
      page_name: "Important reminder page",
      element_id: "16650103",
      element_name: "return",
      module_name: "reminder",
      module_id: "01",
      position_id: "03",
      extra: extra
    });
  }

  static pageLogoutInstructions1666(extra: Record<string, boolean | string | number> = {}): SensorEvent {
    return {
      page_id: "1666",
      page_name: "logout instructions",
      extra: extra
    };
  }

  static expose16660101(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.EXPOSE, {
      page_id: "1666",
      page_name: "logout instructions",
      element_id: "16660101",
      module_name: "logout",
      module_id: "01",
      position_id: "01",
      extra: extra
    });
  }

  static clickCancel16660102(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1666",
      page_name: "logout instructions",
      element_id: "16660102",
      element_name: "cancel",
      module_name: "logout",
      module_id: "01",
      position_id: "02",
      extra: extra
    });
  }

  static clickContinue16660103(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1666",
      page_name: "logout instructions",
      element_id: "16660103",
      element_name: "continue",
      module_name: "logout",
      module_id: "01",
      position_id: "03",
      extra: extra
    });
  }

  static clickReturn16660104(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1666",
      page_name: "logout instructions",
      element_id: "16660104",
      element_name: "return",
      module_name: "logout",
      module_id: "01",
      position_id: "04",
      extra: extra
    });
  }

  static clickSelectReason16660105(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1666",
      page_name: "logout instructions",
      element_id: "16660105",
      element_name: "select reason",
      module_name: "logout",
      module_id: "01",
      position_id: "05",
      extra: extra
    });
  }

  static clickVerify16660106(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1666",
      page_name: "logout instructions",
      element_id: "16660106",
      element_name: "verify",
      module_name: "logout",
      module_id: "01",
      position_id: "06",
      extra: extra
    });
  }

  static clickCheck16660107(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.CLICK, {
      page_id: "1666",
      page_name: "logout instructions",
      element_id: "16660107",
      element_name: "check",
      module_name: "logout",
      module_id: "01",
      position_id: "07",
      extra: extra
    });
  }

  static popViewRetainPop30505(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.POPVIEW, {
      page_id: "1666",
      page_name: "logout instructions",
      pop_id: "pop30505",
      pop_name: "retain",
      extra: extra
    });
  }

  static popClickCancelPop3050501(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.POPCLICK, {
      page_id: "1666",
      page_name: "logout instructions",
      element_id: "pop3050501",
      element_name: "cancel",
      pop_id: "pop30505",
      pop_name: "retain",
      extra: extra
    });
  }

  static popClickContinePop3050502(extra: Record<string, boolean | string | number> = {}): void {
    NativeSensorModule.reportSence(SensorType.POPCLICK, {
      page_id: "1666",
      page_name: "logout instructions",
      element_id: "pop3050502",
      element_name: "contine",
      pop_id: "pop30505",
      pop_name: "retain",
      extra: extra
    });
  }
}
