/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-02 15:25
 * @description
 */

import React from "react";
import { View, StyleSheet, ScrollView, BackHandler, NativeEventSubscription } from "react-native";
import BaseContainer, { Type } from "common/components/BaseContainer";
import ProcessHeader from "./components/ProcessHeader";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import AccountLogoutInstructionStore from "./store/AccountLogoutInstructionStore";
import { inject, observer } from "mobx-react";
import { Android, FigmaStyle } from "@akulaku-rn/akui-rn";
import { InstructionPage } from "./types";
import InstructionInfo from "./components/InstructionInfo";
import AkButton, { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import InstructionReason from "./components/InstructionReason";
import { logoutAccountBack } from "./utils";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import InstructionVerify from "./components/InstructionVerify";
import InstructionSign from "./components/InstructionSign";
import { NativeRiskCheckModule } from "common/nativeModules";
import { CheckRiskType } from "common/nativeModules/bussinse/nativeRiskCheckModule";
import NativeEventReportModule, { ReportScene } from "common/nativeModules/basics/nativeEventReportModule";
import LogoutAgreement from "./components/LogoutAgreement";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import AKDialog, { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import AccountLogoutSensorManager from "./AccountLogoutSensorManager";
import InstructionSensorExpose from "./components/InstructionSensorExpose";

@RootView({
  withI18n: [
    "AccountLogoutInstruction",
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json")
    }
  ],
  store: AccountLogoutInstructionStore,
  keyApi: []
})
@withTranslation("AccountLogoutInstruction")
@inject("store")
@observer
export default class AccountLogoutInstruction extends BaseContainer<
  AccountLogoutInstructionStore,
  { closeFlowId: number }
> {
  constructor(props: Type.Props<AccountLogoutInstructionStore, { closeFlowId: number }>) {
    super(props);
    this.props.store.pageStore.isLoading = false;
    this.props.store.pageStore.setCloseFlowId(props.store.navParams.closeFlowId);
    if (Android) {
      this.backHandler = BackHandler.addEventListener("hardwareBackPress", this.goBack);
    }
    this.props.configSensorEvent(AccountLogoutSensorManager.pageLogoutInstructions1666());
  }

  componentWillUnmount(): void {
    if (!!this.backHandler) {
      BackHandler.removeEventListener("hardwareBackPress", this.goBack);
    }
  }

  backHandler?: NativeEventSubscription;

  navigationBarProps = () => {
    return {
      title: this.props.t("注销须知")
    };
  };

  goBack = () => {
    AccountLogoutSensorManager.clickReturn16660104({
      Aku_buttonStatus: `${this.props.store.pageStore.currentIndex}`
    });
    logoutAccountBack();
    return true;
  };

  safeVerify = async () => {
    AccountLogoutSensorManager.clickVerify16660106({ Aku_buttonStatus: `${InstructionPage.Verify}` });
    this.props.store.pageStore.operationId = "";
    let reportData = null;
    const { data, isSM } = await NativeEventReportModule.getAllDeviceInfoNew(ReportScene.DEFULIT_ZERO);
    if (isSM) {
      reportData = JSON.stringify((data.data as any)?.deceiveInfoReportData);
    } else {
      reportData = JSON.stringify(data.data);
    }
    Android && Loading.show();
    // const checkRisk: any = await new Promise(resolve =>
    //   setTimeout(() => {
    //     resolve({ success: true, data: { operationId: "*********" } });
    //   }, 500)
    // );
    const checkRisk = await NativeRiskCheckModule.checkRisk({
      type: 79,
      phone: this.props.store.user.userDetail.data?.phoneNumber ?? "",
      reportData: reportData
    });
    if (!checkRisk.success) {
      NativeToast.showMessage(checkRisk.errMsg);
    } else {
      this.props.store.pageStore.saveOperationId(checkRisk.data.operationId);
      this.onContinue();
    }
    Android && Loading.dismiss();
    return checkRisk.success;
  };

  renderContent = (index: InstructionPage) => {
    switch (index) {
      case InstructionPage.Info:
        return (
          <InstructionSensorExpose key={`${InstructionPage.Info}`} viewType={index}>
            <InstructionInfo t={this.props.t} />
          </InstructionSensorExpose>
        );
      case InstructionPage.Reason:
        return (
          <InstructionSensorExpose key={`${InstructionPage.Reason}`} viewType={index}>
            <InstructionReason
              otherReasonText={this.props.store.pageStore.inputText}
              reasonArr={this.props.store.pageStore.reasons}
              onChangeSelect={this.props.store.pageStore.onChangeSelect}
              showOtherReasonError={this.props.store.pageStore.showInputError}
              onInputChangeText={this.props.store.pageStore.onInputChangeText}
              t={this.props.t}
            />
          </InstructionSensorExpose>
        );
      case InstructionPage.Verify:
        return (
          <InstructionSensorExpose key={`${InstructionPage.Verify}`} viewType={index}>
            <InstructionVerify t={this.props.t} onPress={this.safeVerify} />
          </InstructionSensorExpose>
        );
      case InstructionPage.Sign:
        return (
          <InstructionSensorExpose key={`${InstructionPage.Sign}`} viewType={index}>
            <InstructionSign t={this.props.t} />
          </InstructionSensorExpose>
        );
    }
  };

  cancelLogout = () => {
    AccountLogoutSensorManager.clickCancel16660102({
      Aku_buttonStatus: `${this.props.store.pageStore.currentIndex}`
    });
    logoutAccountBack();
  };

  onContinue = () => {
    AccountLogoutSensorManager.clickContinue16660103({
      Aku_buttonStatus: `${this.props.store.pageStore.currentIndex}`
    });
    switch (this.props.store.pageStore.currentIndex) {
      case InstructionPage.Info:
        this.props.store.pageStore.changeIndex(InstructionPage.Reason);
        return;
      case InstructionPage.Reason:
        this.props.store.pageStore.changeIndex(InstructionPage.Verify);
        return;
      case InstructionPage.Verify:
        this.props.store.pageStore.changeIndex(InstructionPage.Sign);
        return;
      case InstructionPage.Sign:
        AccountLogoutSensorManager.popViewRetainPop30505({ Aku_buttonStatus: `${InstructionPage.Sign}` });
        AKDialog.show({
          desc: this.props.t("确定要离开Akulaku吗？一旦离开，你将丢失所有已获得的权益。"),
          positiveText: this.props.t("我再想想"),
          negativeText: this.props.t("狠心离开"),
          onPositivePress: () => {
            AccountLogoutSensorManager.popClickCancelPop3050501({ Aku_buttonStatus: `${InstructionPage.Sign}` });
            logoutAccountBack();
          },
          onNegativePress: () => {
            AccountLogoutSensorManager.popClickContinePop3050502({ Aku_buttonStatus: `${InstructionPage.Sign}` });
            this.props.store.pageStore.doLogoutAccount();
          },
          type: DialogType.C3_1_1
        });
        return;
    }
  };

  getHeaderCurrentIndex = (currentIndex: InstructionPage): number => {
    switch (currentIndex) {
      case InstructionPage.Reason:
        return 1;
      case InstructionPage.Verify:
        return 2;
      case InstructionPage.Sign:
        return 3;
      case InstructionPage.Info:
      default:
        return 0;
    }
  };

  _render(): React.ReactElement {
    const {
      t,
      store: { pageStore }
    } = this.props;
    return (
      <View style={{ flex: 1 }}>
        <ProcessHeader
          style={{ marginTop: 16, marginBottom: 20 }}
          currentIndex={this.getHeaderCurrentIndex(pageStore.currentIndex)}
          tabs={[t("注销须知"), t("注销原因"), t("身份验证"), t("签署协议")]}
        />
        <View style={{ height: 8, backgroundColor: FigmaStyle.Color.Grey_Bg }} />
        <ScrollView style={{ flex: 1 }}>{this.renderContent(pageStore.currentIndex)}</ScrollView>
        {pageStore.currentIndex === InstructionPage.Sign && (
          <LogoutAgreement
            t={this.props.t}
            isCheck={this.props.store.pageStore.agreeAgreement}
            onChangeStatus={this.props.store.pageStore.changeAgreeAgreement}
          />
        )}
        <View style={styles.bottomBtnContainer}>
          <AkButton
            style={{ flex: 1 }}
            onPress={this.cancelLogout}
            text={this.props.t("取消")}
            type={AKButtonType.B1_2_2}
          />
          <View style={{ width: 8 }} />
          <AkButton
            disabled={!this.props.store.pageStore.buttonEnable}
            style={{ flex: 1 }}
            onPress={this.onContinue}
            text={this.props.t("确定")}
            type={AKButtonType.B1_1_2}
          />
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  bottomBtnContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
    borderTopColor: FigmaStyle.Color.Grey_Divider,
    borderTopWidth: StyleSheet.hairlineWidth
  }
});
