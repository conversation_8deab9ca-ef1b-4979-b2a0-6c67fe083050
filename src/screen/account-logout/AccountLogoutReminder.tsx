/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-02 15:25
 * @description
 */

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, NativeEventSubscription, ScrollView, StyleSheet, View } from "react-native";
import BaseContainer, { Type } from "common/components/BaseContainer";
import ReminderItem from "./components/ReminderItem";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import AccountLogoutReminderStore from "./store/AccountLogoutReminderStore";
import AkButton, { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import CountDownBtn from "./components/CountDownBtn";
import { Android, FigmaStyle } from "@akulaku-rn/akui-rn";
import { logoutAccountBack } from "./utils";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import AccountLogoutSensorManager from "./AccountLogoutSensorManager";

@RootView({
  withI18n: [
    "AccountLogoutReminder",
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json")
    }
  ],
  store: AccountLogoutReminderStore,
  keyApi: []
})
@withTranslation("AccountLogoutReminder")
@inject("store")
@observer
export default class AccountLogoutReminder extends BaseContainer<AccountLogoutReminderStore> {
  constructor(props: Type.Props<AccountLogoutReminderStore>) {
    super(props);
    this.props.configSensorEvent(AccountLogoutSensorManager.pageImportantReminderPage1665());
  }

  componentDidMount(): void {
    this.props.store.pageStore.isLoading = false;
    this.init();
    if (Android) {
      this.backHandler = BackHandler.addEventListener("hardwareBackPress", this.goBack);
    }
  }

  componentWillUnmount(): void {
    if (!!this.backHandler) {
      BackHandler.removeEventListener("hardwareBackPress", this.goBack);
    }
  }

  backHandler?: NativeEventSubscription;

  goBack = () => {
    AccountLogoutSensorManager.clickReturn16650103();
    logoutAccountBack();
    return true;
  };

  init = async () => {
    this.props.store.pageStore.fetchEnableLogoutPreCheck();
  };

  errorComponentOnRetryPress = () => {
    this.props.store.pageStore.isError = false;
    this.props.store.pageStore.isLoading = false;
    this.init();
  };

  navigationBarProps = () => {
    return {
      title: this.props.t("重要提醒"),
      onBackPress: this.goBack
    };
  };

  remainderMap = [
    {
      title: this.props.t("账户信息删除"),
      content: this.props.t(
        "账户相关信息将会被删除，包括绑定信息 （手机号、银行卡、身份证等），交易、还款等订单信息（交易、账单等），后续将无法再使用该账户交易。"
      )
    },
    {
      title: this.props.t("账户权益清空"),
      content: this.props.t(
        "相关权益（优惠券、奖劢、AKu币等） 将会被清空，消费额度将会被清空，后续无法继续享用相关权益和消费额度。"
      )
    },
    {
      title: this.props.t("账户无法找回"),
      content: this.props.t(
        "注销后账号将彻底删除，无法找回，代表你完全放弃了本账户相关的一切服务，使用AKulaku账号的数据将无法找回。"
      )
    },
    {
      title: this.props.t("不再提供咨询和售后服务"),
      content: this.props.t("后续将不再提供关于该账户的相关咨询和售后服务。")
    },
    {
      title: this.props.t("注销后重新注册，可能会影响额度的获取"),
      content: this.props.t(
        "为防范欺诈风险，若注销此账号后重新注册，新账号在本平台的授信申请审批及额度获取可能会收到影响。"
      )
    }
  ];

  cancelLogout = () => {
    AccountLogoutSensorManager.clickCancel16650101();
    logoutAccountBack();
  };

  continueLogout = () => {
    AccountLogoutSensorManager.clickContinue16650102();
    if (!!this.props.store.pageStore.closeFlowId) {
      this.props.navigation.navigate({
        screen: "AccountLogoutInstruction",
        params: {
          closeFlowId: this.props.store.pageStore.closeFlowId
        }
      });
    } else {
      NativeToast.showMessage(`closeFlowId Error: ${this.props.store.pageStore.closeFlowId}`);
    }
  };

  _render(): React.ReactElement {
    return (
      <View style={styles.container}>
        <ScrollView
          contentContainerStyle={{ paddingHorizontal: 16, paddingBottom: 40 }}
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
        >
          {this.remainderMap.map((item, index) => {
            return (
              <ReminderItem
                style={{ marginTop: index === 0 ? 0 : 20 }}
                key={`${item.title}_${index}`}
                content={item.content}
                title={item.title}
              />
            );
          })}
        </ScrollView>
        <View style={styles.bottomBtnContainer}>
          <AkButton
            style={{ flex: 1 }}
            onPress={this.cancelLogout}
            text={this.props.t("取消")}
            type={AKButtonType.B1_2_2}
          />
          <View style={{ width: 8 }} />
          <CountDownBtn
            startCountDown={this.props.store.pageStore.startCountDown}
            style={{ flex: 1 }}
            onPress={this.continueLogout}
            text={this.props.t("确定")}
          />
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  bottomBtnContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
    borderTopColor: FigmaStyle.Color.Grey_Divider,
    borderTopWidth: StyleSheet.hairlineWidth
  }
});
