/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-09 21:16
 * @description
 */

import React, { useState, useCallback } from "react";
import { Image, ImageSourcePropType, Text, TouchableOpacity, View, Keyboard, TextInput } from "react-native";
import { UrlImage } from "@akulaku-rn/akui-rn";
import { Fields } from "../types";
import RefreshImageCodeBtn from "common/components/ImageVerifyDialog/RefreshImageCodeBtn";
import SMSVerify from "common/components/SMSVerify/SMSVerify";
import AuthSensorManager from "../util/AuthSensorManager";
import AkConsole from "common/util/AkConsole/AkConsole";
import { NativeNavigationModule } from "common/nativeModules";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { TFunction } from "i18next";
import AnimatedTextInput from "../util/AnimatedTextInput";
import ShadowContainer from "./ShadowContainer";
import Email from "./Email";
import { debounce } from "lodash";
import { AKButton, AKButtonType } from "common/components/AKButton";
import { InputBizInfo } from "@akulaku-rn/rn-v4-sdk/src/ReporterSdk/types";
import { HocActionLogInput } from "@akulaku-rn/rn-v4-sdk";
import styles from "./styles/AuthPageComponent.styls";

type Props = {
  t: TFunction;
  icon: string;
  info: string;
  fields: Array<any>;
  tip?: string;
  onClickTip?: () => void;
  btnText: string;
  onSubmit: () => void;
  getSmsCode?: (loginParams?: any) => Promise<void>;
  getEmailCode?: () => Promise<void>;
  getImageCode?: () => void;
  tipIcon?: ImageSourcePropType;
  imageCode?: string;
  backgroundImg?: string;
  accountInfoError: boolean;
  accountInfoErrorText: string;
  initSMSBtnText?: string;
  smsCodeError?: boolean;
  imageCodeError?: boolean;
  emailCodeError?: boolean;
  validErrorText?: string;
  changeError: (key: string, value: boolean) => void;
  faqUrl: string; //忘记密码
  protocolUrl: string; //协议url
  // pattern?: string;
  errorText?: string;
  nullText?: string;
  path?: string;
};

const ActionLogInputCache: { [key: string]: any } = {};

const getActionLogInput = (data: InputBizInfo) => {
  if (data.cn) {
    if (!ActionLogInputCache[`${data.cn}`]) {
      ActionLogInputCache[`${data.cn}`] = HocActionLogInput(data)(TextInput);
    }
    return ActionLogInputCache[`${data.cn}`];
  }
  return null;
};

export default function AuthPageComponent({
  icon = "",
  info,
  fields,
  tip,
  onClickTip,
  btnText,
  onSubmit,
  backgroundImg = "",
  tipIcon,
  getSmsCode,
  getEmailCode,
  getImageCode,
  imageCode,
  accountInfoError,
  accountInfoErrorText,
  initSMSBtnText,
  smsCodeError = false,
  imageCodeError = false,
  emailCodeError = false,
  validErrorText,
  changeError,
  faqUrl,
  protocolUrl,
  t,
  nullText,
  path
}: Props) {
  const [checked, setChecked] = useState(true);

  /**
   * 是否展示邮箱提示
   */
  const [showEmailTip, setShowEmailTip] = useState(false);

  const getBtnDisabled = (fieldsTemp: any, checkReverse?: boolean) => {
    AkConsole("Auth", `fieldsTemp: `, fieldsTemp);
    const res = fieldsTemp.some((item: any) => !item.value);
    AkConsole("Auth", `getBtnDisabled: `, res);
    if (checkReverse) {
      return res || checked;
    } else {
      return res || !checked;
    }
  };

  const [btnDisabled, setBtnDisabled] = useState(true);
  const [account, setAccount] = useState("");
  const [pwd, setPwd] = useState("");
  const [smsCode, setSmsCode] = useState("");
  const [imageCodeS, setImageCodeS] = useState("");
  const [emailCode, setEmailCode] = useState("");
  const [isShowPwd, setChangedPwdState] = useState(true);

  AkConsole("Auth", `smsCodeError: `, smsCodeError);

  // 确认操作
  const _onSubmit = useCallback(
    debounce(() => {
      Keyboard.dismiss();
      if (!checked) {
        NativeToast.showMessage(t("请勾选协议"));
        return;
      }
      onSubmit && onSubmit();
      AuthSensorManager.sc.clickFormSubmit();
    }),
    [onSubmit, checked]
  );
  // 查看协议内容
  const agree = () => {
    NativeNavigationModule.navigate({ url: protocolUrl });
    AuthSensorManager.sc.clickProtocol();
    AuthSensorManager.v4.clickPersonProtocol();
  };
  // 阅读协议
  const read = () => {
    setChecked(!checked);
    setBtnDisabled(getBtnDisabled(fields, true));
    AuthSensorManager.sc.checkProtocol();
  };
  // 忘记密码
  const forgot = () => {
    NativeNavigationModule.navigate({ url: faqUrl });
    AuthSensorManager.sc.clickForgetAccount();
    AuthSensorManager.v4.clickForgetAccount();
  };

  // 一键清除输入框
  const _onDeleteInputContent = (item: Fields) => {
    item.value = "";
    setBtnDisabled(getBtnDisabled(fields));
    switch (item.inputMethod) {
      case "textFiled": // 电话号码或者邮箱
        setAccount("");
        break;
      case "passwordFiled": // 密码
        setPwd("");
        break;
      case "smsCodeFiled": // 验证码
        setSmsCode("");
        break;
      default:
        break;
    }
  };

  // 显示和隐藏密码
  const _onChangdedEyeIcon = () => {
    setChangedPwdState(!isShowPwd);
  };

  // TODO 根据表单接口返回的正则判断账号输入是否需要邮箱，从而发送验证码的接口调用短信验证码还是邮箱验证码接口
  const isEmail = false;
  return (
    <View style={styles.container}>
      {/*<View style={styles.header}>*/}
      <UrlImage style={styles.bannerImg} source={backgroundImg} />
      {/*<Image style={styles.logoImg} source={!!icon ? { uri: icon } : require("")} />*/}
      {/*</View>*/}

      {/*<Text style={styles.info}>{info}</Text>*/}

      <View
        style={{
          marginTop: 123
        }}
      >
        <ShadowContainer>
          <View style={styles.shadow}>
            {fields.map((item: Fields, index: number) => {
              let lastMarginBottom = 12;
              if (index === fields.length - 1 && accountInfoError) {
                lastMarginBottom = 0;
              } else {
                lastMarginBottom = 20;
              }
              const _onFocus = (method: string) => () => {
                // @ts-ignore
                AuthSensorManager.sc[`${method}`]({
                  fieldID: item.id,
                  fieldName: item.name,
                  fieldType: item.inputMethod,
                  require: 1
                });
                AuthSensorManager.v4.clickV4FormItem();

                // AuthSensorManager.sc.clickForm();
              };
              switch (item.inputMethod) {
                case "textFiled": // 电话号码或者邮箱
                  let emptyText;
                  switch (path) {
                    case "social_insurance.bpjstku":
                      emptyText = t("请填写社保网站登录邮箱");
                      break;
                    default:
                      emptyText = t("请填写账号信息");
                  }
                  return (
                    <View
                      style={{
                        width: "100%",
                        marginBottom: lastMarginBottom
                      }}
                      key={item.name}
                    >
                      <AnimatedTextInput
                        emptyText={emptyText}
                        onFocus={_onFocus("clickForm")}
                        value={account}
                        id={index + 1}
                        placeholder={item.placeHolder}
                        isShowColoseIcon={!!account}
                        onDeleteInputContent={() => _onDeleteInputContent(item)}
                        setValue={(value: string) => {
                          setShowEmailTip(value.endsWith("@"));
                          item.value = value;
                          setAccount(value);
                          setBtnDisabled(getBtnDisabled(fields));
                        }}
                        pattern={item.pattern}
                        errorText={item.errTip}
                        nullText={nullText}
                        listenerName={"setEmail"}
                        mergeListenerValue={true}
                      />
                    </View>
                  );
                case "passwordFiled":
                  return (
                    <View
                      style={{
                        width: "100%",
                        marginBottom: lastMarginBottom
                      }}
                      key={item.name}
                    >
                      <AnimatedTextInput
                        emptyText={t("请填写登陆密码")}
                        onFocus={_onFocus("clickForm")}
                        secureTextEntry={isShowPwd}
                        onChangdedEyeIcon={_onChangdedEyeIcon}
                        isShowEyeIcon={true}
                        isShowColoseIcon={!!pwd}
                        onDeleteInputContent={() => _onDeleteInputContent(item)}
                        value={pwd}
                        id={index + 1}
                        placeholder={item.placeHolder}
                        setValue={(value: string) => {
                          item.value = value;
                          setPwd(value);
                          setBtnDisabled(getBtnDisabled(fields));
                        }}
                      />
                    </View>
                  );
                case "smsCodeFiled":
                  return (
                    <View style={{ flexDirection: "row" }}>
                      <View
                        style={{
                          marginBottom: lastMarginBottom,
                          flex: 1
                        }}
                        key={item.name}
                      >
                        <AnimatedTextInput
                          emptyText={t("请填写验证码")}
                          onFocus={_onFocus("clickForm")}
                          secureTextEntry={isShowPwd}
                          onChangdedEyeIcon={_onChangdedEyeIcon}
                          onDeleteInputContent={() => _onDeleteInputContent(item)}
                          value={smsCode}
                          id={index + 1}
                          placeholder={item.placeHolder}
                          isShowSendButton={true}
                          timerCount={60}
                          onGetCodeClick={getSmsCode}
                          setValue={(value: string) => {
                            item.value = value;
                            setSmsCode(value);
                            setBtnDisabled(getBtnDisabled(fields));
                          }}
                        />
                      </View>
                    </View>
                  );
                case "emailCodeFiled":
                  return (
                    <View style={{ flexDirection: "row" }}>
                      <View
                        style={{
                          marginBottom: lastMarginBottom,
                          flex: 1
                        }}
                        key={item.name}
                      >
                        <AnimatedTextInput
                          emptyText={t("请填写验证码")}
                          onFocus={_onFocus("clickForm")}
                          secureTextEntry={isShowPwd}
                          onChangdedEyeIcon={_onChangdedEyeIcon}
                          onDeleteInputContent={() => _onDeleteInputContent(item)}
                          value={smsCode}
                          id={index + 1}
                          placeholder={item.placeHolder}
                          isShowSendButton={true}
                          timerCount={60}
                          onGetCodeClick={getEmailCode}
                          setValue={(value: string) => {
                            item.value = value;
                            setSmsCode(value);
                            setBtnDisabled(getBtnDisabled(fields));
                          }}
                        />
                      </View>
                    </View>
                  );
              }
            })}
          </View>
          <Email isShow={showEmailTip} value={account} />
        </ShadowContainer>
      </View>

      {faqUrl ? (
        <Text style={styles.forgotPasswd} onPress={forgot}>
          {t("忘记账号或密码")}
        </Text>
      ) : null}

      <TouchableOpacity style={styles.touchCheck} activeOpacity={1} onPress={read}>
        <Image
          source={checked ? require("../img/radio_checked.webp") : require("../img/radio_unchecked.webp")}
          style={{ width: 16, height: 16, marginRight: 8 }}
        />
        <Text style={styles.hasRead}>
          {t("我已阅读并同意") + " "}
          <Text style={styles.textAuthProtocol} onPress={agree}>
            {t("个人信息授权协议")}
          </Text>
        </Text>
      </TouchableOpacity>
      {!!tip && (
        <View style={styles.tipContainer}>
          <Image style={styles.tipIcon} source={tipIcon || require("")} />
          <Text style={styles.tipText} onPress={onClickTip}>
            {tip}
          </Text>
        </View>
      )}
      <AKButton
        disabled={btnDisabled}
        onPress={_onSubmit}
        text={btnText}
        type={AKButtonType.B1_1_1}
        style={{
          marginTop: 32,
          width: "100%"
        }}
      />
    </View>
  );
}
