import { FontStyles, FontUtil, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";
import { p2d } from "common/util";
export default StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    alignItems: "center"
  },
  headerIcon: {
    width: 73,
    height: 73,
    marginBottom: 12
  },
  info: {
    fontSize: 12,
    color: "#999999",
    textAlign: "center",
    marginBottom: 24
  },
  tipContainer: {
    flexDirection: "row",
    width: "100%",
    marginTop: 16,
    alignItems: "center"
  },
  tipIcon: {
    height: 16,
    width: 16,
    marginRight: 4
  },
  tipText: {
    color: "#666666",
    fontSize: 12
  },
  btnContainer: {
    height: 40,
    paddingVertical: 10.5,
    backgroundColor: "#E62117",
    borderRadius: 4,
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 32
  },
  btnText: {
    fontSize: 16,
    color: "#FFF",
    ...FontStyles["rob-medium"]
  },
  header: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center"
  },
  bannerImg: {
    height: WINDOW_WIDTH * 0.739,
    width: WINDOW_WIDTH,
    position: "absolute"
  },
  logoImg: {
    marginTop: p2d(-38),
    height: p2d(60),
    width: p2d(60)
  },
  rightBtn: {
    marginBottom: 8,
    position: "absolute",
    right: 0,
    bottom: 0,
    backgroundColor: "#E62117",
    borderRadius: 4,
    height: 24,
    paddingHorizontal: 4,
    minWidth: 70,
    justifyContent: "center",
    alignItems: "center"
  },
  rightBtnDisable: {
    backgroundColor: "#F2908B"
  },
  rightBtnText: {
    color: "#FFF",
    fontSize: 10
  },
  rightImage: {
    flexDirection: "row",
    marginBottom: 8,
    position: "absolute",
    right: 0,
    bottom: 0,
    alignItems: "center"
  },
  imageCodeRefresh: {
    height: 16,
    width: 16
  },
  imageCode: {
    // backgroundColor: "red",
    height: 32,
    width: 64,
    marginRight: 12
  },
  accountErrorIcon: {
    height: 12,
    width: 12,
    marginRight: 4
  },
  accountErrorText: {
    color: "#E62117",
    fontSize: 11
  },
  smsUI: {
    width: "100%",
    flexDirection: "row",
    alignItems: "flex-end"
  },
  forgotPasswd: {
    fontSize: 14,
    color: "#6E737D",
    marginTop: 16,
    alignSelf: "flex-end",
    marginRight: 16
  },
  touchCheck: {
    alignSelf: "flex-start",
    marginTop: 40,
    flexDirection: "row"
  },
  hasRead: {
    width: WINDOW_WIDTH - 56,
    alignSelf: "center",
    fontSize: 12,
    color: "#6E737D"
  },
  textAuthProtocol: {
    fontSize: 12,
    color: "#43474C",
    textDecorationLine: "underline"
  },
  shadow: {
    width: WINDOW_WIDTH - 32,
    alignSelf: "center",
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 12,
    backgroundColor: "#ffffff",
    paddingTop: 22,
    paddingBottom: 22
  }
});
