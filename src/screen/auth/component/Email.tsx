import React, { Component } from "react";
import { View, Text, DeviceEventEmitter, Image, TouchableOpacity, StyleSheet } from "react-native";
import { FontStyles, NAV_BAR_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { Shadow } from "@akulaku-rn/akui-rn/src/components/NeomorphShadows";
import { observer } from "mobx-react";

type Props = {
  isShow: boolean; //是否展示自身
  value: string; //需要展示的文案
};

type State = {};

const emailList = [
  {
    mailSuffix: "gmail.com",
    icon: require("common/images/credit_input_email_ico_gmail.webp")
  },
  {
    mailSuffix: "yahoo.com",
    icon: require("common/images/credit_input_email_ico_yahoo.webp")
  }
];

@observer
export default class Email extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
  }

  returnEmail = () => {
    const { value } = this.props;
    if (!value) return null;
    return value;
  };

  render() {
    const { isShow } = this.props;
    if (!isShow) return null;
    const email = this.returnEmail();
    return (
      <View style={[styles.container, { top: 74 }]}>
        <Shadow width={WINDOW_WIDTH - 64} height={96} style={styles.containerView}>
          {emailList.map((e, i) => (
            <TouchableOpacity
              key={i}
              style={[
                styles.item,
                i !== 0 && {
                  borderColor: "#E6E6E6",
                  borderTopWidth: 0.5
                }
              ]}
              onPress={() => {
                DeviceEventEmitter.emit("setEmail", e.mailSuffix);
              }}
            >
              <Text style={styles.itemTitle}>
                {email}
                <Text style={styles.boldTitle}>{e.mailSuffix}</Text>
              </Text>
              <Image style={styles.icon} source={e.icon} />
            </TouchableOpacity>
          ))}
        </Shadow>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    zIndex: 1,
    overflow: "visible",
    alignSelf: "center"
  },
  containerView: {
    width: WINDOW_WIDTH - 64,
    height: 96,
    borderRadius: 8,
    backgroundColor: "#fff",
    shadowOffset: { width: 0, height: 2 },
    shadowColor: "rgb(0,0,0)",
    shadowRadius: 8,
    shadowOpacity: 0.08
  },
  item: {
    width: WINDOW_WIDTH - 76,
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center",
    height: 48,
    paddingLeft: 8,
    paddingRight: 8,
    justifyContent: "space-between"
  },
  itemTitle: {
    fontSize: 14,
    color: "#B3B3B3"
  },
  icon: {
    width: 24,
    height: 24
  },
  boldTitle: {
    color: "#333",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  shadowImg: {
    width: WINDOW_WIDTH - 28,
    height: (WINDOW_WIDTH - 28) / 3.12,
    position: "absolute",
    zIndex: -1,
    left: -8
  }
});
