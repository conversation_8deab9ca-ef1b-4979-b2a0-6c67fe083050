/**
 * Create by z<PERSON><PERSON><PERSON> on 2021-02 10:19
 * @description
 */

import React, { useState } from "react";
import { View, StyleSheet, LayoutChangeEvent } from "react-native";
import { Shadow } from "@akulaku-rn/akui-rn/src/components/NeomorphShadows";

type Props = {
  children: undefined | any;
};

export default function ShadowContainer({ children }: Props) {
  const [height, setHeight] = useState(0);
  const [width, setWidth] = useState(0);
  const styles = {
    shadow: {
      shadowOffset: {
        height: 2,
        width: 0
      },
      borderRadius: 20,
      shadowColor: "rgb(0,0,0)",
      shadowRadius: 6,
      backgroundColor: "#00000000",
      width: width,
      height: height,
      shadowOpacity: 0.08
    }
  };

  const _onLayout = (e: LayoutChangeEvent) => {
    if (e.nativeEvent.layout.width !== width) {
      setWidth(e.nativeEvent.layout.width);
    }
    if (e.nativeEvent.layout.height !== height) {
      setHeight(e.nativeEvent.layout.height);
    }
  };

  if (width === 0 || height === 0) {
    return <View onLayout={_onLayout}>{children}</View>;
  }

  return (
    <Shadow height={height} width={width} style={styles.shadow}>
      <View onLayout={_onLayout}>{children}</View>
    </Shadow>
  );
}
