/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-10 10:58
 * @description
 */

import React from "react";
import { View, StyleSheet, Image, Text, ScrollView } from "react-native";
import { toJS } from "mobx";
import { WINDOW_HEIGHT, NAV_BAR_HEIGHT, NavigationBar, WINDOW_WIDTH, NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import AuthPageComponent from "./AuthPageComponent";
import AuthPageStore from "../store";
import { observer } from "mobx-react";
import { TFunction } from "i18next";

const styles = StyleSheet.create({
  viewTip: {
    width: WINDOW_WIDTH,
    height: 40,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF5EC",
    paddingLeft: 16,
    paddingRight: 16
  },
  textTip: {
    color: "#fe9333",
    fontSize: 12,
    lineHeight: 14,
    marginLeft: 8
  }
});

type Props = {
  store: AuthPageStore;
  t: TFunction;
  getFields: () => void;
  onBackPress: () => void;
  login: () => void;
  onClickTip: () => void;
  nullText?: string;
};
// TODO电商提额授权页
export default observer(function PageComponent(props: Props) {
  const {
    store: {
      protocolDesc,
      backgroundImg,
      title,
      topTip,
      desc,
      icon,
      fields,
      loadFailed,
      loading,
      firstLoading,
      graphicCodeData,
      getGraphicCode,
      getEmailCode,
      sendSmsCode,
      sendSmsCodeV2,
      sendEmailCode,
      accountInfoError,
      emailCodeError,
      imageCodeError,
      smsCodeError,
      validErrorText,
      changeError,
      faqUrl,
      protocolUrl
    },
    t,
    getFields,
    onBackPress,
    login,
    onClickTip,
    nullText
  } = props;
  if (loadFailed) {
    return (
      <View
        style={{
          height: WINDOW_HEIGHT - NAV_BAR_HEIGHT,
          width: "100%",
          justifyContent: "center"
        }}
      >
        <NetworkErrorComponent
          message={t("global:啊，网络故障导致我们的距离好远啊!")}
          onRetryPress={() => {
            getFields();
          }}
          buttonText={t("global:刷新")}
        />
      </View>
    );
  }
  if (loading) return null;
  return (
    <View style={{ height: "100%", width: "100%" }}>
      <NavigationBar onBackPress={onBackPress} title={title} />
      <ScrollView>
        <View style={styles.viewTip}>
          <Image
            style={{
              width: 12,
              height: 12
            }}
            source={require("../img/icon_info_taxcard.webp")}
          />
          <Text style={styles.textTip}>{topTip}</Text>
        </View>
        <AuthPageComponent
          changeError={changeError}
          emailCodeError={emailCodeError}
          imageCodeError={imageCodeError}
          smsCodeError={smsCodeError}
          validErrorText={validErrorText}
          initSMSBtnText={t("发送")}
          accountInfoErrorText={t("账号或密码错误")}
          accountInfoError={accountInfoError}
          imageCode={graphicCodeData}
          getEmailCode={sendEmailCode}
          getImageCode={getGraphicCode}
          getSmsCode={sendSmsCodeV2}
          tipIcon={require("../img/DJPonline_ico.webp")}
          info={desc}
          btnText={t("提交")}
          onSubmit={login}
          fields={fields}
          icon={toJS(icon) || ""}
          backgroundImg={toJS(backgroundImg) || ""}
          onClickTip={onClickTip}
          tip={protocolDesc}
          faqUrl={faqUrl}
          protocolUrl={protocolUrl}
          t={t}
          nullText={nullText}
        />
        <View
          style={{
            height: 20
          }}
        />
      </ScrollView>
    </View>
  );
});
