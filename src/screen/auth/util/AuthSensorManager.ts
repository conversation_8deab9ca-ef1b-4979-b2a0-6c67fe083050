/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-09 16:21
 * @description
 */
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { NativeNavigationModule } from "common/nativeModules";
import _, { get } from "lodash";
import { reportClick, reporter } from "@akulaku-rn/rn-v4-sdk";

let PAGE_BASE_PARAMS = { page_id: "", page_name: "" };

let EXTRA_BASE_PARAMS = {};

export const setBaseParmas = ({ page_id, page_name, ...otherExtra }: { [key: string]: any } = {}) => {
  PAGE_BASE_PARAMS = { page_id: "915", page_name: "quota submission page" };
  EXTRA_BASE_PARAMS = otherExtra;
};

export const getPageBaseParams = (): { page_id: string; page_name: string } => ({ ...PAGE_BASE_PARAMS });

export const getExtraBaseParams = (): any => ({ ...EXTRA_BASE_PARAMS });

export default {
  sc: {
    adExpose() {
      NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS,
          pop_id: "pop30074",
          pop_name: "System maintenance"
        }
      });
    },
    adClick() {
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        element_id: "pop3007401",
        element_name: "got it",
        position_id: "01",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS,
          pop_id: "pop30074",
          pop_name: "System maintenance"
        }
      });
    },
    clickForgetAccount() {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9150105",
        element_name: "forget",
        module_id: "01",
        module_name: "quota",
        position_id: "05",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS
        }
      });
    },
    checkProtocol() {
      // NativeSensorModule.sensorLogger(SensorType.CLICK, {
      //   element_id: "8890106",
      //   element_name: "check aggreement",
      //   module_id: "01",
      //   module_name: "authorize account",
      //   position_id: "06",
      //   ...PAGE_BASE_PARAMS,
      //   extra: {
      //     ...EXTRA_BASE_PARAMS
      //   }
      // });
    },
    clickProtocol() {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9150106",
        element_name: "agreement",
        module_id: "01",
        module_name: "quota",
        position_id: "06",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS
        }
      });
    },
    clickBack() {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9150101",
        element_name: "return",
        module_id: "01",
        module_name: "quota",
        position_id: "01",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS
        }
      });
    },
    clickBackOnResultPage(pageStatus: string) {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "********",
        element_name: "return",
        module_id: "01",
        module_name: "quota",
        position_id: "01",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS,
          Aku_PageStatus: pageStatus
        }
      });
    },
    clickGotIt() {
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        element_id: "pop3007301",
        element_name: "got it",
        position_id: "01",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS,
          pop_id: "pop30073",
          pop_name: "Error prompt"
        }
      });
    },
    exposeErrorDialog() {
      NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS,
          pop_id: "pop30073",
          pop_name: "Error prompt"
        }
      });
    },
    clickReturnCenter(pageStatus: string) {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "10000102",
        element_name: "return center",
        module_id: "01",
        module_name: "quota",
        position_id: "02",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS,
          Aku_PageStatus: pageStatus
        }
      });
    },
    clickForm(filedData: { [key: string]: any } = {}) {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9150102",
        element_name: "field",
        module_id: "01",
        module_name: "quota",
        position_id: "02",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS,
          ...filedData
        }
      });
    },
    clickFormSubmit() {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9150103",
        element_name: "submit",
        module_id: "01",
        module_name: "quota",
        position_id: "03",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS
        }
      });
    },
    clickFormSend() {
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9150104",
        element_name: "Send",
        module_id: "01",
        module_name: "quota",
        position_id: "04",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS
        }
      });
    },
    imageDialogExpose() {
      NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS
        }
      });
    },
    clickImageDialogInput() {
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        ...PAGE_BASE_PARAMS,
        element_id: "pop3003601",
        element_name: "input",
        position_id: "01"
      });
    },
    clickImageDialogNext() {
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        ...PAGE_BASE_PARAMS,
        element_id: "pop3003602",
        element_name: "next",
        position_id: "02"
      });
    },
    clickImageDialogCancel() {
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        ...PAGE_BASE_PARAMS,
        element_id: "pop3003603",
        element_name: "cancel",
        position_id: "03"
      });
    },

    smsDialogExpose() {
      NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
        ...PAGE_BASE_PARAMS
      });
    },
    clickSmsDialogSend() {
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        ...PAGE_BASE_PARAMS,
        element_id: "pop3003701",
        element_name: "send",
        position_id: "01"
      });
    },
    clickSmsDialogInput() {
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        ...PAGE_BASE_PARAMS,
        element_id: "pop3003702",
        element_name: "input",
        position_id: "02"
      });
    },
    clickSmsDialogNext() {
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        ...PAGE_BASE_PARAMS,
        element_id: "pop3003703",
        element_name: "submit",
        position_id: "03"
      });
    },
    clickSmsDialogCancel() {
      NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
        ...PAGE_BASE_PARAMS,
        element_id: "pop3003704",
        element_name: "close",
        position_id: "04"
      });
    },
    clickFormImageCode() {
      // 点击图形验证码
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9150201",
        element_name: "captcha",
        module_id: "02",
        module_name: "verification",
        position_id: "01",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS
        }
      });
    },
    clickFormSmsCode() {
      // 点击短信验证码
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9150202",
        element_name: "SMS code",
        module_id: "02",
        module_name: "verification",
        position_id: "02",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS
        }
      });
    },
    clickFormEmailCode() {
      // 点击邮箱验证码
      NativeSensorModule.sensorLogger(SensorType.CLICK, {
        element_id: "9150104",
        element_name: "E-mail code",
        module_id: "01",
        module_name: "verification",
        position_id: "04",
        ...PAGE_BASE_PARAMS,
        extra: {
          ...EXTRA_BASE_PARAMS
        }
      });
    }
  },
  v4: {
    showSysTemErrorDialog() {
      reporter.enterScreen(this.getErrorScreenParams());
    },
    dismissSysTemErrorDialog() {
      reporter.leaveScreen(this.getErrorScreenParams());
    },
    getErrorScreenParams() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100636,
        sp: { Aku_buttonId, jumpSource }
      };
    },
    clickIKnow() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100636,
        cn: 1,
        sp: { Aku_buttonId, jumpSource }
        // ext: getExtraBaseParams()
      });
    },
    getResultScreenParams() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100635,
        sp: { Aku_buttonId, jumpSource }
      };
    },
    clickBack() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100635,
        cn: 1,
        sp: { Aku_buttonId, jumpSource }
        // ext: getExtraBaseParams()
      });
    },
    clickForgetAccount() {
      const { Aku_buttonId, jumpSource, path } = getExtraBaseParams();
      reportClick({
        sn: 100633,
        cn: 18,
        sp: { Aku_buttonId, jumpSource, path }
        // ext: getExtraBaseParams()
      });
    },
    clickPersonProtocol() {
      const { Aku_buttonId, jumpSource, path } = getExtraBaseParams();
      reportClick({
        sn: 100633,
        cn: 19,
        sp: { Aku_buttonId, jumpSource, path }
        // ext: getExtraBaseParams()
      });
    },
    getScreenParams() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100633,
        sp: { Aku_buttonId, jumpSource }
      };
    },
    exposeErrorDialog() {
      const { Aku_buttonId, source, jumpSource } = getExtraBaseParams();
      const v4data = reporter.generateExposeEvent([
        {
          sn: 100633,
          cn: 20,
          sp: { Aku_buttonId, jumpSource }
        }
      ]);
      reporter.expose(v4data);
    },
    clickGotIt() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100633,
        cn: 21,
        sp: { Aku_buttonId, jumpSource }
      });
    },
    clickV4FormItem() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100633,
        cn: 1,
        sp: { Aku_buttonId, jumpSource },
        ext: getExtraBaseParams()
      });
    },
    clickV4FormSubmit() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100633,
        cn: 2,
        sp: { Aku_buttonId, jumpSource }
      });
    },
    clickImageDialogNext() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100633,
        cn: 7,
        sp: { Aku_buttonId, jumpSource }
      });
    },

    clickImageDialogClose() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100633,
        cn: 8,
        sp: { Aku_buttonId, jumpSource }
      });
    },

    clickSMSDialogClose() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100633,
        cn: 12,
        sp: { Aku_buttonId, jumpSource }
      });
    },

    clickSMSDialogNext() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({
        sn: 100633,
        cn: 11,
        sp: { Aku_buttonId, jumpSource }
      });
    },

    clickGetSmsCode() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({ sn: 100633, cn: 9, sp: { Aku_buttonId, jumpSource } });
    },

    clickClose(data: { sn: number; cn: number; ext?: any; [key: string]: any }) {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      reportClick({ ...data, sp: { Aku_buttonId, jumpSource } });
    },

    getImageCodeInputData() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100633,
        cn: 3,
        sp: { Aku_buttonId, jumpSource }
      };
    },
    getDialogImageCodeInputData() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100633,
        cn: 6,
        sp: { Aku_buttonId, jumpSource }
      };
    },
    getSMSCodeInputData() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100633,
        cn: 4,
        sp: { Aku_buttonId, jumpSource }
      };
    },
    getDialogSMSCodeInputData() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100633,
        cn: 10,
        sp: { Aku_buttonId, jumpSource }
      };
    },
    getEmailCodeInputData() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100633,
        cn: 5,
        sp: { Aku_buttonId, jumpSource }
      };
    },
    getDialogEmailCodeInputData() {
      const { Aku_buttonId, jumpSource } = getExtraBaseParams();
      return {
        sn: 100633,
        cn: 10,
        sp: { Aku_buttonId, jumpSource },
        ext: getExtraBaseParams()
      };
    }
  }
};
