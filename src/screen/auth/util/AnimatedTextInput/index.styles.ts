import { FontStyles } from "@akulaku-rn/akui-rn";
import { StyleSheet, Platform } from "react-native";
export default StyleSheet.create({
  container: {
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    paddingTop: 23,
    paddingBottom: 11
  },
  textInput: {
    height: 16,
    padding: 0,
    fontSize: 14,
    textAlign: "left",
    textAlignVertical: "center",
    color: "#333",
    ...Platform.select({
      ios: { lineHeight: 16 }
    })
  },
  errorView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 3,
    marginBottom: -14.5
  },
  errorText: {
    height: 11,
    fontSize: 10,
    lineHeight: 11,
    color: "#E62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginRight: 4
  },
  sendView: {
    width: 60,
    height: 24,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center"
  },
  sendText: {
    fontSize: 10,
    color: "#FFF",
    ...FontStyles["roboto-bold"]
  }
});
