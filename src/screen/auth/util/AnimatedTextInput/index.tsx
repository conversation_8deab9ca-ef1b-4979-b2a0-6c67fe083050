import React, { PureComponent } from "react";
import {
  View,
  Text,
  TextInput,
  Animated,
  DeviceEventEmitter,
  Image,
  findNodeHandle,
  UIManager,
  EmitterSubscription,
  TouchableOpacity
} from "react-native";
import NativeConfigModule from "common/nativeModules/basics/nativeConfigModule";
import styles from "./index.styles";

type Props = {
  setValue: (value: string, id?: number) => void;
  savePageY?: (pageY: number) => void;
  textFormat?: (value: string) => string;
  onFocus?: (id?: number) => void;
  value: string;
  placeholder: string;
  pattern?: any;
  id?: number;
  containerStyle?: object;
  nullId?: number | null;
  keyboardType?: any;
  editable?: boolean;
  listenerName?: string;
  mergeListenerValue?: boolean;
  errorText?: string;
  nullText?: string;
  secureTextEntry?: boolean; // 控制文本显示还是隐藏打点（密码类场景）
  textInputComponent?: any;
  showErrorText?: boolean;
  emptyText: string; // 默认提示文案
  isShowColoseIcon?: boolean; // 是否显示一键清除按钮
  isShowEyeIcon?: boolean; //是否显示眼睛
  isGraphicCode?: boolean; // 是否图像验证码
  isShowSendButton?: boolean; // 是否显示发送按钮
  timerCount?: number; // 倒计时秒数
  onGetCodeClick?: () => void; // 获取验证码
  onChangdedEyeIcon?: () => void; // 改变输入框的显示和隐藏
  onDeleteInputContent?: () => void; // 清除输入框中的内容
};

type States = {
  value: string;
  color: string;
  isfocus: boolean;
  error: boolean;
  caretHidden: boolean;
  empty: boolean;
  timerTitle: string; // 发送按钮文案
  sendState: boolean; // 是否发送中
  timerCountNumber: number; // 倒计时开始秒数
};

export default class Index extends PureComponent<Props, States> {
  textInputRef: any;

  top: Animated.Value;

  fontSize: Animated.Value;

  keyboardDidHideListener?: EmitterSubscription;

  listener?: EmitterSubscription;

  timer?: any;

  interval?: number;

  static defaultProps = {
    editable: true,
    nullId: null,
    keyboardType: "default"
  };

  constructor(props: Props) {
    super(props);
    this.state = {
      value: props.value,
      color: props.value ? "#999" : "#B3B3B3",
      isfocus: false,
      error: false,
      empty: false,
      caretHidden: false,
      timerTitle: "获取验证码",
      timerCountNumber: props.timerCount ?? 0,
      sendState: false
    };
    this.top = new Animated.Value(props.value ? 0 : 23);
    this.fontSize = new Animated.Value(props.value ? 12 : 14);
  }

  // 发送获取验证码按钮倒计时
  countDownAction = () => {
    const { timerCount, onGetCodeClick } = this.props;
    if (!this.state.sendState) {
      onGetCodeClick && onGetCodeClick();
      this.interval = setInterval(() => {
        const timer = this.state.timerCountNumber - 1;
        if (timer <= 0) {
          this.interval && clearInterval(this.interval);
          this.setState({
            timerCountNumber: timerCount ?? 0,
            timerTitle: "获取验证码",
            sendState: false
          });
        } else {
          this.setState({
            timerCountNumber: timer,
            timerTitle: timer + "s",
            sendState: true
          });
        }
      }, 1000);
    }
  };

  async componentDidMount() {
    const { setValue, id, listenerName, mergeListenerValue } = this.props;
    if (listenerName) {
      this.listener = DeviceEventEmitter.addListener(listenerName, value => {
        const newText = mergeListenerValue ? this.state.value + value : value;
        this.setState({ value: newText });
        !!id ? setValue(newText, id) : setValue(newText);
      });
    }
    // this.keyboardDidHideListener = Keyboard.addListener("keyboardDidHide", this.keyboardDidHide);
    const {
      data: { deviceBrand, osVersionCode }
    } = await NativeConfigModule.getEnvInfoSilent();
    this.setState({
      caretHidden: deviceBrand === "Xiaomi" && osVersionCode === 29
    });
  }

  UNSAFE_componentWillReceiveProps(nextProps: any) {
    this.setState(
      {
        value: nextProps.value
      },
      () => {
        this.onFocusAnimation();
        this.onEndEditing();
      }
    );
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    this.interval && clearInterval(this.interval);
    this.listener && this.listener.remove();
    this.keyboardDidHideListener && this.keyboardDidHideListener.remove();
  }

  keyboardDidHide = () => {
    this.textInputRef && this.textInputRef.blur();
  };

  onFocus = () => {
    const { onFocus, id } = this.props;
    !!onFocus && onFocus(id);
    this.setState({ isfocus: true }, () => {
      this.onFocusAnimation();
    });
  };

  onFocusAnimation = () => {
    const { value, isfocus } = this.state;
    if (isfocus || (!!value && !!value.length)) {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 12,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#999"
      });
    }
  };

  onBlur = () => {
    this.setState({ isfocus: false }, () => {
      this.onEndEditing();
      const { textFormat } = this.props;
      let text = this.state.value;
      if (!!textFormat) {
        text = textFormat(text);
      }
      const empty = !(text.length > 0);
      this.setState({ empty });
    });
    if (this.state.value && !!this.state.value.length) {
      return;
    } else {
      Animated.parallel([
        Animated.timing(this.top, {
          toValue: 23,
          duration: 200,
          useNativeDriver: false
        }),
        Animated.timing(this.fontSize, {
          toValue: 14,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
      this.setState({
        color: "#B3B3B3"
      });
    }
  };

  onChangeText = (value: string) => {
    const { setValue, id } = this.props;
    this.setState({ value, error: false });
    !!id ? setValue(value, id) : setValue(value);
  };

  onEndEditing = () => {
    if (this.state.isfocus) {
      return;
    }
    const { pattern, textFormat } = this.props;
    let text = this.state.value;
    if (!!textFormat) {
      text = textFormat(text);
    }
    let error = false;
    if (!!pattern && !!text) {
      const reg = new RegExp(pattern);
      error = !reg.test(text);
    }
    if (this.props.showErrorText !== undefined) error = this.props.showErrorText;
    this.setState({ error });
  };

  clickText = () => {
    if (!this.props.editable) return;
    this.textInputRef.focus();
  };

  onLayout = () => {
    const { savePageY } = this.props;
    if (!savePageY) return;
    const handle = findNodeHandle(this.textInputRef);
    if (handle) {
      this.timer = setInterval(() => {
        UIManager.measure(handle, (_x, _y, _width, _height, _pageX, pageY) => {
          savePageY && savePageY(pageY);
        });
      }, 500);
    }
  };

  showError = () => {
    const { error, value, empty } = this.state;
    const { id, nullId, errorText, nullText } = this.props;
    if (empty) {
      return true;
    }
    if (!errorText && !nullText) {
      return false;
    }
    if (error) {
      return true;
    }
    if (nullId === id && !value) {
      return true;
    }
    return false;
  };

  renderTextInput = () => {
    const { textInputComponent: TextInputComponent, editable, keyboardType } = this.props;
    const { value, caretHidden } = this.state;
    const refFunc = (textInputRef: TextInput | null) => {
      this.textInputRef = textInputRef;
    };
    const props = {
      ref: refFunc,
      editable: editable,
      selectionColor: "#e62117",
      numberOfLines: 1,
      style: styles.textInput,
      maxLength: 100,
      underlineColorAndroid: "transparent",
      onChangeText: this.onChangeText,
      onFocus: this.onFocus,
      onBlur: this.onBlur,
      value: value,
      hitSlop: {
        top: 10,
        bottom: 10
      },
      keyboardType: keyboardType,
      secureTextEntry: this.props.secureTextEntry,
      caretHidden
    };
    if (!!TextInputComponent) {
      return <TextInputComponent {...props} />;
    }
    return <TextInput {...props} />;
  };

  render() {
    const {
      placeholder,
      containerStyle,
      errorText,
      nullText,
      emptyText,
      isShowColoseIcon,
      isShowEyeIcon,
      secureTextEntry,
      isShowSendButton,
      isGraphicCode,
      onChangdedEyeIcon,
      onDeleteInputContent
    } = this.props;
    const { isfocus, error, empty, timerTitle, sendState } = this.state;
    const showError = this.showError();
    console.log("zhangxiao", "showError-->" + showError);
    console.log("zhangxiao", "empty-->" + empty);
    const sendTextColor = sendState ? "#F99794" : "#F32823";
    return (
      <View style={containerStyle} onLayout={this.onLayout}>
        <View
          style={[
            styles.container,
            isfocus && { borderBottomColor: "#666" },
            showError && { borderBottomColor: "#e62117" }
          ]}
        >
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <View style={{ flex: 1 }}>{this.renderTextInput()}</View>
            {isShowColoseIcon ? (
              <TouchableOpacity onPress={onDeleteInputContent}>
                <Image
                  style={{
                    width: 16,
                    height: 16
                  }}
                  source={require("../../img/icon_delete.webp")}
                />
              </TouchableOpacity>
            ) : null}
            {isShowEyeIcon ? (
              <TouchableOpacity onPress={onChangdedEyeIcon}>
                <Image
                  style={{
                    width: 16,
                    height: 16,
                    marginLeft: 8
                  }}
                  source={
                    secureTextEntry ? require("../../img/icon_hide.webp") : require("../../img/icon_visible.webp")
                  }
                />
              </TouchableOpacity>
            ) : null}
            {isShowSendButton ? (
              <TouchableOpacity onPress={() => this.countDownAction()}>
                <View
                  style={[
                    styles.sendView,
                    {
                      backgroundColor: sendTextColor
                    }
                  ]}
                >
                  <Text style={styles.sendText}>{timerTitle}</Text>
                </View>
              </TouchableOpacity>
            ) : null}
            {isGraphicCode ? (
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Image
                  style={{ width: 56, height: 28 }}
                  source={{
                    uri:
                      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADMAAAAzCAYAAAA6oTAqAAAAEXRFWHRTb2Z0d2FyZQBwbmdjcnVzaEB1SfMAAABQSURBVGje7dSxCQBACARB+2/ab8BEeQNhFi6WSYzYLYudDQYGBgYGBgYGBgYGBgYGBgZmcvDqYGBgmhivGQYGBgYGBgYGBgYGBgYGBgbmQw+P/eMrC5UTVAAAAABJRU5ErkJggg=="
                    // uri: `data:image/png;base64,`
                  }}
                />
                <TouchableOpacity onPress={() => {}}>
                  <Image style={{ width: 16, height: 16, marginLeft: 4 }} source={require("./img/ic_retry.webp")} />
                </TouchableOpacity>
              </View>
            ) : null}
          </View>
          <Animated.View
            style={{
              position: "absolute",
              zIndex: 1,
              top: this.top
            }}
          >
            <Animated.Text
              onPress={this.clickText}
              style={{ fontSize: this.fontSize, lineHeight: 16, color: this.state.color }}
            >
              {placeholder}
            </Animated.Text>
          </Animated.View>
        </View>
        {showError && (
          <View style={styles.errorView}>
            <Image style={styles.errorIcon} source={require("./img/error_icon.webp")} />
            <Text style={styles.errorText}>{empty ? emptyText : error ? errorText : nullText}</Text>
          </View>
        )}
      </View>
    );
  }
}
