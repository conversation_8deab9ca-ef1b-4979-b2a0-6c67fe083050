/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-09 16:58
 * @description
 */
import { Fields } from "../types";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import i18next, { TFunction } from "i18next";

export const checkIfValid = (fields: Array<Fields>, t: TFunction) => {
  const flag = true;

  // fields.forEach((item: Fields)  => {
  //   switch (item.inputMethod) {
  //     case "textFiled": // 电话号码或者电商账号
  //       if (item.id === "mobile") {
  //         this.needPhoneNumber = true;
  //         !item.value && (NativeToast.showMessage(t("请填写社保网站登录邮箱"));
  //       } else {
  //         this.needAccount = true;
  //       }
  //       break;
  //     case "passwordFiled":
  //       this.needPassword = true;
  //       //TODO:
  //       break;
  //     case "validCodeFiled":
  //       this.needGraphicCode = true;
  //       break;
  //     case "smsCodeFiled":
  //       this.needSmsCode = true;
  //
  //     default:
  //       break;
  //   }
  // });
  //
  // if (needAccount && !userId) {
  //   Toast.show(t("请填写社保网站登录邮箱"), { position: Toast.positions.CENTER });
  //   return false;
  // }
  //
  // if (needAccount && !/^[a-zA-Z0-9\_\-\.]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/g.test(userId as string)) {
  //   Toast.show(t("邮箱格式有误，请重新输入"), { position: Toast.positions.CENTER });
  //   return false;
  // }
  //
  // if (needPassword && !password) {
  //   Toast.show(t("请填写登陆密码"), { position: Toast.positions.CENTER });
  //   return false;
  // }
  //
  // if (needSmsCode && !smsCode) {
  //   Toast.show(t("请输入验证码"), { position: Toast.positions.CENTER });
  //   return false;
  // }

  // if (needGraphicCode && !graphicCode) {
  //   Toast.show(t("请输入图形验证码"), { position: Toast.positions.CENTER });
  //   return false;
  // }

  return flag;
};
