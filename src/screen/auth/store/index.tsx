/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-09 21:36
 * @description
 */
import Basic from "common/store/Basic";
import { action, observable } from "mobx";
import { Loading } from "@akulaku-rn/akui-rn";
import * as API from "../api";
import { Fields, LoginFieldsResponse, ProtocolImages } from "../types";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
// @ts-ignore
import JSEncrypt from "jsencrypt";
import { publicKey } from "../util/publicKey";
import { EMAIL_CAPTCHA, INCREASE_LIMIT, INCREASE_PAGE_INFO, QUERY_INCREASE_STATUS } from "../api";
import i18next, { TFunction } from "i18next";
import { CountryID } from "common/components/BaseContainer/Type";
import ImageVerifyDialog from "common/components/ImageVerifyDialog";
import React from "react";
import SMSVerifyDialog from "common/components/SMSVerify/SMSVerifyDialog";
import SMSVerify from "common/components/SMSVerify/SMSVerify";
import IncreaseLimitSMSInput from "common/components/SMSVerify/IncreaseLimitSMSInput";
import ImageVerify from "common/components/ImageVerifyDialog/ImageVerify";
import { DeviceEventEmitter, ImageSourcePropType, TextInput } from "react-native";
import AuthSensorManager from "../util/AuthSensorManager";
import DialogTopLimit from "../../increaseLimit/component/Dialog/DialogTopLimit";
import { isEqual } from "lodash";
import { NativeResponse } from "common/util/NativeModuleResHandler";
import { NetworkResponse } from "common/nativeModules/network/nativeNetworkModule";
import { ButtonSequence } from "@akulaku-rn/akui-rn/src/components/Dialog";
import AkConsole from "common/util/AkConsole/AkConsole";
import ErrorDialog from "../dialog";
import { DialogContainer } from "common/components/DialogContainer";
import { HocActionLogInput } from "@akulaku-rn/rn-v4-sdk";
import CountLoading from "../../loading";
import SMSDialog from "../dialog/SMSDialog";
import EmailDialog from "../dialog/EmailDialog";
import SMSVerificationDialog from "../dialog/SMSVerificationDialog";

const encrypt = new JSEncrypt();
encrypt.setPublicKey(publicKey);

const ACCOUNT_INFO_ERROR = "**************"; // 账号密码信息错误
const SMS_CODE_ERROR = "**************"; // 短信验证码错误
const IMAGE_CODE_ERROR = "**************"; // 图片验证码错误
const EMAIL_CODE_ERROR = "**************"; // 邮箱验证码错误

type Options = {
  url?: string;
  nextLoginExtParams?: object;
  onLoginSuccessCallback?: (success: boolean, errCode: string, errMsg: string) => void;
};

export default class AuthPageStore extends Basic {
  @observable info = "";

  @observable btnText = "";

  @observable fields: Array<Fields> = [];

  @observable image = "";

  @observable tip = "";

  @observable loadFailed = false;

  @observable needPhoneNumber = false;

  @observable needAccount = false;

  @observable needPassword = false;

  @observable needGraphicCode = false;

  @observable needSmsCode = false;

  @observable loading = true;

  @observable firstLoading = true;

  @observable backgroundImg = "";

  @observable title = "";

  @observable topTip = "";

  @observable desc = "";

  @observable protocolDesc = "";

  @observable protocolImages: Array<ProtocolImages> = [];

  @observable icon = "";

  @observable graphicCodeData = "";

  @observable accountInfoError = false;

  @observable smsCodeError = false;

  @observable imageCodeError = false;

  @observable emailCodeError = false;

  @observable validErrorText = "";

  @observable faqUrl = ""; //忘记密码url

  @observable protocolUrl = ""; //协议url

  @observable secondTimeVerification = ""; // 第二次校验的输入内容（例如pin码，验证码验证问题答案等）

  sessionId = "";

  formId = 0;

  path = "";

  jumpSource = 0;

  countryId: number = CountryID.ID;

  t?: TFunction;

  private showingDialog = false;

  setParams = ({ t, countryId }: { t?: TFunction; countryId: number }) => {
    this.t = t;
  };

  @action
  getsecondTimeVerificationInfo = (textInputValue: string) => {
    this.secondTimeVerification = textInputValue;
  };

  /**
   * 获取表单项
   */
  @action
  getFields = (
    countryId: number,
    { path, jumpSource, type, url }: { path?: string; jumpSource: number; type?: number; url: string }
  ) => {
    return new Promise(resolve => {
      if (this.firstLoading) this.firstLoading = false;
      path && (this.path = path);
      this.loadFailed = false;
      this.loading = true;
      Loading.show({ useBack: true });
      this.io.POST(
        url,
        {
          // countryId,
          // ...(!!type && { type }),
          ...(!!path && { path }),
          jumpSource
        },
        (response: NativeResponse<LoginFieldsResponse>) => {
          this.loadFailed = false;
          this.loading = false;
          Loading.dismiss();
          const { data, success, errCode, errMsg } = response;
          if (!data) {
            resolve(false);
            return;
          }
          !!data.backgroundImgUrl && (this.backgroundImg = data.backgroundImgUrl);
          this.title = data.title;
          this.topTip = data.topTip;
          // this.desc = data.desc;
          this.fields = data.fields;
          this.formId = data.formId;
          // this.icon = data.icon;
          this.sessionId = data.sessionId;
          this.faqUrl = data.faqUrl;
          this.protocolUrl = data.protocolUrl;
          this.jumpSource = jumpSource;
          // !!data.protocolDesc && (this.protocolDesc = data.protocolDesc);
          // !!data.protocolImages && (this.protocolImages = data.protocolImages);
          // this.graphicCodeData = data.validCodeData;
          this.makeFieldsToFlags();
          if (!!data.fields.some((item: Fields) => item.inputMethod === "validCodeFiled")) {
            this.getGraphicCode();
          }
          resolve(true);
        },
        (error: Error) => {
          this.loadFailed = true;
          this.loading = false;
          Loading.dismiss();
          AkConsole("Auth", "----------error-------", error);
          resolve(false);
        }
      );
    });
  };

  makeFieldsToFlags = () => {
    AkConsole("Auth", this.fields);
    this.fields.forEach((item: Fields) => {
      switch (item.inputMethod) {
        case "textFiled": // 电话号码或者电商账号
          if (item.id === "login_mobile") {
            this.needPhoneNumber = true;
          } else {
            this.needAccount = true;
          }
          break;
        case "passwordFiled":
          this.needPassword = true;
          //TODO:
          break;
        case "validCodeFiled":
          this.needGraphicCode = true;
          break;
        case "smsCodeFiled":
          this.needSmsCode = true;
          break;
        default:
          break;
      }
    });
  };

  /**
   * 授权登录（第一次授权和第二次授权都调用的此函数）
   */
  @action
  login = async (
    params: any,
    options: Options = {},
    isSecondRequest?: boolean
  ): Promise<{ success: boolean; [key: string]: any }> => {
    return new Promise(resolve => {
      CountLoading.show({ useBack: true });
      AuthSensorManager.v4.clickV4FormSubmit();
      // 第一次授权登录和第二次授权登录入参不同
      const requstParams = isSecondRequest
        ? {
            ...(!!this.path && { path: this.path }),
            formId: this.formId,
            sessionId: this.sessionId,
            ...params
          }
        : {
            ...(!!this.path && { path: this.path }),
            formId: this.formId,
            sessionId: this.sessionId,
            ...options.nextLoginExtParams,
            ...params
          };
      this.io.POST(
        options.url || API.LOGIN,
        requstParams,
        async (response: NetworkResponse) => {
          AkConsole("Auth", `response: `, response);
          const { success, errCode, errMsg, data = {} } = response;
          // 如果需要二次登录授权
          if (data && !!data.needNextLogin) {
            CountLoading.dismiss();
            this.showingDialog = true;
            this.handleVerifyCodeDialog(response.data, options);
            return;
          }

          if (!success) {
            CountLoading.dismiss();
            // NativeToast.showMessage(response.errMsg);
            this.handleLoginError(errCode, errMsg);
            DialogContainer.show({ renderContent: <ErrorDialog errorMsg={errMsg} /> });
            return;
          }
          // this.accountInfoError = !response.success && response.errCode === ACCOUNT_INFO_ERROR;

          if (this.showingDialog) {
            await DialogContainer.dismiss();
          }
          resolve(response);
        },
        (error: any) => {
          AkConsole("Auth", `error: `, error);
          CountLoading.dismiss();
          const { data, success, errCode, errMsg, message } = error;
          if (!success) {
            this.handleLoginError(errCode, errMsg || message);
          }

          resolve({
            success,
            errCode,
            errMsg
          });
        }
      );
    });
  };

  handleLoginError = (errCode: string, errMsg: string) => {
    this.accountInfoError = false;
    this.smsCodeError = false;
    this.imageCodeError = false;
    this.emailCodeError = false;
    this.validErrorText = errMsg;
    switch (errCode) {
      case ACCOUNT_INFO_ERROR:
        this.accountInfoError = true;
        break;
      case SMS_CODE_ERROR:
        this.smsCodeError = true;
        break;
      case IMAGE_CODE_ERROR:
        this.imageCodeError = true;
        break;
      case EMAIL_CODE_ERROR:
        this.emailCodeError = true;
        break;
      default:
        // NativeToast.showMessage(errMsg);
        break;
    }
  };

  transformDataByFields = (inputMethod?: string): { [key: string]: any } => {
    const res: any = {};
    let findItem = {};
    this.fields.forEach((item: Fields) => {
      switch (item.inputMethod) {
        case "textFiled": // 电话号码或者电商账号
          if (item.id === "login_mobile") {
            res.mobile = item.value;
          } else {
            res.account = item.value;
          }
          break;
        case "passwordFiled":
          // res.password = item.value;
          res.password = encrypt.encrypt(item.value);
          //TODO:
          break;
        case "validCodeFiled":
          res.imageCaptcha = item.value;
          break;
        case "smsCodeFiled":
          res.smsCaptcha = item.value;
          break;
        case "emailCodeFiled":
          res.emailCode = item.value;
          break;
        default:
          break;
      }
      item.inputMethod === inputMethod && (findItem = item);
    });
    if (!!inputMethod) {
      return findItem;
    }
    return res;
  };

  /**
   * 短信验证码
   */
  @action
  sendSmsCode = async (params?: any): Promise<{ success: boolean; [key: string]: any }> => {
    const item = this.transformDataByFields("textFiled");
    if (!item.value) {
      NativeToast.showMessage((this.t && this.t("请输入xxx信息", { xxx: item.name })) || "");
      return Promise.resolve({ success: false });
    }
    return new Promise(resolve => {
      this.io.POST(
        API.SMS_CAPTCHA,
        {
          path: this.path,
          formId: this.formId,
          countryId: this.countryId,
          sessionId: this.sessionId,
          mobile: item.value,
          ...params
        },
        (response: any) => {
          resolve(response);
        },
        (error: any) => {
          const { data, success, code, message } = error;

          resolve({
            success,
            errCode: code,
            errMsg: message
          });
        }
      );
    });
  };

  // 发送邮箱验证码
  @action
  sendEmailCode = async (): Promise<void> => {
    const item = this.transformDataByFields("textFiled");
    if (!item.value) {
      NativeToast.showMessage((this.t && this.t("请输入xxx信息", { xxx: item.name })) || "");
      return;
    }
    const paramData = {
      path: this.path,
      jumpSource: this.jumpSource,
      sessionId: this.sessionId,
      formId: this.formId,
      email: this.transformDataByFields("textFiled").value
    };
    let response;
    try {
      response = await this.io.post(API.EMAIL_CAPTCHA_V2, paramData);
      if (response.success) {
      } else {
        NativeToast.showMessage(response.errMsg);
      }
    } catch (e) {
    } finally {
    }
  };

  // 发送短信验证码
  @action
  sendSmsCodeV2 = async (loginParams?: any): Promise<void> => {
    const item = this.transformDataByFields("textFiled");
    if (!item.value) {
      NativeToast.showMessage((this.t && this.t("请输入xxx信息", { xxx: item.name })) || "");
      return;
    }
    let paramData;
    if (!loginParams) {
      paramData = {
        path: this.path,
        jumpSource: this.jumpSource,
        sessionId: this.sessionId,
        formId: this.formId,
        mobile: this.transformDataByFields("textFiled").value
      };
    } else {
      paramData = {
        path: this.path,
        jumpSource: this.jumpSource,
        ...loginParams
      };
    }
    let response;
    try {
      response = await this.io.post(API.SMS_CAPTCHA_V2, paramData);
      if (response.success) {
      } else {
        NativeToast.showMessage(response.errMsg);
      }
    } catch (e) {
    } finally {
    }
  };

  /**
   * 图片验证码
   */
  @action
  getGraphicCode = async (params?: object): Promise<{ success: boolean; [key: string]: any }> => {
    return new Promise(resolve => {
      AkConsole("Auth", "getGraphicCode");
      this.io.POST(
        API.IMAGE_CAPTCHA,
        {
          path: this.path,
          formId: this.formId,
          sessionId: this.sessionId,
          ...params
        },
        (response: any) => {
          const { data, success, errCode, errMsg } = response;
          this.graphicCodeData = data.captchaData;
          if (!success) {
            NativeToast.showMessage(errMsg);
          }
          resolve(response);
        },
        (error: any) => {
          const { data, success, code, message } = error;

          resolve({
            success,
            errCode: code,
            errMsg: message
          });
        }
      );
    });
  };

  /**
   * 图片验证码
   */
  @action
  getEmailCode = async (params?: any): Promise<{ success: boolean; [key: string]: any }> => {
    return new Promise(resolve => {
      const item = this.transformDataByFields("textFiled");
      if (!item.value) {
        NativeToast.showMessage((this.t && this.t("请输入xxx信息", { xxx: item.name })) || "");
        return Promise.resolve({ success: false });
      }
      this.io.POST(
        EMAIL_CAPTCHA,
        {
          path: this.path,
          formId: this.formId,
          email: item.value,
          sessionId: this.sessionId,
          ...params
        },
        (response: any) => {
          const { data, success, errCode, errMsg } = response;
          resolve({
            success,
            errCode,
            errMsg
          });
        },
        (error: any) => {
          const { data, success, code, message } = error;

          resolve({
            success,
            errCode: code,
            errMsg: message
          });
        },
        {
          success: true,
          errMsg: "邮箱验证码发送成功",
          errCode: "520",
          data: null
        }
      );
    });
  };

  /**
   * 发起提额申请
   * @param params   value： 授权成功的sessionId
   * */
  increaseLimit = (params: {
    processId: string;
    entries: Array<{ entryId: number; type: string; value: string }>;
  }): Promise<{ success: boolean; [key: string]: any }> => {
    return new Promise(resolve => {
      this.io.POST(
        INCREASE_LIMIT,
        params,
        (response: any) => {
          DeviceEventEmitter.emit("increaseLimitUpdate");
          const { data, success, errCode, errMsg } = response;
          if (isEqual(errCode, "UA.0057")) {
            CountLoading.dismiss();
          } else if (!success) {
            CountLoading.dismiss();
            NativeToast.showMessage(errMsg);
          }
          resolve(response);
        },
        (error: any) => {
          CountLoading.dismiss();
          const { data, success, code, message } = error;
          NativeToast.showMessage(error.errorMsg || error.message);
          resolve({
            success,
            errCode: code,
            errMsg: message
          });
        }
      );
    });
  };

  /**
   * 查询提额状态
   * @param processId
   */
  queryIncreaseStatus = (params: { asiApplicationId: string }): Promise<{ success: boolean; [key: string]: any }> => {
    return new Promise(resolve => {
      this.io.POST(
        QUERY_INCREASE_STATUS,
        params,
        (response: any) => {
          CountLoading.dismiss();
          resolve(response);
        },
        (error: any) => {
          const { data, success, code, message } = error;
          CountLoading.dismiss();
          resolve({
            success,
            errCode: code,
            errMsg: message
          });
        }
      );
    });
  };

  getEntryId = (processId: string): Promise<{ success: boolean; [key: string]: any }> => {
    return new Promise(resolve => {
      this.io.POST(
        INCREASE_PAGE_INFO,
        { processId },
        (response: any) => {
          const { data, success, errCode, errMsg } = response;
          if (!success) {
            NativeToast.showMessage(errMsg);
          }
          resolve(response);
        },
        (error: any) => {
          const { data, success, code, message } = error;
          NativeToast.showMessage(error.errorMsg || error.message);
          resolve({
            success,
            errCode: code,
            errMsg: message
          });
        }
      );
    });
  };

  // 二次授权弹窗逻辑
  handleVerifyCodeDialog = (data: { fields: Array<Fields>; sessionId: string; formId: number }, options: Options) => {
    const { fields, sessionId, formId } = data;
    const verifyItem = fields[0];
    // 第二次校验入参取第一次授权成功返回结果中的参数作为入参
    const loginParams = {
      sessionId,
      formId
    };
    // 不同类型授权的弹窗处理
    switch (verifyItem.inputMethod) {
      case "validCodeFiled": // 图形验证码（没有场景使用）
        ImageVerifyDialog.show({
          resetImageCode: async (context: ImageVerifyDialog) => {
            const imageRes = await this.getGraphicCode();
            if (imageRes.success) {
              context.setState({ codeImage: imageRes.data.captchaData });
            }
          },
          verifyImageCode: async (context, props, state) => {
            AuthSensorManager.sc.clickImageDialogNext();
            AuthSensorManager.v4.clickImageDialogNext();
            const { success, errCode, errMsg } = await this.login(
              { imageCaptcha: state.codeValue, ...loginParams },
              options
            );
            options.onLoginSuccessCallback && options.onLoginSuccessCallback(success, errCode, errMsg);
          },
          onMounted: () => {
            AuthSensorManager.sc.imageDialogExpose();
          },
          onCancel: () => {
            AuthSensorManager.sc.clickImageDialogCancel();
            AuthSensorManager.v4.clickImageDialogClose();
          },
          // @ts-ignore
          renderDialogContent: function IncreaseLimitVerifyUI(
            context: ImageVerifyDialog,
            props: object,
            state: { codeValue: string; codeImage: ImageSourcePropType }
          ) {
            return (
              <ImageVerify
                textInputProps={{
                  onFocus: () => {
                    AuthSensorManager.sc.clickImageDialogInput();
                  }
                }}
                CusTextInput={HocActionLogInput(AuthSensorManager.v4.getDialogImageCodeInputData())(TextInput)}
                onRefresh={context.onRefresh}
                placeholder={verifyItem.placeHolder}
                codeValue={state.codeValue}
                onChangeText={text => {
                  context.setState({ codeValue: text });
                }}
                imageCode={state.codeImage}
                title={verifyItem.name}
                buttonSequence={ButtonSequence.NegativeFirst}
                positiveText={"confirmText"}
                negativeText={"cancelText"}
                onPositivePress={() => {
                  AkConsole("Auth", "this.onConfirm");
                  return context.verifyImageCode();
                }}
                onNegativePress={() => AkConsole("Auth", "this.onCancel")}
              />
            );
          }
        });
        break;
      case "smsCodeFiled": // TODO输入的验证码取值
        DialogContainer.show({
          renderContent: (
            <SMSVerificationDialog
              t={this.t}
              getsecondTimeVerificationInfo={this.getsecondTimeVerificationInfo}
              onConfirm={async () => {
                // 取第一次登录授权的成功返回数据中的ID作为key,做第二次授权
                const id = verifyItem.id;
                const loginFields = {
                  [id]: this.secondTimeVerification
                };
                const { success, errCode, errMsg } = await this.login({ loginFields, ...loginParams }, options, true);
                options.onLoginSuccessCallback && options.onLoginSuccessCallback(success, errCode, errMsg);
              }}
              onSendSMSCode={async () => {
                const reslut = await this.sendSmsCodeV2(loginParams);
              }}
              iphoneNumber={""} // TODO手机号
            />
          )
        });
        break;
      case "emailCodeFiled": // TODO输入的验证码取值
        DialogContainer.show({
          renderContent: (
            <EmailDialog
              t={this.t}
              getsecondTimeVerificationInfo={this.getsecondTimeVerificationInfo}
              onConfirm={async () => {
                // 取第一次登录授权的成功返回数据中的ID作为key,做第二次授权
                const id = verifyItem.id;
                const loginFields = {
                  [id]: this.secondTimeVerification
                };
                const { success, errCode, errMsg } = await this.login({ loginFields, ...loginParams }, options, true);
                options.onLoginSuccessCallback && options.onLoginSuccessCallback(success, errCode, errMsg);
              }}
              onSendEmailCode={async () => {
                const reslut = await this.sendSmsCodeV2(loginParams);
              }}
              emailName={""} // TODO 邮箱地址
            />
          )
        });
        break;
      case "linkSmsCodeFiled":
        DialogContainer.show({
          renderContent: (
            <SMSDialog
              t={this.t}
              onConfirm={async () => {
                // 取第一次登录授权的成功返回数据中的ID作为key,做第二次授权
                const id = verifyItem.id;
                const loginFields = {
                  [id]: ""
                };
                // loginFields.login_link_sms_code = "";
                const { success, errCode, errMsg } = await this.login({ loginFields, ...loginParams }, options, true);
                options.onLoginSuccessCallback && options.onLoginSuccessCallback(success, errCode, errMsg);
              }}
              onResend={async () => {
                const reslut = await this.sendSmsCodeV2(loginParams);
              }}
            />
          )
        });
        break;
      default:
        break;
    }
  };

  changeError = (key: string, value: boolean) => {
    // @ts-ignore
    this[key] = value;
  };
}
