/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-09 12:55
 * @description
 */

import { HocBasePageProps } from "common/components/HocBasePage";
import AuthPageStore from "../store";

export type LoginFieldsResponse = {
  formId: number;
  fields: Array<Fields>;
  sessionId: string;
  icon: string;
  desc: string;
  iconUrl?: string;
  backgroundImg?: string;
  title: string;
  topTip: string;
  protocolDesc?: string;
  protocolImages?: Array<ProtocolImages>;
  faqUrl: string;
  protocolUrl: string;
  backgroundImgUrl: string;
};

export type ProtocolImages = {
  width: number;
  height: number;
  image: string;
};

export type Fields = {
  id: string;
  name: string;
  inputMethod: string;
  placeHolder: string;
  sort: number;
  smsCodeSendInterval: number;
  smsCaptchaFields: Array<string>;
  value?: string;
  pattern: string;
  errTip: string;
};

export type ContainerProps = HocBasePageProps<AuthPageStore> & { onSuccessCallback: (...args: any) => Promise<any> };

export enum AuthSource {
  INCREASE_LIMIT = 1, //提额
  TRADE_AUTH = 2 //交易链路
}

export enum AuthPageType {
  ECOM = 1, // 电商
  SOCIAL, // 社保
  TAX // 税卡
}
