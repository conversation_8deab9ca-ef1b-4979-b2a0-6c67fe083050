import { FontStyles, WINDOW_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    borderRadius: 8,
    paddingHorizontal: 16,
    height: WINDOW_HEIGHT * 0.85
  },
  hintText: {
    fontSize: 14,
    color: "#6E737D"
  },
  userInfoText: {
    fontSize: 14,
    marginTop: 4,
    color: "#282B2E",
    ...FontStyles["rob-medium"]
  },
  titleText: {
    fontSize: 16,
    color: "#282B2E",
    ...FontStyles["roboto-bold"]
  },
  inputView: {
    height: 40,
    paddingHorizontal: 12,
    backgroundColor: "#EFF2F6",
    marginBottom: 16,
    marginTop: 32,
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center"
  },
  closeTouch: {
    position: "absolute",
    top: 14,
    right: 12
  }
});
