import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    backgroundColor: "#EFF2F6",
    flex: 1
  },
  resendText: {
    fontSize: 12,
    color: "#2092E5",
    marginBottom: 32
  },
  countsText: {
    fontSize: 12,
    color: "#989FA9",
    marginBottom: 32,
    ...FontStyles["rob-medium"]
  },
  hintText: {
    fontSize: 14,
    color: "#282B2E",
    marginBottom: 12,
    fontWeight: "bold",
    marginTop: 39,
    ...FontStyles["rob-medium"]
  },
  titleText: {
    fontSize: 16,
    color: "#282B2E",
    fontWeight: "bold",
    marginTop: 16,
    ...FontStyles["roboto-bold"]
  },
  closeTouch: {
    position: "absolute",
    top: 14,
    right: 12
  }
});
