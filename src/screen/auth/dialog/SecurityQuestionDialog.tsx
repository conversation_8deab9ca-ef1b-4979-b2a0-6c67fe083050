import { AKButton, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akulaku-ec-common/src/components/AKButton";
import { BottomViewContainer, DialogContainer } from "@akulaku-rn/akulaku-ec-common/src/components/DialogContainer";
import React, { useEffect, useRef, useState } from "react";
import { TFunction } from "react-i18next";
import { View, Text, Image, TouchableOpacity, TextInput } from "react-native";
import styles from "./styles/SecurityQuestionDialog.styls";

type SecurityQuestionDialogProps = {
  onConfirm?: () => void;
  problemName: string;
  getsecondTimeVerificationInfo: (values: string) => void; // 获取输入框中的内容
  t?: TFunction | undefined;
};

// 安全问题校验
const SecurityQuestionDialog = (props: SecurityQuestionDialogProps): JSX.Element => {
  const { onConfirm, t, problemName, getsecondTimeVerificationInfo } = props;

  const renderTitle = () => {
    return (
      <View style={styles.titleView}>
        <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
          <Text style={styles.titleText}>{t && t("安全问题验证")}</Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            DialogContainer.dismiss();
          }}
        >
          <Image
            style={{
              width: 24,
              height: 24
            }}
            source={require("../img/close.webp")}
          />
        </TouchableOpacity>
      </View>
    );
  };

  // 弹窗内容
  const renderContent = () => {
    return (
      <View>
        <Text style={styles.hintText}>{problemName}</Text>
        <View style={styles.inputView}>
          <TextInput
            onChangeText={values => getsecondTimeVerificationInfo(values)}
            placeholder={t && t("请输入您设置的答案")}
          />
        </View>
        <AKButton onPress={onConfirm} text={`${t && t("提交")}`} type={AKButtonType.B1_1_1} />
      </View>
    );
  };

  return (
    <BottomViewContainer style={styles.container}>
      {renderTitle()}
      {renderContent()}
    </BottomViewContainer>
  );
};

export default React.memo(SecurityQuestionDialog);
