/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2021-06 20:07
 * @description 信息提交失败弹窗
 */

import React, { Component } from "react";
import { View, StyleSheet, Image, Text, TouchableOpacity } from "react-native";
import { CenterViewContainer, DialogContainer } from "common/components/DialogContainer";
import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";
import { Translation } from "react-i18next";
import AuthSensorManager from "../util/AuthSensorManager";
import { ViewExposer } from "@akulaku-rn/rn-v4-sdk";

const styles = StyleSheet.create({
  errorText: {
    ...FontStyles["rob-medium"],
    alignSelf: "center",
    fontSize: 16,
    color: "#43474C",
    marginTop: 25,
    marginHorizontal: 40,
    textAlign: "center"
  },
  errorMsg: {
    fontSize: 14,
    color: "#6E737D",
    alignSelf: "center",
    marginHorizontal: 24,
    marginTop: 8
  },
  touchIKnown: {
    width: "100%",
    height: 50,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    borderTopWidth: 0.5,
    borderTopColor: "#E2E5E9",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 16
  }
});

type State = {};

type Props = {
  errorMsg: string;
  t?: TFunction;
};

export default class ErrorDialog extends Component<Props, State> {
  viewExposer?: ViewExposer;

  constructor(props: any) {
    super(props);
    this.viewExposer = new ViewExposer({
      bizInfos: [
        {
          sn: 100633,
          cn: 20
        }
      ],
      needExtraField: false
    });
  }

  componentDidMount() {
    AuthSensorManager.sc.exposeErrorDialog();
    this.viewExposer && this.viewExposer.startExpose(false);
  }

  componentWillUnmount() {
    this.viewExposer && this.viewExposer.endExpose();
  }

  onPress = () => {
    AuthSensorManager.sc.clickGotIt();
    AuthSensorManager.v4.clickGotIt();
    DialogContainer.dismiss();
  };

  render() {
    const { errorMsg, t } = this.props;
    return (
      <Translation ns={"Auth"}>
        {t => {
          return (
            <View style={{ height: "100%", width: "100%", justifyContent: "center", alignItems: "center" }}>
              <View
                style={{
                  borderRadius: 12,
                  paddingTop: 35,
                  justifyContent: "center",
                  alignItems: "center",
                  backgroundColor: "#FFF",
                  width: WINDOW_WIDTH - 72
                }}
              >
                <Image
                  source={require("../img/img_dialog_fail.webp")}
                  style={{
                    width: 136,
                    height: 90
                  }}
                />
                <Text style={styles.errorText}>{t("抱歉，信息提交失败")}</Text>
                <Text style={styles.errorMsg}>{errorMsg}</Text>
                <TouchableOpacity style={styles.touchIKnown} onPress={this.onPress}>
                  <Text
                    style={{
                      fontSize: 16,
                      color: "#F32823",
                      ...FontStyles["rob-medium"]
                    }}
                  >
                    {t("我知道了")}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          );
        }}
      </Translation>
    );
  }
}
