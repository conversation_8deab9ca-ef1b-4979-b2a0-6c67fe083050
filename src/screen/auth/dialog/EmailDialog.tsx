import { AKButton, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akulaku-ec-common/src/components/AKButton";
import { BottomViewContainer, DialogContainer } from "@akulaku-rn/akulaku-ec-common/src/components/DialogContainer";
import React, { useEffect, useRef, useState } from "react";
import { TFunction } from "react-i18next";
import { View, Text, Image, TouchableOpacity, TextInput } from "react-native";
import styles from "./styles/EmailDialog.styls";

type EmailDialogProps = {
  onConfirm?: () => void; // 提交确认
  onSendEmailCode?: () => void; //发送邮箱验证码
  emailName: string; // 邮箱名称
  getsecondTimeVerificationInfo: (values: string) => void; // 获取输入框中的内容
  t?: TFunction | undefined;
};

// 二次校验邮箱验证
const EmailDialog = (props: EmailDialogProps): JSX.Element => {
  const { onConfirm, t, onSendEmailCode, emailName, getsecondTimeVerificationInfo } = props;

  const renderTitle = () => {
    return (
      <View
        style={{
          marginTop: 16,
          marginBottom: 39,
          flexDirection: "row"
        }}
      >
        <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
          <Text style={styles.titleText}>{"邮箱验证"}</Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            DialogContainer.dismiss();
          }}
        >
          <Image
            style={{
              width: 24,
              height: 24
            }}
            source={require("../img/close.webp")}
          />
        </TouchableOpacity>
      </View>
    );
  };

  // 弹窗内容
  const renderContent = () => {
    return (
      <View>
        <Text style={styles.hintText}>{t && t("一个验证码将发送至您的邮箱")}</Text>
        <Text style={styles.userInfoText}>{emailName}</Text>
        <View style={styles.inputView}>
          <View style={{ flex: 1 }}>
            <TextInput
              onChangeText={values => getsecondTimeVerificationInfo(values)}
              placeholder={t && t("请输入验证码")}
            />
          </View>
          <TouchableOpacity onPress={onSendEmailCode}>
            <View
              style={{
                backgroundColor: "#F32823",
                paddingHorizontal: 18,
                height: 24,
                alignItems: "center",
                justifyContent: "center",
                borderRadius: 12
              }}
            >
              <Text style={{ color: "#FFFFFF", fontSize: 10, fontWeight: "bold" }}>{t && t("发送")}</Text>
            </View>
          </TouchableOpacity>
        </View>
        <AKButton onPress={onConfirm} text={`${t && t("提交")}`} type={AKButtonType.B1_1_1} />
      </View>
    );
  };

  return (
    <BottomViewContainer style={styles.container}>
      {renderTitle()}
      {renderContent()}
    </BottomViewContainer>
  );
};

export default React.memo(EmailDialog);
