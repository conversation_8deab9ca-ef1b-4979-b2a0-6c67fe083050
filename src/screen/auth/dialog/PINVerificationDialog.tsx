import { AKButton, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akulaku-ec-common/src/components/AKButton";
import { BottomViewContainer, DialogContainer } from "@akulaku-rn/akulaku-ec-common/src/components/DialogContainer";
import CodeInputView from "@akulaku-rn/akulaku-ec-common/src/screen/bindBankCard/components/CodeInputView";
import React, { useEffect, useRef, useState } from "react";
import { TFunction } from "react-i18next";
import { View, Text, Image, TouchableOpacity, TextInput } from "react-native";
import styles from "./styles/PINVerificationDialog.styls";

type PINVerificationDialogProps = {
  onConfirm?: () => void;
  t?: TFunction | undefined;
};

// PIN码验证
const PINVerificationDialog = (props: PINVerificationDialogProps): JSX.Element => {
  const { onConfirm, t } = props;

  const renderTitle = () => {
    return (
      <View style={styles.titleView}>
        <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
          <Text style={styles.titleText}>{t && t("PIN码验证")}</Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            DialogContainer.dismiss();
          }}
        >
          <Image
            style={{
              width: 24,
              height: 24
            }}
            source={require("../img/close.webp")}
          />
        </TouchableOpacity>
      </View>
    );
  };

  // 弹窗内容
  const renderContent = () => {
    return (
      <View>
        <Text style={styles.hintText}>{t && t("请输入PIN码")}</Text>
        <View style={{ marginBottom: 16, width: WINDOW_WIDTH * 0.6 }}>
          <CodeInputView length={5} />
        </View>
        <AKButton onPress={onConfirm} text={`${t && t("提交")}`} type={AKButtonType.B1_1_1} />
      </View>
    );
  };

  return (
    <BottomViewContainer style={styles.container}>
      {renderTitle()}
      {renderContent()}
    </BottomViewContainer>
  );
};

export default React.memo(PINVerificationDialog);
