import { AKButton, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akulaku-ec-common/src/components/AKButton";
import { BottomViewContainer, DialogContainer } from "@akulaku-rn/akulaku-ec-common/src/components/DialogContainer";
import React, { useEffect, useRef, useState } from "react";
import { TFunction } from "react-i18next";
import { View, Text, Image, TouchableOpacity } from "react-native";
import styles from "./styles/SMSDialog.styls";

type SMSDialogProps = {
  onConfirm?: () => void;
  onResend?: () => void;
  t?: TFunction | undefined;
};

// 二次校验短信链接验证
const SMSDialog = (props: SMSDialogProps): JSX.Element => {
  const { onConfirm, t, onResend } = props;

  const timer = useRef<ReturnType<typeof setInterval> | null>(null);
  const [count, setCount] = useState(60);
  const start = () => {
    setCount(60);
    onResend && onResend();
    timer.current = setInterval(() => {
      setCount(count => count - 1);
    }, 1000);
  };

  const stop = () => {
    if (timer.current !== null) {
      clearInterval(timer.current);
      timer.current = null;
    }
  };

  useEffect(() => {
    timer.current = setInterval(() => {
      if (timer.current === 0) {
        // 此处判断latestCount.current，而不是count
        clearInterval(timer.current);
        return;
      }
      setCount(c => c - 1);
    }, 1000);
    //组件销毁时，去掉计时器
    if (count === 0 && timer.current !== null) {
      clearInterval(timer.current);
      timer.current = null;
    }
  }, []);

  return (
    <BottomViewContainer
      style={{
        borderRadius: 8
      }}
    >
      <View
        style={{
          width: WINDOW_WIDTH
        }}
      >
        <View
          style={{
            alignItems: "center"
          }}
        >
          <TouchableOpacity
            style={styles.closeTouch}
            onPress={() => {
              stop();
              DialogContainer.dismiss();
            }}
          >
            <Image
              style={{
                width: 24,
                height: 24
              }}
              source={require("../img/close.webp")}
            />
          </TouchableOpacity>
          <Text style={styles.titleText}>{t && t("短信验证")}</Text>
        </View>
        <View style={{ paddingHorizontal: 16 }}>
          <Text style={styles.hintText}>{t && t("一条安全链接将发送至您的手机")}</Text>
          <View
            style={{
              flexDirection: "row"
            }}
          >
            <Text
              style={{
                fontSize: 12,
                color: "#989FA9"
              }}
            >
              {t && t("没有收到短信吗？")}
            </Text>
            {count > 0 ? (
              <Text style={styles.countsText}>{`${count}s`}</Text>
            ) : (
              <TouchableOpacity
                onPress={() => {
                  start();
                }}
              >
                <Text style={styles.resendText}>{t && t("重新发送")}</Text>
              </TouchableOpacity>
            )}
          </View>
          <AKButton onPress={onConfirm} text={`${t && t("我已点击")}`} type={AKButtonType.B1_1_1} />
        </View>
      </View>
    </BottomViewContainer>
  );
};

export default React.memo(SMSDialog);
