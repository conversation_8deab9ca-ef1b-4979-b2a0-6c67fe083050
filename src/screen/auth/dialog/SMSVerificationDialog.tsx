import { AKButton, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akulaku-ec-common/src/components/AKButton";
import { BottomViewContainer, DialogContainer } from "@akulaku-rn/akulaku-ec-common/src/components/DialogContainer";
import { values } from "lodash";
import React, { useEffect, useRef, useState } from "react";
import { TFunction } from "react-i18next";
import { View, Text, Image, TouchableOpacity, TextInput } from "react-native";
import styles from "./styles/SMSVerificationDialog.styls";

type SMSVerificationDialogProps = {
  onConfirm?: () => void; // 二次校验提交
  onSendSMSCode?: () => void; // 发送短信验证码
  iphoneNumber: string; // 手机号
  getsecondTimeVerificationInfo: (values: string) => void; // 获取输入框中的内容
  t: TFunction | undefined;
};

// 二次校验短信验证码验证
const SMSVerificationDialog = (props: SMSVerificationDialogProps): JSX.Element => {
  const { onConfirm, t, iphoneNumber, onSendSMSCode, getsecondTimeVerificationInfo } = props;

  const renderTitle = () => {
    return (
      <View
        style={{
          marginTop: 16,
          marginBottom: 39,
          flexDirection: "row"
        }}
      >
        <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
          <Text style={styles.titleText}>{t && t("短信验证")}</Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            DialogContainer.dismiss();
          }}
        >
          <Image
            style={{
              width: 24,
              height: 24
            }}
            source={require("../img/close.webp")}
          />
        </TouchableOpacity>
      </View>
    );
  };

  // 弹窗内容
  const renderContent = () => {
    return (
      <View>
        <Text style={styles.hintText}>{t && t("一个验证码将发送至您的手机")}</Text>
        <Text style={styles.userInfoText}>{iphoneNumber}</Text>
        <View style={styles.inputView}>
          <View style={{ flex: 1 }}>
            <TextInput
              onChangeText={values => getsecondTimeVerificationInfo(values)}
              placeholder={t && t("请输入验证码")}
            />
          </View>
          <TouchableOpacity onPress={onSendSMSCode}>
            <View
              style={{
                backgroundColor: "#F32823",
                paddingHorizontal: 18,
                height: 24,
                alignItems: "center",
                justifyContent: "center",
                borderRadius: 12
              }}
            >
              <Text style={{ color: "#FFFFFF", fontSize: 10, fontWeight: "bold" }}>{t && t("发送")}</Text>
            </View>
          </TouchableOpacity>
        </View>
        <AKButton onPress={onConfirm} text={`${t && t("提交")}`} type={AKButtonType.B1_1_1} />
      </View>
    );
  };

  return (
    <BottomViewContainer style={styles.container}>
      {renderTitle()}
      {renderContent()}
    </BottomViewContainer>
  );
};

export default React.memo(SMSVerificationDialog);
