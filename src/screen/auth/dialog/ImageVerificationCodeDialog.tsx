import { AKButton, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akulaku-ec-common/src/components/AKButton";
import { BottomViewContainer, DialogContainer } from "@akulaku-rn/akulaku-ec-common/src/components/DialogContainer";
import CodeInputView from "@akulaku-rn/akulaku-ec-common/src/screen/bindBankCard/components/CodeInputView";
import React, { useEffect, useRef, useState } from "react";
import { TFunction } from "react-i18next";
import { View, Text, Image, TouchableOpacity, TextInput } from "react-native";
import styles from "./styles/ImageVerificationCodeDialog.styls";

type ImageVerificationCodeDialogProps = {
  onConfirm?: () => void;
  t?: TFunction | undefined;
};

// PIN码验证
const ImageVerificationCodeDialog = (props: ImageVerificationCodeDialogProps): JSX.Element => {
  const { onConfirm, t } = props;

  const renderTitle = () => {
    return (
      <View style={styles.titleView}>
        <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
          <Text style={styles.titleText}>{"PIN Verification"}</Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            DialogContainer.dismiss();
          }}
        >
          <Image
            style={{
              width: 24,
              height: 24
            }}
            source={require("../img/close.webp")}
          />
        </TouchableOpacity>
      </View>
    );
  };

  // 弹窗内容
  const renderContent = () => {
    return (
      <View>
        <Text style={styles.hintText}>Please enter verification code</Text>
        <View style={{ flexDirection: "row" }}>
          <View style={styles.inputView}>
            <TextInput placeholder={"Please enter verification code"} />
          </View>
          <View style={{ marginLeft: 12, flexDirection: "row", alignItems: "center" }}>
            <Image
              style={{ width: 56, height: 28 }}
              source={{
                uri:
                  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADMAAAAzCAYAAAA6oTAqAAAAEXRFWHRTb2Z0d2FyZQBwbmdjcnVzaEB1SfMAAABQSURBVGje7dSxCQBACARB+2/ab8BEeQNhFi6WSYzYLYudDQYGBgYGBgYGBgYGBgYGBgZmcvDqYGBgmhivGQYGBgYGBgYGBgYGBgYGBgbmQw+P/eMrC5UTVAAAAABJRU5ErkJggg=="
                // uri: `data:image/png;base64,`
              }}
            />
            <TouchableOpacity onPress={() => {}}>
              <Image style={{ width: 16, height: 16, marginLeft: 4 }} source={require("./img/ic_retry.webp")} />
            </TouchableOpacity>
          </View>
        </View>
        <AKButton onPress={onConfirm} text={`${t && t("提交")}`} type={AKButtonType.B1_1_1} />
      </View>
    );
  };

  return (
    <BottomViewContainer style={styles.container}>
      {renderTitle()}
      {renderContent()}
    </BottomViewContainer>
  );
};

export default React.memo(ImageVerificationCodeDialog);
