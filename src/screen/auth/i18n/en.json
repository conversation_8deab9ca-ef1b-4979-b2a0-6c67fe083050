{"发送": "Send", "提交": "Submit", "授权": "Authorize", "请输入xxx信息": "Please enter {{xxx}}", "账号或密码错误": "Incorrect account or password", "xxx账号授权（xxxx指的是shopee，lazada等电商平台）": "{{xxx}} account authorization", "验证码已发送至xxx": "Verification code has been sent to", "恭喜！你的额度已成功提升": "Congratulations! Your credit limit has been successfully increased by ", "继续提额": "Continue Increasing Limit", "马上去消费": "Shopping Now", "恭喜！你的提额申请已提交，额度最高可提升": "Congratulations! Your application for credit limit increase has been submitted, and the maximum limit can be increased by", "审核结果将在1-3个工作日内通知您，请耐心等待": "The review result will be notified to you within 1-3 working days, please wait patiently", "Akulaku将给您银行级别的安全防护以及个人信息数据的隐私加密": "<PERSON><PERSON><PERSON><PERSON> will provide bank level security protection and encrypt your personal information data ", "请输入您的账号": "Please enter your account", "请输入您的密码": "Please enter your password", "忘记账号或密码": "Forget account or password", "我已阅读并同意": "I have read and agreed to", "个人信息授权协议": "The Personal Information Authorization Agreement", "请勾选": "Please tick", "请输入验证码": "Please enter verification code", "短信验证": "SMS Verification", "将会有一条短信发送至": "SMS will be sent to", "在线客服": "Online Customer Service", "抱歉，信息提交失败": "Sorry, the information submission failed", "我知道了": "Got It", "提交成功": "Submitted Successfully", "提额成功": "Succeed to Increase Limit ", "提额失败": "Failed to Increase Limit ", "请等待提额结果": "Please wait for the credit limit increase application result.", "AKULAKU 已经收到了您提交的信息，当前正在审核中。请您耐心等待": "<PERSON><PERSON><PERSON><PERSON> has received your credit limit increase application and it is currently under review. Please wait patiently.", "很抱歉您的提额未成功，请尝试其他提额方式": "Sorry, the limit increase was unsuccessful. Please try another method of limit increase", "已提额": "Limit Increased", "请填写社保网站登录邮箱": "Please fill in BPJS web login e-mail", "请填写登陆密码": "Please fill in login password", "邮箱格式有误，请重新输入": "Incorrect e-mail format, please re-enter", "请填写账号信息": "Please fill in account information", "填写账户信息过长，请重新填写": "Account information too long, please fill again", "账号仅可输入数字": "Number only in account", "我们将为您提供数据安全防护以及对您的个人信息进行隐私加密。": "We will provide you with data security protection and encrypt your privacy of personal information.", "请勾选协议": "Please select the agreement", "恭喜，提额成功啦！": "Congrats! The credit limit has been increased successfully!", "分期额度提升": "Installment credit limit increased by {{x}}", "免息额度提升": "Interest-free credit limit increased by {{x}}", "返回额度中心": "Back to Credit Limit Center", "恭喜，您的额度成功升级，支持3/6/9/12期使用": "Congrats! Your credit limit has been successfully upgraded and can support the installment of 3/6/9/12 months.", "分期额度提升（支持3/6/9/12期使用）": "Installment credit limit increased by {{x}} (Support installment of 3/6/9/12 months)", "我已点击": "Clicked", "邮箱验证": "Email Verification", "PIN码验证": "PIN Verification", "安全问题验证": "Security Question Verification", "请输入PIN码": "Please enter PIN", "请输入您设置的答案": "Please enter the answer you set", "请点击短信中的安全链接": "Please click the security link in the SMS", "一条短信将发送至...": "An SMS will be sent to", "一个验证码将发送至...": "A verification code will be sent to", "一条安全链接将发送至...": "A security link will be sent to", "一条短信将发送至您的手机": "An SMS will be sent to your mobile phone", "一个验证码将发送至您的手机": "A verification code will be sent to your mobile phone", "一个验证码将发送至您的邮箱": "A verification code will be sent to your email", "一条安全链接将发送至您的手机": "A security link will be sent to your mobile phone", "重新发送": "Resend>>", "没有收到短信吗？": "Didn't get a text message？"}