/**
 * Create by z<PERSON><PERSON><PERSON> on 2020-09 17:24
 * @description 电商、社保、税卡 统一适用这个页面
 */

import React, { Component } from "react";
import { BackHandler, NativeEventSubscription } from "react-native";
import { inject, observer } from "mobx-react";
import { withTranslation } from "common/services/i18n";
import RootView from "common/components/Layout/RootView";
import store from "./store";
import AuthPageStore from "./store";
import { TFunction } from "i18next";

import _, { get } from "lodash";
import { AuthPageType, AuthSource } from "./types";
import AuthSensorManager, { getExtraBaseParams, getPageBaseParams, setBaseParmas } from "./util/AuthSensorManager";
import { ScreenSensorEvent } from "common/nativeModules/sdk/nativeSensroModule";
import { NativeNavigationModule } from "common/nativeModules";
import { Android, iOS } from "@akulaku-rn/akui-rn/src/components/index";
import { PageConfig } from "@akulaku-rn/rn-v4-sdk";
import ActivityAdPopup from "common/components/ADGroup/ActivityAdPopup";
import { ResAD } from "common/components/ADGroup/helpers/types";
import { LOGIN, LOGIN_FORM } from "./api";
import PageComponent from "./component/PageComponent";
import { Translation } from "react-i18next";
import { DialogContainer } from "common/components/DialogContainer";
import ErrorDialog from "./dialog";

type Props = {
  type: AuthPageType;
  store: {
    pageStore: AuthPageStore;
    navParams: {
      jumpSource: number; //跳转源 0-->提额, 1-->交易链路, 2-->广告, 3-->现金贷, 4-->现金分期
      jumpLink: string; //如果有传入链接,授权登录成功跳转链接
      dataString?: string; //授权成功跳转需要携带的数据
      name?: string; //授权页面标题
      source?: AuthSource; // 是否是提额
      processId?: string;
      type: string; // 接口参数type
      path?: string; // 印尼社保：social_insurance.bpjstku、印尼税卡：tax.tax、印尼BCA：bank.klikbca、印尼Mandiri：bank.mandiri
      extra: { [key: string]: any };
    };
    [key: string]: any;
  };
  t: TFunction;
  configSensorEvent: (event: ScreenSensorEvent) => void;
  configPageInfo: (config: PageConfig, isSupportV3?: boolean) => void;
};

/**
 * 电商授权
 * @param path 电商授权渠道
 */
@RootView({
  withI18n: [
    "Auth",
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json"),
      ms: require("./i18n/ms.json"),
      vi: require("./i18n/vi.json")
    }
  ],
  store
})
@withTranslation("Auth")
@inject("store")
@observer
export default class AuthPage extends Component<Props> {
  backHandler?: NativeEventSubscription;

  constructor(props: Props) {
    super(props);
    //------兼容性代码----------开始//
    if (!get(this.props, "store.navParams.jumpSource")) {
      this.props.store.navParams.jumpSource = 0;
    }
    if (get(this.props, "store.navParams.jumpSource", 0) === 1) {
      this.props.store.navParams = {
        // @ts-ignore
        source: 2,
        name: "Akun BPJS",
        path: "social_insurance.bpjstku",
        ...this.props.store.navParams
      };
    }
    this.props.store.navParams.extra = {
      ...this.props.store.navParams.extra,
      jumpSource: get(this.props, "store.navParams.jumpSource", 0)
    };
    //------兼容性代码----------结束//
    setBaseParmas(this.props.store.navParams.extra);
    this.props.configSensorEvent({ ...getPageBaseParams(), extra: getExtraBaseParams() });
    this.props.configPageInfo(AuthSensorManager.v4.getScreenParams());
  }

  adClick = (adData: ResAD) => {
    AuthSensorManager.sc.adClick();
    AuthSensorManager.v4.clickIKnow();
    NativeNavigationModule.goBack();
  };

  adExpose = (adData: ResAD) => {
    AuthSensorManager.sc.adExpose();
    AuthSensorManager.v4.showSysTemErrorDialog();
  };

  onClose = () => {
    AuthSensorManager.v4.dismissSysTemErrorDialog();
  };

  getFields = async () => {
    const {
      store: {
        pageStore: { getFields },
        runtime,
        navParams: { path, jumpSource }
      }
    } = this.props;
    getFields((runtime as any).countryId, {
      path: path,
      jumpSource: jumpSource,
      url: LOGIN_FORM
    });
  };

  handleLoginRes = async (success: boolean, errCode: string, errMsg: string) => {
    const {
      store: {
        pageStore: { sessionId, transformDataByFields }
      },
      t
    } = this.props;
    if (success) {
      const { success } = await this.onSuccessCallback(sessionId, transformDataByFields("textFiled").value);
      if (!success) {
        this.getFields();
        DialogContainer.show({
          renderContent: <ErrorDialog errorMsg={errMsg} t={t} />
        });
      }
    } else {
      this.getFields();
      DialogContainer.show({
        renderContent: <ErrorDialog errorMsg={errMsg} t={t} />
      });
    }
  };

  // 提额第三方授权登录跳转
  login = _.debounce(
    async () => {
      const {
        store: {
          pageStore: { login, transformDataByFields },
          navParams: { jumpSource }
        }
      } = this.props;
      // Loading.show({ useBack: true });
      const { success, errCode, errMsg } = await login(
        {
          ...transformDataByFields(),
          jumpSource
        },
        {
          url: LOGIN,
          nextLoginExtParams: transformDataByFields(),
          onLoginSuccessCallback: this.handleLoginRes
        }
      );
      // Loading.dismiss();
      this.handleLoginRes(success, errCode, errMsg);
    },
    500,
    {
      leading: true,
      trailing: false
    }
  );

  onBackPress = () => {
    const {
      store: {
        navParams: { jumpLink, jumpSource }
      }
    } = this.props;
    if (jumpSource === 1 && jumpLink) {
      //交易链路场景下特殊处理
      if (Android) {
        //安卓不能延迟，否则会打不开页面
        NativeNavigationModule.popToTop();
        NativeNavigationModule.navigate({ screen: "orderList", params: { tabName: "Unpaid", backOnNative: false } });
      } else {
        NativeNavigationModule.navigate({
          screen: "orderList",
          params: { tabName: "Unpaid", gestureEnabled: false },
          popNumber: 6
        });
      }
    } else {
      AuthSensorManager.sc.clickBack();
      NativeNavigationModule.goBack();
    }
  };

  onClickTip = () => {
    NativeNavigationModule.navigate({ url: "https://mall.akulaku.com/sale-v2/self-publish?actId=21991" });
  };

  getAdGroupId = () => {
    const {
      store: {
        navParams: { path }
      }
    } = this.props;
    let adGroupId = 0;
    switch (path) {
      case "social_insurance.bpjstku":
        adGroupId = 163;
        break;
      case "tax.tax":
        adGroupId = 177;
        break;
      case "bank.klikbca":
        adGroupId = 289;
        break;
      case "bank.mandiri":
        adGroupId = 290;
        break;
      case "e_commerce.shopee":
        adGroupId = 291;
        break;
    }
    return adGroupId;
  };

  componentDidMount(): void {
    const {
      store: {
        navParams: { jumpLink, jumpSource }
      }
    } = this.props;

    ActivityAdPopup.show({
      adData: { adGroupId: this.getAdGroupId() },
      adClick: this.adClick,
      adExpose: this.adExpose,
      onClose: this.onClose,
      hideCloseIcon: true,
      forbitBackPress: true
    });

    this.getFields();

    // AkConsole("Auth", `this.props.store: `, this.props.store);
    (this.props.store.pageStore as AuthPageStore).setParams({
      t: this.props.t,
      countryId: this.props.store.runtime.countryId
    });
    if (jumpSource === 1 && jumpLink && !iOS) {
      //交易链路场景下特殊处理
      this.backHandler = BackHandler.addEventListener("hardwareBackPress", () => {
        NativeNavigationModule.popToTop();
        NativeNavigationModule.navigate({
          screen: "orderList",
          params: {
            tabName: "Unpaid",
            backOnNative: false
          }
        });
        return true;
      });
    }
  }

  componentWillUnmount() {
    this.backHandler && this.backHandler.remove();
  }

  onSuccessCallback = async (sessiondId: string, account: string): Promise<{ success: boolean }> => {
    const {
      store: {
        pageStore: { title },
        navParams: { extra, jumpLink, dataString = "", jumpSource }
      }
    } = this.props;
    if (!!jumpLink) {
      if (+jumpSource === 3 || +jumpSource === 4) {
        //瑞文说他那边多传参数会报错,特殊处理一下
        NativeNavigationModule.navigate({
          url: encodeURIComponent(dataString).length
            ? jumpLink + "&dataString=" + encodeURIComponent(dataString)
            : jumpLink
        });
      } else {
        NativeNavigationModule.navigate({
          url:
            (encodeURIComponent(dataString).length
              ? jumpLink + "&dataString=" + encodeURIComponent(dataString)
              : jumpLink) +
            "&sessiondId=" +
            sessiondId +
            "&account=" +
            account +
            "&extra=" +
            JSON.stringify(extra) +
            "&title=" +
            title
        });
      }
      return new Promise(resolve => true);
    }
    return new Promise(resolve => false);
  };

  render() {
    const { t } = this.props;
    return (
      <PageComponent
        store={this.props.store.pageStore}
        t={t}
        getFields={this.getFields}
        onBackPress={this.onBackPress}
        login={this.login}
        onClickTip={this.onClickTip}
      />
    );
  }
}
