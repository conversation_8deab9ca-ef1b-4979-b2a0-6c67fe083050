/*
 * @LastEditors: zou yu
 * @Description: 神策
 * @FilePath: /react-native/src/screen/social-test/utils/sensorReport.ts
 */

import { ResAD } from "common/components/ADGroup/helpers/types";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";

export const reportClickSocial = (adData: ResAD, isClick: boolean) => {
  NativeSensorModule.sensorLogger(isClick ? SensorType.CLICK : SensorType.EXPOSE, {
    element_id: "10560101",
    element_name: "exclusive rights",
    page_id: "1056",
    page_name: "Social Security Supplement",
    module_id: "01",
    module_name: "rights",
    position_id: "01",
    extra: {
      advertisingID: adData.id,
      image_id: adData.creatives[0].id,
      adPositionid: adData.spotId,
      links: adData.creatives[0].destUrl,
      label_id: adData.tagId || ""
    }
  });
};

export const reportClickNoSocial = (adData: ResAD, isClick: boolean) => {
  NativeSensorModule.sensorLogger(isClick ? SensorType.CLICK : SensorType.EXPOSE, {
    element_id: "10560102",
    element_name: "basic rights",
    page_id: "1056",
    page_name: "Social Security Supplement",
    module_id: "01",
    module_name: "rights",
    position_id: "02",
    extra: {
      advertisingID: adData.id,
      image_id: adData.creatives[0].id,
      adPositionid: adData.spotId,
      links: adData.creatives[0].destUrl,
      label_id: adData.tagId || ""
    }
  });
};
