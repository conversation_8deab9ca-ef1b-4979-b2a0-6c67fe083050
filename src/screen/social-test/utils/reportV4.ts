/*
 * @LastEditors: zou yu
 * @Description: v4埋点
 * @FilePath: /react-native/src/screen/social-test/utils/reportV4.ts
 */
import { ResAD } from "common/components/ADGroup/helpers/types";
import { get } from "lodash";
import { reporter, ViewExposer } from "@akulaku-rn/rn-v4-sdk";

export const createExposerSocial = (adData: ResAD) => {
  return new ViewExposer({
    bizInfos: [
      {
        sn: 201042,
        cn: 1,
        eit: 2,
        ei: {
          spot_id: get(adData, "spotId", ""),
          ad_id: get(adData, "id", ""),
          image_id: get(adData, "creatives[0].id", "")
        }
      }
    ],
    needExtraField: false
  });
};

export const createExposerNoSocial = (adData: ResAD) => {
  return new ViewExposer({
    bizInfos: [
      {
        sn: 201042,
        cn: 2,
        eit: 2,
        ei: {
          spot_id: get(adData, "spotId", ""),
          ad_id: get(adData, "id", ""),
          image_id: get(adData, "creatives[0].id", "")
        }
      }
    ],
    needExtraField: false
  });
};

export const reportV4ClickSocial = (adData: ResAD) => {
  reporter.click({
    sn: 201042,
    cn: 1,
    bct: 8,
    bi: {
      spot_id: get(adData, "spotId", ""),
      ad_id: get(adData, "id", ""),
      image_id: get(adData, "creatives[0].id", "")
    }
  });
};

export const reportV4ClickNoSocial = (adData: ResAD) => {
  reporter.click({
    sn: 201042,
    cn: 2,
    bct: 8,
    bi: {
      spot_id: get(adData, "spotId", ""),
      ad_id: get(adData, "id", ""),
      image_id: get(adData, "creatives[0].id", "")
    }
  });
};
