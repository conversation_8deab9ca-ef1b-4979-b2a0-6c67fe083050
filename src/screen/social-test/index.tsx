import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Text, View } from "react-native";
import { inject, observer } from "mobx-react";
import { withTranslation } from "common/services/i18n";
import RootView from "common/components/Layout/RootView";
import store from "./store";
import { Type } from "common/components/BaseContainer";
import { NativeNavigationModule } from "common/nativeModules";
import LinearGradient from "react-native-linear-gradient";
import ImageAd from "common/components/ADGroup/ImageAd";
import { styles } from "./styles";
import { NAV_BAR_HEIGHT, WINDOW_WIDTH, WINDOW_HEIGHT, Loading, NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import { ResAD } from "common/components/ADGroup/helpers/types";
import { reportClickSocial, reportClickNoSocial } from "./utils/sensorReport";
import {
  reportV4ClickSocial,
  reportV4ClickNoSocial,
  createExposerSocial,
  createExposerNoSocial
} from "./utils/reportV4";
import DefaultComponent from "common/components/DefaultComponent";
import { ViewExposer } from "@akulaku-rn/rn-v4-sdk";

interface NavParams {
  resultParams: {
    gestureEnabled: boolean;
    isFirstApply: boolean;
    data: { adjustId: string; deviceId: string };
    gobackNum: number;
  };
}

/**
 * 电商授权
 * @param path 电商授权渠道
 */
@RootView({
  withI18n: [
    "SocialTest",
    {
      en: require("./i18n/en.json"),
      in: require("./i18n/in.json"),
      vi: require("./i18n/vi.json")
    }
  ],
  store
})
@withTranslation("SocialTest")
@inject("store")
@observer
export default class SocialTest extends React.Component<Type.Props<store, NavParams>> {
  imageSizeFirst!: { width: number; height: number };

  imageSizeSencond!: { width: number; height: number };

  ad1Exposer?: ViewExposer;

  ad2Exposer?: ViewExposer;

  //广告1返回结果
  resAD1?: ResAD[];

  //广告2返回结果
  resAD2?: ResAD[];

  constructor(props: Type.Props<store, NavParams>) {
    super(props);
    console.log("zy:SocialTest=>", this.props.store.navParams);

    this.props.configSensorEvent({
      page_id: "1056",
      page_name: "Social Security Supplement"
    });
    this.props.configPageInfo({ sn: 201042 }, false);
    const width = WINDOW_WIDTH - 24;
    const height1 = (310 / 336) * width;
    const height2 = (230 / 336) * width;
    this.imageSizeFirst = { width, height: height1 };
    this.imageSizeSencond = { width, height: height2 };
  }

  componentDidMount() {
    this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.backHandler);
  }

  componentWillUnmount() {
    this.backHandlerListener && BackHandler.removeEventListener("hardwareBackPress", this.backHandler);
  }

  backHandler = () => {
    return true;
  };

  backHandlerListener: any = null;

  scrollY = new Animated.Value(0);

  animatedOpacity = this.scrollY.interpolate({
    inputRange: [0, 137],
    outputRange: [0, 1]
  });

  _onPressBeSocial = (resAD: ResAD) => {
    const {
      store: { navParams }
    } = this.props;

    NativeNavigationModule.navigate({
      screen: "AuthPage",
      params: {
        jumpSource: 5,
        isNeedLogin: 1,
        authType: 2,
        path: "social_insurance.bpjstku",
        name: "Akun BPJS",
        jumpLink: "ak://m.akulaku.com/1602?screen=/authorize-credit/result-page", //授权登录成功跳转链接
        dataString: JSON.stringify(navParams.resultParams)
      }
    });

    reportV4ClickSocial(resAD);
    reportClickSocial(resAD, true);
  };

  _onPressNoSocial = (resAD: ResAD) => {
    const {
      store: { navParams }
    } = this.props;
    this.props.navigation.navigate({
      screen: "CreditResultPage",
      params: navParams.resultParams
    });

    reportV4ClickNoSocial(resAD);
    reportClickNoSocial(resAD, true);
  };

  //刷新页面
  _refreshData = () => {
    const {
      store: { pageStore }
    } = this.props;
    this.resAD1 = undefined;
    this.resAD2 = undefined;
    pageStore.updateIsLoading(true);
    //通过控制isError切换，让ImageAd组件重新加载达到刷新效果
    pageStore.updateIsError(false);
  };

  _configExposer = () => {
    const exposer = [];
    this.ad1Exposer && exposer.push(this.ad1Exposer);
    this.ad2Exposer && exposer.push(this.ad2Exposer);
    this.props.configExposer(exposer);
  };

  //刷新页面状态
  _refreshPageStatus = () => {
    const {
      store: { pageStore }
    } = this.props;
    if (this.resAD1 === undefined || this.resAD2 === undefined) return;
    pageStore.updateIsError(!this.resAD1.length || !this.resAD2.length);
    pageStore.updateIsLoading(false);
  };

  //社保广告请求回调
  _socialRequest = (adlist: ResAD[]) => {
    this.resAD1 = adlist;
    this._refreshPageStatus();
  };

  //非社保广告请求回调
  _noSocialRequest = (adlist: ResAD[]) => {
    this.resAD2 = adlist;
    this._refreshPageStatus();
  };

  _renderContent() {
    const {
      t,
      store: { pageStore }
    } = this.props;
    if (pageStore.isLoading) {
      return <Loading />;
    }
    if (pageStore.isError) {
      return (
        <View style={styles.fill}>
          <NetworkErrorComponent
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            onRetryPress={this._refreshData}
            buttonText={t("global:刷新")}
          />
        </View>
      );
    }
  }

  render() {
    const {
      t,
      store: { pageStore }
    } = this.props;
    return (
      <LinearGradient style={styles.container} colors={["#FFF1E0", "#FFFFFF"]}>
        {pageStore.isError ? null : (
          <Animated.ScrollView
            onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: this.scrollY } } }], {
              useNativeDriver: true
            })}
            contentContainerStyle={{ paddingTop: NAV_BAR_HEIGHT - 22 }}
            bounces={false}
            scrollEventThrottle={16}
            style={{ opacity: pageStore.isLoading ? 0 : 1 }}
          >
            <ImageAd
              adGroup={245}
              onRequest={this._socialRequest}
              onClick={this._onPressBeSocial}
              imageWidth={this.imageSizeFirst.width}
              imageHeight={this.imageSizeFirst.height}
              wrapperStyle={{ marginLeft: 12 }}
              onExpose={resAD => {
                //v4
                this.ad1Exposer = createExposerSocial(resAD);
                this.ad1Exposer && this.ad1Exposer.startExpose();
                this._configExposer();
                //神策
                reportClickSocial(resAD, false);
              }}
            />
            <ImageAd
              adGroup={246}
              onRequest={this._noSocialRequest}
              onClick={this._onPressNoSocial}
              imageWidth={this.imageSizeSencond.width}
              imageHeight={this.imageSizeSencond.height}
              wrapperStyle={{ marginLeft: 12, marginTop: 12 }}
              onExpose={resAD => {
                //v4
                this.ad2Exposer = createExposerNoSocial(resAD);
                this.ad2Exposer && this.ad2Exposer.startExpose();
                this._configExposer();
                //神策
                reportClickNoSocial(resAD, false);
              }}
            />
          </Animated.ScrollView>
        )}
        {this._renderContent()}
        <View style={[styles.AnimatedView]}>
          <Animated.View style={[styles.AnimatedView, { opacity: this.animatedOpacity, backgroundColor: "#ffffff" }]} />
          <View style={styles.navContainer}>
            <Text style={styles.title} numberOfLines={1}>
              {t("测试页标题")}
            </Text>
          </View>
        </View>
      </LinearGradient>
    );
  }
}
