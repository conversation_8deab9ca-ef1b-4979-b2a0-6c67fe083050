/*
 * @LastEditors: zou yu
 * @Description: 社保测试页store
 * @FilePath: /react-native/src/screen/social-test/store/index.ts
 */

import { action, observable } from "mobx";
import Basic from "common/store/Basic";
import { NativeNetworkModuleV2, NativeAkuAddressModule } from "common/nativeModules";

export default class Store extends Basic {
  @observable isLoading = true;

  @observable isError = false;

  @action
  updateIsLoading = (isLoading: boolean) => {
    if (this.isLoading === isLoading) return;
    this.isLoading = isLoading;
  };

  @action
  updateIsError = (isError: boolean) => {
    if (this.isError === isError) return;
    this.isError = isError;
  };
}
