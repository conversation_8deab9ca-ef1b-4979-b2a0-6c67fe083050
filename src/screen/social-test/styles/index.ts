import { StyleSheet } from "react-native";
import { NAV_BAR_HEIGHT, WINDOW_WIDTH, STATUS_BAR_HEIGHT, iOS, FontStyles } from "@akulaku-rn/akui-rn";

export const styles = StyleSheet.create({
  fill: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "#fff"
  },
  navContainer: {
    paddingTop: STATUS_BAR_HEIGHT + (iOS ? 12 : 20),
    paddingHorizontal: 16,
    alignItems: "center",
    paddingBottom: iOS ? 8 : 12,
    flexDirection: "row"
  },
  titleView: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center"
  },
  title: {
    height: 24,
    lineHeight: 24,
    fontSize: 16,
    color: "#333",
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    maxWidth: WINDOW_WIDTH - 48 - 84
  },
  container: {
    backgroundColor: "#f0f2f5",
    flex: 1
  },
  AnimatedView: {
    position: "absolute",
    backgroundColor: "transparent",
    width: WINDOW_WIDTH,
    height: NAV_BAR_HEIGHT,
    top: 0,
    left: 0
  }
});
