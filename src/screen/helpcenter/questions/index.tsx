import React, { Component } from "react";
import { inject, Observer, observer } from "mobx-react";
import { FlatList, StyleSheet, TouchableOpacity, Image, TouchableHighlight } from "react-native";
import { View, Text } from "native-base";
import { withTranslation } from "common/services/i18n";
import RootView from "common/components/Layout/RootView";
import images from "../img";
import store from "./store";

import NavigationBar from "@akulaku-rn/akui-rn/src/components/NavigationBar";
import { toJS } from "mobx";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import DefaultComponent from "@akulaku-rn/akui-rn/src/components/NetworkErrorPage";
import CustomServiceComponent from "../homeScreen/component/CustomServiceComponent";
import ServiceInfo from "../serviceInfo";
import { Props } from "common/components/BaseContainer/Type";
import { item } from "./store";
import { NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import NativeSensorModule, { SensorType } from "nativeModules/sdk/nativeSensroModule";
import AkListComponent, { AkListType } from "@akulaku-rn/akui-rn/src/components/AKListComponent";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white"
  },
  item: {
    // height: 60,
    paddingLeft: 10,
    paddingRight: 17,
    paddingVertical: 15,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around"
  },
  itemArrow: {
    width: 16,
    height: 16
  },
  itemTitle: {
    marginLeft: 10,
    marginRight: 12,
    fontSize: 14,
    color: "#333333"
  },
  sepLine: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: "#ebebeb",
    marginLeft: 12
  }
});

@RootView({
  withI18n: [
    "helpCenter",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json"),
      ms: require("../i18n/ms.json")
    }
  ],
  store,
  keyApi: ["/capi/personal/public/faq-center/faq-question/list"]
})
@withTranslation("helpCenter")
@inject("store")
@observer
export default class QuestionsScreen extends Component<Props<store>, {}> {
  constructor(props: Props<store>) {
    super(props);
  }

  componentDidMount(): void {
    const {
      pageStore,
      pageStore: { serviceTime = ServiceInfo.serviceTime },
      navParams
    } = this.props.store;
    const { faq, categoryId } = navParams;
    if (serviceTime === "") {
      pageStore.fetchServiceInfo();
    }
    pageStore.fetchFaqList(categoryId);
    pageStore.setItemDatas(faq);
    this.props.configSensorEvent({
      page_id: "852",
      page_name: "List of questions page"
    });
  }

  _renderItem = ({ item, index }: { item: item; index: number }) => {
    return (
      <Observer>
        {() => (
          <AkListComponent
            key={index}
            type={AkListType.G7_1_1}
            title={item.question}
            onPress={() => this._onPress(item, index)}
          />
        )}
      </Observer>
    );
  };

  _onPress = (item: item, index: number) => {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "8520103",
      element_name: "question",
      page_id: "852",
      page_name: "List of questions page",
      module_id: "01",
      module_name: "questions",
      position_id: "03",
      extra: {
        Aku_buttonName: item?.id,
        positionid: index
      }
    });
    this.props.navigation.navigate({
      screen: "helpCenterFAQScreen",
      params: {
        id: item.id
      }
    });
  };

  onLiveChatClick = () => {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "8520102",
      element_name: "livechat",
      page_id: "852",
      page_name: "List of questions page",
      module_id: "01",
      module_name: "questions",
      position_id: "02"
    });
  };

  renderBottom = () => {
    const {
      store: {
        pageStore: {
          serviceTime = ServiceInfo.serviceTime,
          servicePhone = ServiceInfo.servicePhone,
          serviceEmail = ServiceInfo.serviceEmail
        }
      }
    } = this.props;
    return (
      <CustomServiceComponent
        t={this.props.t}
        countryCode={this.props.store.runtime.countryCode}
        source={"questions"}
        serviceTime={serviceTime}
        serviceTel={servicePhone}
        serviceEmail={serviceEmail}
        onLiveChatClick={this.onLiveChatClick}
        onContactDialogShow={() => {
          NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
            page_name: "List of questions page",
            page_id: "852",
            extra: {
              pop_id: "pop30500",
              pop_name: "contact cs"
            }
          });
        }}
        onContactDialogConfirm={() => {
          NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
            element_id: "pop3050002",
            element_name: "call",
            page_name: "List of questions page",
            page_id: "852",
            extra: {
              pop_id: "pop30500",
              pop_name: "contact cs"
            }
          });
        }}
        onContactDialogCancel={() => {
          NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
            element_id: "pop3050001",
            element_name: "cancel",
            page_name: "List of questions page",
            page_id: "852",
            extra: {
              pop_id: "pop30500",
              pop_name: "contact cs"
            }
          });
        }}
      />
    );
  };

  renderNavigationBar = () => {
    return <NavigationBar title={this.props.t("FAQ分类")} />;
  };

  render() {
    const {
      navigation,
      store: { pageStore, navParams, runtime },
      t
    } = this.props;
    this.renderNavigationBar();
    if (pageStore.loading) {
      return <Loading />;
    }
    if (pageStore.networkDown) {
      return (
        <NetworkErrorComponent
          message={t("global:啊，网络故障导致我们的距离好远啊!")}
          onRetryPress={() => {
            pageStore.fetchFaqList(navParams.categoryId);
          }}
          buttonText={t("global:刷新")}
        />
      );
    }
    return (
      <View style={styles.container}>
        {this.renderNavigationBar()}
        <FlatList
          data={toJS(pageStore.items)}
          renderItem={this._renderItem}
          keyExtractor={(item: item, index: number) => String(index) + item.id}
          style={{ marginBottom: 57, backgroundColor: "#ffffff" }}
        />
        {this.renderBottom()}
      </View>
    );
  }
}
