import { action, observable, toJS } from "mobx";
import Basic from "common/store/Basic";
import ServiceInfo from "../../serviceInfo";

export type item = {
  id: number;
  title: string;
  content: string;
  categoryId: number;
  question: string;
};

const FAQ_QUESTION_LIST = "/capi/personal/public/faq-center/faq-question/list";
const CUSTOM_SERVICE_INFO = "/capi/personal/public/faq-center/customer-services/get";

export default class Store extends Basic {
  @observable items: Array<item> = [];

  @observable faq = "";

  @observable loading = true;

  @observable networkDown = false;

  @observable serviceTime = "";

  @observable servicePhone = "";

  @observable serviceEmail = "";

  @action
  setItemDatas(faq: string) {
    this.faq = faq;
  }

  @action
  async fetchFaqList(categoryId: number) {
    this.loading = true;
    try {
      const json = JSON.stringify({ categoryId });
      const response = await this.io.postJson(FAQ_QUESTION_LIST, json, { baseUrlType: "1" });
      this.items = response.data;
      this.networkDown = false;
    } catch (e) {
      this.networkDown = true;
    } finally {
      this.loading = false;
    }
  }

  @action
  async fetchServiceInfo() {
    try {
      const response = await this.io.post(CUSTOM_SERVICE_INFO, "");
      if (response.success) {
        ServiceInfo.serviceTime = response?.data?.serviceTime;
        ServiceInfo.servicePhone = response?.data?.serviceTelPhone;
        ServiceInfo.serviceEmail = response?.data?.serviceEmail;
        this.serviceTime = ServiceInfo.serviceTime;
        this.servicePhone = ServiceInfo.servicePhone;
        this.serviceEmail = ServiceInfo.serviceEmail;
      }
    } catch (e) {}
  }
}
