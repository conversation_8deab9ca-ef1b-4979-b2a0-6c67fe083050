import React, { Component } from "react";
import { withTranslation } from "common/services/i18n";
import { inject, observer } from "mobx-react";
import RootView from "common/components/Layout/RootView";
import { EmitterSubscription, Image, ScrollView, StyleSheet, TouchableOpacity, View } from "react-native";
import { Text } from "native-base";
import images from "../img";
import store, { Category } from "./store";
import NavigationBar from "@akulaku-rn/akui-rn/src/components/NavigationBar";
import Selector from "common/components/Selector/index";
import AKTextInput from "common/components/AKTextInput/index";
import NativeCameraModule from "common/nativeModules/basics/nativeCameraModule/index";
import Dialog, { ButtonSequence } from "@akulaku-rn/akui-rn/src/components/Dialog";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import NativeSensorModule, { CustomParams, SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import NativeActionLogModule from "common/nativeModules/basics/nativeActionLogModule";
import AkuNativeEventEmitter from "common/nativeModules/emitter/nativeEventEmitter";
import { NativeConfigModule } from "common/nativeModules";
import { Android } from "@akulaku-rn/akui-rn";
import NativeLoganModule from "common/nativeModules/basics/NativeLoganModule";
import { Type } from "common/components/BaseContainer";
import { reportClick } from "@akulaku-rn/rn-v4-sdk";
import AKButton, { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";

type States = {
  caretHidden: boolean;
};

const styles = StyleSheet.create({
  rowContainer: {
    height: 61,
    flexDirection: "row",
    alignItems: "center",
    paddingLeft: 36
  },
  necIcon: {
    width: 12,
    height: 12,
    position: "absolute",
    left: 16,
    bottom: 15
  }
});

const MAX_UPLOAD_IMAGE = 3;

@RootView({
  withI18n: [
    "feedBackScreen",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json"),
      ms: require("../i18n/ms.json")
    }
  ],
  store,
  keyApi: ["/capi/personal/public/faq-center/feedback-category/list"]
})
@withTranslation("feedBackScreen")
@inject("store")
@observer
export default class FeedbackScreen extends Component<Type.Props<store>, States> {
  userNameInputRef?: AKTextInput;

  emailInputRef?: AKTextInput;

  orderInputRef?: AKTextInput;

  descInputRef?: AKTextInput;

  sensorLogger: (type: SensorType, customParams: CustomParams) => void;

  screenEventListener: EmitterSubscription;

  constructor(props: Type.Props<store>) {
    super(props);
    this.state = {
      caretHidden: false
    };
    this.sensorLogger = NativeSensorModule.initPageParams({
      page_id: "854",
      page_name: "feedback Page",
      module_id: "01",
      module_name: "feedback"
    });
    this.props.configSensorEvent({
      page_id: "854",
      page_name: "feedback Page"
    });
    this.screenEventListener = AkuNativeEventEmitter.addListener(props.store.navParams.screen, event => {
      if (event.eventName === "onEnter") {
        NativeActionLogModule.reportEnter({
          screenNum: "100180"
        });
      } else if (event.eventName === "onLeave") {
        NativeActionLogModule.reportLeave({
          screenNum: "100180"
        });
      }
    });
    //v4埋点
    props.configPageInfo({ sn: 100180 }, true);
  }

  async componentDidMount() {
    const {
      store: { pageStore, runtime }
    } = this.props;
    try {
      pageStore.getUserInfo();
      pageStore.loadQuestionCategory({ countryId: Number(runtime.countryId) });
    } catch (e) {}
    const {
      data: { deviceBrand, osVersionCode }
    } = await NativeConfigModule.getEnvInfoSilent();
    this.setState({
      caretHidden: deviceBrand === "Xiaomi" && osVersionCode === 29
    });
  }

  componentWillUnmount(): void {
    this.screenEventListener.remove();
  }

  renderPicker() {
    const { pageStore } = this.props.store;
    const { t } = this.props;
    return (
      <View style={styles.rowContainer}>
        <Image source={images.icon_necessary} style={styles.necIcon} />
        <Selector
          placeHolder={t("请选择问题类别")}
          selectContents={pageStore.categoryArr.map((category: Category) => (!!category ? category.category : ""))}
          onSelect={({ content, index }: { content: string; index: number }) => {
            pageStore.setCategory(pageStore.categoryArr[index]);
          }}
          onShow={() => {
            if (Android) {
              // 因为Modal问题，处理Android端需要手动清除焦点
              this.userNameInputRef && this.userNameInputRef.textInput && this.userNameInputRef.textInput.blur();
              this.emailInputRef && this.emailInputRef.textInput && this.emailInputRef.textInput.blur();
              this.orderInputRef && this.orderInputRef.textInput && this.orderInputRef.textInput.blur();
            }
          }}
          popWindowTitle={t("请选择")}
        />
      </View>
    );
  }

  renderUserName() {
    const { pageStore } = this.props.store;
    const { caretHidden } = this.state;
    return (
      <View style={styles.rowContainer}>
        <Image source={images.icon_necessary} style={styles.necIcon} />
        <AKTextInput
          caretHidden={caretHidden}
          maxTextLen={50}
          ref={(comp: AKTextInput) => (this.userNameInputRef = comp)}
          placeholder={this.props.t("请输入姓名")}
          onChangeText={(userName: string) => pageStore.setUserName(userName)}
          defaultValue={pageStore.defaultUserName}
        />
      </View>
    );
  }

  renderEmail() {
    const { pageStore } = this.props.store;
    return (
      <View style={styles.rowContainer}>
        <Image source={images.icon_necessary} style={styles.necIcon} />
        <AKTextInput
          maxTextLen={100}
          ref={(comp: AKTextInput) => (this.emailInputRef = comp)}
          placeholder={this.props.t("请输入邮箱地址")}
          onChangeText={(email: string) => pageStore.setEmail(email)}
          defaultValue={pageStore.defaultEmail}
        />
      </View>
    );
  }

  renderOrderNumberInput() {
    const {
      store: { pageStore },
      t
    } = this.props;
    return (
      <View style={styles.rowContainer}>
        <View style={styles.necIcon} />
        <AKTextInput
          maxTextLen={30}
          keyboardType={"numeric"}
          ref={(comp: AKTextInput) => (this.orderInputRef = comp)}
          placeholder={t("请输入订单号（如果您有）")}
          onChangeText={(orderNumber: string) => pageStore.setOrderNumber(orderNumber)}
        />
      </View>
    );
  }

  renderDesc() {
    const {
      store: { pageStore },
      t
    } = this.props;
    return (
      <View style={{ height: 160, flexDirection: "row", paddingLeft: 36 }}>
        <Image
          source={images.icon_necessary}
          style={{
            width: 12,
            height: 12,
            position: "absolute",
            left: 16,
            top: 18
          }}
        />
        <AKTextInput
          maxTextLen={1000}
          style={{ marginTop: 10 }}
          ref={(comp: AKTextInput) => (this.orderInputRef = comp)}
          placeholder={t("请详细描述您的问题，以便我们能够更快速的为您们解决问题")}
          onChangeText={(desc: string) => pageStore.setDesc(desc)}
          multiline={true}
        />
      </View>
    );
  }

  renderUploadView() {
    const {
      store: { pageStore },
      t
    } = this.props;
    return (
      <View style={{ marginLeft: 38, marginTop: 12, backgroundColor: "white", paddingBottom: 10 }}>
        <View style={{ flexDirection: "row", flexWrap: "wrap", marginLeft: 2 }}>
          {pageStore.images.map((base64: string, index: number) => (
            <View style={{ paddingTop: 10, paddingRight: 10, marginTop: -10, marginRight: -10 }} key={index}>
              <View
                style={{
                  borderWidth: 1,
                  borderColor: "#d9d9d9",
                  borderRadius: 5,
                  width: 60,
                  height: 60,
                  marginRight: 20,
                  marginBottom: 10
                }}
              >
                <Image style={{ width: 58, height: 58, resizeMode: "contain" }} source={{ uri: base64 }} />
              </View>
              <TouchableOpacity
                onPress={() => {
                  Dialog.show({
                    desc: t("你确定要删除这张照片吗"),
                    onPositivePress: () => {
                      pageStore.deleteUploadImage(index);
                    },
                    onNegativePress: () => {},
                    buttonSequence: ButtonSequence.NegativeFirst,
                    positiveText: t("删除"),
                    negativeText: t("取消")
                  });
                }}
                style={{
                  width: 30,
                  height: 30,
                  justifyContent: "center",
                  alignItems: "center",
                  position: "absolute",
                  right: 16,
                  top: -5
                }}
              >
                <Image source={images.icon_delete_img} style={{ width: 12, height: 12 }} />
              </TouchableOpacity>
            </View>
          ))}
          {pageStore.uploadImages.length >= MAX_UPLOAD_IMAGE ? null : (
            <TouchableOpacity
              style={{ marginBottom: 10 }}
              onPress={async () => {
                try {
                  const image = await NativeCameraModule.takePhoto();
                  pageStore.addImage(image);
                } catch (e) {
                  console.log(e);
                }
              }}
            >
              <Image source={images.icon_upload} style={{ width: 60, height: 60 }} />
            </TouchableOpacity>
          )}
        </View>
        <Text style={{ color: "#999999", fontSize: 14 }}>{t("支持jpgpng格式文件")}</Text>
      </View>
    );
  }

  showToast(message: string) {
    Toast.show(message, {
      position: 0,
      opacity: 0.6
    });
  }

  checkInputIsValid = () => {
    const {
      store: { pageStore },
      t
    } = this.props;
    console.log("username:", pageStore.userName);
    if (pageStore.userName.length > 50) {
      this.showToast(t("姓名最大50位"));
      return false;
    }
    if (pageStore.email.length > 30) {
      this.showToast(t("邮箱最大30位"));
      return false;
    }
    if (pageStore.orderNumber.length > 30) {
      this.showToast(t("订单号最大30位"));
      return false;
    }
    return true;
  };

  renderBottomView() {
    const {
      store: { pageStore, runtime },
      t,
      navigation
    } = this.props;
    return (
      <View
        key={"bottom"}
        style={{
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: "transparent"
        }}
      >
        <Image
          source={require("../img/shadow1.webp")}
          resizeMode="stretch"
          style={{
            width: "100%",
            height: 10
          }}
        />
        <View style={{ height: 72, backgroundColor: "white", paddingTop: 12, paddingHorizontal: 16 }}>
          <AKButton
            disabled={pageStore.submitDisabled}
            text={t("提交")}
            type={AKButtonType.B1_1_2}
            onPress={async () => {
              if (this.checkInputIsValid() === false) {
                return;
              }
              Loading.show();
              try {
                this.sensorLogger(SensorType.CLICK, {
                  element_id: "8540101",
                  element_name: "submit",
                  position_id: "01"
                });
                NativeActionLogModule.reportClick({
                  controlNum: 1,
                  screenNum: "100180"
                });
                //v4埋点
                reportClick({
                  cn: 1,
                  bct: 17
                });
                console.log("v4埋点");
                await pageStore.submit();
                NativeLoganModule.promotSendLogan();
                // @ts-ignore
                Toast.show(
                  <View style={{ padding: 13, alignItems: "center" }}>
                    <Image source={images.icon_success} style={{ width: 32, height: 32, resizeMode: "stretch" }} />
                    <Text
                      style={{
                        color: "#ffffff",
                        textAlign: "center",
                        marginTop: 10
                      }}
                    >
                      {t("您的反馈我们已收到，我们会尽快与您联系")}
                    </Text>
                  </View>,
                  {
                    position: 0,
                    opacity: 0.6
                  }
                );
                setTimeout(() => {
                  navigation.popTo(2);
                }, 2000);
              } catch (e) {
                if (e.message === "upload failed") {
                  Toast.show(
                    <View style={{ padding: 13, alignItems: "center" }}>
                      <Image source={images.icon_fail} style={{ width: 32, height: 32, resizeMode: "stretch" }} />
                      <Text
                        style={{
                          color: "#ffffff",
                          textAlign: "center",
                          marginTop: 10
                        }}
                      >
                        {t("图片上传失败，请重试")}
                      </Text>
                    </View>,
                    {
                      position: 0,
                      opacity: 0.6
                    }
                  );
                } else {
                  this.showToast(e.message);
                }
                console.log(e.message || e.code);
              } finally {
                Loading.dismiss();
              }
            }}
          />
        </View>
      </View>
    );
  }

  render() {
    const {
      navigation,
      store: { pageStore, navParams, runtime },
      t
    } = this.props;
    console.log("--------feedback render");
    let content = null;
    if (pageStore.loading) {
      content = <Loading />;
    } else {
      content = [
        <ScrollView
          key={"scrollview"}
          style={{ backgroundColor: "#ffffff" }}
          contentContainerStyle={{ minHeight: 680 }}
          // keyboardShouldPersistTaps={"always"}
        >
          {this.renderPicker()}
          {this.renderUserName()}
          {this.renderEmail()}
          {this.renderOrderNumberInput()}
          {this.renderDesc()}
          {this.renderUploadView()}
        </ScrollView>,
        this.renderBottomView()
      ];
    }
    return (
      <View style={{ flex: 1 }}>
        <NavigationBar title={t("feedback")} />
        {content}
      </View>
    );
  }
}
