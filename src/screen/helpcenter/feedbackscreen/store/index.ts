import { NetworkResponse } from "common/nativeModules/network/nativeNetworkModule/index";
import { action, observable, toJS } from "mobx";
import Basic from "common/store/Basic";
import NativeUserInfoModule from "common/nativeModules/bussinse/nativeUserInfoModule";
import { Android } from "@akulaku-rn/akui-rn";

const FEEDBACK_SUBMIT = "/capi/personal/user/faq-center/feedBack/submit";
const QUESTION_CATEGORY = "/capi/personal/public/faq-center/feedback-category/list";

export type Category = {
  countryId: number;
  id: number;
  title: string;
  category: string;
  email: string; //客服邮箱
} | null;

export default class FeedbackStore extends Basic {
  @observable category: Category = null;

  @observable categoryArr: Array<Category> = [];

  @observable submitDisabled = true;

  userName = "";

  @observable defaultUserName = "";

  email = "";

  @observable defaultEmail = "";

  orderNumber = "";

  desc = "";

  @observable images: Array<string> = [];

  @observable loading = true;

  uploadImages: Array<string> = [];
  // @observable windowHeight: number = WINDOW_HEIGHT;

  @action
  setUserName(userName: string): void {
    this.userName = userName;
    this._validSubmitDisabled();
  }

  @action
  setEmail(email: string): void {
    this.email = email;
    this._validSubmitDisabled();
  }

  @action
  setOrderNumber(orderNumber: string): void {
    this.orderNumber = orderNumber;
  }

  @action
  setDesc(desc: string): void {
    this.desc = desc;
    this._validSubmitDisabled();
  }

  @action
  setCategory(category: Category): void {
    this.category = category;
    this._validSubmitDisabled();
  }

  @action
  deleteUploadImage(index: number) {
    this.images.splice(index, 1);
    this.uploadImages = Array.from(this.uploadImages);
    this.uploadImages.splice(index, 1);
  }

  @action
  addImage(uri: string) {
    if (uri) {
      // iOS回传base64，Android回传绝对路径
      this.uploadImages = this.uploadImages.concat(uri);
      this.images.push(Android ? "file://" + uri : "data:image/png;base64," + uri);
    }
  }

  @action
  async loadQuestionCategory(params: object) {
    try {
      const res = await this.io.post(QUESTION_CATEGORY, params);
      this.categoryArr = res.data;
    } catch (e) {
    } finally {
      this.loading = false;
    }
  }

  @action
  async submit() {
    let uploadRes: NetworkResponse;
    let files: Array<string> = [];
    if (this.uploadImages.length > 0) {
      console.log(`---------upload image, type: user:feedback:question, length: ${this.uploadImages.length}`);
      try {
        // uploadRes = await this.io.uploadFilesToVendorServer("6", this.uploadImages);
        uploadRes = await this.io.uploadImgs({
          type: 6,
          base64: this.uploadImages,
          filePaths: this.uploadImages
        });
      } catch (e) {
        console.log(e);
        throw new Error("upload failed");
      }
      console.log("---------upload result", uploadRes);
      if (uploadRes.success) {
        files = uploadRes.data.uploadInfo.map((info: { cdnUrl: string }) => {
          return info.cdnUrl;
        });
      } else {
        throw new Error("upload failed");
      }
    }
    const submitParams = {
      feedbackUser: this.userName,
      feedbackEmail: this.email,
      feedbackOrderNo: this.orderNumber,
      categoryID: this.category && this.category.id,
      category: this.category && this.category.category,
      email: this.category && this.category.email,
      feedbackContent: this.desc
    };
    if (files.length > 0) {
      Object.assign(submitParams, { keys: files });
    }
    await this.io.post(FEEDBACK_SUBMIT, submitParams);
  }

  @action
  async getUserInfo() {
    let userInfo = await NativeUserInfoModule.getUserInfoForceServer();
    if (typeof userInfo === "string") userInfo = JSON.parse(userInfo);
    this.defaultUserName = userInfo.data?.realName ?? "";
    this.userName = this.defaultUserName;
    this.defaultEmail = userInfo.data?.email ?? "";
    this.email = this.defaultEmail;
  }

  _validSubmitDisabled() {
    this.submitDisabled = !(this.userName && this.category && this.email && this.desc);
  }
}
