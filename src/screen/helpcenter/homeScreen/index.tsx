import React, { Component } from "react";
import NavigationBar from "@akulaku-rn/akui-rn/src/components/NavigationBar";
import {
  FlatList,
  Image,
  TouchableHighlight,
  TouchableOpacity,
  TextInput,
  EmitterSubscription,
  BackHandler,
  NativeEventSubscription
} from "react-native";
import { Container, Text, View } from "native-base";
import { withTranslation } from "common/services/i18n/index";
import { inject, observer, Observer } from "mobx-react";
import RootView from "common/components/Layout/RootView";
import store, { FAQCategory } from "./store";
import RecommendComponent from "./component/RecommendComponent";
import images from "../img";
import { toJS } from "mobx";
import styles from "./styles";
import Loading from "@akulaku-rn/akui-rn/src/components/Loading";
import CustomServiceComponent from "./component/CustomServiceComponent";
import AkuNativeEventEmitter from "common/nativeModules/emitter/nativeEventEmitter";
import NativeActionLogModule from "common/nativeModules/basics/nativeActionLogModule";
import UrlImage from "@akulaku-rn/akui-rn/src/components/UrlImage";
import { CenterViewContainer, DialogContainer } from "common/components/DialogContainer";
import { Android, NetworkErrorComponent } from "@akulaku-rn/akui-rn";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import { NativeConfigModule, NativeNavigationModule } from "common/nativeModules";
import { Type } from "common/components/BaseContainer";
import { DevScreenName } from "../../../split-package-config";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import AkListComponent, { AkListType } from "@akulaku-rn/akui-rn/src/components/AKListComponent";

type States = {
  caretHidden: boolean;
};

enum SourceType {
  FreezeResult = "FreezeResult", //冻结结果页跳转这里做特殊处理
  UnfreezeResult = "UnfreezeResult" //解冻结果页跳转这里做特殊处理
}

@RootView({
  withI18n: [
    "helpCenterHome",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json"),
      ms: require("../i18n/ms.json")
    }
  ],
  store,
  keyApi: ["/capi/personal/public/faq-center/recommend-question/list"]
})
@withTranslation("helpCenterHome")
@inject("store")
@observer
export default class HelpCenterHomeScreen extends Component<Type.Props<store>, States> {
  screenEventListener: EmitterSubscription;

  inputText: string | number;

  backHandlerListener: NativeEventSubscription | undefined;

  constructor(props: Type.Props<store>) {
    super(props);
    this.inputText = "";
    this.state = {
      caretHidden: false
    };
    this.props.configSensorEvent({
      page_id: "851",
      page_name: "Help center homepage"
    });
    this.screenEventListener = AkuNativeEventEmitter.addListener(props.store.navParams.screen, event => {
      if (event.eventName === "onEnter") {
        NativeActionLogModule.reportEnter({
          screenNum: "100158"
        });
      } else if (event.eventName === "onLeave") {
        NativeActionLogModule.reportLeave({
          screenNum: "100158"
        });
      }
    });
    //v4埋点
    props.configPageInfo({ sn: 100158 }, true);
  }

  async componentDidMount() {
    this.fetchData();
    const {
      data: { deviceBrand, osVersionCode }
    } = await NativeConfigModule.getEnvInfoSilent();
    this.setState({
      caretHidden: deviceBrand === "Xiaomi" && osVersionCode === 29
    });
    if (Android) {
      this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
    }
  }

  componentWillUnmount(): void {
    this.screenEventListener.remove();
    this.backHandlerListener?.remove();
  }

  onBackPress = () => {
    const {
      store: {
        navParams: { source }
      }
    } = this.props;
    if (source === SourceType.FreezeResult || source === SourceType.UnfreezeResult) {
      NativeNavigationModule.popToHome(3);
    } else {
      NativeNavigationModule.goBack();
    }
    return true;
  };

  navigationBarClickTime = 0;

  fetchData = () => {
    const {
      store: { pageStore, runtime }
    } = this.props;
    pageStore.fetchSelfService(runtime.countryId);
    pageStore.fetchFaqs(runtime.countryId);
    pageStore.fetchServiceInfo();
  };

  onMenuSelected = (index: number) => {
    const {
      store: { pageStore },
      navigation
    } = this.props;
    const url = pageStore.menuInfos[index].url;
    const title = pageStore.menuInfos[index].title;
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "8510102",
      element_name: "recommended",
      page_id: "851",
      page_name: "Help center homepage",
      module_id: "01",
      module_name: "help center",
      position_id: "02",
      extra: {
        Aku_buttonName: title,
        positionid: index
      }
    });
    navigation.navigate({
      url
    });
  };

  entryPress = () => {
    if (this.inputText === "123") {
      this.jumpToRnSetting();
      this.inputText = 0;
    } else {
      Toast.show("password error!", { position: Toast.positions.CENTER });
    }
  };

  renderHeader() {
    const { t } = this.props;
    const { caretHidden } = this.state;
    return (
      <>
        <NavigationBar title={t("帮助中心")} onBackPress={this.onBackPress} />
        <Text
          style={styles.entryText}
          onPress={() => {
            this.navigationBarClickTime = this.navigationBarClickTime + 1;
            if (this.navigationBarClickTime % 5 === 0) {
              DialogContainer.show({
                renderContent: (
                  <CenterViewContainer>
                    <Text style={styles.entryDesc}>This function is only for the developer</Text>
                    <View style={styles.entryInputContainer}>
                      <TextInput
                        style={styles.entryInput}
                        caretHidden={caretHidden}
                        onChangeText={text => (this.inputText = text)}
                        placeholder="enter password"
                        placeholderTextColor="#999"
                      />
                    </View>
                    <View style={{ flexDirection: "row", height: 50, alignItems: "center" }}>
                      <Text
                        style={{
                          flex: 1,
                          textAlign: "center"
                        }}
                        onPress={() => {
                          DialogContainer.dismiss();
                        }}
                      >
                        Cancel
                      </Text>
                      <Text
                        style={{
                          flex: 1,
                          textAlign: "center"
                        }}
                        onPress={this.entryPress}
                      >
                        Confirm
                      </Text>
                    </View>
                  </CenterViewContainer>
                )
              });
            }
          }}
        />
      </>
    );
  }

  private async jumpToRnSetting() {
    await DialogContainer.dismiss();
    this.props.navigation.navigate({
      screen: DevScreenName,
      params: {}
    });
  }

  renderBigTitle(text: string, style?: Record<string, number>) {
    return <Text style={[styles.bigTitle, style]}>{text}</Text>;
  }

  renderRecommendService = () => {
    const { navigation, t } = this.props;
    return (
      <View style={styles.recommendContainer}>
        {this.renderBigTitle(t("推荐自助服务"))}
        <View style={{ marginTop: 20 }}>
          <RecommendComponent navigation={navigation} onMenuSelected={this.onMenuSelected} />
        </View>
        <View style={styles.border}></View>
        {this.renderBigTitle(t("FAQ"), { marginBottom: 12 })}
      </View>
    );
  };

  renderItem = ({ item, index }: { item: FAQCategory; index: number }) => {
    const { navigation } = this.props;
    return (
      <AkListComponent
        key={index}
        type={AkListType.G7_1_1}
        title={item.categoryName}
        image={item.categoryIcon}
        isNetworkImage={true}
        onPress={() => {
          NativeSensorModule.sensorLogger(SensorType.CLICK, {
            element_id: "8510103",
            element_name: "faq",
            page_id: "851",
            page_name: "Help center homepage",
            module_id: "01",
            module_name: "help center",
            position_id: "03",
            extra: {
              Aku_buttonName: item?.categoryName,
              positionid: index
            }
          });
          navigation.navigate({
            screen: "helpCenterQuestions",
            params: {
              faq: item.categoryName,
              categoryId: item.categoryID
            }
          });
        }}
      />
    );
  };

  renderFooter = () => {
    return <View style={{ height: 24 }} />;
  };

  renderList = () => {
    const {
      store: { pageStore }
    } = this.props;
    return (
      <Observer>
        {() => (
          <FlatList
            overScrollMode={"never"}
            style={{ marginBottom: 57, backgroundColor: "#ffffff" }}
            data={toJS(pageStore.faqItems)}
            renderItem={this.renderItem}
            ListHeaderComponent={this.renderRecommendService()}
            keyExtractor={(item: FAQCategory, index) => item.categoryName + index}
            ListFooterComponent={this.renderFooter()}
          />
        )}
      </Observer>
    );
  };

  onLiveChatClick = () => {
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "8510104",
      element_name: "livechat",
      page_id: "851",
      page_name: "Help center homepage",
      module_id: "01",
      module_name: "help center",
      position_id: "04"
    });
  };

  renderBottom = () => {
    const {
      store: { pageStore }
    } = this.props;
    return (
      <CustomServiceComponent
        t={this.props.t}
        countryCode={this.props.store.runtime.countryCode}
        source={"home"}
        serviceTel={pageStore.servicePhone}
        serviceTime={pageStore.serviceTime}
        serviceEmail={pageStore.serviceEmail}
        onLiveChatClick={this.onLiveChatClick}
        onContactDialogShow={() => {
          NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
            page_name: "Help center homepage",
            page_id: "851",
            extra: {
              pop_id: "pop30500",
              pop_name: "contact cs"
            }
          });
        }}
        onContactDialogConfirm={() => {
          NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
            element_id: "pop3050002",
            element_name: "call",
            page_name: "Help center homepage",
            page_id: "851",
            extra: {
              pop_id: "pop30500",
              pop_name: "contact cs"
            }
          });
        }}
        onContactDialogCancel={() => {
          NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
            element_id: "pop3050001",
            element_name: "cancel",
            page_name: "Help center homepage",
            page_id: "851",
            extra: {
              pop_id: "pop30500",
              pop_name: "contact cs"
            }
          });
        }}
      />
    );
  };

  render() {
    const {
      store: { pageStore },
      t
    } = this.props;
    return (
      <Container style={styles.container}>
        {this.renderHeader()}
        {pageStore.refreshing ? (
          <Loading />
        ) : pageStore.isNetworkDown ? (
          <NetworkErrorComponent
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            onRetryPress={() => {
              this.fetchData();
            }}
            buttonText={t("global:刷新")}
          />
        ) : (
          [this.renderList(), this.renderBottom()]
        )}
      </Container>
    );
  }
}
