import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { StatusBar, StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff"
  },
  recommendContainer: {
    backgroundColor: "white"
  },
  bigTitle: {
    marginTop: 20,
    marginLeft: 12,
    color: "#010101",
    fontSize: 16,
    fontWeight: "500"
  },
  border: {
    height: 8,
    backgroundColor: "#f5f5f5"
  },
  item: {
    paddingLeft: 16,
    paddingRight: 12,
    paddingVertical: 15,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end"
  },
  itemIcon: {
    width: 24,
    height: 24
  },
  itemTitle: {
    width: WINDOW_WIDTH - 100,
    marginLeft: 12,
    fontSize: 14,
    color: "#333333"
  },
  itemArrow: {
    width: 16,
    height: 16
  },
  sepLine: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: "#ebebeb",
    marginLeft: 12
  },
  bottomView: {
    position: "absolute",
    height: 66,
    backgroundColor: "transparent",
    bottom: 0,
    left: 0,
    width: WINDOW_WIDTH
    // alignItems: 'center',
  },
  bottomViewBtn: {
    flex: 1,
    backgroundColor: "#ffffff"
  },
  entryText: {
    width: 100,
    height: 100,
    position: "absolute",
    right: 0,
    top: StatusBar.currentHeight
  },
  entryDesc: {
    width: "70%",
    height: 60,
    textAlign: "center",
    alignSelf: "center",
    color: "#000",
    top: 20
  },
  entryInputContainer: {
    backgroundColor: "#fff",
    paddingLeft: 2,
    // paddingRight: 22,
    height: 72,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottomColor: "#f5f5f5",
    borderBottomWidth: 1
  },
  entryInput: {
    width: "100%",
    fontSize: 14,
    color: "#999",
    alignSelf: "center",
    height: 50
  }
});
