import { iOS } from "@akulaku-rn/akui-rn";
import { action, observable, toJS } from "mobx";
import Basic from "common/store/Basic";
import ServiceInfo from "../../serviceInfo";

export type SelfService = {
  id: number;
  title: string;
  url: string;
  status: number;
  type: number;
  sort: number;
  icon: string;
  question: string;
  countryId: null;
};

export type FAQCategory = {
  categoryID: number;
  categoryName: string;
  categoryIcon: string;
};

const RECOMMEND_QUESTION_LIST = "/capi/personal/public/faq-center/recommend-question/list";
const QUESTION_CATEGORY_LIST = "/capi/personal/public/faq-center/question-category/list";
const CUSTOM_SERVICE_INFO = "/capi/personal/public/faq-center/customer-services/get";

export default class HelpCenterHomeStore extends Basic {
  @observable menuInfos: Array<SelfService> = [];

  @observable faqItems: Array<FAQCategory> = [];

  @observable isNetworkDown = false;

  @observable refreshing = true;

  @observable serviceTime = "";

  @observable servicePhone = "";

  @observable serviceEmail = "";

  @action
  async fetchSelfService(countryId: number) {
    this.refreshing = true;
    try {
      const response = await this.io.post(RECOMMEND_QUESTION_LIST, "");
      this.menuInfos = [];
      if (iOS) {
        //ios 的Pengkinian Data功能原生部分还没做，暂时屏蔽
        response.data.forEach((data: SelfService) => {
          if (data.title !== "Pengkinian Data") {
            this.menuInfos.push(data);
          }
        });
      } else {
        this.menuInfos = response.data;
      }

      this.isNetworkDown = false;
    } catch (e) {
      this.isNetworkDown = true;
      throw e;
    } finally {
      this.refreshing = false;
    }
  }

  @action
  async fetchServiceInfo() {
    try {
      const response = await this.io.post(CUSTOM_SERVICE_INFO, "");
      if (response.success) {
        ServiceInfo.serviceTime = response.data && response.data.serviceTime;
        ServiceInfo.servicePhone = response.data && response.data.serviceTelPhone;
        ServiceInfo.serviceEmail = response.data && response.data.serviceEmail;
        this.serviceEmail = ServiceInfo.serviceEmail;
        this.servicePhone = ServiceInfo.servicePhone;
        this.serviceTime = ServiceInfo.serviceTime;
      }
    } catch (e) {}
  }

  @action
  async fetchFaqs(countryId: number) {
    try {
      const response = await this.io.postJson(QUESTION_CATEGORY_LIST, "");
      this.faqItems = response.data;
    } catch (e) {
      throw e;
    }
  }
}
