import React, { Component } from "react";
import { TouchableOpacity, StyleSheet, ScrollView, NativeSyntheticEvent, NativeScrollEvent } from "react-native";
import { Button, Text, View, Container } from "native-base";
import RecommendMenuComponent from "./RecommendMenuComponent";
import { inject, observer } from "mobx-react";
import { withTranslation } from "common/services/i18n";
import PageControl from "react-native-page-control";
import store, { SelfService } from "../store";
import { pageStoreModel, navigationModel } from "common/components/BaseContainer/Type";
import { WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

type Props1 = {
  navigation: navigationModel;
  onMenuSelected: Function;
  store?: pageStoreModel<store>;
};

type State = {
  currentPage: number;
};

const styles = StyleSheet.create({
  itemsView: {
    paddingHorizontal: 12,
    flexDirection: "row",
    flexWrap: "wrap",
    width: WINDOW_WIDTH
  },
  menuContainer: {
    flexDirection: "row"
  },
  pageControl: {
    marginVertical: 10
  },
  redDot: {
    borderRadius: 1,
    height: 2,
    width: 12,
    backgroundColor: "#e62117",
    marginLeft: 1,
    marginRight: 0
  },
  dot: {
    borderRadius: 1,
    height: 2,
    width: 12,
    backgroundColor: "#ebebeb",
    marginLeft: 1,
    marginRight: 1
  }
});

@inject("store")
@observer
export default class RecommendComponent extends Component<Props1, State> {
  constructor(props: Props1) {
    super(props);
    this.state = {
      currentPage: 0
    };
  }

  onScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffsetX = e.nativeEvent.contentOffset.x;

    const currentPage = Math.round(contentOffsetX / WINDOW_WIDTH);
    if (currentPage !== this.state.currentPage) {
      this.setState({
        currentPage
      });
    }
  };

  render() {
    const { store, onMenuSelected } = this.props;
    const menuItems =
      store &&
      store.pageStore &&
      store.pageStore.menuInfos.map((menu: SelfService, index: number) => {
        return (
          <RecommendMenuComponent
            onPress={() => {
              onMenuSelected && onMenuSelected(index);
            }}
            icon={menu.icon}
            title={menu.question}
            key={index}
          />
        );
      });
    const menuViews = [];
    const pageCount = !!menuItems && Math.ceil(menuItems.length / 8);
    for (let i = 0; i < pageCount; i++) {
      const menus = menuItems && menuItems.slice(i * 8, i * 8 + 8);
      const menuView = (
        <View style={styles.itemsView} key={i}>
          {menus}
        </View>
      );
      menuViews.push(menuView);
    }
    return (
      <View>
        <ScrollView horizontal={true} showsHorizontalScrollIndicator={false} pagingEnabled onScroll={this.onScroll}>
          <View style={styles.menuContainer}>{menuViews}</View>
        </ScrollView>
        <PageControl
          style={styles.pageControl}
          numberOfPages={pageCount}
          currentPage={this.state.currentPage}
          hidesForSinglePage
          pageIndicatorTintColor="#ebebeb"
          currentPageIndicatorTintColor={"#e62117"}
          indicatorStyle={styles.dot}
          currentIndicatorStyle={styles.redDot}
        />
      </View>
    );
  }
}
