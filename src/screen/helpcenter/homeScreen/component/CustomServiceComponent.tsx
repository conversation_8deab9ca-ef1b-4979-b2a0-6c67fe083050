import React, { Component } from "react";
import { Image, View, TouchableOpacity, Text, StyleSheet, Linking, Clipboard } from "react-native";
import Dialog, { ButtonSequence } from "@akulaku-rn/akui-rn/src/components/Dialog";
import ServiceInfo from "../../serviceInfo";
import NativePhoneModule from "common/nativeModules/basics/nativePhoneModule";
import images from "../../img";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import NativeActionLogModule from "common/nativeModules/basics/nativeActionLogModule";
import { TFunction } from "i18next";
import { FigmaStyle, FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { reportClick } from "@akulaku-rn/rn-v4-sdk";
import { GlobalRuntime } from "common/constant";
import { NativeNavigationModule } from "common/nativeModules";

const styles = StyleSheet.create({
  bottomView: {
    position: "absolute",
    height: 66,
    backgroundColor: "transparent",
    bottom: 0,
    left: 0,
    width: WINDOW_WIDTH
    // alignItems: 'center',
  },
  bottomViewBtn: {
    flex: 1,
    backgroundColor: "#ffffff"
  },
  shadow: {
    width: "100%",
    height: 10
  },
  footerBtn: {
    flex: 1,
    flexDirection: "row",
    backgroundColor: "#ffffff"
  },
  line: {
    width: 0.5,
    marginTop: 14,
    marginBottom: 14,
    backgroundColor: "#EBEBEB"
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    height: 78,
    width: "100%"
    // // ios
    // shadowColor: '#000000',
    // shadowOffset: { width: 0, height: -2 },
    // shadowOpacity: 0.08,
    // shadowRadius: 8,
    // // android
    // elevation: 5
  },
  btn: {
    flex: 1,
    alignItems: "center"
  },
  icon: {
    marginTop: 4,
    width: 24,
    height: 24
  },
  text: {
    marginTop: 3,
    fontSize: 14,
    color: FigmaStyle.Color.Grey_Text2,
    fontWeight: "400",
    textAlign: "center"
  }
});

type Props = {
  t: TFunction;
  countryCode: string;
  source: string;
  serviceTime: string;
  serviceTel: string;
  serviceEmail?: string;
  onLiveChatClick?: () => void;
  onContactDialogShow?: () => void;
  onContactDialogConfirm?: () => void;
  onContactDialogCancel?: () => void;
};

export default class CustomServiceComponent extends Component<Props> {
  async onCustomerServicePress() {
    const {
      source,
      serviceEmail,
      serviceTel,
      serviceTime,
      onContactDialogShow,
      onContactDialogConfirm,
      onContactDialogCancel
    } = this.props;
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      page_id: source === "home" ? "851" : "852",
      page_name: source === "home" ? "Help center homepage" : "List of questions page",
      module_id: "01",
      module_name: source === "home" ? "help center" : "questions",
      element_id: source === "home" ? "8510101" : "8520101",
      element_name: "telephone",
      position_id: "01"
    });
    NativeActionLogModule.reportClick({
      controlNum: source === "home" ? 4 : 7,
      screenNum: "100158"
    });
    //v4埋点
    reportClick({
      cn: source === "home" ? 4 : 7,
      bct: 17
    });

    const { t } = this.props;
    if (this.props.countryCode === "PH") {
      if (serviceEmail && serviceEmail.length > 0) {
        const url = "mailto:" + serviceEmail;

        Linking.canOpenURL(url)
          .then(supported => {
            if (!supported) {
              console.log("Can't handle url: " + url);
              Dialog.show({
                desc: t("请发送邮件", { email: serviceEmail }),
                descStyle: { textAlign: "center" },
                positiveText: t("复制"),
                onNegativePress: () => {
                  Clipboard.setString(serviceEmail);
                }
              });
            } else {
              return Linking.openURL(url);
            }
          })
          .catch(err => console.log("An error occurred", err));
        return;
      } else {
        Toast.show("email error");
        return;
      }
    }

    if (onContactDialogShow) {
      onContactDialogShow();
    }
    Dialog.show({
      desc: t("联系客服") + `(${serviceTime})`,
      descStyle: { textAlign: "center" },
      onPositivePress: () => {
        if (onContactDialogConfirm) {
          onContactDialogConfirm();
        }
        // NativeLoggerModule.sensorLogger(SensorType.CLICK, {
        //   page_id: "190",
        //   page_name: "please select",
        //   module_id: "02",
        //   module_name: "please select",
        //   element_id: "1900201",
        //   element_name: "点击call",
        //   position_id: "01"
        // });
        NativePhoneModule.callCustomerService(ServiceInfo.servicePhone);
      },
      onNegativePress: () => {
        if (onContactDialogCancel) {
          onContactDialogCancel();
        }
        // NativeLoggerModule.sensorLogger(SensorType.CLICK, {
        //   page_id: "190",
        //   page_name: "please select",
        //   module_id: "02",
        //   module_name: "please select",
        //   element_id: "1900202",
        //   element_name: "点击cancel",
        //   position_id: "02"
        // });
      },
      buttonSequence: ButtonSequence.NegativeFirst,
      positiveText: t("拨打"),
      negativeText: t("取消"),
      renderContent: (
        <Text
          style={{
            ...FontStyles["DIN-Bold"],
            fontSize: 20,
            color: "#333"
          }}
        >
          {serviceTel}
        </Text>
      )
    });
  }

  onLiveChatClick = () => {
    const { onLiveChatClick } = this.props;
    if (onLiveChatClick) {
      onLiveChatClick();
    }
    //v4上报
    // reportClick({
    //   cn: 9,
    //   bct: 17
    // });
    console.log("zhangxiao", "GlobalRuntime.isLogin-->" + GlobalRuntime.isLogin);
    if (!GlobalRuntime.isLogin) {
      NativeNavigationModule.navigate({
        url: "ak://m.akulaku.com/36"
      });
      return;
    }
    // NativeActionLogModule.reportClick({
    //   controlNum: 9,
    //   screenNum: "100158"
    // });
    // NativeSensorModule.sensorLogger(SensorType.CLICK, {
    //   element_id: "8530102",
    //   element_name: "feedback",
    //   page_id: "853",
    //   page_name: "Question details page",
    //   module_id: "01",
    //   module_name: "questions",
    //   position_id: "02"
    // });
    // //v4埋点
    // reportClick({
    //   cn: 9,
    //   bct: 17r
    // });
    NativeNavigationModule.navigate({
      url: "ak://m.akulaku.com/1203?shopName=CS AKULAKU&shopId=1&orgId=1&orgAccountType=3"
    });
  };

  render() {
    const { t, serviceTime } = this.props;
    const shadow = (
      <Image style={{ width: WINDOW_WIDTH, height: 10 }} resizeMode="stretch" source={images.icon_shadow} />
    );
    return (
      <View style={styles.footer}>
        <Image source={require("../../img/shadow1.webp")} resizeMode="stretch" style={styles.shadow} />
        <View style={styles.footerBtn}>
          <TouchableOpacity style={styles.btn} onPress={this.onCustomerServicePress.bind(this)}>
            <Image source={require("../../img/contactus.webp")} style={styles.icon} />
            <Text style={styles.text}>{t("联系客服") + `(${serviceTime})`}</Text>
          </TouchableOpacity>
          <View style={styles.line}></View>
          <TouchableOpacity style={styles.btn} onPress={this.onLiveChatClick}>
            <Image source={require("../../img/livechat.webp")} style={styles.icon} />
            <Text style={styles.text}>{t("CS LiveChat") + `(${serviceTime})`}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}
