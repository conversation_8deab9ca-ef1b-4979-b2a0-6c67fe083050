import { UrlImage, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import React, { Component } from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";

type Props = {
  onPress: () => void;
  icon: string;
  title: string;
};

const ITEM_WIDTH = (WINDOW_WIDTH - 24) / 4;
const ITEM_HEIGHT = 80;
const ICON_WIDTH = 44;
const ICON_HEIGHT = 44;

const styles = StyleSheet.create({
  container: {
    height: ITEM_HEIGHT,
    alignItems: "center",
    width: ITEM_WIDTH,
    marginBottom: 20
  },
  icon: {
    width: ICON_WIDTH,
    height: ICON_HEIGHT
  },
  title: {
    color: "#3c3c3c",
    fontSize: 12,
    marginTop: 8,
    textAlign: "center"
  }
});

export default class RecommendMenuComponent extends Component<Props> {
  render() {
    return (
      <TouchableOpacity style={styles.container} onPress={this.props.onPress}>
        <UrlImage style={styles.icon} source={this.props.icon} width={44} />
        <Text style={styles.title}>{this.props.title}</Text>
      </TouchableOpacity>
    );
  }
}
