import React, { Component } from "react";
import { inject, observer } from "mobx-react";
import { Image, Linking, Platform, ScrollView, TouchableOpacity, Clipboard } from "react-native";
import { Container, Text, View } from "native-base";
import { withTranslation } from "common/services/i18n";
import RootView from "common/components/Layout/RootView";

import styles from "./styles";
import store from "./store";
import NativePhoneModule from "common/nativeModules/basics/nativePhoneModule";
import Dialog, { ButtonSequence } from "@akulaku-rn/akui-rn/src/components/Dialog";

import NavigationBar from "@akulaku-rn/akui-rn/src/components/NavigationBar";
import Html from "./components/Html";
import ServiceInfo from "../serviceInfo";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import NativeSensorModule, { ScreenSensorEvent, SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import NativeActionLogModule from "common/nativeModules/basics/nativeActionLogModule";
import NativeNavigationModule from "common/nativeModules/router/nativeNavigationModule";
import { FontStyles, STATUS_BAR_HEIGHT, WINDOW_HEIGHT } from "@akulaku-rn/akui-rn";
import { Type } from "common/components/BaseContainer";
import { reportClick } from "@akulaku-rn/rn-v4-sdk";
import CustomServiceComponent from "../homeScreen/component/CustomServiceComponent";

@RootView({
  withI18n: [
    "helpCenter",
    {
      en: require("../i18n/en.json"),
      in: require("../i18n/in.json"),
      vi: require("../i18n/vi.json"),
      ms: require("../i18n/ms.json")
    }
  ],
  store,
  keyApi: ["/capi/personal/public/faq-center/question-detail/get"]
})
@withTranslation("helpCenter")
@inject("store")
@observer
export default class Faq extends Component<Type.Props<store>, {}> {
  componentDidMount() {
    const {
      runtime,
      pageStore,
      pageStore: { serviceTime = ServiceInfo.serviceTime },
      navParams
    } = this.props.store;
    if (serviceTime === "") {
      pageStore.fetchServiceInfo();
    }
    pageStore.fetchFaqDetail(navParams.id);
    this.props.configSensorEvent({
      page_id: "853",
      page_name: "Question details page"
    });
  }

  onBackPress = () => {
    NativeNavigationModule.goBack();
  };

  onLiveChatClick = () => {
    //v4上报
    reportClick({
      cn: 9,
      bct: 17
    });
    if (this.props.store.user.isLogin === false) {
      this.props.navigation.navigate({
        url: "ak://m.akulaku.com/36"
      });
      return;
    }
    NativeActionLogModule.reportClick({
      controlNum: 9,
      screenNum: "100158"
    });
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "8530103",
      element_name: "livechat",
      page_id: "853",
      page_name: "Question details page",
      module_id: "01",
      module_name: "questions",
      position_id: "03"
    });
    //v4埋点
    reportClick({
      cn: 9,
      bct: 17
    });
    const { navigation } = this.props;
    navigation.navigate({
      url: "ak://m.akulaku.com/1203?shopName=CS AKULAKU&shopId=1&orgId=1&orgAccountType=3"
    });
  };

  onCustomerServicePress = async () => {
    const {
      store: {
        pageStore: {
          serviceTime = ServiceInfo.serviceTime,
          servicePhone = ServiceInfo.servicePhone,
          serviceEmail = ServiceInfo.serviceEmail
        }
      }
    } = this.props;
    NativeSensorModule.sensorLogger(SensorType.CLICK, {
      element_id: "8530101",
      element_name: "telephone",
      page_id: "853",
      page_name: "Question details page",
      module_id: "01",
      module_name: "questions",
      position_id: "01"
    });
    NativeActionLogModule.reportClick({
      controlNum: 8,
      screenNum: "100158"
    });
    //v4埋点
    reportClick({
      cn: 8,
      bct: 17
    });
    const { t } = this.props;
    if (this.props.store.runtime.countryCode === "PH") {
      if (serviceEmail && serviceEmail.length > 0) {
        const url = "mailto:" + serviceEmail;
        Linking.canOpenURL(url)
          .then(supported => {
            if (!supported) {
              console.log("Can't handle url: " + url);
              Dialog.show({
                desc: t("请发送邮件", { email: serviceEmail }),
                descStyle: { textAlign: "center" },
                positiveText: t("复制"),
                onNegativePress: () => {
                  Clipboard.setString(serviceEmail);
                }
              });
            } else {
              return Linking.openURL(url);
            }
          })
          .catch(err => console.log("An error occurred", err));
        return;
      } else {
        Toast.show("email error");
        return;
      }
    }
    Dialog.show({
      desc: t("联系客服") + `(${serviceTime})`,
      descStyle: { textAlign: "center" },
      onPositivePress: () => {
        NativePhoneModule.callCustomerService(ServiceInfo.servicePhone);
      },
      onNegativePress: () => {},
      buttonSequence: ButtonSequence.NegativeFirst,
      positiveText: t("拨打"),
      negativeText: t("取消"),
      renderContent: (
        <Text
          style={{
            ...FontStyles["DIN-Bold"],
            fontSize: 20,
            color: "#333"
          }}
        >
          {servicePhone}
        </Text>
      )
    });
  };

  render() {
    const {
      navigation,
      store: {
        pageStore,
        pageStore: { serviceTime = ServiceInfo.serviceTime },
        navParams,
        runtime
      },
      t
    } = this.props;
    return (
      <Container>
        <NavigationBar title={t("FAQ详情")} onBackPress={this.onBackPress} />
        <ScrollView
          style={styles.container}
          scrollEnabled={Platform.OS === "web" ? true : false}
          contentContainerStyle={{ minHeight: WINDOW_HEIGHT - 60 - STATUS_BAR_HEIGHT }}
          keyboardShouldPersistTaps={"always"}
        >
          <View style={styles.question}>
            <Text style={[styles.questionText, FontStyles["rob-medium"]]}>Q:{pageStore.title}</Text>
          </View>
          <Html style={styles.content} html={pageStore.content} />
        </ScrollView>
        <CustomServiceComponent
          t={this.props.t}
          countryCode={this.props.store.runtime.countryCode}
          source={"home"}
          serviceTel={pageStore.servicePhone}
          serviceTime={pageStore.serviceTime}
          serviceEmail={pageStore.serviceEmail}
          onLiveChatClick={this.onLiveChatClick}
          onContactDialogShow={() => {
            NativeSensorModule.sensorLogger(SensorType.POPVIEW, {
              page_name: "Question details page",
              page_id: "853",
              extra: {
                pop_id: "pop30500",
                pop_name: "contact cs"
              }
            });
          }}
          onContactDialogConfirm={() => {
            NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
              element_id: "pop3050002",
              element_name: "call",
              page_name: "Question details page",
              page_id: "853",
              extra: {
                pop_id: "pop30500",
                pop_name: "contact cs"
              }
            });
          }}
          onContactDialogCancel={() => {
            NativeSensorModule.sensorLogger(SensorType.POPCLICK, {
              element_id: "pop3050001",
              element_name: "cancel",
              page_name: "Question details page",
              page_id: "853",
              extra: {
                pop_id: "pop30500",
                pop_name: "contact cs"
              }
            });
          }}
        />
      </Container>
    );
  }
}
