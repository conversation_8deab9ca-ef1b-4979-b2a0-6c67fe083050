import React, { Component } from "react";
import { Platform } from "react-native";
import { WebView, WebViewMessageEvent } from "react-native-webview";
import { View } from "native-base";

import styles from "./styles";
import { NativeNavigationModule } from "common/nativeModules";

// rn webview 需要用 html 容器初始化样式
const htmlTemplate = (html: any) => `
  <!DOCTYPE html>
  <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
      <meta name="format-detection" content="telephone=no" />
      <style>
        /*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
        html {
          line-height: 1.15; /* 1 */
          -webkit-text-size-adjust: 100%; /* 2 */
          // overflow: hidden;
        }
        body {
          margin: 0;
          // overflow: hidden;
        }
        main {
          display: block;
        }
        h1 {
          font-size: 2em;
          margin: 0.67em 0;
        }
        hr {
          box-sizing: content-box; /* 1 */
          height: 0; /* 1 */
          overflow: visible; /* 2 */
        }
        pre {
          font-family: monospace, monospace; /* 1 */
          font-size: 1em; /* 2 */
        }
        a {
          background-color: transparent;
        }
        abbr[title] {
          border-bottom: none; /* 1 */
          text-decoration: underline; /* 2 */
          text-decoration: underline dotted; /* 2 */
        }
        b,
        strong {
          font-weight: bolder;
        }
        code,
        kbd,
        samp {
          font-family: monospace, monospace; /* 1 */
          font-size: 1em; /* 2 */
        }
        small {
          font-size: 80%;
        }
        sub,
        sup {
          font-size: 75%;
          line-height: 0;
          position: relative;
          vertical-align: baseline;
        }
        sub {
          bottom: -0.25em;
        }
        sup {
          top: -0.5em;
        }
        img {
          border-style: none;
        }
        button,
        input,
        optgroup,
        select,
        textarea {
          font-family: inherit; /* 1 */
          font-size: 100%; /* 1 */
          line-height: 1.15; /* 1 */
          margin: 0; /* 2 */
        }
        button,
        input { /* 1 */
          overflow: visible;
        }
        button,
        select { /* 1 */
          text-transform: none;
        }
        button,
        [type="button"],
        [type="reset"],
        [type="submit"] {
          -webkit-appearance: button;
        }
        button::-moz-focus-inner,
        [type="button"]::-moz-focus-inner,
        [type="reset"]::-moz-focus-inner,
        [type="submit"]::-moz-focus-inner {
          border-style: none;
          padding: 0;
        }
        button:-moz-focusring,
        [type="button"]:-moz-focusring,
        [type="reset"]:-moz-focusring,
        [type="submit"]:-moz-focusring {
          outline: 1px dotted ButtonText;
        }
        fieldset {
          padding: 0.35em 0.75em 0.625em;
        }
        legend {
          box-sizing: border-box; /* 1 */
          color: inherit; /* 2 */
          display: table; /* 1 */
          max-width: 100%; /* 1 */
          padding: 0; /* 3 */
          white-space: normal; /* 1 */
        }
        progress {
          vertical-align: baseline;
        }
        textarea {
          overflow: auto;
        }
        [type="checkbox"],
        [type="radio"] {
          box-sizing: border-box; /* 1 */
          padding: 0; /* 2 */
        }
        [type="number"]::-webkit-inner-spin-button,
        [type="number"]::-webkit-outer-spin-button {
          height: auto;
        }
        [type="search"] {
          -webkit-appearance: textfield; /* 1 */
          outline-offset: -2px; /* 2 */
        }
        [type="search"]::-webkit-search-decoration {
          -webkit-appearance: none;
        }
        ::-webkit-file-upload-button {
          -webkit-appearance: button; /* 1 */
          font: inherit; /* 2 */
        }
        details {
          display: block;
        }
        summary {
          display: list-item;
        }
        template {
          display: none;
        }
        [hidden] {
          display: none;
        }
      </style>
    </head>
    <body>
      ${html}
    </body>
    <script>
    window.onload = function() {
  // 监听图片点击事件
  const images = document.querySelectorAll("img");
  images.forEach(img => {
    img.addEventListener("click", function(event) {
      const videoSrc = (!!event.target.dataset && event.target.dataset.videoUrl) || "";
      if (!videoSrc) {
        const imgSrc = event.target.currentSrc || event.target.src;
        window.ReactNativeWebView.postMessage(
          JSON.stringify({
            type: "image",
            src: imgSrc,
            // poster: 
          })
        );
      } else {
        const videoPoster = event.target.currentSrc || event.target.src;
        window.ReactNativeWebView.postMessage(
          JSON.stringify({
            type: "video",
            src: videoSrc,
            poster: videoPoster
          })
        );
      }
      
    });
  });
};

    </script>
  </html>
`;

type Props = {
  html: string;
  style?: any;
};

// 外层有 view 的话记得给 view 设置 flex: 1
export default class Html extends Component<Props, {}> {
  state: any = {};

  // 处理从 WebView 传回的消息
  handleMessage = (event: WebViewMessageEvent) => {
    const data = JSON.parse(event.nativeEvent.data);
    if (data.type === "video") {
      console.log("点击了视频", `视频链接：${data.src}`);
      NativeNavigationModule.navigate({
        screen: "VideoPage",
        params: {
          uri: data.src
          // poster: data.poster
        }
      });
    } else if (data.type === "image") {
      console.log("点击了图片", `图片链接：${data.src}`);
      // 这里可以打开图片预览
    }
  };

  render() {
    const { html, style } = this.props;

    return (
      <View
        style={[
          styles.webview,
          style,
          this.state.height
            ? {
                height: this.state.height
              }
            : {}
        ]}
      >
        {Platform.OS === "web" ? (
          <div dangerouslySetInnerHTML={{ __html: html }}></div>
        ) : (
          <WebView
            originWhitelist={["*"]}
            // transparent 去掉 webview 的边框
            style={{ flex: 1, backgroundColor: "transparent" }}
            source={{ html: htmlTemplate(html) }}
            onMessage={this.handleMessage}
          />
        )}
      </View>
    );
  }
}
