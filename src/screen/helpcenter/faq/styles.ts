import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    backgroundColor: "#ffffff"
  },
  question: {
    marginLeft: 20,
    paddingTop: 19,
    backgroundColor: "#ffffff",
    paddingBottom: 11,
    borderBottomWidth: 0.5,
    borderColor: "#EBEBEB"
  },
  questionText: {
    fontWeight: "bold",
    fontSize: 16,
    color: "rgba(51,51,51,1)"
  },
  content: {
    marginTop: 16,
    marginLeft: 20,
    marginRight: 20,
    marginBottom: 50
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    height: 78,
    width: "100%"
    // // ios
    // shadowColor: '#000000',
    // shadowOffset: { width: 0, height: -2 },
    // shadowOpacity: 0.08,
    // shadowRadius: 8,
    // // android
    // elevation: 5
  },
  shadow: {
    width: "100%",
    height: 10
  },
  footerBtn: {
    flex: 1,
    flexDirection: "row",
    backgroundColor: "#ffffff"
  },
  line: {
    width: 0.5,
    marginTop: 14,
    marginBottom: 14,
    backgroundColor: "#EBEBEB"
  },
  btn: {
    flex: 1,
    alignItems: "center"
  },
  icon: {
    marginTop: 4,
    width: 24,
    height: 24
  },
  text: {
    marginTop: 3,
    fontSize: 14,
    color: "#666666",
    fontWeight: "400",
    textAlign: "center"
  }
});
