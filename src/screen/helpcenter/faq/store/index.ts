import { action, observable, toJS } from "mobx";
import Basic from "common/store/Basic";
import ServiceInfo from "../../serviceInfo";

const QUESTION_DETAIL = "/capi/personal/public/faq-center/question-detail/get";
const CUSTOM_SERVICE_INFO = "/capi/personal/public/faq-center/customer-services/get";

export default class Store extends Basic {
  @observable title = "";

  @observable content = "";

  @observable serviceTime = "";

  @observable servicePhone = "";

  @observable serviceEmail = "";

  @action
  async fetchFaqDetail(id: string) {
    try {
      const { success, data } = await this.io.post(QUESTION_DETAIL, { questionId: id });
      if (success) {
        this.setDetail(data);
      }
    } catch (e) {
      throw e;
    }
  }

  @action
  setDetail({ questionContent, answerContent }: { questionContent: string; answerContent: string }) {
    this.title = questionContent;
    this.content = answerContent;
  }

  @action
  async fetchServiceInfo() {
    try {
      const response = await this.io.post(CUSTOM_SERVICE_INFO, "");
      if (response.success) {
        ServiceInfo.serviceTime = response?.data?.serviceTime;
        ServiceInfo.servicePhone = response?.data?.serviceTelPhone;
        ServiceInfo.serviceEmail = response?.data?.serviceEmail;
        this.serviceTime = ServiceInfo.serviceTime;
        this.servicePhone = ServiceInfo.servicePhone;
        this.serviceEmail = ServiceInfo.serviceEmail;
      }
    } catch (e) {}
  }
}
