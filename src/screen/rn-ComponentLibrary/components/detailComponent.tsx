import { FontStyles } from "@akulaku-rn/akui-rn";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import React, { PureComponent, ReactNode } from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";

type Props = {
  title: string | AKButtonType;
  desc: string;
  isClick?: boolean; //是否可以点击查看
  onPress?: () => void;
};

export default class DetailComponent extends PureComponent<Props> {
  render(): ReactNode {
    return (
      <TouchableOpacity
        style={{
          paddingTop: 30,
          paddingBottom: 10,
          marginHorizontal: 16,
          borderBottomColor: "#EFF2F6",
          borderBottomWidth: 1
        }}
        disabled={!this.props.isClick}
        onPress={this.props.onPress}
      >
        <Text style={{ fontSize: 18, color: "#43474C", ...FontStyles["roboto-bold"] }}>{this.props.title}</Text>
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <Text style={{ fontSize: 12, color: "#989FA9", marginTop: 6 }}>{this.props.desc}</Text>
          {this.props.isClick && (
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <Text style={{ fontSize: 12, color: "#FB3432" }}>查看</Text>
              <Image source={require("../images/check.webp")} style={{ width: 9, height: 9, marginLeft: 4 }} />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  }
}
