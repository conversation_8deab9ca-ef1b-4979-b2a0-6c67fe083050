import { FontSty<PERSON>, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import { NativeNavigationModule } from "@akulaku-rn/akulaku-ec-common/src/nativeModules";
import React, { Component } from "react";
import { View, Image, Text, ImageBackground, TouchableOpacity, StyleSheet } from "react-native";
import { RNComponentType } from "./constants";

export default class RNComponentLibrary extends Component {
  data = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

  returnTypeAndImage = (index: number, returnType?: boolean, returnImage?: boolean) => {
    //返回组件类型和图标
    let type;
    let image;
    switch (index) {
      case 0:
        type = RNComponentType.Button;
        image = require("./images/button.webp");
        break;
      case 1:
        type = RNComponentType.Input;
        image = require("./images/input.webp");
        break;
      case 2:
        type = RNComponentType.Dialog;
        image = require("./images/dialog.webp");
        break;
      case 3:
        type = RNComponentType.PopupContainer;
        image = require("./images/popUpContainer.webp");
        break;
      case 4:
        type = RNComponentType.Toast;
        image = require("./images/toast.webp");
        break;
      case 5:
        type = RNComponentType.PageTipComponent;
        image = require("./images/pageTip.webp");
        break;
      case 6:
        type = RNComponentType.AkuList;
        image = require("./images/akuList.webp");
        break;
      case 7:
        type = RNComponentType.BottomButton;
        image = require("./images/bottomButton.webp");
        break;
      case 8:
        type = RNComponentType.ListBottom;
        image = require("./images/list.webp");
        break;
      case 9:
        type = RNComponentType.TopBar;
        image = require("./images/topNar.webp");
        break;
      case 10:
        type = RNComponentType.AKuTab;
        image = require("./images/tab.webp");
        break;
      default:
        break;
    }
    if (returnType) {
      NativeNavigationModule.navigate({ screen: "ComponentDetail", params: { type } });
    } else {
      return image;
    }
  };

  render() {
    return (
      <ImageBackground source={require("./images/background.webp")} style={styles.background}>
        <Text style={styles.title}>组件列表</Text>
        <View style={styles.container}>
          {this.data.map((item, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  this.returnTypeAndImage(index, true);
                }}
              >
                <Image source={this.returnTypeAndImage(index, false, true)} style={styles.image} />
              </TouchableOpacity>
            );
          })}
        </View>
      </ImageBackground>
    );
  }
}

const styles = StyleSheet.create({
  background: {
    width: WINDOW_WIDTH,
    height: (WINDOW_WIDTH * 692) / 360,
    flex: 1
  },
  title: {
    marginTop: (((WINDOW_WIDTH * 692) / 360) * 58) / 692,
    fontSize: 20,
    color: "#27313D",
    alignSelf: "center",
    ...FontStyles["roboto-bold"]
  },
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: WINDOW_WIDTH - (WINDOW_WIDTH * 70) / 360,
    marginTop: (((WINDOW_WIDTH * 692) / 360) * 80) / 692,
    alignSelf: "center"
  },
  image: {
    width: 80,
    height: 86,
    marginRight: 25,
    marginBottom: 38
  }
});
