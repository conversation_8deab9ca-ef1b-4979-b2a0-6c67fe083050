import { AKButton, NavigationBar, PopUpContainer } from "@akulaku-rn/akui-rn";
import AkListComponent from "@akulaku-rn/akui-rn/src/components/AKListComponent";
import BottomButton from "@akulaku-rn/akui-rn/src/components/BottomButton";
import TopNavigation from "@akulaku-rn/akui-rn/src/components/TopNavigation";
import BottomListComponent from "@akulaku-rn/akui-rn/src/components/BottomListComponent";
import { AKTabs, AKTab } from "@akulaku-rn/akui-rn/src/components/AKTabView";
import { AKButtonType } from "@akulaku-rn/akui-rn/src/components/AKButton";
import AKDialog, { DialogType, LayoutDirection } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import { AkListType } from "@akulaku-rn/akui-rn/src/components/AKListComponent";
import { TabBarType } from "@akulaku-rn/akui-rn/src/components/AKTabView/types";
import AKTextInput, { AkTextInputType } from "@akulaku-rn/akui-rn/src/components/AKTextInput";
import { BottomSuctionBarType } from "@akulaku-rn/akui-rn/src/components/BottomButton";
import {
  BottomListComponentType,
  BottomListLineColor,
  BottomListTextColor
} from "@akulaku-rn/akui-rn/src/components/BottomListComponent";
import PageTipsComponent, { PageTipsType } from "@akulaku-rn/akui-rn/src/components/PageTipsComponent";
import { PopUpContainerType } from "@akulaku-rn/akui-rn/src/components/PopUpContainer";
import { TopNavigationType } from "@akulaku-rn/akui-rn/src/components/TopNavigation";
import RootView from "@akulaku-rn/akulaku-ec-common/src/components/Layout/RootView";
import { inject, observer } from "mobx-react";
import React, { Component } from "react";
import { View, Text, ScrollView } from "react-native";
import DetailComponent from "./components/detailComponent";
import { RNComponentType } from "./constants";

type Props = {
  store: {
    navParams: NavParams;
  };
};

type NavParams = {
  type: RNComponentType;
};

type States = {
  setNavigationBar: number;
  setListBottom: number;
  setAkuTab: number;
};

@RootView({})
@inject("store")
@observer
export default class ComponentDetail extends Component<Props, States, NavParams> {
  constructor(props: Props) {
    super(props);
    this.state = {
      setNavigationBar: 0,
      setListBottom: 0,
      setAkuTab: 0
    };
  }

  buttonData = [
    { type: AKButtonType.B1_1_1, desc: "填充按钮-大按钮", title: "B1.1.1" },
    { type: AKButtonType.B1_1_2, desc: "填充按钮-中按钮", title: "B1.1.2" },
    { type: AKButtonType.B1_1_3, desc: "填充按钮-小按钮", title: "B1.1.3" },
    { type: AKButtonType.B1_1_4, desc: "填充按钮-极小按钮", title: "B1.1.4" },
    { type: AKButtonType.B1_2_1, desc: "线性按钮-大按钮", title: "B1.2.1" },
    { type: AKButtonType.B1_2_2, desc: "线性按钮-中按钮", title: "B1.2.2" },
    { type: AKButtonType.B1_2_3, desc: "线性按钮-小按钮", title: "B1.2.3" },
    { type: AKButtonType.B1_2_4, desc: "线性按钮-极小按钮", title: "B1.2.4" }
  ];

  dialogData = [
    { type: DialogType.C3_1_1, desc: "对话框（常规）I 正文+标题（横向按钮1个）", title: "C3.1.1" },
    { type: DialogType.C3_1_1, desc: "对话框（常规）I 正文+标题 （横向按钮2个）", title: "C3.1.1" },
    { type: DialogType.C3_1_1, desc: "对话框（常规）I 正文+标题 （纵向按钮2个）", title: "C3.1.1" },
    { type: DialogType.C3_1_1, desc: "对话框（常规）I 正文+标题 （纵向按钮3个）", title: "C3.1.1" },
    { type: DialogType.C3_1_1, desc: "对话框（常规）I 仅正文 （横向按钮2个）", title: "C3.1.1" },
    { type: DialogType.C3_1_1, desc: "对话框（常规）I 仅标题 （横向按钮2个）", title: "C3.1.1" },
    { type: DialogType.C3_2_1, desc: "对话框（面性输入框）", title: "C3.2.1" },
    { type: DialogType.C3_3_1, desc: "对话框（带图）- 包含小图", title: "C3.3.1" },
    { type: DialogType.C3_4_1, desc: "对话框（带图）- 凸出", title: "C3.4.1" },
    { type: DialogType.C3_5_1, desc: "对话框（带图）- 铺满", title: "C3.5.1" }
  ];

  getComponentTitle = () => {
    const {
      store: {
        navParams: { type }
      }
    } = this.props;
    let title;
    switch (type) {
      case RNComponentType.Button:
        title = "按钮组件（B1.1.1-B1.2.4）";
        break;
      case RNComponentType.Input:
        title = "输入框组件（A2.1.1）";
        break;
      case RNComponentType.Dialog:
        title = "对话框组件（C3.1.1-C3.3.3）";
        break;
      case RNComponentType.PopupContainer:
        title = "半弹窗组件（D4.1.1-D4.1.2）";
        break;
      case RNComponentType.Toast:
        title = "Toast组件（E5.1.1-E5.1.2）";
        break;
      case RNComponentType.PageTipComponent:
        title = "页面提示组件（F6.1.1）";
        break;
      case RNComponentType.AkuList:
        title = "列表组件（G7.1.1-G7.2.1）";
        break;
      case RNComponentType.BottomButton:
        title = "吸底栏组件（H8.1.1）";
        break;
      case RNComponentType.ListBottom:
        title = "列表底部组件（J9.1.1）";
        break;
      case RNComponentType.TopBar:
        title = "顶部导航组件（K10.1.1-K10.1）";
        break;
      case RNComponentType.AKuTab:
        title = "内页导航组件（L11.1.1-L11.1）";
        break;
      default:
        break;
    }
    return title;
  };

  onPress = (index: number) => {
    switch (index) {
      case 0:
        AKDialog.show({
          type: DialogType.C3_1_1,
          title: "long live",
          desc: "Let nature cherish the moment！",
          positiveText: "Confirm"
        });
        break;
      case 1:
        AKDialog.show({
          type: DialogType.C3_1_1,
          title: "long live",
          desc: "Let nature cherish the moment！",
          positiveText: "Confirm",
          negativeText: "Cancel"
        });
        break;
      case 2:
        AKDialog.show({
          type: DialogType.C3_1_1,
          title: "long live",
          desc: "Let nature cherish the moment！",
          positiveText: "Confirm",
          negativeText: "Cancel",
          layoutDirection: LayoutDirection.LayoutVertical
        });
        break;
      case 3:
        AKDialog.show({
          type: DialogType.C3_1_1,
          title: "long live",
          desc: "Let nature cherish the moment！",
          positiveText: "Confirm",
          negativeText: "Cancel",
          lastButtonText: "button",
          multipleButtons: true,
          layoutDirection: LayoutDirection.LayoutVertical
        });
        break;
      case 4:
        AKDialog.show({
          type: DialogType.C3_1_1,
          desc: "Let nature cherish the moment！",
          positiveText: "Confirm",
          negativeText: "Cancel"
        });
        break;
      case 5:
        AKDialog.show({
          type: DialogType.C3_1_1,
          title: "long live",
          positiveText: "Confirm",
          negativeText: "Cancel"
        });
        break;
      case 6:
        AKDialog.show({
          type: DialogType.C3_2_1,
          title: "long live",
          desc: "Let nature cherish the moment！",
          positiveText: "Confirm",
          negativeText: "Cancel"
        });
        break;
      case 7:
        AKDialog.show({
          type: DialogType.C3_3_1,
          title: "long live",
          desc: "Let nature cherish the moment！",
          image: require("./images/img_creaselimitsucceed.webp"),
          positiveText: "Confirm",
          negativeText: "Cancel"
        });
        break;
      case 8:
        AKDialog.show({
          type: DialogType.C3_4_1,
          title: "long live",
          desc: "Let nature cherish the moment！",
          image: require("./images/img_creaselimitsucceed.webp"),
          positiveText: "Confirm",
          negativeText: "Cancel"
        });
        break;
      case 9:
        AKDialog.show({
          type: DialogType.C3_5_1,
          title: "long live",
          desc: "Let nature cherish the moment！",
          image: require("./images/img_creaselimitsucceed.webp"),
          positiveText: "Confirm",
          negativeText: "Cancel"
        });
        break;
    }
  };

  getComponentContent = () => {
    const {
      store: {
        navParams: { type }
      }
    } = this.props;
    let content;
    switch (type) {
      case RNComponentType.Button:
        content = (
          <>
            {this.buttonData.map((item, index) => {
              return (
                <View style={{ marginBottom: 30 }} key={index}>
                  <DetailComponent title={item.title} desc={item.desc} />
                  <Text style={{ fontSize: 11, color: "#989FA9", marginTop: 24, marginBottom: 8, marginLeft: 16 }}>
                    正常状态
                  </Text>
                  <AKButton text="Big Button" type={item.type} style={{ marginHorizontal: 16 }} onPress={() => {}} />
                  <Text style={{ fontSize: 11, color: "#989FA9", marginTop: 24, marginBottom: 8, marginLeft: 16 }}>
                    不可选状态
                  </Text>
                  <AKButton text="Big Button" type={item.type} disabled={true} style={{ marginHorizontal: 16 }} />
                </View>
              );
            })}
          </>
        );
        break;
      case RNComponentType.Input:
        content = (
          <>
            <DetailComponent title={"A2.1.1"} desc={"单行输入框"} />
            <View style={{ marginHorizontal: 16 }}>
              <AKTextInput type={AkTextInputType.A2_1_1} placeholder={"Default"} value={""} setValue={() => {}} />
            </View>
          </>
        );
        break;
      case RNComponentType.Dialog:
        content = (
          <>
            {this.dialogData.map((item, index) => {
              return (
                <DetailComponent
                  title={item.title}
                  desc={item.desc}
                  key={index}
                  isClick={true}
                  onPress={() => {
                    this.onPress(index);
                  }}
                />
              );
            })}
          </>
        );
        break;
      case RNComponentType.PopupContainer:
        content = (
          <>
            <DetailComponent
              title={"D4.1.1"}
              desc={"半弹窗-包含头部"}
              isClick={true}
              onPress={() => {
                PopUpContainer.show({
                  type: PopUpContainerType.D4_1_1,
                  headerProps: { title: "long live" },
                  renderContent: <View />
                });
              }}
            />
            <DetailComponent
              title={"D4.1.2"}
              desc={"半弹窗-不包含头部"}
              isClick={true}
              onPress={() => {
                PopUpContainer.show({
                  type: PopUpContainerType.D4_1_2,
                  renderContent: <View />
                });
              }}
            />
          </>
        );
        break;
      case RNComponentType.Toast:
        break;
      case RNComponentType.PageTipComponent:
        content = (
          <>
            <DetailComponent title={"F6.1.1"} desc={"常规页面提示"} />
            <Text style={{ fontSize: 11, color: "#989FA9", marginTop: 24, marginBottom: 8, marginLeft: 16 }}>
              正常状态
            </Text>
            <PageTipsComponent
              text="Don't make excuses for failure, just make excuses for success. Life will always give people troubles, life is not perfect, twists and turns are also scenery."
              type={PageTipsType.F6_1_1}
            />
          </>
        );
        break;
      case RNComponentType.AkuList:
        content = (
          <>
            <DetailComponent title={"G7.1.1"} desc={"常规页面提示"} />
            <AkListComponent
              type={AkListType.G7_1_1}
              title={"Rectangle"}
              image={require("./images/icon_repayment.webp")}
              onPress={() => {}}
            />
            <DetailComponent title={"G7.1.2"} desc={"常规页面提示"} />
            <AkListComponent
              type={AkListType.G7_1_2}
              title={"Rectangle"}
              image={require("./images/icon_repayment.webp")}
              onPress={() => {}}
              rightIcon={require("./images/icon_repayment.webp")}
            />
            <DetailComponent title={"G7.1.3"} desc={"常规页面提示"} />
            <AkListComponent
              type={AkListType.G7_1_3}
              title={"Rectangle"}
              image={require("./images/icon_repayment.webp")}
              onPress={() => {}}
              rightText={"TextContent"}
            />
            <DetailComponent title={"G7.2.1"} desc={"常规页面提示"} />
            <AkListComponent
              type={AkListType.G7_2_1}
              title={"Rectangle"}
              image={require("./images/icon_repayment.webp")}
              onPress={() => {}}
              paraText={"Pusat bantuan 23"}
            />
            <DetailComponent title={"G7.2.2"} desc={"常规页面提示"} />
            <AkListComponent
              type={AkListType.G7_2_2}
              title={"Rectangle"}
              image={require("./images/icon_repayment.webp")}
              onPress={() => {}}
              paraText={"Pusat bantuan 23"}
              rightIcon={require("./images/icon_repayment.webp")}
            />
          </>
        );
        break;
      case RNComponentType.BottomButton:
        content = <DetailComponent title={"H8.1.1"} desc={"吸底栏样式-见底部"} />;
        break;
      case RNComponentType.ListBottom:
        content = (
          <>
            <DetailComponent
              title={"J9.1.1"}
              desc={"列表底部单行"}
              isClick={true}
              onPress={() => {
                this.setState({ setListBottom: 1 });
              }}
            />
            <DetailComponent
              title={"J9.1.1"}
              desc={"列表底部多行"}
              isClick={true}
              onPress={() => {
                this.setState({ setListBottom: 2 });
              }}
            />
          </>
        );
        break;
      case RNComponentType.TopBar:
        content = (
          <>
            <DetailComponent
              title={"K10.1.1"}
              desc={"导航栏+文字按钮"}
              isClick={true}
              onPress={() => {
                this.setState({ setNavigationBar: 1 });
              }}
            />
            <DetailComponent
              title={"K10.1.2"}
              desc={"导航栏+1个入口"}
              isClick={true}
              onPress={() => {
                this.setState({ setNavigationBar: 2 });
              }}
            />
            <DetailComponent
              title={"K10.1.3"}
              desc={"导航栏+2个入口"}
              isClick={true}
              onPress={() => {
                this.setState({ setNavigationBar: 3 });
              }}
            />
          </>
        );
        break;
      case RNComponentType.AKuTab:
        content = (
          <>
            <DetailComponent
              title={"L11.1.1"}
              desc={"内页导航（吸底 居中）"}
              isClick={true}
              onPress={() => {
                this.setState({ setAkuTab: 1 });
              }}
            />
            <DetailComponent
              title={"L11.1.2"}
              desc={"内页导航（吸底 左对齐）"}
              isClick={true}
              onPress={() => {
                this.setState({ setAkuTab: 2 });
              }}
            />
            <DetailComponent
              title={"L11.2.1"}
              desc={"内页导航（不吸底 居中）"}
              isClick={true}
              onPress={() => {
                this.setState({ setAkuTab: 3 });
              }}
            />
            <DetailComponent
              title={"L11.2.2"}
              desc={"内页导航（不吸底 左对齐）"}
              isClick={true}
              onPress={() => {
                this.setState({ setAkuTab: 4 });
              }}
            />
            <View style={{ height: 20 }} />
            {this.renderAkuTab(type)}
          </>
        );
        break;
      default:
        break;
    }
    return content;
  };

  renderNavigationBar = () => {
    const title = this.getComponentTitle();
    switch (this.state.setNavigationBar) {
      case 0:
        return <NavigationBar title={title} />;
      case 1:
        return <TopNavigation title="love live" type={TopNavigationType.K10_1_1} rightButtonText={"Button"} />;
      case 2:
        return (
          <TopNavigation
            title="love live"
            type={TopNavigationType.K10_1_2}
            entranceOne={require("./images/icon_repayment.webp")}
          />
        );
      case 3:
        return (
          <TopNavigation
            title="love live"
            type={TopNavigationType.K10_1_3}
            entranceOne={require("./images/icon_repayment.webp")}
            entranceTwo={require("./images/icon_repayment.webp")}
          />
        );
    }
  };

  renderListBottom = (type: RNComponentType) => {
    if (type === RNComponentType.ListBottom && this.state.setListBottom === 1) {
      return (
        <BottomListComponent
          type={BottomListComponentType.J9_1_1}
          text={"Don't make excuses for failure"}
          textColor={BottomListTextColor.ONE}
          lineColor={BottomListLineColor.ONE}
        />
      );
    } else if (type === RNComponentType.ListBottom && this.state.setListBottom === 2) {
      return (
        <BottomListComponent
          type={BottomListComponentType.J9_1_1}
          text={
            "Don't make excuses for failure, just make excuses for success. Life will always give people troubles, life is not perfect, twists and turns are also scenery."
          }
          textColor={BottomListTextColor.ONE}
          lineColor={BottomListLineColor.ONE}
        />
      );
    } else {
      return null;
    }
  };

  renderAkuTab = (type: RNComponentType) => {
    if (type === RNComponentType.AKuTab) {
      switch (this.state.setAkuTab) {
        case 1:
          return (
            <AKTabs type={TabBarType.L11_1_1} onChangeTab={a => {}}>
              {[
                <AKTab key={1} title={`Feature1`}>
                  {<Text>Feature1-----</Text>}
                </AKTab>,
                <AKTab key={2} title={`Feature2`}>
                  {<Text>Feature1-----</Text>}
                </AKTab>,
                <AKTab key={3} title={`Feature3`}>
                  {<Text>Feature3-----</Text>}
                </AKTab>
              ]}
            </AKTabs>
          );
        case 2:
          return (
            <AKTabs type={TabBarType.L11_1_2} onChangeTab={a => {}}>
              {[
                <AKTab key={1} title={`Feature1`}>
                  {<Text>Feature1-----</Text>}
                </AKTab>,
                <AKTab key={2} title={`Feature2`}>
                  {<Text>Feature1-----</Text>}
                </AKTab>,
                <AKTab key={3} title={`Feature3`}>
                  {<Text>Feature3-----</Text>}
                </AKTab>
              ]}
            </AKTabs>
          );
        case 3:
          return (
            <AKTabs type={TabBarType.L11_2_1} onChangeTab={a => {}}>
              {[
                <AKTab key={1} title={`Feature1`}>
                  {<Text>Feature1-----</Text>}
                </AKTab>,
                <AKTab key={2} title={`Feature2`}>
                  {<Text>Feature1-----</Text>}
                </AKTab>,
                <AKTab key={3} title={`Feature3`}>
                  {<Text>Feature3-----</Text>}
                </AKTab>
              ]}
            </AKTabs>
          );
        case 4:
          return (
            <AKTabs type={TabBarType.L11_2_2} onChangeTab={a => {}}>
              {[
                <AKTab key={1} title={`Feature1`}>
                  {<Text>Feature1-----</Text>}
                </AKTab>,
                <AKTab key={2} title={`Feature2`}>
                  {<Text>Feature1-----</Text>}
                </AKTab>,
                <AKTab key={3} title={`Feature3`}>
                  {<Text>Feature3-----</Text>}
                </AKTab>
              ]}
            </AKTabs>
          );
        default:
          break;
      }
    } else {
      return null;
    }
  };

  render(): JSX.Element {
    const {
      store: {
        navParams: { type }
      }
    } = this.props;
    const content = this.getComponentContent();
    return (
      <>
        {this.renderNavigationBar()}
        <View style={{ height: 8, backgroundColor: "#EFF2F6" }} />
        <ScrollView keyboardShouldPersistTaps={"handled"}>{content}</ScrollView>
        {type === RNComponentType.BottomButton && (
          <BottomButton type={BottomSuctionBarType.H8_1_1} text={"Submit"} onPress={() => {}} />
        )}
        {this.renderListBottom(type)}
      </>
    );
  }
}
