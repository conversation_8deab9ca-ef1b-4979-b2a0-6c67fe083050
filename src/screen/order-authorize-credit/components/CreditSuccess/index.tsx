import React, { PureComponent } from "react";
import { Image, View, Text, TouchableOpacity } from "react-native";
import styles from "./styles";
import { NavigationBar } from "@akulaku-rn/akui-rn";
import { NativeNavigationModule } from "common/nativeModules";

type Props = {
  t: any;
  layerGoback: () => void;
};

type State = {};

export default class CreditSuccess extends PureComponent<Props, State> {
  gotoFlashSale = () => {
    NativeNavigationModule.popToHome(0);
  };

  render() {
    const { t, layerGoback } = this.props;
    return (
      <View style={{ flex: 1 }}>
        <NavigationBar title={t("授信通知")} onBackPress={layerGoback} />
        <View style={styles.container}>
          <Image source={require("../../img/credit_successful.webp")} style={styles.img} />
          <Text style={styles.title}>{t("授信成功")}</Text>
          <Text style={styles.text1}>{t("恭喜您！您已经成功获得消费信用额度！")}</Text>
          <Text style={styles.text2}>{t("在您购买商品结算时，选择分期支付，即可使用信用额度支付结算哦~")}</Text>
          <TouchableOpacity style={styles.touch} onPress={this.gotoFlashSale}>
            <Text style={styles.touchText}>{t("返回首页")}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}
