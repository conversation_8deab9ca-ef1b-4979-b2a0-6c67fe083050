/* eslint-disable @typescript-eslint/no-empty-function */
import React, { Component } from "react";
import { View } from "react-native";
import { observer } from "mobx-react";
import { ItemType } from "../../dict/ComponentType";
import DynamicComponent from "../DynamicComponent";
import { NewEntityInfos } from "../../tool/DataHandling";
import { TFunction } from "i18next";
import store from "../../store";
import { EntryInfoList, SubPageInfoList } from "../../dict/ProcessData";
import { pageStoreModel } from "common/components/BaseContainer/Type";

type Props = {
  setWaitArray: (waitArray: any[]) => void;
  t: TFunction;
  missingId: number | null;
  data: NewEntityInfos[];
  setRef: (uid: number, ref: any) => void;
  secondary: SubPageInfoList[];
  setValue: (text: string, id: number, type: string, key: string) => void;
  store: pageStoreModel<store>;
  isFirstApply: boolean;
  allItems: NewEntityInfos[];
  testType: number;
};

type States = {
  needShowItem: any[];
};
@observer
export default class Occupation extends Component<Props, States> {
  selectItems: NewEntityInfos[];

  constructor(props: Props) {
    super(props);
    this.state = {
      needShowItem: []
    };
    this.selectItems = [];
  }

  componentDidMount() {
    this.computeChild();
  }

  computeChild = () => {
    const {
      secondary,
      store: { pageStore }
    } = this.props;
    const data = this.props.data[0];
    if (!!pageStore.useCreditData[data.id]) {
      const needShow = secondary.find(i => {
        return i.entryKey === parseInt(pageStore.useCreditData[data.id]);
      });
      let needShowItem: EntryInfoList[] = [];
      if (!!needShow) {
        // @ts-ignore
        needShowItem = needShow.pageInfo.entries;
      }
      this.setSelectItemsAndWaitArray(needShowItem);
      this.setState({
        //由于风控组给的数据结构如此，但是这个数组只会返回一个子项
        needShowItem: needShowItem
      });
    }
  };

  renderItem = () => {
    const { t, missingId, isFirstApply, store, allItems, testType, setRef } = this.props;
    const { needShowItem } = this.state;
    const needShowItemView: JSX.Element[] = [];
    needShowItem.map((item: NewEntityInfos, index: number) => {
      const containerStyle = { marginBottom: needShowItem.length - 1 === index ? 42 : 24 };
      needShowItemView.push(
        <View onLayout={() => {}} ref={_ref => setRef(item.id, _ref)}>
          <DynamicComponent
            key={index}
            data={item}
            t={t}
            setValue={this.didSelectedItem}
            containerStyle={containerStyle}
            missingId={missingId}
            isFirstApply={isFirstApply}
            selectItems={this.selectItems}
            store={store}
            allItems={allItems}
            testType={testType}
          />
        </View>
      );
    });
    return needShowItemView;
  };

  didSelectedItem = (selectedItem: any, id: any, type: any, key: any) => {
    const { setValue } = this.props;
    setValue(selectedItem, id, type, key);
  };

  occupationSelected = (value: string, id: number, type: string, key: string) => {
    const { setValue, secondary } = this.props;
    const lastValue = this.props.data[0].lastValue;
    const Value = parseInt(value);
    if (Value !== parseInt(lastValue)) {
      const needShow = secondary.find(i => {
        return i.entryKey === Value;
      });
      let needShowItem: EntryInfoList[] = [];
      if (!!needShow) {
        // @ts-ignore
        needShowItem = needShow.pageInfo.entries;
      }
      this.setSelectItemsAndWaitArray(needShowItem);
      this.setState({
        //由于风控组给的数据结构如此，但是这个数组只会返回一个子项
        needShowItem: needShowItem
      });
      setValue(value, id, type, key);
    }
  };

  setSelectItemsAndWaitArray = (needShowItem: any[]) => {
    const { setWaitArray } = this.props;
    const waitArray: any[] = [];
    this.selectItems = [];

    needShowItem.map((item: NewEntityInfos) => {
      if (item.type === "select") {
        this.selectItems.push(item);
      }
      if (item.type as ItemType) {
        waitArray.push(item);
      }
    });
    setWaitArray(waitArray);
  };

  emptyData = () => {
    const { setValue } = this.props;
    const needEmpty: any[] = [];
    this.state.needShowItem.map((item: { id: number }) => {
      needEmpty.push(item.id);
    });
    //得到需要清空的id数组，进行遍历清空
    needEmpty.map(item => {
      setValue("", item, "", "");
    });
  };

  render() {
    const { t, missingId, isFirstApply, store, allItems, testType, setRef } = this.props;
    const data = this.props.data[0];
    return (
      <View>
        <View onLayout={() => {}} ref={_ref => setRef(data.id, _ref)}>
          <DynamicComponent
            data={data}
            t={t}
            setValue={this.occupationSelected}
            containerStyle={{ marginBottom: 24 }}
            missingId={missingId}
            isFirstApply={isFirstApply}
            selectItems={this.selectItems}
            store={store}
            allItems={allItems}
            testType={testType}
          />
        </View>
        {this.renderItem()}
      </View>
    );
  }
}
