import React, { PureComponent } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableHighlight,
  EmitterSubscription,
  TouchableOpacity,
  StyleProp,
  ViewStyle
} from "react-native";
import { TFunction } from "i18next";
import { FontStyles } from "@akulaku-rn/akui-rn";
import { AKButton, AKButtonType } from "common/components/AKButton";
type Props = {
  next: () => void;
  text?: string;
  t: TFunction;
  disabled: boolean;
  style?: StyleProp<ViewStyle>;
};

export default class BottomButton extends PureComponent<Props> {
  endRecord!: EmitterSubscription;

  resetRecord!: EmitterSubscription;

  constructor(props: Props) {
    super(props);
  }

  render() {
    const { text = "Next", style, next, disabled } = this.props;
    return (
      <View style={[styles.container, style]}>
        <AKButton disabled={disabled} style={styles.button} type={AKButtonType.B1_1_2} text={text} onPress={next} />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
    backgroundColor: "#fff"
  },
  button: {
    width: "100%"
  },
  disabled: {
    opacity: 0.25
  },
  buttonText: {
    fontSize: 16,
    color: "#fff",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  // 声纹
  voiceContainer: {
    minHeight: 64,
    paddingHorizontal: 12,
    paddingTop: 8,
    paddingBottom: 16,
    alignItems: "center",
    backgroundColor: "#fff",
    flexDirection: "row"
  },
  leftBtn: {
    flex: 1,
    marginRight: 8,
    backgroundColor: "#fff",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 4,
    borderColor: "#E62117",
    borderWidth: 1
  },
  againText: {
    fontSize: 16,
    color: "#E62117"
  },
  rightBtn: {
    flex: 1,
    backgroundColor: "#E62117",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 4
  },
  agreementView: {
    flexDirection: "row",
    alignSelf: "flex-start",
    marginRight: 33,
    marginBottom: 50
  },
  selectIcon: {
    width: 16,
    height: 16,
    marginRight: 8
  },
  select: {
    width: 16,
    height: 16,
    marginRight: 8,
    marginTop: 2,
    borderRadius: 8,
    borderWidth: 1.2,
    borderColor: "#989FA9"
  },
  unselectIcon: {
    width: 16,
    height: 16,
    marginRight: 8,
    borderRadius: 8,
    borderWidth: 1.2,
    borderColor: "#989FA9"
  },
  agreementTitle: {
    fontSize: 12,
    lineHeight: 14,
    color: "#999"
  }
});
