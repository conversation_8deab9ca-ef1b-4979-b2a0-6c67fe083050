/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/components/SelectItem/OptionsList.tsx
 */

import { StyleSheet, Text, TouchableOpacity, View, Image } from "react-native";
import React from "react";
import VerticalList from "../VerticalList";
import { FontStyles, WINDOW_HEIGHT } from "@akulaku-rn/akui-rn";
import { pageStoreModel } from "@akulaku-rn/akulaku-ec-common/src/components/BaseContainer/Type";
import store from "../../store";

interface ItemI {
  value: string;
  key: number;
  isSpecial?: boolean;
  isShow?: boolean;
}

type Props = {
  didSelectedItem: (item: ItemI) => void;
  selectedItem: string | null;
  data: ItemI[];
  t: any;
  // 1：正常流程 2：合规流程 3: 合规2.0
  version: number;
  store: pageStoreModel<store>;
};

const SIGN = "######";

function OptionsList({ data, didSelectedItem, selectedItem, version, t, store }: Props) {
  const selectInSpecial = () => {
    if (version === 2) return true;
    const findItem = data.find(item => item.value === selectedItem);
    return findItem && findItem.isSpecial;
  };
  const [showAll, setShowAll] = React.useState(selectInSpecial());

  const _renderTipsV2 = () => {
    return (
      <View style={styles.itemStyle}>
        <Image source={require("./img/tips.png")} style={styles.tips} />
        <View style={{ flex: 1 }}>
          <Text style={styles.tipsText}>
            {t("以下选项需额外填写13项资金提供者的信息，包括姓名、身份证号、住址，收入等")}
          </Text>
        </View>
      </View>
    );
  };

  const _renderTips = () => {
    if (!showAll) {
      return (
        <TouchableOpacity
          style={{ paddingVertical: 20, paddingHorizontal: 16, flexDirection: "row", alignItems: "center" }}
          onPress={() => {
            setShowAll(!showAll);
          }}
        >
          <Text style={{ color: "#6E737D", fontSize: 14 }}>{t("点击查看更多")}</Text>
          <Image source={require("./img/pull_icon.webp")} style={{ width: 12, height: 12, marginLeft: 4 }} />
        </TouchableOpacity>
      );
    }
    if (store.pageStore.newUi) {
      return (
        <View style={[styles.tipsContainer, { backgroundColor: "#E6F8FF" }]}>
          <View style={styles.tipsHeader}>
            <Text style={{ color: "#282B2E", fontSize: 14, ...StyleSheet.flatten(FontStyles["rob-medium"]) }}>
              {t("温馨提示：")}
            </Text>
          </View>
          <Text style={{ marginTop: 8, fontSize: 11, color: "#282B2E", lineHeight: 14 }}>
            {t("以下职业无收入来源，")}
            <Text style={{ ...StyleSheet.flatten(FontStyles["rob-medium"]), color: "#282B2E" }}>
              {t("需额外填写资金提供者的信息（13项资料）")}
            </Text>{" "}
            {t("Akulaku可能会电话联系资金提供者核实信息。")}
          </Text>
          <Text style={{ marginTop: 8, fontSize: 11, color: "#282B2E", lineHeight: 14 }}>
            {t("若您有多种职业，请填写有收入的职业类型。如您可能同时是劳动者，家庭主妇，职员，内容创作者等多种职业。")}
          </Text>
        </View>
      );
    }
    return (
      <View style={[styles.tipsContainer, { backgroundColor: store.pageStore.newUi ? "#E6F8FF" : "#FFF4E6" }]}>
        <View style={styles.tipsHeader}>
          <Image source={require("./img/tip_icon.webp")} style={{ width: 14, height: 14, marginRight: 8 }} />
          <Text style={{ color: "#FF8833", fontSize: 14, ...StyleSheet.flatten(FontStyles["rob-medium"]) }}>
            {t("温馨提示：")}
          </Text>
        </View>
        <Text style={{ marginTop: 8, fontSize: 11, color: "#FF8833", lineHeight: 14 }}>
          {t("以下职业无收入来源，")}
          <Text style={{ ...StyleSheet.flatten(FontStyles["rob-medium"]), color: "#F5640A" }}>
            {t("需额外填写资金提供者的信息（13项资料）")}
          </Text>{" "}
          {t("Akulaku可能会电话联系资金提供者核实信息。")}
        </Text>
        <Text style={{ marginTop: 8, fontSize: 11, color: "#FF8833", lineHeight: 14 }}>
          {t("若您有多种职业，请填写有收入的职业类型。如您可能同时是劳动者，家庭主妇，职员，内容创作者等多种职业。")}
        </Text>
      </View>
    );
  };

  const _renderCell = (item: ItemI) => {
    const isSelected = selectedItem === item.value;

    return (
      <TouchableOpacity
        onPress={() => {
          didSelectedItem(item);
        }}
        style={styles.button}
      >
        <Text style={[store.pageStore.newUi ? styles.titleNew : styles.title, isSelected && { color: "#E62117" }]}>
          {item.value}
        </Text>
        {isSelected && (
          <Image
            style={styles.selectIcon}
            source={store.pageStore.newUi ? require("../img/select_icon_new.png") : require("../img/select_icon.png")}
          />
        )}
        <View style={store.pageStore.newUi ? styles.sepNew : styles.sep} />
      </TouchableOpacity>
    );
  };

  const _renderItem = (item: ItemI, index: number) => {
    if (item.value === SIGN) {
      if (version === 2) {
        return _renderTipsV2();
      } else {
        return _renderTips();
      }
    }
    if (version === 2) {
      return _renderCell(item);
    }
    if (showAll || item.isShow) {
      return _renderCell(item);
    }
    return null;

    // const isSelected = selectedItem === item.value;
    // return (
    //   <TouchableOpacity
    //     onPress={() => {
    //       didSelectedItem(item);
    //     }}
    //     style={styles.button}
    //   >
    //     <Text style={[styles.title, isSelected && { color: "#E62117" }]}>{item.value}</Text>
    //     {isSelected && <Image style={styles.selectIcon} source={require("../img/select_icon.png")} />}
    //     <View style={styles.sep} />
    //   </TouchableOpacity>
    // );
  };

  if (store.pageStore.newUi) {
    return (
      <View style={{ height: WINDOW_HEIGHT * 0.85 - 66 }}>
        <VerticalList
          style={{ width: "100%", paddingLeft: 0 }}
          renderItem={_renderItem}
          data={data}
          didSelectedItem={didSelectedItem}
          selectedItem={selectedItem}
        />
      </View>
    );
  }

  return (
    <VerticalList
      style={{ width: "100%", paddingLeft: 0 }}
      renderItem={_renderItem}
      data={data}
      didSelectedItem={didSelectedItem}
      selectedItem={selectedItem}
    />
  );
}

export default OptionsList;

const styles = StyleSheet.create({
  container: {
    paddingBottom: 10,
    paddingRight: 12,
    borderBottomColor: "#D9D9D9",
    borderBottomWidth: 1,
    paddingTop: 24,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  placeholderView: {
    position: "absolute",
    zIndex: 2
  },
  placeholderText: {
    lineHeight: 18,
    maxWidth: 270,
    height: 18,
    textAlignVertical: "center"
  },
  selectedItem: {
    fontSize: 14,
    color: "#333",
    maxWidth: "90%"
  },
  errorView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8
  },
  errorText: {
    fontSize: 14,
    color: "#e62117"
  },
  errorIcon: {
    height: 8,
    width: 8,
    marginRight: 4
  },
  button: {
    flexDirection: "row",
    justifyContent: "space-between",
    height: 50,
    paddingLeft: 16,
    alignItems: "center",
    width: "100%",
    backgroundColor: "#fff"
  },
  sep: {
    height: 0.5,
    position: "absolute",
    bottom: 0,
    left: 16,
    right: 0,
    backgroundColor: "#EBEBEB"
  },
  sepNew: {
    height: 0.5,
    position: "absolute",
    bottom: 0,
    left: 16,
    right: 0,
    backgroundColor: "#E2E5E9"
  },
  title: {
    fontSize: 14,
    color: "#333"
  },
  titleNew: {
    fontSize: 14,
    color: "#282B2E",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },

  itemStyle: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: "#EFF2F6",
    flexDirection: "row",
    alignItems: "center"
  },
  tips: { width: 14, height: 14, marginRight: 8 },
  tipsText: { fontSize: 11, color: "#6E737D" },
  selectIcon: { width: 24, height: 24, marginRight: 12 },
  tipsContainer: {
    backgroundColor: "#FFF4E6",
    borderRadius: 6,
    padding: 12,
    margin: 16,
    marginBottom: 0
  },
  tipsHeader: {
    flexDirection: "row",
    alignItems: "center"
  }
});
