/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/components/UploadExample.tsx
 */
import { StyleSheet, Text, View, Image } from "react-native";
import React from "react";
import { FontStyles, UrlImage } from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";

type Props = {
  t: TFunction;
  title: string;
};
const WIDTH = (288 - 24 * 3) / 3;

const UploadExample = ({ t, title }: Props) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <UrlImage
        source={
          "https://akulaku-app.oss-ap-southeast-5.aliyuncs.com/RN/ec_screen_credit_photo_example_35cf3d56b0febf0fcf28278917e8cd0a.webp"
        }
        width={240}
        style={{ width: 240, height: 150, marginTop: 16 }}
      />
      <View style={styles.imgContent}>
        <View style={styles.content}>
          <View style={styles.image}>
            <Image source={require("../img/id_photo_1.webp")} style={styles.tipImage} />
          </View>
          <Image source={require("../img/icon-16-error.webp")} style={styles.error} />
          <Text style={styles.detail}>{t("照片模糊")}</Text>
        </View>
        <View style={styles.content}>
          <View style={styles.image}>
            <Image source={require("../img/id_photo_2.webp")} style={styles.tipImage} />
          </View>
          <Image source={require("../img/icon-16-error.webp")} style={styles.error} />
          <Text style={styles.detail}>{t("图片太亮")}</Text>
        </View>
        <View style={styles.content}>
          <View style={styles.image}>
            <Image source={require("../img/id_photo_3.webp")} style={styles.tipImage} />
          </View>
          <Image source={require("../img/icon-16-error.webp")} style={styles.error} />
          <Text style={styles.detail}>{t("图片太暗")}</Text>
        </View>
      </View>
    </View>
  );
};

export default UploadExample;

const styles = StyleSheet.create({
  container: { width: 288, alignItems: "center" },
  title: { fontSize: 16, color: "#282B2E", ...StyleSheet.flatten(FontStyles["rob-medium"]) },
  imgContent: {
    width: 288,
    paddingHorizontal: 24,
    flexDirection: "row",
    marginTop: 20,
    justifyContent: "space-between"
  },
  content: {
    width: WIDTH,
    alignItems: "center"
  },
  image: {
    width: WIDTH,
    height: WIDTH,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#EFF2F6",
    borderWidth: 1,
    borderColor: "#E62117",
    borderRadius: 6
  },
  tipImage: {
    width: WIDTH - 8,
    height: ((WIDTH - 8) * 40) / 64
  },
  detail: {
    marginTop: 16,
    textAlign: "center",
    fontSize: 10,
    color: "#282B2E"
  },
  error: {
    width: 16,
    height: 16,
    position: "absolute",
    top: WIDTH - 8
  }
});
