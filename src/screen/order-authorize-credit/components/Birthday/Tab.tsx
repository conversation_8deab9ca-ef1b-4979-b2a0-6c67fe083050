import React, { PureComponent, Component } from "react";
import { StyleSheet, View, TouchableOpacity, Text, ScrollView } from "react-native";
import { FontStyles, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";

type Props = {
  data: Array<any>;
  setSelectTab: any;
  activeIndex: number;
};
type States = {
  [key: string]: any;
};

export default class Tab extends Component<Props, States> {
  itemWidthArray: any;

  scrollRef: any;

  contentWidth: any;

  scrollOffsetX: any;

  constructor(props: Props) {
    super(props);
    this.state = {
      activeIndex: props.activeIndex
    };
    this.itemWidthArray = {};
  }

  UNSAFE_componentWillReceiveProps(nextProps: any) {
    this.setState({
      activeIndex: nextProps.activeIndex
    });
  }

  componentDidUpdate(_prevProps: Readonly<Props>, prevState: Readonly<States>, _snapshot?: any): void {
    if (prevState.activeIndex !== this.state.activeIndex) {
      this.fixScrollOffsetX(this.state.activeIndex);
    }
  }

  fixScrollOffsetX(_activeIndex: number): void {
    let offsetX = 0;
    let currentItemWidth = 0;
    const itemWidthArray: Array<any> = [];
    for (const i in this.itemWidthArray) {
      itemWidthArray.push(this.itemWidthArray[i]);
    }
    itemWidthArray.forEach((width, index) => {
      if (index < this.state.activeIndex) {
        offsetX += width;
      }
      if (index === this.state.activeIndex) {
        currentItemWidth = width;
      }
    });
    if (!this.isInside(offsetX, currentItemWidth)) {
      this.scrollRef &&
        this.scrollRef.scrollTo({
          x: offsetX + currentItemWidth >= WINDOW_WIDTH ? offsetX + currentItemWidth - WINDOW_WIDTH : offsetX,
          animated: true
        });
    }
  }

  isInside(offsetX: number, currentItemWidth: number): boolean {
    return offsetX >= this.scrollOffsetX && offsetX + currentItemWidth <= this.scrollOffsetX + WINDOW_WIDTH;
  }

  setItemWidthArray = (itemWidthArray: any) => {
    this.itemWidthArray = { ...this.itemWidthArray, ...itemWidthArray };
  };

  setSelectTab = (activeIndex: number) => {
    const { setSelectTab } = this.props;
    // this.setState({
    //   activeIndex,
    // });
    setSelectTab(activeIndex);
  };

  returnItem = () => {
    const { data } = this.props;
    return data.map((item, index) => {
      return (
        <Item
          item={item.value}
          key={index}
          setSelectTab={this.setSelectTab}
          index={index}
          tabIndex={this.state.activeIndex}
          itemWidthArray={this.itemWidthArray}
          setItemWidthArray={this.setItemWidthArray}
        />
      );
    });
  };

  render() {
    return (
      <View style={{ backgroundColor: "#EFF2F6", marginTop: -16 }}>
        <ScrollView
          horizontal
          bounces={false}
          style={styles.container}
          showsHorizontalScrollIndicator={false}
          ref={comp => {
            this.scrollRef = comp;
          }}
          onScroll={event => {
            const {
              nativeEvent: {
                contentOffset: { x },
                contentSize: { width }
              }
            } = event;
            this.contentWidth = width;
            this.scrollOffsetX = x;
          }}
        >
          {this.returnItem()}
        </ScrollView>
      </View>
    );
  }
}

type ItemProps = {
  item: string;
  setSelectTab: any;
  index: number;
  tabIndex: number;
  itemWidthArray: any;
  setItemWidthArray: any;
};
class Item extends PureComponent<ItemProps, {}> {
  setSelectTab = () => {
    const { setSelectTab, index } = this.props;
    setSelectTab && setSelectTab(index);
  };

  touchOnLayout = (event: any) => {
    const { itemWidthArray, index, setItemWidthArray } = this.props;
    const newItemWidthArray = { ...itemWidthArray };
    if (!newItemWidthArray[index]) {
      newItemWidthArray[index] = event.nativeEvent.layout.width;
      setItemWidthArray && setItemWidthArray(newItemWidthArray);
    }
  };

  render() {
    const { item, index, tabIndex } = this.props;
    return (
      <TouchableOpacity
        style={[styles.item, tabIndex === index && { borderBottomColor: "#e62117", borderBottomWidth: 2 }]}
        onPress={this.setSelectTab}
        onLayout={this.touchOnLayout}
      >
        <Text style={[styles.text, tabIndex === index && { color: "#e62117" }]}>{item}</Text>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingLeft: 16
  },
  text: {
    fontSize: 16,
    color: "#282b2e",
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  item: {
    paddingVertical: 12,
    marginRight: 32
  }
});
