import { StyleSheet } from "react-native";
import { WINDOW_WIDTH, WINDOW_HEIGHT, FontStyles, FigmaStyle } from "@akulaku-rn/akui-rn";

export default StyleSheet.create({
  container: {
    flex: 1,
    width: WINDOW_WIDTH,
    backgroundColor: "#fff"
  },
  rejectedHeader: {
    height: 50,
    width: WINDOW_WIDTH,
    backgroundColor: "#fff",
    shadowOffset: { width: 0, height: 2 },
    shadowColor: "rgba(0,0,0,0.1)",
    shadowRadius: 3,
    shadowOpacity: 1,
    marginBottom: 24,
    justifyContent: "center",
    paddingLeft: 24
  },
  rejectedHeaderText: {
    fontSize: 14,
    color: "#E62117"
  },
  bannerImg: { width: WINDOW_WIDTH, height: WINDOW_WIDTH * (212 / 720) },
  bannerImgNew: { width: WINDOW_WIDTH, height: WINDOW_WIDTH * (234 / 1080) },
  headerImg: {
    marginBottom: 16
  },
  headerImg2: {
    marginBottom: 0
  },
  headerText: {
    marginBottom: 13,
    color: "#333",
    fontSize: 14,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  headerTextNew: {
    marginBottom: 16,
    color: "#282B2E",
    fontSize: 18,
    ...StyleSheet.flatten(FontStyles["roboto-bold"])
  },
  loadingView: {
    width: WINDOW_WIDTH,
    height: WINDOW_HEIGHT,
    alignItems: "center",
    justifyContent: "center"
  },
  listItem: {
    marginHorizontal: 16
  },
  navIcon: {
    paddingHorizontal: 12,
    justifyContent: "center",
    alignItems: "center"
  },
  iosNavImage: {
    width: 11,
    height: 17
  },
  androidNavImage: {
    width: 24,
    height: 24
  },
  agreementTitle: {
    fontSize: 12,
    lineHeight: 14,
    color: "#6E737D"
  },
  icNumberView: {
    marginTop: 12,
    width: 280,
    paddingHorizontal: 24
  },
  icNumberViewNew: {
    marginTop: 8,
    alignItems: "center",
    paddingHorizontal: 24
  },
  icNumber: {
    fontSize: 14,
    color: "#333",
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    textAlign: "left",
    width: "100%"
  },
  icNumberNew: {
    fontSize: 14,
    lineHeight: 18,
    color: "#282B2E",
    textAlign: "center",
    width: "100%"
  },
  agreementView: {
    flexDirection: "row",
    alignSelf: "flex-start",
    marginRight: 33,
    marginBottom: 50
  },
  selectIcon: {
    width: 16,
    height: 16,
    marginRight: 8
  },
  unselectIcon: {
    width: 16,
    height: 16,
    marginRight: 8,
    borderRadius: 8,
    borderWidth: 1.2,
    borderColor: "#989FA9"
  },
  agreementContainer: {
    paddingHorizontal: 16,
    paddingTop: 8
  },
  tipsContainer: {
    backgroundColor: "#FFF4E6",
    borderRadius: 6,
    padding: 12,
    margin: 16,
    marginTop: 8
  },
  idPhotoTipsContainer: {
    backgroundColor: "#E6F8FF",
    borderRadius: 6,
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 20,
    marginTop: 8
  },
  tipsHeader: {
    flexDirection: "row",
    alignItems: "center"
  },
  tipsTitle: {
    color: "#FF8833",
    fontSize: 14,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  tipsTitle2: {
    color: "#282B2E",
    fontSize: 12
  },
  tipsMain: {
    marginTop: 8,
    fontSize: 11,
    color: "#FF8833",
    lineHeight: 14
  },
  tipsDetail: {
    fontSize: 11,
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    color: "#F5640A"
  },
  example: {
    color: "#0072D6",
    fontSize: 14,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  foldViewContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16
  },
  foldViewText: {
    color: FigmaStyle.Color.Grey_Text2,
    marginRight: 4
  },
  foldViewIcon: {
    height: 12,
    width: 12
  }
});
