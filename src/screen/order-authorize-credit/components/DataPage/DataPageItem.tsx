/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 17:21
 * @description
 */

import React, { useState } from "react";
import { LayoutChangeEvent, Text, TouchableOpacity, View, Image } from "react-native";
import { HandleGroupInfoList } from "../../dict/ProcessData";
import { NewEntityInfos } from "../../tool/DataHandling";
import Associated from "../Associated";
import DynamicComponent from "../DynamicComponent";
import { TFunction } from "i18next";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import CreditStore from "../../store";
import styles from "./styles";
import { AkuTextComponent, AkuTextComponentType } from "@akulaku-rn/akui-rn";

type Props = {
  item: HandleGroupInfoList;
  index: number;
  t: TFunction;
  store: pageStoreModel<CreditStore>;
  missingId: number | null;
  isFirstApply: boolean;
  testType: number;
  setContentContainerRef: (ref: View | null, i: NewEntityInfos) => void;
  contentContainerOnLayout?: (event: LayoutChangeEvent) => void;
  showUploadExample: () => void;
  setRef: (uid: number, ref: any) => void;
  setWaitArray: (waitArray: any[], id: number) => void;
  setValue: (text: string, id: number, type: string, key: string) => void;
  allItems: any[];
  selectItems: any[];
  renderHeaderNoSticky: () => JSX.Element;
};

const DataPageItem = React.memo(function DatapageItemComp({
  item,
  index,
  t,
  store,
  missingId,
  isFirstApply,
  testType,
  setContentContainerRef,
  contentContainerOnLayout,
  showUploadExample,
  setRef,
  setWaitArray,
  setValue,
  allItems,
  selectItems,
  renderHeaderNoSticky
}: Props) {
  const [showFold, setShowFold] = useState<boolean>(false);

  const containerStyle = { marginBottom: store.pageStore.newUi ? 18 : 24 };

  const onClickShowFold = () => {
    setShowFold(!showFold);
  };

  const showFoldItem = (itemNeedFold: boolean, showFoldItem: boolean) => {
    if (!showFoldItem) {
      return !itemNeedFold;
    }
    return showFoldItem;
  };

  const itemContent = item.entries.map((i: NewEntityInfos, k: number) => {
    return (
      <View
        key={k}
        style={[styles.listItem]}
        ref={ref => {
          setContentContainerRef(ref, i);
        }}
        onLayout={contentContainerOnLayout}
      >
        {!k && !!item.title ? (
          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={store.pageStore.newUi ? styles.headerTextNew : styles.headerText}>{item.title}</Text>
            {i.fieldName === "Upload ID photo" && !store.pageStore.showHideComponent ? (
              <TouchableOpacity onPress={showUploadExample}>
                <Text style={styles.example}>{t("查看示例")}</Text>
              </TouchableOpacity>
            ) : null}
          </View>
        ) : null}
        {showFoldItem(i.fold ?? false, showFold) ? (
          i.keyOptions ? (
            <Associated
              t={t}
              setRef={setRef}
              missingId={missingId}
              setWaitArray={setWaitArray}
              data={i}
              setValue={setValue}
              isFirstApply={isFirstApply}
              store={store}
              allItems={allItems}
              testType={testType}
            />
          ) : (
            <DynamicComponent
              data={i}
              t={t}
              setValue={setValue}
              containerStyle={containerStyle}
              missingId={missingId}
              isFirstApply={isFirstApply}
              selectItems={selectItems}
              store={store}
              allItems={allItems}
              testType={testType}
            />
          )
        ) : null}
      </View>
    );
  });
  // 插入不需要吸顶的头部提示组件
  if (index === 0) {
    itemContent.unshift(renderHeaderNoSticky());
  }
  if (!!item.showFoldView) {
    itemContent.push(
      <TouchableOpacity
        style={[styles.foldViewContainer, containerStyle]}
        activeOpacity={0.85}
        onPress={onClickShowFold}
      >
        <AkuTextComponent
          style={styles.foldViewText}
          type={AkuTextComponentType.Aku_font_14_regular}
          text={showFold ? t("收起") : t("展开")}
        />
        <Image
          source={showFold ? require("../../img/arrow_up.webp") : require("../../img/arrow_down.webp")}
          style={styles.foldViewIcon}
        />
      </TouchableOpacity>
    );
  }
  return <View style={{ marginTop: store.pageStore.newUi && index !== 0 ? 10 : 12 }}>{itemContent}</View>;
});

export default DataPageItem;
