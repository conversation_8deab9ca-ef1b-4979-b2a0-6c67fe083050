import React, { Component } from "react";
import {
  FlatList,
  View,
  KeyboardAvoidingView,
  Text,
  StatusBar,
  Image,
  Animated,
  NativeModules,
  UIManager,
  findNodeHandle,
  LayoutChangeEvent,
  StyleSheet,
  TouchableOpacity,
  ListRenderItem,
  ListRenderItemInfo
} from "react-native";
import _ from "lodash";
import styles from "./styles";
import BottomButton from "../BottomButton";
import Occupation from "../Occupation";
import {
  Loading,
  WINDOW_HEIGHT,
  NavigationBar,
  iOS,
  WINDOW_WIDTH,
  NetworkErrorComponent,
  AKDialog,
  FontStyles
} from "@akulaku-rn/akui-rn";
import PopUpPanel from "@akulaku-rn/akui-rn/src/components/PopUpPanel";
import Dialog from "@akulaku-rn/akui-rn/src/components/Dialog";
import { Shadow } from "@akulaku-rn/akui-rn/src/components/NeomorphShadows";
import Email from "../Email";
import api from "../../store/api";
import { autorun } from "mobx";
import { AgreementDialog, AgreementPopView } from "../Dialog/Agreement";
import { observer } from "mobx-react";
import {
  ClickNext,
  EnterLeavePage,
  SensorClickUploadExample,
  SensorPersonalBackClick,
  SensorSocialProtocolClick,
  SensorSocialProtocolExpose,
  SensorTypeClickAgreement,
  SensorTypePopClick,
  SensorTypePopView
} from "../../tool/EventTracking";
const { GetEnvInfoModule } = NativeModules;
import {
  AllItemDataHandling,
  BasisDataHandling,
  FormatData,
  NewEntityInfos,
  SelectItemsDataHandling,
  SubmitDataHandling,
  WaitArrayDataHandling
} from "../../tool/DataHandling";
import DynamicComponent from "../DynamicComponent";
import { ItemType } from "../../dict/ComponentType";
import { NativeImageCaptureModule, NativeConfigModule, NativeNavigationModule } from "common/nativeModules";
import NativeEventReportModule, {
  ReportEventName,
  ReportScene
} from "common/nativeModules/basics/nativeEventReportModule";
import NativeAdjustModule, { getEventName, AdjustEventType } from "common/nativeModules/sdk/nativeAdjustModule";
import NativeFirebaseModule from "common/nativeModules/sdk/nativeFirebaseModule";
import { StoreData } from "common/util/cache";
import ConfigHelper from "common/util/ConfigHelper";
import DeviceInfoReportHelper from "common/util/DeviceInfoReportHelper";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import { PageType } from "../../dict/PageType";
import NativePricyDialogModule, {
  NativePricyDialogModuleScene
} from "common/nativeModules/bussinse/nativePricyDialogModule";
import { PopupExposerHelper, reportClick, reporter } from "@akulaku-rn/rn-v4-sdk";
import nativeConfigModule from "common/nativeModules/basics/nativeConfigModule";
import { isLaunchSm } from "../../tool/SmUtils";
import { DialogType } from "@akulaku-rn/akui-rn/src/components/AKDialog";
import Associated from "../Associated";
import { PageItemI } from "../../dict/type";
import { BottomProtocol, IdPhotoProtocol, SocialProtocol } from "../BottomProtocol";
import { pageStoreModel } from "common/components/BaseContainer/Type";
import store from "../../store";
import UploadExample from "../UploadExample";
import { HandleGroupInfoList } from "../../dict/ProcessData";
import KindReminder from "../KindReminder";
import DataPageItem from "./DataPageItem";
import { formatDatePure } from "../../../authorize-credit/tool/dateUtils";
import { isJsonStr } from "../../tool/utils";
type Props = {
  navigation: any;
  store: pageStoreModel<store>;
  t: any;
  data: PageItemI;
  // 1：正常流程 2：合规流程 3: 合规2.0  4：授信BO流程打磨版本
  version: 1 | 2 | 3 | 4;
  layerNext: () => void;
  layerGoback: () => void;
  pageLength: number;
  nowStep: number;
  isFirstApply: boolean;
  partRejected: boolean;
  setpageType: (pageType: number) => void;
};

type States = {
  loadFailed: boolean;
  refreshing: boolean;
  dataSource: HandleGroupInfoList[];
  shadow: boolean;
  description: string;
  footView: null | boolean;
  navTitle: string;
  missingId: number | null;
  bannerImg: string;
  topTip: boolean;
  protocol: boolean;
  agreement: boolean;
  isAgree: boolean;
};
const AnimatedFlatList = Animated.createAnimatedComponent(FlatList);
@observer
export default class DataPage extends Component<Props, States> {
  selectItems: any[];

  waitArray: NewEntityInfos[];

  firstWaitArray: NewEntityInfos[];

  allItems: any[];

  loadDone: boolean;

  isConfirmIcNumber: boolean;

  beforeStep: number | null;

  scrollY: any;

  aliveDetect: boolean;

  flatListRef!: FlatList;

  nowShowItem: NewEntityInfos[];

  // 所有的 item的 ref
  mRefs: Record<number, any> = {};

  occupationRef?: any;

  //flatlist 内容高度
  contentHeight = 0;

  flatListY = 0;

  flatListHeight = 0;

  constructor(props: Props) {
    super(props);
    this.state = {
      loadFailed: false,
      refreshing: true,
      dataSource: [],
      missingId: null,
      shadow: false,
      description: "",
      footView: null,
      navTitle: "",
      bannerImg: "",
      isAgree: false,
      topTip: false,
      protocol: false,
      agreement: false
    };
    //所有selectView的子项数据
    this.selectItems = [];
    //当前页面所有需要提交的数据
    this.firstWaitArray = [];
    this.waitArray = [];
    //当前页面所有的选项
    this.allItems = [];
    this.loadDone = false;
    this.isConfirmIcNumber = false;
    this.beforeStep = null;
    this.scrollY = new Animated.Value(0);
    this.aliveDetect = false;
    this.nowShowItem = [];
  }

  componentDidMount() {
    const {
      nowStep,
      store: { pageStore },
      setpageType,
      isFirstApply
    } = this.props;
    autorun(() => {
      if (pageStore.nowStep !== this.beforeStep) {
        if (pageStore.nowStep === nowStep) {
          this.getPageData();
        }
        this.beforeStep = pageStore.nowStep;
      }
    });
  }

  _onLayoutContainer = (event: LayoutChangeEvent) => {
    this.flatListY = event.nativeEvent.layout.y;
  };

  _onLayoutFlatList = (event: LayoutChangeEvent) => {
    this.flatListHeight = event.nativeEvent.layout.height;
  };

  scrollToItemIndex = (missingId: number) => {
    const viewNode = findNodeHandle(this.mRefs[missingId]);
    viewNode &&
      UIManager.measure(
        viewNode,
        (x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
          const headerHeight = WINDOW_WIDTH * (212 / 720);
          if (pageY < this.flatListY + headerHeight) {
            this.flatListRef && this.flatListRef.scrollToOffset({ offset: 0, animated: true });
          } else if (pageY + height > this.flatListY + this.flatListHeight) {
            this.flatListRef && this.flatListRef.scrollToOffset({ offset: this.flatListHeight, animated: true });
          }
        }
      );
  };

  getPageData = () => {
    Loading.show();
    const {
      data: mData,
      store: { pageStore },
      setpageType,
      isFirstApply
    } = this.props;
    this.setState({ refreshing: true });
    pageStore.post(
      api.ENTRY_LIST,
      {
        pageNo: mData.pageNo,
        appVersion: GetEnvInfoModule.versionName,
        asiApplicationId: pageStore.asiApplicationId
      },
      async (response: any) => {
        const { data, success } = response;
        if (success) {
          const formatData = FormatData(data, pageStore, mData.pageType);
          const { groupInfos, title, bannerImg, topTip, protocol, agreement } = formatData;

          AllItemDataHandling.createData(formatData);
          WaitArrayDataHandling.createData(formatData);
          SelectItemsDataHandling.createData(formatData);
          BasisDataHandling.createData(formatData);
          // 后台需要展示社保协议，默认选中
          if (agreement) {
            pageStore.isSelectSocialProtocol = true;
            SensorSocialProtocolExpose();
          }

          this.allItems = AllItemDataHandling.allItems;
          this.waitArray = WaitArrayDataHandling.waitArray;
          this.firstWaitArray = WaitArrayDataHandling.waitArray;
          this.selectItems = SelectItemsDataHandling.selectItems;
          const { asiApplicationId, pageType } = BasisDataHandling;
          setpageType(mData.pageType);
          pageStore.asiApplicationId = asiApplicationId;
          const enterLeavepageData = {
            pageType: mData.pageType,
            isFirst: isFirstApply,
            enter: true,
            // testType: mData.testType,
            showBackBtn: pageStore.showBackBtn
          };
          EnterLeavePage(enterLeavepageData);
          SubmitDataHandling.setLastValue(this.allItems, pageStore);
          this.setState(
            {
              refreshing: false,
              loadFailed: false,
              dataSource: groupInfos,
              bannerImg,
              navTitle: title,
              topTip,
              protocol,
              agreement
            },
            () => {
              Loading.dismiss();
              this.loadDone = true;
            }
          );
          Loading.dismiss();
        } else {
          this.setState({ loadFailed: true, refreshing: false }, () => {
            Loading.dismiss();
          });
        }
      },
      () => {
        this.setState({ loadFailed: true, refreshing: false }, () => {
          Loading.dismiss();
        });
      }
    );
  };

  _setRef = (uid: number, ref: any) => {
    this.mRefs[uid] = ref;
  };

  _onLayoutItems = (event: LayoutChangeEvent) => {};

  // 根据关联项id，将可选项插入到对应的位置上，（主要是为了保证资料项的顺序）
  setWaitArray = (waitArray: any[], id: number) => {
    const newWaitArray: NewEntityInfos[] = [];
    this.firstWaitArray.forEach(element => {
      newWaitArray.push(element);
      if (element.id === id) {
        newWaitArray.push(...waitArray);
      }
    });
    this.waitArray = Array.from(new Set(newWaitArray));
  };

  renderItem: ListRenderItem<HandleGroupInfoList> = (info: ListRenderItemInfo<HandleGroupInfoList>) => {
    const { item, index } = info;
    const {
      t,
      isFirstApply,
      store
      // data: { testType }
    } = this.props;
    const { missingId } = this.state;

    return (
      <DataPageItem
        setWaitArray={this.setWaitArray}
        setValue={this.setValue}
        selectItems={this.selectItems}
        renderHeaderNoSticky={this.renderHeaderNoSticky}
        allItems={this.allItems}
        setRef={this._setRef}
        showUploadExample={this.showUploadExample}
        contentContainerOnLayout={this._onLayoutItems}
        setContentContainerRef={(_ref, info) => {
          if (_ref) {
            this.mRefs[info.id] = _ref;
          }
        }}
        item={item}
        index={index}
        t={t}
        store={store}
        missingId={missingId}
        isFirstApply={isFirstApply}
        testType={1}
      />
    );
  };

  setValue = (text: string, id: number, type: string, key: string) => {
    const {
      data,
      store: {
        pageStore,
        runtime: { uid }
      }
    } = this.props;

    pageStore.useCreditData[id] = text;
    StoreData(`${uid}useCreditDatas`, JSON.stringify(pageStore.useCreditData));
  };

  keyExtractor = (item: any, index: number) => `${item.id}-${index}`;

  go2AliveDetect = async () => {
    const { t } = this.props;
    const data = this.allItems.find(i => {
      return i.type === ItemType.living;
    });
    const response = await NativeImageCaptureModule.go2AliveDetect(data.kycStatus ? 19 : 18);

    if (response.success && !_.isEmpty(response.data)) {
      this.aliveDetect = true;
      this.setValue(response.data.businessId, data.id, data.type, "");
      this.nextAction();
    } else {
      NativeToast.showMessage(t("人脸识别失败，请重试"));
    }
  };

  next = _.debounce(
    () => {
      this.nextAction();
    },
    2000,
    { leading: true, trailing: false }
  );

  nextAction = async () => {
    const {
      pageLength,
      store: { pageStore },
      t,
      isFirstApply,
      data
    } = this.props;
    ClickNext(data.pageType, isFirstApply);
    if (data.pageType === PageType.face_recognition && !this.aliveDetect) {
      this.listPermission();
      return;
    }
    const submitData = SubmitDataHandling.createData(this.waitArray, pageStore);
    if (!submitData.isComplete) {
      this.setState({ missingId: submitData.missingId });
      const findIndex = this.allItems.findIndex(item => {
        return item.id === submitData.missingId;
      });
      this.scrollToItemIndex(submitData.missingId);
      if (findIndex === 3 && data.pageType === PageType.id_Photo) {
        this.flatListRef.scrollToOffset({ offset: WINDOW_HEIGHT, animated: true });
      }
      return;
    }

    if (data.pageType === PageType.id_Photo && !this.isConfirmIcNumber && isFirstApply) {
      //TODO身份证确认弹窗 弹窗从缓存中取
      const icNumberItem = this.allItems.find(i => {
        return `${i.id}` === "1101001";
      });
      const icNameItem = this.allItems.find(i => {
        return `${i.id}` === "1101000";
      });
      const icBirthdayItem = this.allItems.find(i => {
        return `${i.id}` === "1101009";
      });
      const icBirthAddressItem = this.allItems.find(i => {
        return `${i.id}` === "1101004";
      });
      this.alertIcNumber(icNumberItem, icNameItem, icBirthAddressItem, icBirthdayItem);
      return;
    }
    const waitSumbit = {
      pageNo: data.pageNo,
      entries: submitData.entries,
      asiApplicationId: pageStore.asiApplicationId
    };
    if (!!pageStore.progressInfosNodeObj) {
      const checkRes = pageStore.progressInfosNodeObj.checkPageNoPass(data.pageNo);
      if (!checkRes.canSubmit) {
        NativeToast.showMessage(this.props.t("抱歉！网络异常，请退出重新进入"));
        return;
      }
      pageStore.dataSubmit(waitSumbit, () => {
        checkRes.item.submitted = true;
        this.layerNextAndReport();
      });
    } else {
      pageStore.dataSubmit(waitSumbit, this.layerNextAndReport);
    }
  };

  listPermission = () => {
    //申请列表权限
    if (iOS) {
      this.privacySubmit();
    } else {
      const { isFirstApply } = this.props;

      reporter.setUri("js:/authorize-credit/apply-info");
      reporter.setPageConfig({ sp: { status: isFirstApply ? "first" : "not_first" }, sn: 300136 });
      //处理安卓隐私披露弹窗
      this.handleAndroidPermission();
    }
  };

  handleSubmitData = (isAgree: boolean) => {
    const { isFirstApply } = this.props;
    if (isAgree) {
      SensorTypePopClick("agree", isFirstApply);
      this.privacySubmit();
    } else {
      SensorTypePopClick("disagree", isFirstApply);
    }
  };

  handleAndroidPermission = async () => {
    if (NativeModules.GetEnvInfoModule.versionCode >= 1186) {
      const pass = await NativePricyDialogModule.showPricyDialog({ scene: NativePricyDialogModuleScene.Credit });
      this.handleSubmitData(pass);
    } else {
      const { t } = this.props;
      const dialog = new PopupExposerHelper(
        Dialog,
        {
          cn: 2,
          ei: {},
          eit: 0,
          ext: {}
        },
        { screenNumber: "300136" },
        {
          beforeReportV4: data => {
            SensorTypePopView();
            return data;
          }
        }
      );
      AKDialog.show({
        type: DialogType.C3_1_1,
        title: t("授权信息"),
        desc: t("为了给您提供完善的授信服务，我们需要获取您的安装列表和本机号码。如果您不同意，则我们视为你取消申请。"),
        positiveText: t("同意"),
        onPositivePress: () => {
          this.handleSubmitData(true);
        },
        negativeText: t("不同意"),
        onNegativePress: () => {
          this.handleSubmitData(false);
        }
      });
    }
  };

  // 是否调用数美sdk
  toInvokeSm = async () => {
    const isLaunch = await isLaunchSm();
    if (isLaunch) {
      NativeEventReportModule.reportSmDeviceData();
    }
  };

  privacySubmit = async () => {
    const {
      store: { pageStore },
      isFirstApply
    } = this.props;
    const powers = iOS
      ? [
          ReportEventName.GPS,
          ReportEventName.CONTACT,
          ReportEventName.BATTERY,
          ReportEventName.DEVICE_FINGER_PRINT,
          ReportEventName.APP_LIST
        ]
      : [
          ReportEventName.GPS,
          ReportEventName.DEVICE_FINGER_PRINT,
          ReportEventName.SENSORS,
          ReportEventName.BATTERY,
          ReportEventName.CONTACT,
          ReportEventName.APP_LIST
        ];
    const reportData: any = {
      scene: ReportScene.SUBMIT_CREDIT_AUTH,
      events: powers,
      configs: { skipReportRes: true, ext: { apply_id: pageStore.asiApplicationId } }
    };
    const result = await NativeConfigModule.getEnvInfoSilent();
    if (result.success && result.data.osVersionCode >= 31) {
      reportData.callbacks = {
        onCallbackNeedPreciseLocation: async (): Promise<void> => {
          const { t } = this.props;
          return new Promise((resolve, reject) => {
            AKDialog.show({
              type: DialogType.C3_1_1,
              title: t("确切位置提示"),
              desc: t("为了向您提供更优质的金融服务以及风险管理，建议您授权您的确切位置"),
              positiveText: t("我知道了-权限"),
              onPositivePress: () => {
                resolve();
              }
            });
          });
        }
      };
    }
    const response = await DeviceInfoReportHelper.reportEventsWithAskPermissions(reportData);
    const { success } = response;

    if (success) {
      await nativeConfigModule.requestLocationBeforeCreditApplyCommit({
        asiApplicationId: pageStore.asiApplicationId,
        status: isFirstApply ? "first" : "not_first"
      });
      this.toInvokeSm();
      this.go2AliveDetect();
    } else {
      this.refusePower();
    }
  };

  refusePower = () => {
    const { t } = this.props;
    AKDialog.show({
      type: DialogType.C3_1_1,
      desc: t("请允许AKULAKU获取您的联系人、位置和电话管理权限以便给您提供完善的金融服务"),
      positiveText: t("确认")
    });
  };

  onContentSizeChange = (w: number, h: number) => {
    this.contentHeight = h;
    if (WINDOW_HEIGHT - 194 < h) {
      this.setState({
        shadow: true
      });
    }
    if (h > WINDOW_HEIGHT - 194 && h - WINDOW_HEIGHT < 130 && this.state.footView === null) {
      this.setState({
        footView: true
      });
    }
  };

  private async reportMalai() {
    const {
      store: { runtime }
    } = this.props;
    const {
      data: { adjustId, deviceId }
    } = await NativeConfigModule.getEnvInfoSilent();
    NativeAdjustModule.logger(getEventName(AdjustEventType.aku_credit_initiate_apply_202011), {
      adjust_id: adjustId || null,
      uid: runtime.uid || null,
      deviceId: deviceId || null
    });
    NativeFirebaseModule.logger("aku_credit_initiate_apply_202011", {
      adjust_id: adjustId || null,
      uid: runtime.uid || null,
      deviceId: deviceId || null,
      application_credit_id: null
    });
  }

  layerNextAndReport = () => {
    const {
      layerNext,
      nowStep,
      isFirstApply,
      data: { pageType }
    } = this.props;
    const enterLeavepageData = { pageType: pageType, isFirst: isFirstApply, testType: 1 };
    EnterLeavePage(enterLeavepageData);
    if (nowStep === 0) {
      if (ConfigHelper.isSilvrrPlus()) {
        this.reportMalai();
      }
    }
    layerNext();
  };

  onScroll = (e: any) => {
    this.props.store.pageStore.showEmailTips = false;
  };

  layerGoback = () => {
    const { nowStep, layerGoback, isFirstApply, data } = this.props;
    const enterLeavepageData = { pageType: data.pageType, isFirst: isFirstApply };
    EnterLeavePage(enterLeavepageData);
    layerGoback && layerGoback();
    if (nowStep === 0) {
      SensorPersonalBackClick();
    }
  };

  showUploadExample = () => {
    const { t } = this.props;
    SensorClickUploadExample();
    Dialog.show({
      renderContent: <UploadExample t={t} title={t("正确示例")} />,
      containerStyle: { borderRadius: 12, width: 288 },
      positiveText: t("好的"),
      positiveStyle: { color: "#F32823", fontSize: 16, ...StyleSheet.flatten(FontStyles["rob-medium"]) },
      seperatorColor: "#E2E5E9"
    });
  };

  showProtocols = (item: any) => {
    const { t } = this.props;
    if (item.type === 1) {
      Dialog.show({
        renderContent: <AgreementDialog t={t} data={item} />,
        positiveText: t("我知道了")
      });
    }
    if (item.type === 2) {
      PopUpPanel.show({
        title: "",
        childrenComponent: <AgreementPopView t={t} data={item} />
      });
    }
  };

  alertIcNumber = (icNumberItem: any, icNameItem: any, icBirthAddressItem: any, icBirthdayItem: any) => {
    const {
      store: { pageStore },
      t,
      isFirstApply
    } = this.props;
    const birthAddressItemJson = isJsonStr(pageStore.useCreditData[icBirthAddressItem.id]);
    const showBirthAddress = birthAddressItemJson.success
      ? `${birthAddressItemJson.data.city}`
      : birthAddressItemJson.data;
    const dialog = new PopupExposerHelper(
      Dialog,
      {
        cn: 102,
        ei: {},
        eit: 0,
        ext: {},
        sp: { status: isFirstApply ? "first" : "not_first" }
      },
      { screenNumber: "300005" }
    );
    dialog.show({
      containerStyle: { borderRadius: 12 },
      seperatorColor: "#E2E5E9",
      onPositivePress: () => {
        this.isConfirmIcNumber = true;
        reportClick({
          sn: 300005,
          cn: 103,
          sp: { status: isFirstApply ? "first" : "not_first" }
        });
        this.nextAction();
      },
      onNegativePress: () => {
        reportClick({
          sn: 300005,
          cn: 104,
          sp: { status: isFirstApply ? "first" : "not_first" }
        });
      },
      renderContent: (
        <View style={{ width: 280 }}>
          <Text
            style={{
              textAlign: "center",
              fontSize: 16,
              color: "#282B2E",
              marginBottom: 8,
              ...StyleSheet.flatten(FontStyles["rob-medium"])
            }}
          >
            {t("再次确认")}
          </Text>
          <View style={{ paddingHorizontal: 24 }}>
            <Text style={{ textAlign: "center", fontSize: 14, color: "#6E737D" }}>
              {t("仔细核对信息，若错误请返回修改，请确保真实有效。")}
            </Text>
          </View>
          <View style={styles.icNumberViewNew}>
            <Text style={styles.icNumberNew}>
              {t("姓名")}:{pageStore.useCreditData[icNameItem.id]}
            </Text>
            <Text style={[styles.icNumberNew]}>
              {t("身份证件号")}:{pageStore.useCreditData[icNumberItem.id]}
            </Text>
            <Text style={[styles.icNumberNew]}>
              {t("出生地/出生日期")}:
              {`${showBirthAddress}, ${formatDatePure(Number(pageStore.useCreditData[icBirthdayItem.id])) || ""}`}
            </Text>
          </View>
        </View>
      ),
      buttonSequence: 0,
      positiveText: t("确认"),
      negativeText: t("修改"),
      negativeStyle: { color: "#6E737D", fontSize: 16, ...StyleSheet.flatten(FontStyles["rob-medium"]) },
      positiveStyle: { color: "#F32823", fontSize: 16, ...StyleSheet.flatten(FontStyles["rob-medium"]) }
    });
  };

  listHeaderComponent = () => {
    const {
      t,
      partRejected,
      data,
      store: { pageStore }
    } = this.props;
    const { topTip } = this.state;
    if (partRejected && data.pageType !== PageType.face_recognition) {
      return (
        <Shadow height={50} width={WINDOW_WIDTH} style={styles.rejectedHeader}>
          <Text style={styles.rejectedHeaderText}>{t("请修改以下信息")}</Text>
        </Shadow>
      );
    }

    if (!this.state.bannerImg) return null;
    return (
      <>
        <Image
          source={{ uri: this.state.bannerImg }}
          style={[pageStore.newUi ? styles.bannerImgNew : styles.bannerImg]}
        />
        {pageStore.newUi ? <View style={{ height: 8, backgroundColor: "#EFF2F6" }} /> : null}
      </>
    );
  };

  renderHeaderNoSticky = () => {
    const {
      t,
      data,
      store: { pageStore }
    } = this.props;
    const { topTip } = this.state;
    if (topTip) {
      return <KindReminder isNew={pageStore.newUi} t={t} />;
    }
    if (data.pageType === PageType.id_Photo) {
      return (
        <View style={{ backgroundColor: "#fff" }}>
          <View style={styles.idPhotoTipsContainer}>
            <View style={styles.tipsHeader}>
              <Image
                source={require("../../img/id_photo_tips.webp")}
                style={{ width: 12, height: 15, marginRight: 12 }}
              />
              <View style={{ flex: 1 }}>
                <Text style={styles.tipsTitle2}>{t("Akulaku为您提供银行级别的安全防护以保护您的个人隐私信息")}</Text>
              </View>
            </View>
          </View>
        </View>
      );
    }
    return <View style={{ height: 8 }} />;
  };

  showFooter = () => {
    const {
      data: mData,
      t,
      version,
      store: { pageStore }
    } = this.props;
    const { protocol } = this.state;
    // 正常流程 不用显示协议
    if (version === 1) return false;
    // 服务器控制
    if (!protocol) return false;
    if (mData.pageType === PageType.id_Photo) {
      return pageStore.showHideComponent;
    }

    return mData.pageType === PageType.emergency_contact;
  };

  listFooterComponent = () => {
    const showFooter = this.showFooter();

    const {
      data: mData,
      t,
      store: { pageStore }
    } = this.props;
    const { isAgree, agreement } = this.state;
    if (showFooter) {
      if (mData.pageType === PageType.emergency_contact) {
        return (
          <BottomProtocol
            isSelected={isAgree}
            isNew={pageStore.newUi}
            content={t("特此声明所填写的资金提供者信息及资金来源均真实准确。")}
            onPressIcon={() => {
              this.setState({ isAgree: !isAgree });
              SensorTypeClickAgreement(mData.pageType);
            }}
          />
        );
      } else if (mData.pageType === PageType.id_Photo) {
        return (
          <IdPhotoProtocol
            config4030={pageStore.complianceConfig}
            t={t}
            style={{ marginBottom: pageStore.newUi ? 0 : 24 }}
            isNew={pageStore.newUi}
            isSelected={isAgree}
            onPressIcon={() => {
              this.setState({ isAgree: !isAgree });
              SensorTypeClickAgreement(mData.pageType);
            }}
          />
        );
      }
    }
    if (agreement) {
      return (
        <SocialProtocol
          style={{ marginBottom: pageStore.newUi ? 0 : 24 }}
          isNew={pageStore.newUi}
          isSelected={pageStore.isSelectSocialProtocol}
          content={`Anda setuju dan mengizinkan Akulaku untuk melakukan verifikasi data pribadi Anda yang terdapat di BPJSTK. Verifikasi ini bertujuan untuk meningkatkan penilaian kredit Anda.`}
          protocol={"Kerjasama Akulaku dan BPJSTK."}
          onPressIcon={() => {
            pageStore.isSelectSocialProtocol = !pageStore.isSelectSocialProtocol;
            SensorSocialProtocolClick();
          }}
          onPressContent={() => {
            NativeNavigationModule.navigate({
              url: "https://www.akulaku.com/artikel/bpjsauth/"
            });
          }}
        />
      );
    }
    return <View />;
  };

  render() {
    const {
      nowStep,
      store,
      store: { pageStore },
      data: mData,
      version,
      t
    } = this.props;
    const { dataSource, loadFailed, refreshing, missingId, isAgree } = this.state;
    if (pageStore.nowStep !== nowStep) {
      return <View style={styles.container} />;
    }
    if (refreshing) {
      return <View style={styles.container} />;
    }
    if (loadFailed) {
      return (
        <View style={{ flex: 1 }}>
          <NavigationBar title={t("加载中")} onBackPress={this.layerGoback} />
          <NetworkErrorComponent
            containerStyle={styles.container}
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            onRetryPress={this.getPageData}
            buttonText={t("global:刷新")}
          />
        </View>
      );
    }
    const disabled = this.showFooter() && !isAgree;
    return (
      <View style={styles.container}>
        {iOS && <StatusBar barStyle="dark-content" />}
        <NavigationBar
          renderLeft={nowStep === 0 && !pageStore.showBackBtn ? <View style={{ width: 32, height: 32 }} /> : null}
          title={this.state.navTitle}
          onBackPress={this.layerGoback}
        />
        <KeyboardAvoidingView style={{ flex: 1 }} onLayout={this._onLayoutContainer}>
          <Animated.FlatList
            onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: this.scrollY } } }], {
              useNativeDriver: false,
              listener: event => {
                // 在这里做一些额外的工作
                this.onScroll(event);
              }
            })}
            stickyHeaderIndices={pageStore.newUi ? [] : [0]}
            onLayout={this._onLayoutFlatList}
            ref={(flatList: FlatList) => {
              this.flatListRef = flatList;
            }}
            removeClippedSubviews={false}
            scrollEventThrottle={1}
            bounces={false}
            keyExtractor={this.keyExtractor}
            data={dataSource}
            extraData={[missingId, pageStore.isSelectSocialProtocol]}
            renderItem={this.renderItem}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps={"handled"}
            ListHeaderComponent={this.listHeaderComponent}
            ListFooterComponent={this.listFooterComponent}
            onContentSizeChange={this.onContentSizeChange}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
          <Email store={store} allItems={this.allItems} />
          <BottomButton
            style={{ paddingTop: pageStore.newUi ? 12 : 8 }}
            text={mData.pageType === PageType.face_recognition ? t("立即刷脸") : t("下一步")}
            next={this.next}
            disabled={disabled}
            t={t}
          />
        </KeyboardAvoidingView>
      </View>
    );
  }
}
