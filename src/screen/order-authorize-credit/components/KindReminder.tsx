/*
 * @LastEditors: zou yu
 * @Description:
 * @FilePath: /akulaku-ec-user-in/src/screen/authorize-credit/components/KindReminder.tsx
 */
import { StyleSheet, Text, View, Image } from "react-native";
import React from "react";
import { FontStyles } from "@akulaku-rn/akui-rn";
import { TFunction } from "i18next";

type Props = {
  t: TFunction;
  isNew: boolean;
};

const KindReminder = ({ t, isNew }: Props) => {
  if (isNew) {
    return (
      <View style={{ backgroundColor: "#fff" }}>
        <View style={styles.tipsContainerNew}>
          <View style={styles.tipsHeader}>
            <Text style={styles.tipsTitleNew}>{t("温馨提示：")}</Text>
          </View>
          <Text style={styles.tipsMainNew}>{t("您选择的职业没有收入来源，需要您填写资金提供者信息。")}</Text>
          <Text style={styles.tipsDetailNew}>{t("温馨提示，不能填写与本人相同的信息。")}</Text>
        </View>
      </View>
    );
  }
  return (
    <View style={{ backgroundColor: "#fff" }}>
      <View style={styles.tipsContainer}>
        <View style={styles.tipsHeader}>
          <Image source={require("./SelectItem/img/tip_icon.webp")} style={{ width: 14, height: 14, marginRight: 8 }} />
          <Text style={styles.tipsTitle}>{t("温馨提示：")}</Text>
        </View>
        <Text style={styles.tipsMain}>{t("您选择的职业没有收入来源，需要您填写资金提供者信息。")}</Text>
        <Text style={styles.tipsDetail}>{t("温馨提示，不能填写与本人相同的信息。")}</Text>
      </View>
    </View>
  );
};

export default KindReminder;

const styles = StyleSheet.create({
  tipsContainer: {
    backgroundColor: "#FFF4E6",
    borderRadius: 6,
    padding: 12,
    margin: 16,
    marginTop: 8
  },
  tipsContainerNew: {
    backgroundColor: "#E6F8FF",
    borderRadius: 6,
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 18,
    marginTop: 8
  },
  tipsHeader: {
    flexDirection: "row",
    alignItems: "center"
  },
  tipsTitle: {
    color: "#FF8833",
    fontSize: 14,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  tipsTitleNew: {
    color: "#282B2E",
    fontSize: 14,
    ...StyleSheet.flatten(FontStyles["rob-medium"])
  },
  tipsTitle2: {
    color: "#282B2E",
    fontSize: 12
  },
  tipsMain: {
    marginTop: 8,
    fontSize: 11,
    color: "#FF8833",
    lineHeight: 14
  },
  tipsMainNew: {
    marginTop: 8,
    fontSize: 11,
    color: "#282B2E",
    lineHeight: 14
  },
  tipsDetail: {
    fontSize: 11,
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    color: "#F5640A"
  },
  tipsDetailNew: {
    fontSize: 11,
    ...StyleSheet.flatten(FontStyles["rob-medium"]),
    color: "#282B2E"
  }
});
