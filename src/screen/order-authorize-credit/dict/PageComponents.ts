import React from "react";
import {
  Address,
  AnimatedTextInput,
  FaceVerification,
  Search,
  SelectItem,
  Photo,
  ContactComponent,
  Slider,
  UndefinedComponent
} from "../components";
import SimpleAddress from "../components/SimpleAddress";
import { ItemType } from "./ComponentType";
import IdAddress from "../components/Address/IdAddress";

export interface ComponentInfo {
  component: typeof React.Component | React.FunctionComponent<any>;
  extraPropsKeys?: Array<string>;
}

export type PageComponentsMap = Record<ItemType, ComponentInfo>;

const PAGE_COMPONENTS_MAP: any = {
  [ItemType.date]: {
    component: SelectItem
  },
  [ItemType.select]: {
    component: SelectItem
  },
  [ItemType.search_company]: {
    component: Search
  },
  [ItemType.text]: {
    component: AnimatedTextInput
  },
  [ItemType.number]: {
    component: AnimatedTextInput
  },
  [ItemType.id_number]: {
    component: AnimatedTextInput
  },
  [ItemType.pic]: {
    component: Photo
  },
  [ItemType.address]: {
    component: Address
  },
  [ItemType.simpleAddress]: {
    component: SimpleAddress
  },
  [ItemType.idAddress]: {
    component: IdAddress
  },
  [ItemType.iphone]: {
    component: AnimatedTextInput
  },
  [ItemType.email]: {
    component: AnimatedTextInput
  },
  [ItemType.living]: {
    component: FaceVerification
  },
  [ItemType.contact]: {
    component: ContactComponent
  },
  [ItemType.slider]: {
    component: Slider
  },
  [ItemType.hidden]: {
    component: UndefinedComponent
  }
};

export default PAGE_COMPONENTS_MAP;
