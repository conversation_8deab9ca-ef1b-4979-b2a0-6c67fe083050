import { isJsonStr } from "./utils";
import { NewEntityInfos } from "./DataHandling";
import { ItemType } from "../dict/ComponentType";

type FormattedItemData = {
  id: number;
  pageType: number;
  lastValue: string;
  [key: string]: any;
};

type initConfigType = { ocrTextImageUi?: boolean; cacheData: Record<number, string> };

/**
 *  @description init 格式化数据后的数据处理
 *  @description initCache 进入授信页面的时候，缓存数据的处理
 *  @description preSubmit 页面提交数据，最后的数据处理
 */

export default class DataProcess {
  static DataProcessObj: Record<
    number,
    {
      init?: (formattedItemData: NewEntityInfos, config: initConfigType) => Record<string, any>; // 拉取数据后数据处理
      initCache?: (cacheData: Record<number, string>, config: initConfigType) => Record<string, any>; // 拉取数据后数据处理
      preSubmit?: (data: { entryId: number; value: string }) => { entryId: number; value: string }; // 提交接口前数据处理
    }
  > = {
    1103010: {
      init: (formattedItemData: NewEntityInfos, config: initConfigType) => {
        if (formattedItemData.type === ItemType.slider) {
          //110301040 是slider的第一个选项， 用这个来判断lastValue是不是新的值，用于修复用户缓存了旧组件的值
          if (Number(formattedItemData.lastValue) < 110301040) {
            formattedItemData.lastValue = "";
            config.cacheData[formattedItemData.id] = "";
          }
        }
        return formattedItemData;
      },
      initCache: (cacheData: Record<number, string>, config: initConfigType) => cacheData,
      preSubmit: (data: { entryId: number; value: string }) => data
    },
    1101004: {
      initCache: (data: Record<number, string>, config: { ocrTextImageUi?: boolean }) => {
        if (!!data[1101004]) {
          /// 在OCR需求中出生地址换成了TEXT组件类型，历史数据需要进行修复
          const jsonData = isJsonStr(data[1101004]);
          // 灰度内接口返回json串 -- 灰度内需要将地址json 转变成 city的字符串
          if (!!config.ocrTextImageUi && jsonData.success) {
            data[1101004] = `${jsonData.data.city || ""}`;
          }
          // 灰度外接口返回不是json串 -- 灰度外需要将非json串 重制为空字符串
          if (!config.ocrTextImageUi && !jsonData.success) {
            data[1101004] = "";
          }
          // 灰度外接口返回是json串, 但是没有Province -- 灰度外需要将json串 重制为空字符串
          if (!config.ocrTextImageUi && !jsonData.success) {
            data[1101004] = "";
          }
        }
        return data;
      },
      init: (formattedItemData: NewEntityInfos, config: { ocrTextImageUi?: boolean }) => {
        if (!!formattedItemData.lastValue) {
          /// 在OCR需求中出生地址换成了TEXT组件类型，历史数据需要进行修复
          const jsonData = isJsonStr(formattedItemData.lastValue);
          // 灰度内接口返回json串 -- 灰度内需要将地址json 转变成 city的字符串
          if (!!config.ocrTextImageUi && jsonData.success) {
            formattedItemData.lastValue = `${jsonData.data.city || ""}`;
          }
          // 灰度外接口返回不是json串 -- 灰度外需要将非json串 重制为空字符串
          if (!config.ocrTextImageUi && !jsonData.success) {
            formattedItemData.lastValue = "";
          }
          // 灰度外接口返回是json串, 但是没有Province -- 灰度外需要将json串 重制为空字符串
          if (!config.ocrTextImageUi && !jsonData.success) {
            formattedItemData.lastValue = "";
          }
        }
        console.log("init 1 formattedItemData", formattedItemData);
        return formattedItemData;
      },
      preSubmit: item => {
        const jsonRes = isJsonStr(item.value);
        if (!jsonRes.success) {
          item.value = JSON.stringify({ city: item.value });
        }
        return item;
      }
    }
  };

  static preSubmitProcess = (entries: { entryId: number; value: string }[]) => {
    entries.forEach(item => {
      const dataObj = DataProcess.DataProcessObj[item.entryId];
      if (!!dataObj) {
        dataObj.preSubmit && dataObj.preSubmit(item);
      }
    });
  };

  static initDataProcess = (formattedItemData: NewEntityInfos, config: initConfigType) => {
    const dataObj = DataProcess.DataProcessObj[formattedItemData.id];
    if (!!dataObj) {
      dataObj.init && dataObj.init(formattedItemData, config);
    }
  };

  static initCacheData = (cacheData: Record<number, string>, config: initConfigType) => {
    Object.keys(cacheData).forEach((key: string) => {
      const dataObj = DataProcess.DataProcessObj[Number(key)];
      if (!!dataObj) {
        dataObj.initCache && dataObj.initCache(cacheData, config);
      }
    });
  };
}
