import { ClickNextDic, EnterLeavePageDic, SensorClickItemDic, V3V4ClickItemDic } from "../dict/EventTrackingDictionary";
import _ from "lodash";
import NativeSensorModule, { SensorType } from "common/nativeModules/sdk/nativeSensroModule";
import { NativeActionLogModule, NativeRNBuzSentryModule } from "common/nativeModules";
import { ScreenAction } from "common/nativeModules/basics/nativeActionLogModule";
import { reportClick, reporter } from "@akulaku-rn/rn-v4-sdk";
import { PageType } from "../dict/PageType";
import { SensorEvent } from "nativeModules/sdk/nativeSensroModule";

const EnableSensorReport = true;

// 授信模块基础调用 NativeSensorModule.reportSence 神策上报统一方法，方便后续在这里加统一的参数
const CreditBaseReportSence = (type: SensorType, event: SensorEvent) => {
  if (EnableSensorReport) {
    const finalEvent = {
      ...event,
      extra: {
        ...(event.extra || {}),
        Aku_scene: "2"
      }
    };
    NativeSensorModule.reportSence(type, finalEvent);
  }
};

const SensorTypeClickItem = (pageType: number, entryid: number) => {
  const dic = SensorClickItemDic[pageType];
  if (!!dic) {
    dic.extra = { entryid };
    CreditBaseReportSence(SensorType.CLICK, dic);
  }
};

const ClickNext = (pageType: number, isFirst: boolean) => {
  const dic = ClickNextDic[pageType];
  if (!!dic) {
    !!dic.sensorLogger && CreditBaseReportSence(SensorType.CLICK, dic.sensorLogger);

    if (dic.v3Dic && Object.keys(dic.v3Dic).length) {
      NativeActionLogModule.reportClick(dic.v3Dic);
    }

    if (!!dic.v4Dic && Object.keys(dic.v4Dic).length) {
      dic.v4Dic.sp = { status: isFirst ? "first" : "not_first" };
      reportClick(dic.v4Dic);
    }
  }
};

const EnterLeavePage = (data: {
  pageType: number;
  isFirst: boolean;
  enter?: boolean;
  testType?: number;
  showBackBtn?: boolean;
}) => {
  const { pageType, isFirst, enter, testType, showBackBtn } = data;
  const dic = EnterLeavePageDic[pageType];
  const testDict =
    testType !== undefined
      ? {
          ABTest: testType === 1 ? "new_nameinFirst" : "new_nameinThird",
          Backtest: showBackBtn ? "Back" : "NoBack",
          Aku_UserStatus: "-1"
        }
      : {};
  if (!!dic) {
    if (enter) {
      !!dic.sensorLogger &&
        CreditBaseReportSence(SensorType.VIEW_SCREEN_ENTER, { ...dic.sensorLogger, extra: testDict });

      !!dic.v3Dic && NativeActionLogModule.reportEnter(dic.v3Dic);

      if (!!dic.v4Dic) {
        dic.v4Dic.sp = { status: isFirst ? "first" : "not_first" };
        reporter.enterScreen(dic.v4Dic);
      }
    } else {
      !!dic.sensorLogger &&
        CreditBaseReportSence(SensorType.VIEW_SCREEN_LEAVE, { ...dic.sensorLogger, extra: testDict });

      !!dic.v3Dic && NativeActionLogModule.reportLeave(dic.v3Dic);

      if (!!dic.v4Dic) {
        dic.v4Dic.sp = { status: isFirst ? "first" : "not_first" };
        reporter.leaveScreen(dic.v4Dic);
      }
    }
  }
};

const V3ClickItem = (entryid: number, end = false) => {
  const dic = V3V4ClickItemDic[entryid];
  if (!!dic && !!dic.v3Dic) {
    if (dic.v3Dic.controlAction === "input") {
      delete dic.v3Dic.controlAction;
      dic.v3Dic.screenAction = ScreenAction.stop;
      NativeActionLogModule.reportInput(dic.v3Dic);
    } else if (dic.v3Dic.controlAction === "begin/end") {
      delete dic.v3Dic.controlAction;
      dic.v3Dic.screenAction = ScreenAction.stop;
      if (end) {
        NativeActionLogModule.reportEnd(dic.v3Dic);
      } else {
        NativeActionLogModule.reportBegin(dic.v3Dic);
      }
    }
  }
};

const SensorClickOccupationItem = (data: { entryid: number; buttonName: string }) => {
  CreditBaseReportSence(SensorType.POPCLICK, {
    page_name: "Personal Information page",
    page_id: "841",
    pop_id: "pop30397",
    pop_name: "occupation",
    element_id: "pop3039701",
    element_name: "occupation",
    extra: {
      entryid: data.entryid,
      buttonName: data.buttonName
    }
  });
};

const V4ClickItem = (data: { entryid: number; type: string; isFirst: boolean; selectData?: any }) => {
  const { entryid, type, isFirst, selectData } = data;
  const dic = V3V4ClickItemDic[entryid];
  if (!!dic && !!dic.v4Dic) {
    delete dic.v4Dic.controlAction;
    dic.v4Dic.sp = { status: isFirst ? "first" : "not_first" };
    if (type === "select") {
      dic.v4Dic.bct = 33;
      dic.v4Dic.bi = { key: _.isNil(selectData) ? -1 : selectData };
      reportClick(dic.v4Dic);
    } else {
      reportClick(dic.v4Dic);
    }
  }
};

const SensorTypeClickContacts = (isFirst: boolean) => {
  CreditBaseReportSence(SensorType.CLICK, {
    element_id: "8420102",
    element_name: "use contacts",
    module_id: "01",
    position_id: "02",
    page_id: "842"
  });
  reportClick({
    cn: 50,
    sn: 300006,
    sp: { status: isFirst ? "first" : "not_first" }
  });
};

const SensorTypePopView = () => {
  CreditBaseReportSence(SensorType.POPVIEW, {
    page_name: "Face Verification page",
    page_id: "997",
    pop_id: "rn100002",
    pop_name: "Device list authorization"
  });
};

const SensorFundingPopView = () => {
  CreditBaseReportSence(SensorType.POPVIEW, {
    page_name: "Personal Information page",
    page_id: "841",
    pop_id: "pop30399",
    pop_name: "funding guide"
  });
};

const SensorFundingPopClick = () => {
  CreditBaseReportSence(SensorType.POPCLICK, {
    page_name: "Personal Information page",
    page_id: "841",
    pop_id: "pop30399",
    pop_name: "funding guide",
    element_id: "pop3039901",
    element_name: "i know"
  });
};

const SensorTypeClickAgreement = (pageType: PageType) => {
  if (pageType === PageType.emergency_contact) {
    CreditBaseReportSence(SensorType.CLICK, {
      element_id: "8420301",
      element_name: "statement",
      module_id: "03",
      module_name: "contact",
      position_id: "01",
      page_id: "842"
    });
  } else if (pageType === PageType.id_Photo) {
    CreditBaseReportSence(SensorType.CLICK, {
      element_id: "8450301",
      element_name: "statement",
      module_id: "03",
      module_name: "photo",
      position_id: "01",
      page_id: "845"
    });
  }
};

const SensorTypePopClick = (value: string, isFirst: boolean) => {
  switch (value) {
    case "agree":
      CreditBaseReportSence(SensorType.POPCLICK, {
        page_name: "Face Verification page",
        element_id: "rn10000201",
        element_name: "agree",
        position_id: "01",
        page_id: "997",
        pop_id: "rn100002",
        pop_name: "Device list authorization"
      });
      reportClick({
        cn: 3,
        sn: 300136,
        sp: { status: isFirst ? "first" : "not_first" }
      });
      break;
    case "disagree":
      CreditBaseReportSence(SensorType.POPCLICK, {
        page_name: "Face Verification page",
        element_id: "rn10000202",
        element_name: "disagree",
        position_id: "02",
        page_id: "997",
        pop_id: "rn100002",
        pop_name: "Device list authorization"
      });
      reportClick({
        cn: 4,
        sn: 300136,
        sp: { status: isFirst ? "first" : "not_first" }
      });
      break;
    default:
      break;
  }
};

const SensorFundingGuidePopView = () => {
  CreditBaseReportSence(SensorType.POPVIEW, {
    page_name: "Personal Information page",
    page_id: "841",
    pop_id: "pop30398",
    pop_name: "funding"
  });
};

const SensorFundingContinuePopClick = () => {
  CreditBaseReportSence(SensorType.POPCLICK, {
    page_name: "Personal Information page",
    page_id: "841",
    pop_id: "pop30398",
    pop_name: "funding",
    element_id: "pop3039801",
    element_name: "continue"
  });
};

const SensorFundingResetPopClick = () => {
  CreditBaseReportSence(SensorType.POPCLICK, {
    page_name: "Personal Information page",
    page_id: "841",
    pop_id: "pop30398",
    pop_name: "funding",
    element_id: "pop3039802",
    element_name: "change occupation"
  });
};

const SensorTypeIdSS = (type: string, extra?: any) => {
  switch (type) {
    case "idss-submit":
      // 印尼社保 提交
      CreditBaseReportSence(SensorType.CLICK, {
        page_name: "Authorized account login page",
        element_id: "8890101",
        element_name: "submit",
        module_id: "01",
        module_name: "authorize account",
        position_id: "01",
        page_id: "889",
        extra: {
          ...extra
        }
      });
      break;
    case "idss-account":
      // 印尼社保 输入账号
      CreditBaseReportSence(SensorType.INPUT, {
        page_name: "Authorized account login page",
        element_id: "8890102",
        element_name: "account",
        module_id: "01",
        module_name: "authorize account",
        position_id: "02",
        page_id: "889",
        extra: {
          ...extra
        },
        // @ts-ignore
        input: {}
      });
      break;
    case "idss-password":
      // 印尼社保 输入密码
      CreditBaseReportSence(SensorType.INPUT, {
        page_name: "Authorized account login page",
        element_id: "8890103",
        element_name: "password",
        module_id: "01",
        module_name: "authorize account",
        position_id: "03",
        page_id: "889",
        extra: {
          ...extra
        },
        // @ts-ignore
        input: {}
      });
      break;
    case "idss-help":
      // 印尼社保 帮助
      CreditBaseReportSence(SensorType.CLICK, {
        page_name: "Authorized account login page",
        element_id: "8890104",
        element_name: "help",
        module_id: "01",
        module_name: "authorize account",
        position_id: "04",
        page_id: "889",
        extra: {
          ...extra
        }
      });
      break;
    case "idss-result-return":
      // 印尼社保成功页 返回上一页
      CreditBaseReportSence(SensorType.CLICK, {
        page_name: "Authorized account result page",
        element_id: "8900101",
        element_name: "return",
        module_id: "01",
        module_name: "authorize account",
        position_id: "01",
        page_id: "890",
        extra: {
          ...extra
        }
      });
      break;
    case "idss-enter":
      // 印尼社保授权页
      CreditBaseReportSence(SensorType.VIEW_SCREEN_ENTER, {
        page_name: "Authorized account login page",
        page_id: "889",
        extra: {
          ...extra
        }
      });
      break;
    case "idss-leave":
      // 印尼授权页
      CreditBaseReportSence(SensorType.VIEW_SCREEN_LEAVE, {
        page_name: "Authorized account login page",
        page_id: "889",
        extra: {
          ...extra
        }
      });
      break;
    case "idss-result-enter":
      // 印尼社保授权结果页
      CreditBaseReportSence(SensorType.VIEW_SCREEN_ENTER, {
        page_name: "Authorized account result page",
        page_id: "890",
        extra: {
          ...extra
        }
      });
      break;
    case "idss-result-leave":
      // 印尼社保授权结果页
      CreditBaseReportSence(SensorType.VIEW_SCREEN_LEAVE, {
        page_name: "Authorized account result page",
        page_id: "890",
        extra: {
          ...extra
        }
      });
      break;
    case "idss-popview":
      // 印尼社保弹窗展示
      CreditBaseReportSence(SensorType.POPVIEW, {
        page_name: "Authorized account login page",
        element_id: "8890105",
        element_name: "error",
        module_id: "01",
        module_name: "authorize account",
        position_id: "05",
        page_id: "889",
        extra: {
          ...extra
        }
      });
      break;
    default:
      break;
  }
};

const SensorTypeVoice = (status: string, entryid: number) => {
  switch (status) {
    case "startVoice":
      CreditBaseReportSence(SensorType.CLICK, {
        page_name: "Application progress page",
        element_id: "8460101",
        element_name: "click data item",
        module_id: "01",
        module_name: "Data item",
        position_id: "01",
        page_id: "846",
        extra: {
          entryid: entryid,
          buttonName: "开始录制"
        }
      });
      break;
    case "resetVoice":
      CreditBaseReportSence(SensorType.CLICK, {
        page_name: "Application progress page",
        element_id: "8460101",
        element_name: "click data item",
        module_id: "01",
        module_name: "Data item",
        position_id: "01",
        page_id: "846",
        extra: {
          entryid: entryid,
          buttonName: "重新录制"
        }
      });
      break;
    default:
      break;
  }
};

const SensorTypeResultPage = (isFirst: boolean, subStatus: number | undefined) => {
  CreditBaseReportSence(SensorType.CLICK, {
    page_name: "Information submission page",
    element_id: "8480102",
    element_name: "done",
    module_id: "01",
    module_name: "submission",
    position_id: "02",
    page_id: "848"
  });
  reportClick({
    cn: 2,
    sn: 300131,
    sp: { status: isFirst ? "first" : "not_first", result: subStatus === 4 ? "failed" : "success" }
  });
};

const SensorCreditIntentionPopView = () => {
  CreditBaseReportSence(SensorType.POPVIEW, {
    page_name: "Information submission page",
    page_id: "848",
    pop_id: "pop30301",
    pop_name: "credit purpose"
  });
};

const SensorCreditIntentionClick = (intentionType: number) => {
  if (intentionType === 1) {
    CreditBaseReportSence(SensorType.POPCLICK, {
      page_name: "Information submission page",
      page_id: "848",
      pop_id: "pop30301",
      pop_name: "credit purpose",
      element_id: "pop3030101",
      element_name: "loan"
    });
  } else if (intentionType === 2) {
    CreditBaseReportSence(SensorType.POPCLICK, {
      page_name: "Information submission page",
      page_id: "848",
      pop_id: "pop30301",
      pop_name: "credit purpose",
      element_id: "pop3030102",
      element_name: "shopping"
    });
  } else if (intentionType === 3) {
    CreditBaseReportSence(SensorType.POPCLICK, {
      page_name: "Information submission page",
      page_id: "848",
      pop_id: "pop30301",
      pop_name: "credit purpose",
      element_id: "pop3030103",
      element_name: "skip"
    });
  }
};

const SensorCreditIntentionClose = () => {
  CreditBaseReportSence(SensorType.POPCLICK, {
    page_name: "Information submission page",
    page_id: "848",
    pop_id: "pop30301",
    pop_name: "credit purpose",
    element_id: "pop3030104",
    element_name: "close"
  });
};

const CreditApplyReport = (pageType: number | null, isFirst: boolean) => {
  let screenNum = "";
  switch (pageType) {
    case 1: //个人信息页
      screenNum = "300097";
      break;
    case 2: //紧急人联系页
      screenNum = "300006";
      break;
    case 3: //职业信息页
      screenNum = "300007";
      break;
    // case 4://职业信息页-照片
    //   screenNum = "300105";
    //   break;
    case 5: //证件照页面
      screenNum = "300005";
      break;
    // case 6://声纹页
    //   screenNum = "";
    //   break;
    case 7: //账号授权页
      screenNum = "300098";
      break;
    case 9: //人脸
      screenNum = "300136";
      break;
    default:
  }
  NativeActionLogModule.reportClick({
    screenNum,
    controlNum: 100,
    controlValue: "apply_active_credit_successfully",
    screenValue: isFirst ? "first" : "not_first"
  });
  reportClick({
    sn: !!screenNum ? Number(screenNum) : 0,
    cn: 100,
    bct: 31,
    sp: { status: isFirst ? "first" : "not_first" },
    bi: { result: "apply_active_credit_successfully" }
  });
};

const SensorPersonalBackClick = () => {
  CreditBaseReportSence(SensorType.CLICK, {
    page_name: "Personal Information page",
    element_id: "8410202",
    element_name: "Back",
    module_id: "02",
    module_name: "next",
    position_id: "02",
    page_id: "841"
  });
};

const SensorSocialProtocolClick = () => {
  CreditBaseReportSence(SensorType.CLICK, {
    page_name: "Personal Information page",
    element_id: "8410301",
    element_name: "bpjs",
    module_id: "03",
    module_name: "bpjs",
    position_id: "01",
    page_id: "841"
  });
};

const SensorSocialProtocolExpose = () => {
  CreditBaseReportSence(SensorType.EXPOSE, {
    page_name: "Personal Information page",
    element_id: "8410301",
    element_name: "bpjs",
    module_id: "03",
    module_name: "bpjs",
    position_id: "01",
    page_id: "841"
  });
};

const SensorClickUploadExample = () => {
  CreditBaseReportSence(SensorType.CLICK, {
    element_id: "8450403",
    element_name: "example",
    module_id: "04",
    module_name: "kyc",
    position_id: "03",
    page_name: "ID photo page",
    page_id: "845"
  });
};

const SensorCreditCompanyPopView = () => {
  CreditBaseReportSence(SensorType.POPVIEW, {
    page_name: "Personal Information page",
    page_id: "841",
    pop_id: "pop30396",
    pop_name: "company"
  });
};

const SensorCompanyInputClick = () => {
  CreditBaseReportSence(SensorType.POPCLICK, {
    page_name: "Personal Information page",
    page_id: "841",
    pop_id: "pop30396",
    pop_name: "company",
    element_id: "pop3039601",
    element_name: "input boz"
  });
};

const SensorCompanyInputClose = () => {
  CreditBaseReportSence(SensorType.POPCLICK, {
    page_name: "Personal Information page",
    page_id: "841",
    pop_id: "pop30396",
    pop_name: "company",
    element_id: "pop3039602",
    element_name: "close"
  });
};

const SensorClickKTPAddress = (yes: boolean) => {
  CreditBaseReportSence(SensorType.CLICK, {
    element_id: yes ? "8450401" : "8450402",
    element_name: yes ? "yes" : "no",
    module_id: "04",
    module_name: "kyc",
    position_id: yes ? "01" : "02",
    page_name: "ID photo page",
    page_id: "845"
  });
};

export {
  SensorTypeClickItem,
  ClickNext,
  EnterLeavePage,
  V3ClickItem,
  V4ClickItem,
  SensorTypeClickContacts,
  SensorTypePopView,
  SensorTypePopClick,
  SensorTypeIdSS,
  SensorTypeVoice,
  SensorTypeResultPage,
  CreditApplyReport,
  SensorCreditIntentionPopView,
  SensorCreditIntentionClick,
  SensorCreditIntentionClose,
  SensorFundingPopView,
  SensorFundingPopClick,
  SensorTypeClickAgreement,
  SensorFundingGuidePopView,
  SensorFundingContinuePopClick,
  SensorFundingResetPopClick,
  SensorClickOccupationItem,
  SensorSocialProtocolClick,
  SensorSocialProtocolExpose,
  SensorPersonalBackClick,
  SensorClickUploadExample,
  SensorCreditCompanyPopView,
  SensorCompanyInputClick,
  SensorCompanyInputClose,
  SensorClickKTPAddress
};

export class NoviceStaySensor {
  static noviceStayPopView = (extra: Record<string, string | number>) => {
    CreditBaseReportSence(SensorType.POPVIEW, {
      page_name: "Personal Information page",
      page_id: "841",
      pop_id: "rn100004",
      pop_name: "Recall reminder",
      extra
    });
  };

  static noviceStayPopClickGiveUp = (extra: Record<string, string | number>) => {
    CreditBaseReportSence(SensorType.POPCLICK, {
      page_name: "Personal Information page",
      element_id: "rn10000402",
      element_name: "give up",
      position_id: "02",
      page_id: "841",
      extra
    });
  };

  static noviceStayPopClickImprove = (extra: Record<string, string | number>) => {
    CreditBaseReportSence(SensorType.POPCLICK, {
      page_name: "Personal Information page",
      element_id: "rn10000401",
      element_name: "Continue to improve",
      position_id: "01",
      page_id: "841",
      extra: extra
    });
  };
}

export class NoviceGiftSensor {
  static noviceGiftPopView = (extra: Record<string, string | number>) => {
    CreditBaseReportSence(SensorType.POPVIEW, {
      page_name: "Personal Information page",
      page_id: "841",
      pop_id: "rn100003",
      pop_name: "Reminder of gift package arrival",
      extra
    });
  };

  static noviceGiftPopClickImprove = (extra: Record<string, string | number>) => {
    CreditBaseReportSence(SensorType.POPCLICK, {
      page_name: "Personal Information page",
      element_id: "rn10000301",
      element_name: "Continue to improve",
      position_id: "01",
      page_id: "841",
      pop_id: "rn100003",
      pop_name: "Reminder of gift package arrival",
      extra: extra
    });
  };
}

export class ResultPageSensor {
  static adBannerClick = (extra: Record<string, string | number>) => {
    CreditBaseReportSence(SensorType.CLICK, {
      element_id: "8480202",
      element_name: "banner",
      page_id: "848",
      page_name: "Information submission page",
      module_id: "02",
      module_name: "ad",
      position_id: "02",
      extra
    });
  };

  static adBannerExpose = (extra: Record<string, string | number>) => {
    CreditBaseReportSence(SensorType.EXPOSE, {
      element_id: "8480202",
      element_name: "banner",
      page_id: "848",
      page_name: "Information submission page",
      module_id: "02",
      module_name: "ad",
      position_id: "02",
      extra
    });
  };
}
