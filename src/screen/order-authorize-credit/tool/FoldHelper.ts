/**
 * Create by z<PERSON><PERSON><PERSON> on 2024-03 16:49
 * @description
 */

export default class FoldHelper {
  static largeLoadUnEditableItemEntryId: number[] = [
    // 1101000,
    // 1103401,
    1101020,
    1101021,
    // 1101008,
    // 1101004,
    // 1101009,
    // 1101010,
    // 1101075,
    // 1104000, // 身份证直接展开
    1104001,
    1104050,
    1104051,
    // 1101001,
    // 1106060,
    1104004,
    1101014,
    1101015,
    1101018,
    1105000
  ];

  static isFoldId = (id: number) => {
    return FoldHelper.largeLoadUnEditableItemEntryId.includes(id);
  };
}
