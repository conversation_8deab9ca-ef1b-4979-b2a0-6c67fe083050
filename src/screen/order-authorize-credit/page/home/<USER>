import React, { Component } from "react";
import { inject, observer } from "mobx-react";
import { <PERSON><PERSON><PERSON><PERSON>, FlatList, Image, StatusBar, View, ViewabilityConfigCallbackPairs } from "react-native";
import _, { isNil } from "lodash";
import store from "../../store";
import styles from "./styles";
import {
  AkuTextComponent,
  AkuTextComponentType,
  Android,
  FigmaStyle,
  iOS,
  Loading,
  NavigationBar,
  NetworkErrorComponent,
  WINDOW_WIDTH
} from "@akulaku-rn/akui-rn";
import api from "../../store/api";
import CreditSuccess from "../../components/CreditSuccess";
import ResultPage from "../../components/ResultPage";
import { CreditApplyReport, EnterLeavePage } from "../../tool/EventTracking";
import DataPage from "../../components/DataPage";
import RootView from "common/components/Layout/RootView";
import { NativeNavigationModule, NativeConfigModule, NativeActionLogModule } from "common/nativeModules";
import NativeEventReportModule, { ReportScene } from "common/nativeModules/basics/nativeEventReportModule";
import NativeAdjustModule, { getEventName, AdjustEventType } from "common/nativeModules/sdk/nativeAdjustModule";
import NativeFacebookModule from "common/nativeModules/sdk/nativeFacebookModule";
import NativeFirebaseModule from "common/nativeModules/sdk/nativeFirebaseModule";
import { DeleteData, RetrieveData } from "common/util/cache";
import ConfigHelper from "common/util/ConfigHelper";
import { withTranslation } from "common/services/i18n";
import NativeToast from "common/nativeModules/basics/nativeUIModule/Toast";
import StayDialog from "../../components/StayDialog";
import NoviceGift from "../../components/StayDialog/novice_gift";
import NoviceStay from "../../components/StayDialog/novice_stay";
import { getAdList } from "common/components/ADGroup/helpers";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import { TFunction } from "i18next";
import { configSensorEvent } from "common/components/BaseContainer/Type";
import NativeEventModule from "common/nativeModules/bussinse/nativeEventModule";
import { PopupExposerHelper } from "@akulaku-rn/rn-v4-sdk";
import PayPasswordModule, { PinSceneEnum } from "common/nativeModules/bussinse/PayPasswordModule";
import { isLaunchSm } from "../../tool/SmUtils";
import { CreditChanelType } from "../../dict/PageType";
import { AccountApplyError, ApiProcessPageData, PageItemI } from "../../dict/type";
import { NetworkResponse } from "common/nativeModules/network/nativeNetworkModule";
import { StoreBusinessKeyData, StoreBusinessKeyDatas } from "../../dict/constants";
import ProgressInfosNodeItem from "../../dict/ProgressInfosNodeItem";
import DataProcess from "../../tool/DataProcess";

export type ApplyInfoNavParams = {
  fromOpenPayToUseAkuPay: CreditChanelType; //op站内标识
  callBackUrl: string; //回调地址
  redirectUrl?: string;
  qrCode?: string;
};

type Props = {
  navigation: navigationModel;
  store: pageStoreModel<store, ApplyInfoNavParams>;
  t: TFunction;
  configSensorEvent: configSensorEvent;
};

type States = {
  loadFailed: boolean;
  showUpgrading: boolean;
  review: boolean;
  creditSuccess: boolean;
  data: PageItemI[];
  indexNum: number;
  tips: any;
  // 1：正常流程 2：合规流程 3: 合规2.0 4：授信BO流程打磨版本
  version: 1 | 2 | 3 | 4;
};
@RootView({
  withI18n: [
    "OrderIDApplyInfo",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store,
  keyApi: ["/capi/credit/id/credit/progress"]
})
@withTranslation("OrderIDApplyInfo")
@inject("store")
@observer
export default class ApplyInfo extends Component<Props, States, ApplyInfoNavParams> {
  flatListRef: any;

  indexNum: number;

  partRejected: boolean;

  viewabilityConfigCallbackPairs: ViewabilityConfigCallbackPairs;

  pageType: any;

  isFirstApply: boolean;

  payLater = false;

  //是否启用 测试社保页
  socialSecurityPageTested?: boolean;

  //是否可以返回
  showBack = true;

  backHandlerListener: any;

  constructor(props: Props) {
    super(props);
    this.state = {
      loadFailed: false,
      showUpgrading: false,
      review: false,
      creditSuccess: false,
      data: [],
      indexNum: 0,
      tips: null,
      version: 1
    };
    this.showBack = parseInt(props.store.navParams.fromRegister) !== 1;
    this.indexNum = 0;
    this.partRejected = false;
    this.pageType = null;
    this.isFirstApply = false;
    this.viewabilityConfigCallbackPairs = [
      {
        viewabilityConfig: {
          minimumViewTime: 100,
          viewAreaCoveragePercentThreshold: 80,
          waitForInteraction: false
        },
        onViewableItemsChanged: this._onViewableItemsChanged
      }
    ];
    this.checkNavParams();
  }

  componentDidMount() {
    // 如果来源是注册，则走不显示返回按钮逻辑，请求配置中心，看是否被灰度到
    if (!this.showBack) {
      if (Android) {
        this.backHandlerListener = BackHandler.addEventListener("hardwareBackPress", this.backHandler);
      }
      // this.props.store.pageStore.getConfig1025();
    }
    this.props.store.pageStore.getPhoneNumber();
    this.props.store.pageStore.getPinCodeStatus();
    this.getVersion();
  }

  componentWillUnmount() {
    if (!this.showBack) {
      if (Android) {
        this.backHandlerListener && BackHandler.removeEventListener("hardwareBackPress", this.backHandler);
      }
    }
    const enterLeavepageData = { pageType: this.pageType, isFirst: this.isFirstApply, testType: this.testType };
    EnterLeavePage(enterLeavepageData);
  }

  // 检查qrCode & redirectUrl
  checkNavParams = () => {
    const { qrCode, redirectUrl } = this.props.store.navParams;
    if (!qrCode || !redirectUrl) {
      console.log("链接错误", qrCode, redirectUrl);
    }
  };

  backHandler = () => {
    // 第一页并且 不显示返回按钮时，禁用屋里返回
    return this.indexNum === 0 && !this.props.store.pageStore.showBackBtn;
  };

  //ABTest字段
  testType = -1;

  getVersion = async () => {
    Loading.show();
    // await this.props.store.pageStore.getConfigCompliance();
    // 去掉/capi/credit/common/get/version接口，现在服务端已经都用默认值
    this.payLater = true;
    this.testType = 1;
    this.socialSecurityPageTested = false;
    // this.getCreditStatus();
    await this.getProcessPage();
    Loading.dismiss();
    NativeActionLogModule.reportDeviceFingerPrint(ReportScene.ENTER_CREDIT_AUTH);
  };

  backAction = () => {
    const {
      store: { navParams }
    } = this.props;
    if (!!this.state.data.length) {
      this.toStayPop();
    } else {
      if (navParams.goHome) {
        NativeNavigationModule.popToHome(0);
      } else {
        this.props.navigation.goBack();
      }
    }
    const enterLeavepageData = { pageType: this.pageType, isFirst: this.isFirstApply, testType: this.testType };
    EnterLeavePage(enterLeavepageData); //上报离开事件，安卓物理返回键
    return true;
  };

  setpageType = (pageType: number) => {
    this.pageType = pageType;
  };

  getProcessPage = async () => {
    const {
      store: { pageStore, runtime, navParams }
    } = this.props;
    const { uid, countryId } = runtime;
    pageStore.useCreditData = await RetrieveData(`${uid}${StoreBusinessKeyDatas}`);
    pageStore.providentTipShow = await RetrieveData(`providentTipShow`);

    pageStore.post(
      api.PROCESS_PAGE,
      {
        qrCode: navParams.qrCode
      },
      (response: NetworkResponse<ApiProcessPageData & { ocrTextImageUi: boolean }>) => {
        if (!!response.errCode && `${response.errCode}` === "10811801105000") {
          this.getProcessPageError10811801105000();
        } else {
          if (!response.data.progressInfos.length) {
            this.getProcessPageError();
          } else {
            this.showNoviceGift();
            const data = response.data.progressInfos;
            const ProgressNode = new ProgressInfosNodeItem();
            let curObj = ProgressNode;
            for (let i = 0; i < data.length; i++) {
              const item: any = data[i];
              item.testType = this.testType;
              curObj.pageNo = item.pageNo;
              curObj.pageType = item.pageType;
              if (i !== data.length - 1) {
                curObj.next = new ProgressInfosNodeItem();
                curObj = curObj.next;
              } else {
                curObj.isLast = true;
              }
            }
            pageStore.progressInfosNodeObj = ProgressNode;
            this.indexNum = data.findIndex(i => {
              // @ts-ignore
              return +i.pageStep === +navParams.pageStep;
            });
            if (this.indexNum < 0) this.indexNum = 0;
            pageStore.version = 1;
            pageStore.newUi = true;
            pageStore.ocrTextImageUi = response.data.ocrTextImageUi ?? false;
            pageStore.newAddressSubassembly = false;
            pageStore.asiApplicationId = response.data.asiApplicationId;
            DataProcess.initCacheData(pageStore.useCreditData, { ocrTextImageUi: pageStore.ocrTextImageUi });
            this.setState({ indexNum: this.indexNum, data, version: 1, loadFailed: false });
          }
        }
      },
      (error: any) => {
        if (`${error.errorCode}` === "10811801105000") {
          this.getProcessPageError10811801105000();
        } else {
          this.getProcessPageError();
        }
      }
    );
  };

  getProcessPageError10811801105000 = () => {
    this.setState({ showUpgrading: true }, () => {
      Loading.dismiss();
    });
  };

  getProcessPageError = () => {
    this.setState({ loadFailed: true }, () => {
      Loading.dismiss();
    });
  };

  renderItem = ({ item, index }: { item: PageItemI; index: number }) => {
    const { store, t } = this.props;
    const { version } = this.state;
    return (
      <DataPage
        nowStep={index}
        pageLength={this.state.data.length}
        store={store}
        t={t}
        data={item}
        version={version}
        layerNext={this.layerNext}
        layerGoback={this.layerGoback}
        navigation={this.props.navigation}
        setpageType={this.setpageType}
        isFirstApply={this.isFirstApply}
        partRejected={this.partRejected}
      />
    );
  };

  layerNext = _.debounce(
    () => {
      const { data } = this.state;
      if (this.indexNum + 1 === data.length) {
        this.creditApply();
      } else {
        this.indexNum++;
        if (this.indexNum) {
          // sentry bug: http://sentry.al.com/organizations/al-fe/discover/results/?cursor=0%3A50%3A0&field=title&field=release&field=environment&field=user.display&field=timestamp&name=Invariant+Violation%3A+scrollToIndex+out+of+range%3A+requested+index+-1+but+maximum+is+3&project=69&query=issue.id%3A21735109&sort=-timestamp&statsPeriod=90d&widths=-1&widths=-1&widths=-1&widths=-1&widths=-1
          //layerGoback调用多次走到this.toStayPop();导致 this.indexNum这里可能为负数，不改动整体流程最保险改动，小于0取0
          this.flatListRef.scrollToIndex({ viewPosition: 0, index: Math.max(this.indexNum, 0), animated: false });
        } else {
          // sentry升级后再加这代码
          // Sentry.customReport(new Error("授信流程页滑动异常"), "授信");
        }
      }
    },
    300,
    { leading: true, trailing: false }
  );

  layerGoback = _.debounce(
    () => {
      const { gobackNum } = this.props.store.navParams;
      this.indexNum--;
      if (this.indexNum >= 0) {
        this.flatListRef.scrollToIndex({ viewPosition: 0, index: this.indexNum, animated: false });
      } else {
        // sentry bug: http://sentry.al.com/organizations/al-fe/discover/results/?cursor=0%3A50%3A0&field=title&field=release&field=environment&field=user.display&field=timestamp&name=Invariant+Violation%3A+scrollToIndex+out+of+range%3A+requested+index+-1+but+maximum+is+3&project=69&query=issue.id%3A21735109&sort=-timestamp&statsPeriod=90d&widths=-1&widths=-1&widths=-1&widths=-1&widths=-1
        // 小于0 取0
        this.indexNum = 0;
        NativeEventModule.postWeb({
          eventName: "applyCreditEnd",
          extraData: { applySuccess: false }
        });
        if (!!this.state.data.length) {
          this.toStayPop();
        } else {
          if (!isNil(gobackNum)) {
            NativeNavigationModule.popTo(parseInt(gobackNum + "") + 1);
          } else {
            NativeNavigationModule.goBack();
          }
        }
      }
    },
    300,
    { leading: true, trailing: false }
  );

  toStayPop = async () => {
    const { store, t } = this.props;
    const data = await getAdList(188);
    if (!!data && !!data.length) {
      const dialog = new PopupExposerHelper(
        NoviceStay,
        {
          eit: 2,
          cn: 1000,
          ei: {
            spot_id: data[0].spotId,
            ad_id: data[0].id,
            image_id: data[0].creatives[0].id
          }
        },
        {
          screenNumber: "300097",
          controlValue: "1000"
        }
      );
      dialog.show({
        store,
        data: data[0]
      });
    } else {
      StayDialog.show({ t, store });
    }
  };

  showNoviceGift = async () => {
    const {
      store,
      store: { navParams }
    } = this.props;
    if (navParams.isNovice) {
      const data = await getAdList(187);
      if (!!data && !!data.length) {
        const dialog = new PopupExposerHelper(
          NoviceGift,
          {
            eit: 2,
            cn: 1003,
            ei: {
              spot_id: data[0].spotId,
              ad_id: data[0].id,
              image_id: data[0].creatives[0].id
            }
          },
          {
            screenNumber: "300097",
            controlValue: "1003"
          }
        );
        dialog.show({
          store,
          data: data[0]
        });
      }
    }
  };

  creditApply = async () => {
    const {
      t,
      store: { pageStore, runtime, navParams }
    } = this.props;
    const checkRes = pageStore.progressInfosNodeObj?.checkAllSubmitted();
    if (!checkRes) {
      NativeToast.showMessage(this.props.t("抱歉！网络异常，请退出重新进入"));
      return;
    }
    Loading.show({ hasBackcolor: true });
    const { uid, countryId } = runtime;
    const isLaunch = await isLaunchSm();
    // 配置中心是否启用数美
    const { data } = await NativeEventReportModule.getAllDeviceInfoNew(ReportScene.SUBMIT_CREDIT_AUTH);

    pageStore.post(
      api.ACCOUNT_APPLY,
      {
        asiApplicationId: pageStore.asiApplicationId,
        deviceInfo: JSON.stringify((data.data as any)?.eventReportDataInEuFormat),
        qrCode: navParams.qrCode
      },
      (response: NetworkResponse) => {
        const successCallback = () => {
          if (!!navParams.redirectUrl) {
            NativeNavigationModule.goBack();
            NativeNavigationModule.navigate({ url: navParams.redirectUrl });
          } else {
            NativeToast.showMessage("redirectUrl error");
          }
        };
        if (response && response.success) {
          CreditApplyReport(this.pageType, this.isFirstApply);
          Loading.dismiss();
          DeleteData(`${uid}${StoreBusinessKeyDatas}`);
          successCallback();
        } else {
          Loading.dismiss();
          if (response.errCode === AccountApplyError.DuplicateApplication) {
            successCallback();
          } else if (response.errCode === AccountApplyError.LackOfInformation) {
            NativeToast.showMessage(this.props.t("抱歉！网络异常，请退出重新进入"));
          } else {
            NativeToast.showMessage(response.errMsg);
          }
        }
      },
      (error: any) => {
        Loading.dismiss();
        const { errMsg } = error;
        NativeToast.showMessage(errMsg);
      }
    );
  };

  goResultPage = async (data: any) => {
    const {
      navigation,
      store: {
        runtime,
        navParams,
        navParams: { fromOpenPayToUseAkuPay, callBackUrl }
      }
    } = this.props;
    const {
      data: { adjustId, deviceId }
    } = await NativeConfigModule.getEnvInfoSilent();
    //调用adjust device/ad_ids_report接口上报
    NativeAdjustModule.reportAdjustConnect();
    // adjustId
    NativeAdjustModule.logger(
      getEventName(
        ConfigHelper.isSilvrrPlus()
          ? AdjustEventType.aku_credit_succ_apply_202011
          : AdjustEventType.aku_credit_succ_apply_202002
      ),
      {
        adjust_id: adjustId || null,
        uid: runtime.uid || null,
        deviceId: deviceId || null
      }
    );

    // firebase
    NativeFirebaseModule.logger("mobile_level_achieved", {
      adjust_id: adjustId || null,
      uid: runtime.uid || null,
      deviceId: deviceId || null,
      application_credit_id: null
    });
    const resultParams = {
      gestureEnabled: false,
      isFirstApply: this.isFirstApply,
      gobackNum: navParams.gobackNum,
      fromOpenPayToUseAkuPay,
      callBackUrl
    };
    //走PIN码设置页面
    const isGoSetPin = await this.handlePinAction(resultParams);
    if (isGoSetPin) return;
    //需要进入 测试社保页
    if (this.socialSecurityPageTested) {
      // 增加社保测试页
      navigation.navigate({
        screen: "SocialTestScreen",
        params: {
          gestureEnabled: false,
          resultParams
        }
      });
    } else {
      //TODO:未来香蕉会让删掉isFirstApply

      this.goToResultPage(resultParams);
    }
  };

  goToResultPage(params: any) {
    const { navigation } = this.props;
    navigation.navigate({
      screen: "CreditResultPage",
      params: { ...params }
    });
  }

  /**
   * 检测是否走设置PIN码流程
   */
  async handlePinAction(resultParams: any) {
    const {
      store: { pageStore }
    } = this.props;
    if (!pageStore.paymentPwdSwitch) return false;
    if (!PayPasswordModule.canInvokeSetPayPwd()) return false;
    if (!pageStore.havePaymentPsw) {
      //未设置过PIN
      await PayPasswordModule.setPayPwd({ sourceType: 1, scene: PinSceneEnum.SCENE_5, liteMode: true });
      this.goToResultPage(resultParams);
      return true;
    }
    if (!pageStore.kycPassed) {
      //KYC未通过
      await PayPasswordModule.setPayPwd({ sourceType: 2, scene: PinSceneEnum.SCENE_5, liteMode: true });
      this.goToResultPage(resultParams);
      return true;
    }
    return false;
  }

  keyExtractor = (item: any, index: number) => index.toString();

  _onViewableItemsChanged = ({ viewableItems }: any) => {
    const { pageStore } = this.props.store;
    const item: any = _.maxBy(viewableItems, item => {
      return item.index;
    });
    pageStore.nowStep = item && item.index;
  };

  getItemLayout = (data: any, index: number) => {
    return {
      length: WINDOW_WIDTH,
      offset: WINDOW_WIDTH * index,
      index
    };
  };

  render() {
    const {
      t,
      store: { navParams }
    } = this.props;
    const { loadFailed, showUpgrading, review, creditSuccess, data, indexNum } = this.state;
    if (loadFailed) {
      return (
        <View style={{ flex: 1 }}>
          <NavigationBar title={t("加载中")} />
          <NetworkErrorComponent
            message={t("global:啊，网络故障导致我们的距离好远啊!")}
            onRetryPress={this.getVersion}
            buttonText={t("global:刷新")}
          />
        </View>
      );
    }
    if (showUpgrading) {
      return (
        <View style={{ flex: 1 }}>
          <NavigationBar title={t("加载中")} />
          <View style={{ flex: 1, justifyContent: "center", alignItems: "center", marginHorizontal: 24 }}>
            <Image source={require("../../img/upgrading_bg.webp")} style={{ height: 150, width: 266 }} />
            <AkuTextComponent
              style={{ color: FigmaStyle.Color.Grey_Text2, marginTop: 12, textAlign: "center" }}
              type={AkuTextComponentType.Aku_font_14_regular}
              text={t(
                "为了提供更好的服务，我们正在对此功能进行升级改造，在此期间您将无法使用此功能，感谢您的理解和耐心"
              )}
            />
          </View>
        </View>
      );
    }
    if (review) {
      return (
        <ResultPage
          t={t}
          store={this.props.store}
          isFirstApply={this.isFirstApply}
          gobackNum={navParams.gobackNum}
          callBackUrl={navParams.callBackUrl}
          fromOpenPayToUseAkuPay={navParams.fromOpenPayToUseAkuPay}
        />
      );
    }
    if (creditSuccess) {
      return <CreditSuccess t={t} layerGoback={this.layerGoback} />;
    }
    if (!data.length) return null;
    console.log("indexNum", indexNum);
    return (
      <View style={styles.container}>
        {iOS && <StatusBar barStyle="dark-content" />}
        <FlatList
          ref={flatList => {
            this.flatListRef = flatList;
          }}
          keyExtractor={this.keyExtractor}
          initialScrollIndex={indexNum}
          overScrollMode={"never"}
          data={data}
          horizontal={true}
          pagingEnabled={true}
          keyboardShouldPersistTaps={"handled"}
          getItemLayout={this.getItemLayout}
          scrollEnabled={false}
          renderItem={this.renderItem}
          showsHorizontalScrollIndicator={false}
          viewabilityConfigCallbackPairs={this.viewabilityConfigCallbackPairs}
        />
      </View>
    );
  }
}
