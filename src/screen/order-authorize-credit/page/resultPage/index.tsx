import React, { PureComponent } from "react";
import { View, Image, Text, BackHandler, TouchableOpacity } from "react-native";
import { Android, WINDOW_HEIGHT, WINDOW_WIDTH } from "@akulaku-rn/akui-rn";
import PopUpPanel from "@akulaku-rn/akui-rn/src/components/PopUpPanel";
import RootView from "common/components/Layout/RootView";
import { NativeNavigationModule, AkuNativeEventEmitter } from "common/nativeModules";
import { withTranslation } from "common/services/i18n";
import { inject } from "mobx-react";
import styles from "./styles";
import ADBanner from "common/components/ADGroup/ADBanner";
import { Creatives, ResAD } from "common/components/ADGroup/helpers/types";
import ActivityAdPopup from "common/components/ADGroup/ActivityAdPopup";
import {
  SensorTypeResultPage,
  SensorCreditIntentionPopView,
  SensorCreditIntentionClick,
  SensorCreditIntentionClose,
  ResultPageSensor
} from "../../tool/EventTracking";
import { TFunction } from "i18next";
import { navigationModel, pageStoreModel } from "common/components/BaseContainer/Type";
import store from "../../store";
import { isNil } from "lodash";
import NativeEventModule from "common/nativeModules/bussinse/nativeEventModule";
import RecommendList, { RecommendScene } from "common/components/RecommendList";
import { GlobalRuntime } from "common/constant";
import { RecommendItemModel } from "common/components/RecommendListItem/model";
import { reportClick } from "@akulaku-rn/rn-v4-sdk";
import { CreditChanelType } from "../../dict/PageType";
import ResultBottomSheet from "../../components/ResultBottomSheet";
import { ResultState } from "../../dict/type";

type Props = {
  t: TFunction;
  store: pageStoreModel<store>;
  navigation: navigationModel;
};

@RootView({
  withI18n: [
    "IDResultPage",
    {
      en: require("../../i18n/en.json"),
      in: require("../../i18n/in.json"),
      vi: require("../../i18n/vi.json")
    }
  ],
  store
})
@inject("store")
@withTranslation("IDResultPage")
export default class ResultPage extends PureComponent<Props, ResultState> {
  AdPopShow: any; //定时器

  adBanner1Ref!: ADBanner;

  adBanner2Ref!: ADBanner;

  adBanner3Ref!: ADBanner;

  listenerArr: Array<any> = [];

  constructor(props: any) {
    super(props);
    const {
      store: { navParams }
    } = this.props;

    if (navParams.dataString && navParams.dataString.length > 0) {
      try {
        const dataJson = JSON.parse(decodeURIComponent(navParams.dataString));
        for (const key in dataJson) {
          if (Object.prototype.hasOwnProperty.call(dataJson, key)) {
            const element = dataJson[key];
            navParams[key] = element;
          }
        }
      } catch (error) {}
    }

    this.state = {
      dataServer: {},
      loading: true
    };
    props.configSensorEvent({
      page_id: "848",
      page_name: "Information submission page",
      extra: {
        Aku_scene: "2"
      }
    });
    props.configPageInfo({
      sn: 300131,
      sp: { status: !!navParams.isFirstApply ? "first" : "not_first" }
    });
  }

  // 授信意图收集
  onPressIntention = (intentionType: number) => {
    this.props.store.pageStore.postCreditIntention(intentionType);
    SensorCreditIntentionClick(intentionType);
  };

  getResultFromServer = async () => {
    const {
      t,
      store: { pageStore }
    } = this.props;
    const data = await pageStore.getCreditSubmitStatus();
    if (!data) return;
    this.setState({ dataServer: data, loading: false });
    // 授信目的弹窗
    if (data.intentionCollect) {
      SensorCreditIntentionPopView();
      PopUpPanel.show({
        title: " ",
        customStyle: { minHeight: WINDOW_HEIGHT * 0.8 },
        onClose: () => {
          SensorCreditIntentionClose();
        },
        childrenComponent: <ResultBottomSheet t={t} onPressIntention={this.onPressIntention} />
      });
    }
  };

  async componentDidMount() {
    const {
      t,
      store: { navParams }
    } = this.props;
    await this.getResultFromServer();

    if (Android) {
      this.listenerArr.push(
        BackHandler.addEventListener("hardwareBackPress", () => {
          NativeNavigationModule.popToHome(1);
          return true;
        })
      );
    }
    const { screen } = this.props.store.navParams;
    this.listenerArr.push(
      AkuNativeEventEmitter.addListener(screen, (event: any) => {
        if (event.eventName === "onLeave") {
          clearTimeout(this.AdPopShow);
        }
      })
    );
    //TODO:未来香蕉会让删除
    if (navParams.fromOpenPayToUseAkuPay === CreditChanelType.OP) return;
    this.judgewindowsAdShow();
  }

  componentWillUnmount() {
    if (Android) {
      this.listenerArr.pop().remove();
    }
    this.listenerArr && this.listenerArr.pop().remove();
    clearTimeout(this.AdPopShow);
  }

  onBackPress = () => {
    const {
      store: {
        navParams: { isFirstApply, gobackNum, fromOpenPayToUseAkuPay, callBackUrl }
      }
    } = this.props;
    NativeEventModule.postWeb({
      eventName: "applyCreditEnd",
      extraData: { applySuccess: true }
    });
    SensorTypeResultPage(isFirstApply, this.state.dataServer.subStatus);
    if (fromOpenPayToUseAkuPay === CreditChanelType.OP) {
      //OP渠道过来的返回回调地址进行解码
      NativeNavigationModule.navigate({ url: decodeURIComponent(callBackUrl) });
    } else if (!isNil(gobackNum)) {
      NativeNavigationModule.popTo(parseInt(gobackNum + "") + 2);
    } else {
      NativeNavigationModule.popToHome(1);
    }
  };

  adBannerClick = (adData: ResAD, creatives: Creatives) => {
    const {
      store: { navParams }
    } = this.props;
    ResultPageSensor.adBannerClick({
      advertisingID: adData.id,
      image_id: creatives.id,
      adPositionid: adData.spotId
    });
    reportClick({
      sn: 300131,
      cn: 3,
      bct: 8,
      sp: {
        status: !!navParams.isFirstApply ? "first" : "not_first",
        result: this.state.dataServer.subStatus === 4 ? "failed" : "success"
      },
      bi: { spot_id: adData.spotId, ad_id: adData.id, image_id: creatives.id } as any
    });
  };

  adBannerExpose = (adData: ResAD, creatives: Creatives) => {
    ResultPageSensor.adBannerExpose({
      advertisingID: adData.id,
      image_id: creatives.id,
      adPositionid: adData.spotId
    });
  };

  judgewindowsAdShow = () => {
    const { windowsAdInfo } = this.state.dataServer;
    const adexpose = {
      element_id: "8480201",
      element_name: "pop-ad",
      page_id: "848",
      page_name: "Information submission page",
      module_id: "02",
      module_name: "ad",
      position_id: "01"
    };
    const adclick = {
      element_id: "8480201",
      element_name: "pop-ad",
      page_id: "848",
      page_name: "Information submission page",
      module_id: "02",
      module_name: "ad",
      position_id: "01"
    };
    //本次仅配置首次申请
    if (!!windowsAdInfo && windowsAdInfo.applyTimes === 1) {
      this.AdPopShow = setTimeout(() => {
        ActivityAdPopup.show({
          adData: { adGroupId: windowsAdInfo.adId },
          sensorLoggerExposeData: adexpose,
          sensorLoggerClickData: adclick
        });
      }, windowsAdInfo.waitingTime * 1000);
    }
  };

  adBanner1OnLoad = (isLoad: boolean) => {
    !!isLoad && this.adBanner1Ref && this.adBanner1Ref.onExposeHandler();
  };

  adBanner2OnLoad = (isLoad: boolean) => {
    !!isLoad && this.adBanner2Ref && this.adBanner2Ref.onExposeHandler();
  };

  adBanner3OnLoad = (isLoad: boolean) => {
    !!isLoad && this.adBanner3Ref && this.adBanner3Ref.onExposeHandler();
  };

  returnImgAndtitle = () => {
    const { subStatus, tipTitle, tipMsg } = this.state.dataServer;
    let statusImg = null;
    const title = tipTitle;
    const msg = tipMsg;

    switch (subStatus) {
      case 1:
        statusImg = require("../../img/result1.webp");

        break;
      case 2:
        statusImg = require("../../img/result2.webp");

        break;
      case 3:
        statusImg = require("../../img/result3.webp");

        break;
      case 4:
        statusImg = require("../../img/result4.webp");

        break;
    }
    return { statusImg, title, msg };
  };

  onItemClick = (item: RecommendItemModel, index: number) => {
    if (item.spuId) {
      NativeNavigationModule.navigate({
        url: `ak://m.akulaku.com/1301?id=${item.spuId}&countryCode=${GlobalRuntime.countryCode}&skuId=${item.skuId}`
      });
    }
  };

  _getExtPitInfo = () => {
    return {
      cn: 4,
      sn: 300131
    };
  };

  renderADView = () => {
    const { fromOpenPayToUseAkuPay } = this.props.store.navParams;
    const { adStatus } = this.state.dataServer;
    if (!(fromOpenPayToUseAkuPay === CreditChanelType.OP)) {
      return (
        <>
          <ADBanner
            wrapperStyle={styles.wrapperStyle}
            adGroup={138}
            relatedBizCodes={[{ pageStatus: adStatus || "" }]}
            imageWidth={WINDOW_WIDTH - 32}
            imageHeight={(WINDOW_WIDTH - 32) * (143 / 328)}
            creativesExposeHandler={this.adBannerExpose}
            creativesClickHandler={this.adBannerClick}
            onLoad={this.adBanner1OnLoad}
            ref={(ref: ADBanner) => {
              this.adBanner1Ref = ref;
            }}
          />
          <ADBanner
            wrapperStyle={styles.wrapperStyle}
            adGroup={169}
            relatedBizCodes={[{ pageStatus: adStatus || "" }]}
            imageWidth={WINDOW_WIDTH - 32}
            imageHeight={(WINDOW_WIDTH - 32) * (80 / 328)}
            creativesExposeHandler={this.adBannerExpose}
            creativesClickHandler={this.adBannerClick}
            onLoad={this.adBanner2OnLoad}
            ref={(ref: ADBanner) => {
              this.adBanner2Ref = ref;
            }}
          />
          <ADBanner
            wrapperStyle={styles.wrapperStyle}
            adGroup={170}
            relatedBizCodes={[{ pageStatus: adStatus || "" }]}
            imageWidth={WINDOW_WIDTH - 32}
            imageHeight={(WINDOW_WIDTH - 32) * (80 / 328)}
            creativesExposeHandler={this.adBannerExpose}
            creativesClickHandler={this.adBannerClick}
            onLoad={this.adBanner3OnLoad}
            ref={(ref: ADBanner) => {
              this.adBanner3Ref = ref;
            }}
          />
        </>
      );
    } else {
      return null;
    }
  };

  renderHeader = () => {
    const { t } = this.props;
    const { statusImg, title, msg } = this.returnImgAndtitle();
    return (
      <View style={styles.container}>
        <View style={styles.topView}>
          <Image source={statusImg} style={styles.img} />
          <TouchableOpacity onPress={this.onBackPress} style={styles.btnStyle}>
            <Text style={styles.buttonTitle}>{t("完成1")}</Text>
          </TouchableOpacity>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.reason}>{msg}</Text>
        </View>
        <View style={styles.adView}>{this.renderADView()}</View>
      </View>
    );
  };

  render() {
    const { t } = this.props;
    if (this.state.loading) {
      return null;
    }
    return (
      <RecommendList
        RecommendScene={RecommendScene.AUTHORIZE_CREDIT_RESULT}
        renderHeader={this.renderHeader}
        uniqKey={"authorizeCreditResult"}
        onItemClick={this.onItemClick}
        getExtPitInfo={this._getExtPitInfo}
        title={t("新人低价购")}
      />
    );
  }
}
