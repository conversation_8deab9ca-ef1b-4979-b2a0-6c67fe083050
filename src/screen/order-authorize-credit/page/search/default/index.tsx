import React, { PureComponent, Component } from "react";
import { View, Text, TouchableOpacity, Image, TextInput, FlatList, DeviceEventEmitter } from "react-native";
import { NavigationBar } from "@akulaku-rn/akui-rn";
import Toast from "@akulaku-rn/akui-rn/src/components/Toast";
import store from "../../../store";
import _ from "lodash";
import { inject, observer } from "mobx-react";
import styles from "./styles";
import api from "../../../store/api";
import RootView from "common/components/Layout/RootView";
import { withTranslation } from "common/services/i18n";
import { HocActionLogInput, HocActionLogInputCompProps } from "@akulaku-rn/rn-v4-sdk";
import { InputBizInfo } from "@akulaku-rn/rn-v4-sdk/src/ReporterSdk/types";
import { ItemType } from "../../../dict/ComponentType";

type Props = {
  t: any;
  store: any;
  navigation: any;
  configPageInfo: any;
};

type State = {
  data: any[];
  isfocus: boolean;
};

@RootView({
  store
})
@withTranslation("OrderIDApplyInfo")
@inject("store")
@observer
export default class CreditSearchPage extends Component<Props, State> {
  flatListRef: any;

  textInputRef: any;

  searchKey: any;

  actionLogKey: string;

  ActionTextInput: React.ComponentType<HocActionLogInputCompProps>;

  constructor(props: Props) {
    super(props);
    const {
      store: { navParams }
    } = props;
    this.state = {
      data: [],
      isfocus: false
    };
    this.searchKey = "";
    this.textInputRef = null;
    this.actionLogKey = "";
    if (navParams.type === ItemType.search_company) {
      this.props.configPageInfo(
        {
          sn: 300021,
          sp: { status: navParams.isFirstApply ? "first" : "not_first" }
        },
        false
      );
    }
    this.ActionTextInput = this.getActionTextInput();
  }

  componentDidMount() {
    this.featchCompanyData();
  }

  getActionTextInput = () => {
    const navParams = this.props.store.navParams;
    const v4Dic =
      navParams.type === ItemType.search_company
        ? {
            sn: 300021,
            cn: 1,
            sp: { status: navParams.isFirstApply ? "first" : "not_first" }
          }
        : ({} as InputBizInfo);
    if (!this.ActionTextInput) {
      this.ActionTextInput = HocActionLogInput(v4Dic)(TextInput);
    }
    return this.ActionTextInput;
  };

  didSelectedItem = (data: { name: string }) => {
    const {
      store: { navParams }
    } = this.props;
    const params = {
      data,
      id: navParams.id
    };
    DeviceEventEmitter.emit("setValue", params);
    this.props.navigation.goBack();
  };

  featchCompanyData = () => {
    if (!this.searchKey || !this.searchKey.length) return;
    const {
      store: {
        pageStore,
        navParams,
        runtime: { countryId }
      }
    } = this.props;
    let url = "";
    switch (navParams.type) {
      case ItemType.search_company:
        url = api.SEARCH_COMPANY;
        break;
    }
    pageStore.post(
      url,
      { key: this.searchKey, countryId },
      (response: any) => {
        const { data } = response;
        if (!!data) {
          this.setState({ data });
        }
      },
      (error: any) => {
        const { message } = error;
        Toast.show(message, {
          position: 0
        });
      }
    );
  };

  returnNewData = (data: { name: any; id: string }[]) => {
    const { navParams } = this.props.store;
    if (navParams.type !== ItemType.search_company) {
      return data;
    } else {
      const newData = data;
      const findIndex = data.findIndex((item: { name: any }) => item.name === this.searchKey);
      if (findIndex === -1) {
        newData.push({ name: this.searchKey, id: "1437" });
      }
      return newData;
    }
  };

  _handleSearchKey = _.debounce(() => {
    this.featchCompanyData();
  }, 500);

  onChangeText = (searchKey: string) => {
    this.searchKey = searchKey;
    this._handleSearchKey();
  };

  renderItem = ({ item, index }: { item: any; index: number }) => {
    return <Button data={item} key={index} didSelectedItem={this.didSelectedItem} />;
  };

  listHeaderComponent = () => {
    if (!this.searchKey) {
      return (
        <View style={{ paddingVertical: 8, paddingLeft: 16, backgroundColor: "#F6F6F5" }}>
          <Text
            style={{
              fontSize: 14,
              color: "#979696",
              textAlignVertical: "center"
            }}
          >
            {this.props.t("推荐")}
          </Text>
        </View>
      );
    } else {
      return null;
    }
  };

  onFocus = () => {
    this.setState({ isfocus: true });
  };

  onBlur = () => {
    this.setState({ isfocus: false });
  };

  render() {
    const {
      t,
      store: { navParams }
    } = this.props;
    const { data } = this.state;
    const ActionTextInput = this.getActionTextInput();
    return (
      <View style={styles.container}>
        <NavigationBar title={navParams.title} />
        <View style={styles.searchBorderView}>
          <ActionTextInput
            sensitive={false}
            screen={navParams.screen}
            getRef={(ref: TextInput) => (this.textInputRef = ref)}
            selectionColor={"#e62117"}
            style={styles.textInput}
            placeholder={t("请输入名称")}
            placeholderTextColor={"#b3b3b3"}
            secureTextEntry={false}
            onChangeText={this.onChangeText}
          />
          <Image style={styles.searchIcon} source={require("../../../img/credit_compangy_ico_search.webp")} />
        </View>
        {!!data.length && (
          <FlatList
            style={{ backgroundColor: "#fff" }}
            showsVerticalScrollIndicator={false}
            data={data}
            renderItem={this.renderItem}
          />
        )}
      </View>
    );
  }
}

type SelectProps = {
  data: any;
  didSelectedItem: any;
};
class Button extends PureComponent<SelectProps> {
  selectItem = () => {
    const { data, didSelectedItem } = this.props;
    didSelectedItem(data);
  };

  render() {
    const { data } = this.props;
    return (
      <TouchableOpacity style={styles.button} onPress={this.selectItem}>
        <Text style={styles.buttonTitle}>{data.name}</Text>
      </TouchableOpacity>
    );
  }
}
