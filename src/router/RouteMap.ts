const routeConfig = {
  userHomeScreen: "js:/user/homescreen",
  billscreen: "js:/bill/billscreen",
  billscreenV2: "js:/bill/billscreen/billscreenV2",
  billHistory: "js:/bill/billscreen/histroy",
  billDetail: "js:/bill/billscreen/detail",
  billDetailV2: "js:/bill/billscreen/detailV2",
  billCompensation: "js:/bill/billscreen/detail/billCompensation",
  billDueDetail: "js:/bill/billscreen/duedetail",
  billHistoryCompleted: "js:/bill/billscreen/historycompleted",
  billHistoryRepayment: "js:/bill/billscreen/historyrepayment",
  billHistoryCompletedDetails: "js:/bill/billscreen/itemdetailsdetails",
  billDetailItemDetails: "js:/bill/billscreen/itemdetails",
  billRepay: "js:/bill/billscreen/repay",
  newBillRepay: "js:/bill/newBillRepay",
  billAdvancedRepayList: "js:/bill/billscreen/advancedrepaylist",
  billAdvancedRepayDetail: "js:/bill/billscreen/advancedrepaydetail",
  billBalanceRepayResult: "js:/bill/billscreen/balancerepayresult",
  billExtension: "js:/bill/billscreen/extension",
  RepaymentResult: "js:/pay/billscreen/RepaymentResult",
  aotoRepayDetail: "js:/bill/auto-repay/detail",
  aotoRepayResult: "js:/bill/auto-repay/result",
  leaderBoards: "js:/activity/leaderBoards",
  forYou: "js:/activity/forYou",
  hetong: "js:/user/hetong",
  electricSignScreen: "js:/user/basic/electric-sign",
  electricSignScreenGuide: "js:/user/basic/electric-sign/guide",
  SignPage: "js:/user/basic/electric-sign/signPage",
  SignResult: "js:/user/basic/electric-sign/signResult",
  ContractPdf: "js:/user/basic/electric-sign/contractPdfPage",
  flashSaleNewScreen: "js:/activity/flashsale-new",
  flashsaleBranchVenue: "js:/activity/flashsale/BranchVenue",
  creditGuideCoupon: "js:/activity/credit-guide/coupon",
  creditGuideProduct: "js:/activity/credit-guide/product",
  helpCenterFAQScreen: "js:/user/helpcenter/faq",
  helpCenterQuestions: "js:/user/helpcenter/questions",
  helpCenterFeedbackScreen: "js:/user/helpcenter/feedbackscreen",
  helpCenterHomeScreen: "js:/user/helpcenter/homeScreen",
  SelfPublishScreenNormal: "js:/activity/self-publish/normal",
  SelfPublishScreenRank: "js:/activity/self-publish/rank",
  SelfPublishScreenForm: "js:/activity/self-publish/form",
  SelfPublishScreenTab: "js:/activity/self-publish/tab",
  SelfPublishScreenNav: "js:/activity/self-publish/nav",
  EvaluationHome: "js:/trade/evaluationHome",
  EvaluationSubmit: "js:/trade/evaluationSubmit",
  AllEvaluation: "js:/trade/allEvaluation",
  FoldEvaluation: "js:/trade/foldEvaluation",
  EvaluationRule: "js:/trade/evaluationRule",
  payMethodScreen: "js:/pay/pay-settlement/payMethod",
  PayWebViewScreen: "js:/pay/pay-settlement/payMethod/payWebView",
  PayResultForwardScreen: "js:/pay/pay-settlement/payMethod/payResultForward",
  payDetailVAScreen: "js:/pay/pay-settlement/payDetail/va",
  payDetailStoreScreen: "js:/pay/pay-settlement/payDetail/store",
  PayDetailMidtransScreen: "js:/pay/pay-settlement/payDetail/midtrans",
  PayResultScreen: "js:/pay/pay-settlement/payResult",
  OrderConfirm: "js:/trade/pay-settlement/order-confirm",
  LoadingContainer: "js:/trade/pay-settlement/loadingContainer",
  Transfer: "js:/pay/transfer",
  TransferHistory: "js:/pay/transferHistory",
  TransferResult: "js:/pay/transferResutl",
  TransferDetails: "js:/pay/transferDetails",
  TransferRecipients: "js:/pay/transferRecipients",
  AuthorizeCreditApplyInfo: "js:/user/authorize-credit/apply-info",
  CreditResultPage: "js:/user/authorize-credit/result-page",
  CreditInitialsSearch: "js:/user/authorize-credit/initials-search",
  CreditSearchPage: "js:/user/authorize-credit/search-page",
  OrderAuthorizeCreditApplyInfo: "js:/user/order-authorize-credit/apply-info",
  OrderCreditResultPage: "js:/user/order-authorize-credit/result-page",
  OrderCreditInitialsSearch: "js:/user/order-authorize-credit/initials-search",
  OrderCreditSearchPage: "js:/user/order-authorize-credit/search-page",
  // OldAuthorizeCreditApplyInfo: "js:/user/old-authorize-credit/apply-info",
  // OldCreditResultPage: "js:/user/old-authorize-credit/result-page",
  // OldCreditInitialsSearch: "js:/user/old-authorize-credit/initials-search",
  // OldCreditSearchPage: "js:/user/old-authorize-credit/search-page",
  CreditEcommerceAuth: "js:/user/authorize-credit/ecommerce-auth",
  CreditSocialSecurity: "js:/user/authorize-credit/social-security",
  CreditIdSocialSecurity: "js:/user/authorize-credit/id-social-security",
  balanceHomeScreen: "js:/trade/balance/balance-home",
  cashbackHomeScreen: "js:/trade/balance/cashback-home",
  balanceDetailScreen: "js:/trade/balance/balance-detail",
  bankEditScreen: "js:/trade/balance/bank-edit",
  withdrawResultScreen: "js:/trade/balance/withdraw-result",
  afiWithdraw: "js:/trade/balance/afiWithdraw",
  afiWithdrawDetail: "js:/trade/balance/afiWithdraw_detail",
  RnInfomation: "js:/user/RnInfomation",
  VerificationSMS: "js:/pay/bank/VerificationSMS",
  OpenAccount: "js:/pay/bank/OpenAccount",
  BNCHome: "js:/pay/main/BNCHome",
  NewsPage: "js:/pay/bank/News",
  BNCRecharge: "js:/pay/bank/Recharge",
  TransactionOTP: "js:/pay/transactionOTP",
  OpenAccountWebView: "js:/pay/OpenAccountWebView",
  SecurityCenter: "js:/user/securityCenter/home",
  SecurityFaqDetail: "js:/user/securityCenter/faq",
  SecurityDeviceRecords: "js:/user/securityCenter/deviceRecords",
  SecurityOptimizableDetail: "js:/user/securityCenter/optimizable",
  SecurityUnfreeze: "js:/user/securityCenter/unfreeze",
  PINCodeManagement: "js:/user/securityCenter/PINCodeManagement",
  PINVerificationSettings: "js:/user/securityCenter/PINVerificationSettings",
  ResetPassword: "js:/user/securityCenter/ResetPassword",
  UnfreezeResult: "js:/user/securityCenter/UnfreezeResult",
  EmerencyFreeze: "js:/user/securityCenter/EmerencyFreeze",
  ForeverFreeze: "js:/user/securityCenter/ForeverFreeze",
  NoLoginFreeze: "js:/user/securityCenter/NoLoginFreeze",
  CustomerService: "js:/user/securityCenter/CustomerService",
  FreezeResult: "js:/user/securityCenter/FreezeResult",
  orderList: "js:/trade/order/list",
  orderDetail: "js:/trade/order/detail",
  RefundChooseType: "js:/trade/order/RefundChooseType",
  OrderAgreement: "js:/trade/order/agreement",
  orderRefundList: "js:/trade/order/refundList",
  orderRefundDetail: "js:/trade/order/refundDetail",
  orderRefundConsultList: "js:/trade/order/refundConsultList",
  orderRefundApply: "js:/trade/order/refundApply",
  OrderCancelResult: "js:/trade/order/OrderCancelResult",
  LogisticsFill: "js:/trade/order/LogisticsFill",
  ChangeAddressPage: "js:/trade/order/ChangeAddressPage",
  ComplaintSubmitScreen: "js:/trade/order/complaintSubmitScreen",
  ComplaintDetailScreen: "js:/trade/order/complaintDetailScreen",
  OrderSnapshotDetail: "js:/trade/order/OrderSnapshotDetail",
  OrderSnapshotList: "js:/trade/order/OrderSnapshotList",
  OldSystemOrderDetail: "js:/trade/order/OldSystemOrderDetail",
  BrandWallScreen: "js:/activity/brand/brand-wall",
  BrandListScreen: "js:/activity/brand/brand-list",
  BrandDetailScreen: "js:/activity/brand/brand-detail",
  IncreaseLimit: "js:/user/increaseLimit",
  IncreaseLimitV2: "js:/user/increaseLimitV2",
  TemporaryResult: "js:/user/TemporaryResult",
  IncreaseLimitByInfo: "js:/user/increaseLimit/increase_by_info",
  IncreaseLimitDetail: "js:/user/increaseLimit/limit_detail",
  IncreaseLimitByEmail: "js:/user/increaseLimit/increase_infomation/email",
  IncreaseInfomationPage: "js:/user/increaseLimit/infomation_page",
  AuthPage: "js:/user/auth-page",
  IncreaseAuthCallback: "js:/user/increaseLimit/increaseAuthCallback",
  ResultPage: "js:/user/auth-result",
  SocialTestScreen: "js:/user/authorize-credit/socialTest",
  VirtualServices: "js:/trade/virtual-services/virtualServices",
  CrashScreen: "js:/user/CrashScreen",
  accountTransactionHistory: "js:/pay/accountTransactionHistory",
  BnplUpgrade: "js:/trade/bnpl/bnplUpgrade",
  openpayMerchantOfflineList: "js:/trade/openpaymerchant/offlineList",
  openpayMerchantDetail: "js:/trade/openpaymerchant/detail",
  openpayMerchantSearch: "js:/trade/openpaymerchant/search",
  openpayBlacklist: "js:/trade/openpay/blacklist",
  bnplChannel: "js:/trade/bnplChannel",
  AuthorizeCreditUpdate: "js:/user/authorize-credit/authorize-credit-update",
  CreditUpdateResultPage: "js:/user/authorize-credit/authorize-credit-update-result",
  SelectAddress: "js:/user/selectAddress/select-address",
  SelectProvince: "js:/user/selectAddress/select-province",
  SelectCity: "js:/user/selectAddress/select-city",
  ReportFraudTicketHome: "js:/user/report-fraud-ticket/home",
  ReportFraudTicketResult: "js:/user/report-fraud-ticket/result",
  AccountLogoutReminder: "js:/user/accountLogoutReminder",
  AccountLogoutInstruction: "js:/user/accountLogoutInstruction",
  VideoPage: "js:/user/VideoPage"
};
export default routeConfig;
