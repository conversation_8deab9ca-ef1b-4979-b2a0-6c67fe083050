import HomeScreen from "../screen/homescreen";
import HelpCenterFeedbackScreen from "../screen/helpcenter/feedbackscreen";
import HeplCenterFaq from "../screen/helpcenter/faq";
import HelpCenterHomeScreen from "../screen/helpcenter/homeScreen";
import QuestionsScreen from "../screen/helpcenter/questions";
import IncreaseLimitHome from "../screen/increaseLimit/main";
import IncreaseLimitHomeV2 from "../screen/increaseLimit/mainV2";
import IncreaseLimitByInfo from "../screen/increaseLimit/increase_infomation";
import IncreaseLimitDetail from "../screen/increaseLimit/limit_detail";
import IncreaseInfomationPage from "../screen/increaseLimit/increase_infomation_Item_page";
import RnInfomation from "../screen/rn-infomation";
import HeTongScreen from "../screen/hetong";
import HeTongGuidepage from "../screen/hetong/guidepage";
import SignResult from "../screen/hetong/signResult";
import SignPage from "../screen/hetong/signPage";
import ContractPdfPage from "../screen/hetong/ContractPdfPage";
import AuthPage from "../screen/auth";
import IncreaseLimitByEmail from "../screen/increaseLimit/component/Email";
import SecurityCenter from "../screen/securityCenter/pages/home";
import SecurityFaqDetail from "../screen/securityCenter/pages/faqDetail";
import SecurityDeviceRecords from "../screen/securityCenter/pages/deviceRecords";
import SecurityOptimizableDetail from "../screen/securityCenter/pages/optimizableDetail";
import PINCodeManagement from "../screen/securityCenter/pages/SettingPINPage/PINCodeManagement";
import PINVerificationSettings from "../screen/securityCenter/pages/SettingPINPage/PINVerificationSettings";
import Unfreeze from "../screen/securityCenter/pages/unfreeze";
import CrashScreen from "../screen/crash";
import AuthorizeCreditHome from "../screen/authorize-credit/page/home";
import AuthorizeCreditInitialsSearch from "../screen/authorize-credit/page/search/initials";
import AuthorizeCreditResult from "../screen/authorize-credit/page/resultPage";
import AuthorizeCreditSearch from "../screen/authorize-credit/page/search/default";
import OrderAuthorizeCreditHome from "../screen/order-authorize-credit/page/home";
import OrderAuthorizeCreditInitialsSearch from "../screen/order-authorize-credit/page/search/initials";
import OrderAuthorizeCreditResult from "../screen/order-authorize-credit/page/resultPage";
import OrderAuthorizeCreditSearch from "../screen/order-authorize-credit/page/search/default";
import AuthorizeCreditUpdate from "../screen/credit-update/page/home";
import CreditUpdateResultPage from "../screen/credit-update/page/resultPage";
import SocialTest from "../screen/social-test";
import ResetPassword from "../screen/securityCenter/pages/resetPassword";
import UnfreezeResult from "../screen/securityCenter/pages/unfreezeResult";
import EmerencyFreeze from "../screen/securityCenter/pages/emerencyFreeze";
import NoLoginFreeze from "../screen/securityCenter/pages/noLoginFreeze";
import CustomerService from "../screen/securityCenter/pages/customerService";
import ForeverFreeze from "../screen/securityCenter/pages/forerverFreeze";
import FreezeResult from "../screen/securityCenter/pages/freezeResult";
import IncreaseAuthCallback from "../screen/increaseLimit/increaseAuthCallback";
import ResultPage from "../screen/increaseLimit/result";
import TemporaryResult from "../screen/increaseLimit/temporary_result/TemporaryResult";
import SelectAddress from "../screen/select-address";
import SelectProvince from "../screen/select-address/select-province/";
import SelectCity from "../screen/select-address/select-city/";
import ReportFraudTicketHome from "../screen/report-fraud-ticket/pages/home";
import ReportFraudTicketResult from "../screen/report-fraud-ticket/pages/result";
import AccountLogoutReminder from "../screen/account-logout/AccountLogoutReminder";
import AccountLogoutInstruction from "../screen/account-logout/AccountLogoutInstruction";
import VideoPage from "../screen/VideoPage";
export default {
  "/user/homescreen": HomeScreen,
  "/user/helpcenter/homeScreen": HelpCenterHomeScreen,
  "/user/helpcenter/feedbackscreen": HelpCenterFeedbackScreen,
  "/user/helpcenter/faq": HeplCenterFaq,
  "/user/helpcenter/questions": QuestionsScreen,
  "/user/authorize-credit/apply-info": AuthorizeCreditHome,
  "/user/authorize-credit/result-page": AuthorizeCreditResult,
  "/user/authorize-credit/initials-search": AuthorizeCreditInitialsSearch,
  "/user/authorize-credit/search-page": AuthorizeCreditSearch,
  "/user/order-authorize-credit/apply-info": OrderAuthorizeCreditHome,
  "/user/order-authorize-credit/result-page": OrderAuthorizeCreditResult,
  "/user/order-authorize-credit/initials-search": OrderAuthorizeCreditInitialsSearch,
  "/user/order-authorize-credit/search-page": OrderAuthorizeCreditSearch,
  "/user/increaseLimit": IncreaseLimitHome,
  "/user/increaseLimitV2": IncreaseLimitHomeV2,
  "/user/TemporaryResult": TemporaryResult,
  "/user/increaseLimit/increase_by_info": IncreaseLimitByInfo,
  "/user/increaseLimit/limit_detail": IncreaseLimitDetail,
  "/user/increaseLimit/increase_infomation/email": IncreaseLimitByEmail,
  "/user/increaseLimit/increaseAuthCallback": IncreaseAuthCallback,
  "/user/RnInfomation": RnInfomation,
  "/user/hetong": HeTongScreen,
  "/user/basic/electric-sign/guide": HeTongGuidepage,
  "/user/basic/electric-sign": HeTongScreen,
  "/user/basic/electric-sign/signResult": SignResult,
  "/user/basic/electric-sign/signPage": SignPage,
  "/user/basic/electric-sign/contractPdfPage": ContractPdfPage,
  "/user/increaseLimit/infomation_page": IncreaseInfomationPage,
  "/user/auth-page": AuthPage,
  "/user/auth-result": ResultPage,
  "/user/CrashScreen": CrashScreen,
  "/user/securityCenter/home": SecurityCenter,
  "/user/securityCenter/faq": SecurityFaqDetail,
  "/user/securityCenter/deviceRecords": SecurityDeviceRecords,
  "/user/securityCenter/optimizable": SecurityOptimizableDetail,
  "/user/securityCenter/unfreeze": Unfreeze,
  "/user/securityCenter/PINCodeManagement": PINCodeManagement,
  "/user/securityCenter/PINVerificationSettings": PINVerificationSettings,
  "/user/securityCenter/ResetPassword": ResetPassword,
  "/user/securityCenter/UnfreezeResult": UnfreezeResult,
  "/user/securityCenter/EmerencyFreeze": EmerencyFreeze,
  "/user/securityCenter/ForeverFreeze": ForeverFreeze,
  "/user/securityCenter/NoLoginFreeze": NoLoginFreeze,
  "/user/securityCenter/CustomerService": CustomerService,
  "/user/securityCenter/FreezeResult": FreezeResult,
  "/user/authorize-credit/socialTest": SocialTest,
  "/user/authorize-credit/authorize-credit-update": AuthorizeCreditUpdate,
  "/user/authorize-credit/authorize-credit-update-result": CreditUpdateResultPage,
  "/user/selectAddress/select-address": SelectAddress,
  "/user/selectAddress/select-province": SelectProvince,
  "/user/selectAddress/select-city": SelectCity,
  "/user/report-fraud-ticket/home": ReportFraudTicketHome,
  "/user/report-fraud-ticket/result": ReportFraudTicketResult,
  "/user/accountLogoutReminder": AccountLogoutReminder,
  "/user/accountLogoutInstruction": AccountLogoutInstruction,
  "/user/VideoPage": VideoPage
};
