import React, { Component } from "react";
import App from "common/App";
import { updateGlobalRouteMap } from "common/split-package-config";
import router from "./router";
import routeConfig from "./router/RouteMap";
import registerCardManageRoute from "common/screen/card-manage-center/registerRoute";
type Props = {
  params: any;
  configInfo: any;
  screen: string;
  scriptUrl: string;
};
// 注册绑卡公共业务模块
registerCardManageRoute("user", router);
// 更新全局routeMap
updateGlobalRouteMap(routeConfig);
export default class Root extends Component<Props> {
  render() {
    return <App pathPrefix={"user"} bundleFileName={"user"} router={router} {...this.props} />;
  }
}
